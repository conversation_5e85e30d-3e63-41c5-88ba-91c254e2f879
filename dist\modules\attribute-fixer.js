"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/attribute-fixer.js
  var AttributeFixerSystem = class {
    static {
      __name(this, "AttributeFixerSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.fixesApplied = [];
      this.originalAttributes = /* @__PURE__ */ new Map();
    }
    async init() {
      try {
        this.backupOriginalAttributes();
        this.fixCapitalizationIssues();
        this.fixTypos();
        this.fixCategoryAssignments();
        this.fixAttributeOrder();
        this.fixDuplicates();
        this.isInitialized = true;
      } catch (error) {
        console.error("\u274C AttributeFixerSystem initialization failed:", error);
      }
    }
    backupOriginalAttributes() {
      const patrimonioElements = document.querySelectorAll(
        ".patrimonio_interactive_item[ativo-category][ativo-product]"
      );
      patrimonioElements.forEach((el, index) => {
        this.originalAttributes.set(`patrimonio-${index}`, {
          category: el.getAttribute("ativo-category"),
          product: el.getAttribute("ativo-product"),
          element: el
        });
      });
      const ativosElements = document.querySelectorAll(
        ".ativos-grafico-item[ativo-category][ativo-product]"
      );
      ativosElements.forEach((el, index) => {
        this.originalAttributes.set(`ativos-${index}`, {
          category: el.getAttribute("ativo-category"),
          product: el.getAttribute("ativo-product"),
          element: el
        });
      });
    }
    fixCapitalizationIssues() {
      this.applyAttributeFix(
        '.patrimonio_interactive_item[ativo-category="Fundo de Investimento"]',
        "ativo-category",
        "Fundo de investimento",
        "Capitalization: Fundo de Investimento \u2192 Fundo de investimento"
      );
      this.applyAttributeFix(
        '.patrimonio_interactive_item[ativo-category="Renda Vari\xE1vel"]',
        "ativo-category",
        "Renda vari\xE1vel",
        "Capitalization: Renda Vari\xE1vel \u2192 Renda vari\xE1vel"
      );
    }
    fixTypos() {
      this.applyAttributeFix(
        '.patrimonio_interactive_item[ativo-product="Popupan\xE7a"]',
        "ativo-product",
        "Poupan\xE7a",
        "Typo fix: Popupan\xE7a \u2192 Poupan\xE7a"
      );
    }
    fixCategoryAssignments() {
      this.applyAttributeFix(
        '.patrimonio_interactive_item[ativo-category="Opera\xE7\xE3o compromissada"][ativo-product="Criptoativos"]',
        "ativo-category",
        "Outros",
        'Category fix: Criptoativos moved from "Opera\xE7\xE3o compromissada" to "Outros"'
      );
    }
    fixAttributeOrder() {
      const elementsToReorder = document.querySelectorAll("[ativo-product][ativo-category]");
      elementsToReorder.forEach((el) => {
        const category = el.getAttribute("ativo-category");
        const product = el.getAttribute("ativo-product");
        if (category && product) {
          el.removeAttribute("ativo-category");
          el.removeAttribute("ativo-product");
          el.setAttribute("ativo-category", category);
          el.setAttribute("ativo-product", product);
          this.fixesApplied.push(`Attribute order: Standardized order for ${category} + ${product}`);
        }
      });
    }
    fixDuplicates() {
      const ativosElements = document.querySelectorAll(
        ".ativos-grafico-item[ativo-category][ativo-product]"
      );
      const seen = /* @__PURE__ */ new Set();
      const duplicates = [];
      ativosElements.forEach((el) => {
        const category = el.getAttribute("ativo-category");
        const product = el.getAttribute("ativo-product");
        const key = `${category}|${product}`;
        if (seen.has(key)) {
          duplicates.push({ element: el, category, product });
        } else {
          seen.add(key);
        }
      });
      duplicates.forEach((duplicate) => {
        if (duplicate.category === "Fundo de investimento" && duplicate.product === "A\xE7\xF5es") {
          duplicate.element.setAttribute("ativo-category", "Renda vari\xE1vel");
          this.fixesApplied.push(
            'Duplicate fix: Changed duplicate "Fundo de investimento + A\xE7\xF5es" to "Renda vari\xE1vel + A\xE7\xF5es"'
          );
        }
      });
    }
    applyAttributeFix(selector, attribute, newValue, description) {
      const elements = document.querySelectorAll(selector);
      elements.forEach((el) => {
        el.setAttribute(attribute, newValue);
        this.fixesApplied.push(`${description} (${selector})`);
      });
      if (elements.length === 0) {
        console.warn(`  \u26A0\uFE0F No elements found for selector: ${selector}`);
      }
    }
    logFixesSummary() {
    }
    // Validation method to check if fixes worked
    validateFixes() {
      const patrimonioElements = document.querySelectorAll(
        ".patrimonio_interactive_item[ativo-category][ativo-product]"
      );
      const ativosElements = document.querySelectorAll(
        ".ativos-grafico-item[ativo-category][ativo-product]"
      );
      let successfulPairs = 0;
      const patrimonioAttribs = Array.from(patrimonioElements).map((el) => ({
        category: el.getAttribute("ativo-category"),
        product: el.getAttribute("ativo-product")
      }));
      const ativosAttribs = Array.from(ativosElements).map((el) => ({
        category: el.getAttribute("ativo-category"),
        product: el.getAttribute("ativo-product")
      }));
      patrimonioAttribs.forEach((p) => {
        const hasMatch = ativosAttribs.some(
          (a) => a.category === p.category && a.product === p.product
        );
        if (hasMatch) {
          successfulPairs += 1;
        } else {
          console.warn(`\u274C UNPAIRED: "${p.category}" + "${p.product}"`);
        }
      });
      const successRate = successfulPairs / patrimonioElements.length * 100;
      if (successRate === 100) {
      } else {
        console.warn("\u26A0\uFE0F Some elements still cannot be paired. Check logs above.");
      }
      return { successfulPairs, total: patrimonioElements.length, successRate };
    }
    // Restore original attributes (for debugging/testing)
    restoreOriginalAttributes() {
      this.originalAttributes.forEach((backup) => {
        const el = backup.element;
        if (el && el.parentNode) {
          el.setAttribute("ativo-category", backup.category);
          el.setAttribute("ativo-product", backup.product);
        }
      });
      this.fixesApplied = [];
    }
    // Get detailed status
    getStatus() {
      return {
        initialized: this.isInitialized,
        fixesApplied: this.fixesApplied.length,
        hasBackup: this.originalAttributes.size > 0,
        fixes: this.fixesApplied
      };
    }
    // Cleanup method
    cleanup() {
      this.fixesApplied = [];
      this.originalAttributes.clear();
      this.isInitialized = false;
    }
  };
  var attribute_fixer_default = AttributeFixerSystem;
})();
//# sourceMappingURL=attribute-fixer.js.map
