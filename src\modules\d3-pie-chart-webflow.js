/**
 * D3.js Pie Chart Webflow Integration System
 * Creates interactive pie charts in the main-area-content div using D3.js
 * 
 * Features:
 * ✅ D3.js powered pie chart with smooth animations
 * ✅ Hover effects and tooltips
 * ✅ Responsive design
 * ✅ Integration with app-calc-reino data
 * ✅ Automatic updates when data changes
 */

import { eventCoordinator } from './event-coordinator.js';

export class D3PieChartWebflowSystem {
  constructor() {
    this.isInitialized = false;
    this.d3 = null;
    this.chartContainer = null;
    this.svg = null;
    this.currentData = null;
    this.config = {
      width: 400,
      height: 400,
      margin: { top: 20, right: 20, bottom: 20, left: 20 },
      radius: 150,
      colors: [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
      ],
      animationDuration: 750,
      hoverScale: 1.05
    };
  }

  async init() {
    if (this.isInitialized) return;

    try {
      // Wait for D3.js to be available
      await this.waitForD3();
      
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // Initialize chart container
      this.initializeContainer();
      
      // Setup event listeners
      this.setupEventListeners();
      
      this.isInitialized = true;
      
      console.log('✅ [D3PieChart] System initialized successfully');
      
    } catch (error) {
      console.error('❌ [D3PieChart] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Wait for D3.js library to be available
   */
  async waitForD3() {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      const maxAttempts = 100; // 5 seconds max wait
      
      const checkD3 = () => {
        if (window.d3) {
          this.d3 = window.d3;
          resolve();
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(checkD3, 50);
        } else {
          reject(new Error('D3.js library not found'));
        }
      };
      
      checkD3();
    });
  }

  /**
   * Initialize the chart container in main-area-content
   */
  initializeContainer() {
    // Find the main-area-content div
    this.chartContainer = document.querySelector('.main-area-content');
    
    if (!this.chartContainer) {
      console.warn('⚠️ [D3PieChart] main-area-content div not found');
      return;
    }

    // Clear any existing content
    this.chartContainer.innerHTML = '';
    
    // Set container styles
    this.chartContainer.style.display = 'flex';
    this.chartContainer.style.justifyContent = 'center';
    this.chartContainer.style.alignItems = 'center';
    this.chartContainer.style.minHeight = '400px';
    this.chartContainer.style.padding = '20px';
    
    console.log('✅ [D3PieChart] Container initialized');
  }

  /**
   * Setup event listeners for data updates
   */
  setupEventListeners() {
    // Listen for patrimony data changes
    eventCoordinator.on('patrimonySyncUpdated', (data) => {
      this.updateChart(data);
    });

    // Listen for resultado section visibility
    eventCoordinator.on('sectionVisibilityChanged', (data) => {
      if (data.sectionName === 'resultado' && data.isVisible) {
        this.animateChart();
      }
    });

    // Listen for asset selection changes
    eventCoordinator.on('assetSelectionChanged', (data) => {
      this.updateChart(data);
    });
  }

  /**
   * Create or update the pie chart
   */
  createChart(data) {
    if (!this.chartContainer || !this.d3 || !data) {
      console.warn('⚠️ [D3PieChart] Cannot create chart - missing dependencies');
      return;
    }

    // Clear existing chart
    this.chartContainer.innerHTML = '';
    
    const { width, height, margin, radius } = this.config;
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Create SVG
    this.svg = this.d3.select(this.chartContainer)
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .style('background', 'transparent');

    const g = this.svg.append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // Create pie generator
    const pie = this.d3.pie()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = this.d3.arc()
      .innerRadius(0)
      .outerRadius(radius);

    // Create hover arc (slightly larger)
    const hoverArc = this.d3.arc()
      .innerRadius(0)
      .outerRadius(radius * this.config.hoverScale);

    // Process data
    const processedData = this.processData(data);
    const pieData = pie(processedData);

    // Create color scale
    const color = this.d3.scaleOrdinal()
      .domain(processedData.map(d => d.name))
      .range(this.config.colors);

    // Create slices
    const slices = g.selectAll('.slice')
      .data(pieData)
      .enter()
      .append('g')
      .attr('class', 'slice');

    // Add paths
    slices.append('path')
      .attr('d', arc)
      .attr('fill', d => color(d.data.name))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseover', (event, d) => this.onSliceHover(event, d, hoverArc))
      .on('mouseout', (event, d) => this.onSliceOut(event, d, arc))
      .on('click', (event, d) => this.onSliceClick(event, d));

    // Add labels
    slices.append('text')
      .attr('transform', d => `translate(${arc.centroid(d)})`)
      .attr('text-anchor', 'middle')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .attr('fill', '#fff')
      .attr('stroke', '#000')
      .attr('stroke-width', '0.5px')
      .text(d => `${d.data.percentage.toFixed(1)}%`);

    // Create legend
    this.createLegend(processedData, color);
    
    // Create tooltip
    this.createTooltip();

    this.currentData = data;
    console.log('✅ [D3PieChart] Chart created successfully');
  }

  /**
   * Process raw data into chart format
   */
  processData(data) {
    if (!data || !data.ativos || !data.ativos.alocacao) {
      return [];
    }

    const total = data.patrimonio?.total || 0;
    
    return data.ativos.alocacao.map((ativo, index) => ({
      name: ativo.nome || ativo.product || `Ativo ${index + 1}`,
      value: parseFloat(ativo.value) || 0,
      percentage: total > 0 ? (parseFloat(ativo.value) / total) * 100 : 0,
      category: ativo.category || 'Outros',
      originalData: ativo
    })).filter(item => item.value > 0);
  }

  /**
   * Create legend for the chart
   */
  createLegend(data, colorScale) {
    const legendContainer = this.d3.select(this.chartContainer)
      .append('div')
      .style('margin-top', '20px')
      .style('display', 'flex')
      .style('flex-wrap', 'wrap')
      .style('justify-content', 'center')
      .style('gap', '10px');

    const legendItems = legendContainer.selectAll('.legend-item')
      .data(data)
      .enter()
      .append('div')
      .attr('class', 'legend-item')
      .style('display', 'flex')
      .style('align-items', 'center')
      .style('gap', '5px')
      .style('padding', '5px 10px')
      .style('background', '#f8f9fa')
      .style('border-radius', '15px')
      .style('font-size', '12px')
      .style('cursor', 'pointer');

    legendItems.append('div')
      .style('width', '12px')
      .style('height', '12px')
      .style('border-radius', '50%')
      .style('background', d => colorScale(d.name));

    legendItems.append('span')
      .text(d => `${d.name}: ${d.percentage.toFixed(1)}%`);
  }

  /**
   * Create tooltip element
   */
  createTooltip() {
    this.tooltip = this.d3.select('body')
      .append('div')
      .attr('class', 'd3-pie-tooltip')
      .style('position', 'absolute')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', '#fff')
      .style('padding', '10px')
      .style('border-radius', '5px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('opacity', 0)
      .style('z-index', '1000');
  }

  /**
   * Handle slice hover
   */
  onSliceHover(event, d, hoverArc) {
    // Animate slice expansion
    this.d3.select(event.target)
      .transition()
      .duration(200)
      .attr('d', hoverArc);

    // Show tooltip
    if (this.tooltip) {
      const formatValue = new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(d.data.value);

      this.tooltip
        .style('opacity', 1)
        .html(`
          <strong>${d.data.name}</strong><br/>
          Valor: ${formatValue}<br/>
          Percentual: ${d.data.percentage.toFixed(1)}%
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');
    }
  }

  /**
   * Handle slice mouse out
   */
  onSliceOut(event, d, arc) {
    // Animate slice back to normal
    this.d3.select(event.target)
      .transition()
      .duration(200)
      .attr('d', arc);

    // Hide tooltip
    if (this.tooltip) {
      this.tooltip.style('opacity', 0);
    }
  }

  /**
   * Handle slice click
   */
  onSliceClick(event, d) {
    console.log('🖱️ [D3PieChart] Slice clicked:', d.data);
    
    // Emit event for other systems
    eventCoordinator.emit('pieChartSliceClicked', {
      data: d.data,
      event: event
    });
  }

  /**
   * Update chart with new data
   */
  updateChart(data) {
    if (!this.isInitialized) return;
    
    this.createChart(data);
  }

  /**
   * Animate chart entrance
   */
  animateChart() {
    if (!this.svg) return;

    this.svg.selectAll('.slice path')
      .style('opacity', 0)
      .transition()
      .duration(this.config.animationDuration)
      .delay((d, i) => i * 100)
      .style('opacity', 1)
      .attrTween('d', (d) => {
        const interpolate = this.d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
        return (t) => {
          const arc = this.d3.arc()
            .innerRadius(0)
            .outerRadius(this.config.radius);
          return arc(interpolate(t));
        };
      });
  }

  /**
   * Get current chart data
   */
  getData() {
    return this.currentData;
  }

  /**
   * Update chart configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    if (this.currentData) {
      this.createChart(this.currentData);
    }
  }

  /**
   * Clear the chart
   */
  clear() {
    if (this.chartContainer) {
      this.chartContainer.innerHTML = '';
    }
    
    if (this.tooltip) {
      this.tooltip.remove();
      this.tooltip = null;
    }
    
    this.svg = null;
    this.currentData = null;
  }

  /**
   * Destroy the system
   */
  destroy() {
    this.clear();
    
    // Remove event listeners
    eventCoordinator.off('patrimonySyncUpdated');
    eventCoordinator.off('sectionVisibilityChanged');
    eventCoordinator.off('assetSelectionChanged');
    
    this.isInitialized = false;
  }

  /**
   * Get system status for debugging
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasD3: !!this.d3,
      hasContainer: !!this.chartContainer,
      hasSvg: !!this.svg,
      hasData: !!this.currentData,
      config: this.config
    };
  }
}
