{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/config/comissoes-config.js", "../../src/modules/resultado-sync.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Configuração de Comissões\r\n * Define as taxas mínimas e máximas para cada tipo de ativo\r\n * Baseado em dados reais do mercado financeiro brasileiro\r\n */\r\n\r\nexport const COMISSOES_CONFIG = {\r\n  'Renda Fixa': {\r\n    'CDB': { \r\n      min: 0.5, \r\n      max: 2.0,\r\n      description: 'Certificado de Depósito Bancário',\r\n      risco: 'Baixo'\r\n    },\r\n    'CRI': { \r\n      min: 0.8, \r\n      max: 2.5,\r\n      description: 'Certificado de Recebíveis Imobiliários',\r\n      risco: '<PERSON>é<PERSON>'\r\n    },\r\n    'Tí<PERSON>los Públicos': { \r\n      min: 0.3, \r\n      max: 1.5,\r\n      description: '<PERSON><PERSON>uro Direto',\r\n      risco: '<PERSON><PERSON>'\r\n    }\r\n  },\r\n  'Fundo de investimento': {\r\n    'Ações': { \r\n      min: 1.5, \r\n      max: 3.0,\r\n      description: 'Fundos de Ações',\r\n      risco: 'Alto'\r\n    },\r\n    'Liquidez': { \r\n      min: 0.3, \r\n      max: 1.0,\r\n      description: 'Fundos de Liquidez',\r\n      risco: 'Baixo'\r\n    },\r\n    'Renda Fixa': { \r\n      min: 0.8, \r\n      max: 2.0,\r\n      description: 'Fundos de Renda Fixa',\r\n      risco: 'Baixo'\r\n    }\r\n  },\r\n  'Renda variável': {\r\n    'Ações': { \r\n      min: 2.0, \r\n      max: 4.0,\r\n      description: 'Ações Individuais',\r\n      risco: 'Alto'\r\n    },\r\n    'Estruturada': { \r\n      min: 1.0, \r\n      max: 3.5,\r\n      description: 'Produtos Estruturados',\r\n      risco: 'Alto'\r\n    },\r\n    'Carteira administrada': { \r\n      min: 1.5, \r\n      max: 3.0,\r\n      description: 'Carteira Administrada',\r\n      risco: 'Médio-Alto'\r\n    }\r\n  },\r\n  'Outros': {\r\n    'Poupança': { \r\n      min: 0.0, \r\n      max: 0.0,\r\n      description: 'Caderneta de Poupança',\r\n      risco: 'Muito Baixo'\r\n    },\r\n    'Previdência': { \r\n      min: 1.0, \r\n      max: 2.5,\r\n      description: 'Previdência Privada',\r\n      risco: 'Variável'\r\n    },\r\n    'Imóvel': { \r\n      min: 3.0, \r\n      max: 6.0,\r\n      description: 'Investimento Imobiliário',\r\n      risco: 'Médio'\r\n    },\r\n    'COE': { \r\n      min: 0.5, \r\n      max: 2.0,\r\n      description: 'Certificado de Operações Estruturadas',\r\n      risco: 'Médio'\r\n    },\r\n    'Operação compromissada': { \r\n      min: 0.3, \r\n      max: 1.2,\r\n      description: 'Operação Compromissada',\r\n      risco: 'Baixo'\r\n    },\r\n    'Criptoativos': { \r\n      min: 0.5, \r\n      max: 2.0,\r\n      description: 'Criptomoedas e Tokens',\r\n      risco: 'Muito Alto'\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Utilitários para trabalhar com configuração de comissões\r\n */\r\nexport class ComissoesUtils {\r\n  /**\r\n   * Obtém dados de comissão para uma categoria e produto específicos\r\n   * @param {string} category - Categoria do ativo\r\n   * @param {string} product - Produto específico\r\n   * @returns {Object|null} Dados de comissão ou null se não encontrado\r\n   */\r\n  static getComissaoData(category, product) {\r\n    return COMISSOES_CONFIG[category]?.[product] || null;\r\n  }\r\n\r\n  /**\r\n   * Calcula valores de comissão baseado no valor investido\r\n   * @param {number} valor - Valor investido\r\n   * @param {string} category - Categoria do ativo\r\n   * @param {string} product - Produto específico\r\n   * @returns {Object|null} Valores calculados ou null\r\n   */\r\n  static calcularComissao(valor, category, product) {\r\n    const comissaoData = this.getComissaoData(category, product);\r\n    \r\n    if (!comissaoData || valor <= 0) {\r\n      return null;\r\n    }\r\n\r\n    return {\r\n      valorMinimo: (valor * comissaoData.min) / 100,\r\n      valorMaximo: (valor * comissaoData.max) / 100,\r\n      taxaMinima: comissaoData.min,\r\n      taxaMaxima: comissaoData.max,\r\n      description: comissaoData.description,\r\n      risco: comissaoData.risco\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Obtém todas as categorias disponíveis\r\n   * @returns {string[]} Array de categorias\r\n   */\r\n  static getCategorias() {\r\n    return Object.keys(COMISSOES_CONFIG);\r\n  }\r\n\r\n  /**\r\n   * Obtém todos os produtos de uma categoria\r\n   * @param {string} category - Categoria\r\n   * @returns {string[]} Array de produtos\r\n   */\r\n  static getProdutos(category) {\r\n    return Object.keys(COMISSOES_CONFIG[category] || {});\r\n  }\r\n\r\n  /**\r\n   * Obtém estatísticas gerais das comissões\r\n   * @returns {Object} Estatísticas\r\n   */\r\n  static getEstatisticas() {\r\n    const stats = {\r\n      totalCategorias: 0,\r\n      totalProdutos: 0,\r\n      menorTaxa: Infinity,\r\n      maiorTaxa: 0,\r\n      taxaMedia: 0\r\n    };\r\n\r\n    let totalTaxas = 0;\r\n    let countTaxas = 0;\r\n\r\n    Object.keys(COMISSOES_CONFIG).forEach(category => {\r\n      stats.totalCategorias++;\r\n      \r\n      Object.keys(COMISSOES_CONFIG[category]).forEach(product => {\r\n        stats.totalProdutos++;\r\n        \r\n        const data = COMISSOES_CONFIG[category][product];\r\n        \r\n        // Atualizar menor e maior taxa\r\n        if (data.min < stats.menorTaxa) stats.menorTaxa = data.min;\r\n        if (data.max > stats.maiorTaxa) stats.maiorTaxa = data.max;\r\n        \r\n        // Calcular média\r\n        totalTaxas += data.min + data.max;\r\n        countTaxas += 2;\r\n      });\r\n    });\r\n\r\n    stats.taxaMedia = countTaxas > 0 ? totalTaxas / countTaxas : 0;\r\n\r\n    return stats;\r\n  }\r\n\r\n  /**\r\n   * Valida se uma categoria e produto existem\r\n   * @param {string} category - Categoria\r\n   * @param {string} product - Produto\r\n   * @returns {boolean} True se válido\r\n   */\r\n  static isValid(category, product) {\r\n    return !!(COMISSOES_CONFIG[category]?.[product]);\r\n  }\r\n\r\n  /**\r\n   * Obtém produtos por nível de risco\r\n   * @param {string} risco - Nível de risco\r\n   * @returns {Array} Array de produtos com esse nível de risco\r\n   */\r\n  static getProdutosPorRisco(risco) {\r\n    const produtos = [];\r\n\r\n    Object.keys(COMISSOES_CONFIG).forEach(category => {\r\n      Object.keys(COMISSOES_CONFIG[category]).forEach(product => {\r\n        const data = COMISSOES_CONFIG[category][product];\r\n        if (data.risco === risco) {\r\n          produtos.push({\r\n            category,\r\n            product,\r\n            ...data\r\n          });\r\n        }\r\n      });\r\n    });\r\n\r\n    return produtos;\r\n  }\r\n\r\n  /**\r\n   * Obtém range de comissões para um conjunto de ativos\r\n   * @param {Array} ativos - Array de {category, product, valor}\r\n   * @returns {Object} Range total de comissões\r\n   */\r\n  static calcularRangeTotal(ativos) {\r\n    let totalMinimo = 0;\r\n    let totalMaximo = 0;\r\n\r\n    ativos.forEach(ativo => {\r\n      const comissao = this.calcularComissao(ativo.valor, ativo.category, ativo.product);\r\n      if (comissao) {\r\n        totalMinimo += comissao.valorMinimo;\r\n        totalMaximo += comissao.valorMaximo;\r\n      }\r\n    });\r\n\r\n    return {\r\n      totalMinimo,\r\n      totalMaximo,\r\n      economia: totalMaximo - totalMinimo\r\n    };\r\n  }\r\n}\r\n\r\nexport default COMISSOES_CONFIG;\r\n", "/**\r\n * Resultado Sync System\r\n * Sincroniza dados entre patrimônio/alocação e a seção de resultados\r\n * Gerencia valores, percentuais, comissões e visibilidade de elementos\r\n */\r\n\r\nimport { ComissoesUtils } from '../config/comissoes-config.js';\r\n\r\n// Utils object (same as in patrimony-sync.js)\r\nconst Utils = {\r\n  formatCurrency(value) {\r\n    return new Intl.NumberFormat('pt-BR', {\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2,\r\n    }).format(value);\r\n  },\r\n\r\n  parseCurrencyValue(value) {\r\n    if (!value || typeof value !== 'string') return 0;\r\n    const cleanValue = value.replace(/[^\\d,]/g, '').replace(',', '.');\r\n    return parseFloat(cleanValue) || 0;\r\n  },\r\n\r\n  calculatePercentage(value, total) {\r\n    if (!total || total === 0) return 0;\r\n    return (value / total) * 100;\r\n  },\r\n\r\n  formatPercentage(value) {\r\n    return `${value.toFixed(1)}%`;\r\n  },\r\n\r\n  debounce(func, wait) {\r\n    let timeout;\r\n    return function executedFunction(...args) {\r\n      const later = () => {\r\n        clearTimeout(timeout);\r\n        func(...args);\r\n      };\r\n      clearTimeout(timeout);\r\n      timeout = setTimeout(later, wait);\r\n    };\r\n  },\r\n};\r\n\r\n/* global requestAnimationFrame */\r\n\r\nexport class ResultadoSyncSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.patrimonioItems = new Map();\r\n    this.resultadoPatrimonioItems = new Map();\r\n    this.resultadoComissaoItems = new Map();\r\n    this.selectedAssets = new Set();\r\n    this.totalPatrimonio = 0;\r\n\r\n    // Cache configuration\r\n    this.cacheKey = 'resultado_sync_data';\r\n\r\n    // Mapeamento para corrigir inconsistências nos atributos\r\n    this.attributeMapping = {\r\n      // Patrimônio -> Resultado mapping\r\n      'Outros-Popupança': 'Outros-Poupança', // Corrige typo \"Popupança\"\r\n      'Operação compromissada-Criptoativos': 'Outros-Criptoativos', // Corrige categoria errada\r\n      'Fundo de Investimento-Liquidez': 'Fundo de Investimento-Liqudez', // Corrige typo no resultado\r\n      'Fundo de Investimento-Ações': 'Fundo de investimento-Ações',\r\n      'Fundo de Investimento-Renda Fixa': 'Fundo de investimento-Renda Fixa',\r\n      'Renda Variável-Ações': 'Renda Variável-Ações', // Mantém capitalização do resultado\r\n      'Renda Variável-Estruturada': 'Renda Variável-Estruturada',\r\n      'Renda Variável-Carteira administrada': 'Renda Variável-Carteira administrada',\r\n    };\r\n  }\r\n\r\n  // Cache Manager - similar to other modules\r\n  get CacheManager() {\r\n    return {\r\n      set: (key, value) => {\r\n        try {\r\n          window.localStorage.setItem(key, JSON.stringify(value));\r\n        } catch {\r\n          // Silent fail\r\n        }\r\n      },\r\n      get: (key) => {\r\n        try {\r\n          const value = window.localStorage.getItem(key);\r\n          return value ? JSON.parse(value) : null;\r\n        } catch {\r\n          return null;\r\n        }\r\n      },\r\n      remove: (key) => {\r\n        try {\r\n          window.localStorage.removeItem(key);\r\n        } catch {\r\n          // Silent fail\r\n        }\r\n      },\r\n    };\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    this.waitForDOM().then(() => {\r\n      // Wait for other systems to be ready\r\n      this.waitForDependencies().then(() => {\r\n        this.cacheElements();\r\n        this.setupEventListeners();\r\n        this.setupCacheListeners();\r\n        this.loadCachedData();\r\n        this.initialSync();\r\n        this.isInitialized = true;\r\n\r\n        // Dispatch ready event\r\n        document.dispatchEvent(\r\n          new CustomEvent('resultadoSyncReady', {\r\n            detail: { system: this },\r\n          })\r\n        );\r\n      });\r\n    });\r\n  }\r\n\r\n  waitForDOM() {\r\n    return new Promise((resolve) => {\r\n      if (document.readyState === 'loading') {\r\n        document.addEventListener('DOMContentLoaded', resolve);\r\n      } else {\r\n        resolve();\r\n      }\r\n    });\r\n  }\r\n\r\n  // Normaliza chaves para corrigir inconsistências\r\n  normalizeKey(category, product) {\r\n    const originalKey = `${category}-${product}`;\r\n    return this.attributeMapping[originalKey] || originalKey;\r\n  }\r\n\r\n  // Obtém chave mapeada para resultado\r\n  getResultKey(patrimonioKey) {\r\n    return this.attributeMapping[patrimonioKey] || patrimonioKey;\r\n  }\r\n\r\n  waitForDependencies() {\r\n    return new Promise((resolve) => {\r\n      const checkDependencies = () => {\r\n        // Check if patrimony sync is ready\r\n        const patrimonySyncReady = document.querySelector('.patrimonio_interactive_item');\r\n        // Check if resultado section exists\r\n        const resultadoSection = document.querySelector('._4-section-resultado');\r\n\r\n        if (patrimonySyncReady && resultadoSection) {\r\n          resolve();\r\n        } else {\r\n          setTimeout(checkDependencies, 100);\r\n        }\r\n      };\r\n      checkDependencies();\r\n    });\r\n  }\r\n\r\n  cacheElements() {\r\n    // Cache main patrimonio total element using data attribute\r\n    this.patrimonioTotalElement = document.querySelector('[data-patrimonio-total=\"true\"]');\r\n\r\n    // Cache patrimônio items (seção 3)\r\n    const patrimonioElements = document.querySelectorAll('.patrimonio_interactive_item');\r\n\r\n    patrimonioElements.forEach((element) => {\r\n      const category = element.getAttribute('ativo-category');\r\n      const product = element.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        const key = `${category}-${product}`;\r\n        const input = element.querySelector('.currency-input.individual');\r\n        const slider = element.querySelector('range-slider');\r\n        const percentage = element.querySelector('.porcentagem-calculadora');\r\n\r\n        this.patrimonioItems.set(key, {\r\n          element,\r\n          category,\r\n          product,\r\n          input,\r\n          slider,\r\n          percentage,\r\n          key,\r\n        });\r\n      }\r\n    });\r\n\r\n    // Cache resultado patrimônio items\r\n    const resultadoPatrimonioElements = document.querySelectorAll(\r\n      '.patrimonio-ativos-group .ativos-produtos-item'\r\n    );\r\n\r\n    resultadoPatrimonioElements.forEach((element) => {\r\n      const category = element.getAttribute('ativo-category');\r\n      const product = element.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        const key = `${category}-${product}`;\r\n        const valorElement = element.querySelector('.ativo-dinheiro .valor-minimo');\r\n        const percentageElement = element.querySelector('.porcentagem-calculadora.v2');\r\n\r\n        this.resultadoPatrimonioItems.set(key, {\r\n          element,\r\n          category,\r\n          product,\r\n          valorElement,\r\n          percentageElement,\r\n          key,\r\n        });\r\n      }\r\n    });\r\n\r\n    // Cache resultado comissão items\r\n    const resultadoComissaoElements = document.querySelectorAll(\r\n      '.ativos-content-float .ativos-produtos-item'\r\n    );\r\n    resultadoComissaoElements.forEach((element) => {\r\n      const category = element.getAttribute('ativo-category');\r\n      const product = element.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        const key = `${category}-${product}`;\r\n        const taxaMinimaElement = element.querySelector('.ativos-produto-porcentagem .taxa-minima');\r\n        const taxaMaximaElement = element.querySelector('.ativos-produto-porcentagem .taxa-maxima');\r\n        const valorMinimoElement = element.querySelector(\r\n          '.ativo-valor-minimo .ativo-dinheiro .valor-minimo'\r\n        );\r\n        const valorMaximoElement = element.querySelector(\r\n          '.ativo-valor-maximo .ativo-dinheiro .valor-maximo'\r\n        );\r\n\r\n        this.resultadoComissaoItems.set(key, {\r\n          element,\r\n          category,\r\n          product,\r\n          taxaMinimaElement,\r\n          taxaMaximaElement,\r\n          valorMinimoElement,\r\n          valorMaximoElement,\r\n          key,\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  setupEventListeners() {\r\n    // Listen for patrimony changes\r\n    document.addEventListener('patrimonySyncReady', () => {\r\n      this.syncAllValues();\r\n    });\r\n\r\n    // Listen for input changes from currency system\r\n    document.addEventListener('currencyInputChanged', (event) => {\r\n      this.handlePatrimonioChange(event.detail);\r\n    });\r\n\r\n    // Listen for slider changes\r\n    document.addEventListener('sliderChanged', (event) => {\r\n      this.handleSliderChange(event.detail);\r\n    });\r\n\r\n    // Listen for asset selection changes\r\n    document.addEventListener('assetSelectionChanged', (event) => {\r\n      this.handleAssetSelectionChange(event.detail);\r\n    });\r\n\r\n    // Listen for main patrimonio changes (evento correto do patrimony-sync)\r\n    document.addEventListener('patrimonyMainValueChanged', (event) => {\r\n      this.handleMainPatrimonioChanged(event.detail);\r\n    });\r\n\r\n    // Listen for allocation changes\r\n    document.addEventListener('allocationChanged', () => {\r\n      this.syncAllValues();\r\n    });\r\n\r\n    // Listen for allocation status changes\r\n    document.addEventListener('allocationStatusChanged', () => {\r\n      this.syncAllValues();\r\n    });\r\n\r\n    // Listen for patrimony sync updates\r\n    document.addEventListener('patrimonyValueChanged', (event) => {\r\n      this.handlePatrimonyValueChanged(event.detail);\r\n    });\r\n\r\n    // Setup direct input listeners for immediate sync\r\n    this.setupDirectInputListeners();\r\n  }\r\n\r\n  setupDirectInputListeners() {\r\n    // Listen to main patrimonio input\r\n    const mainInput = document.querySelector('.currency-input.main');\r\n    if (mainInput) {\r\n      mainInput.addEventListener(\r\n        'input',\r\n        Utils.debounce(() => {\r\n          this.syncTotalPatrimonio();\r\n        }, 300)\r\n      );\r\n\r\n      mainInput.addEventListener('currencyChange', () => {\r\n        this.syncTotalPatrimonio();\r\n      });\r\n    }\r\n    this.patrimonioItems.forEach((item, key) => {\r\n      if (item.input) {\r\n        // Listen for input events\r\n        item.input.addEventListener(\r\n          'input',\r\n          Utils.debounce(() => {\r\n            this.syncPatrimonioItem(key);\r\n            this.syncComissaoItem(key);\r\n          }, 300)\r\n        );\r\n\r\n        // Listen for currency change events\r\n        item.input.addEventListener('currencyChange', () => {\r\n          this.syncPatrimonioItem(key);\r\n          this.syncComissaoItem(key);\r\n        });\r\n      }\r\n\r\n      if (item.slider) {\r\n        // Listen for slider changes\r\n        item.slider.addEventListener('input', () => {\r\n          this.syncPatrimonioItem(key);\r\n          this.syncComissaoItem(key);\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  setupCacheListeners() {\r\n    // Listen for patrimony sync reset to clear our cache\r\n    document.addEventListener('patrimonySyncReset', () => {\r\n      this.clearCache();\r\n    });\r\n\r\n    // Listen for asset selection system reset\r\n    document.addEventListener('assetSelectionSystemReset', () => {\r\n      this.clearCache();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Save current resultado data to cache\r\n   */\r\n  saveCachedData() {\r\n    try {\r\n      const cacheData = {\r\n        totalPatrimonio: this.totalPatrimonio,\r\n        selectedAssets: Array.from(this.selectedAssets),\r\n        timestamp: Date.now(),\r\n        // Save current display values for restoration\r\n        displayValues: this.getCurrentDisplayValues(),\r\n      };\r\n      this.CacheManager.set(this.cacheKey, cacheData);\r\n    } catch (error) {\r\n      console.warn('Error saving resultado data to cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Load resultado data from cache and restore state\r\n   */\r\n  loadCachedData() {\r\n    try {\r\n      const cachedData = this.CacheManager.get(this.cacheKey);\r\n      if (!cachedData) {\r\n        // If no cached data, ensure containers are hidden initially\r\n        this.updateMainContainersVisibility(false);\r\n        return;\r\n      }\r\n\r\n      // Restore basic data\r\n      this.totalPatrimonio = cachedData.totalPatrimonio || 0;\r\n      this.selectedAssets = new Set(cachedData.selectedAssets || []);\r\n\r\n      // Restore display values if available\r\n      if (cachedData.displayValues) {\r\n        this.restoreDisplayValues(cachedData.displayValues);\r\n      }\r\n\r\n      // Update visibility based on cached selections\r\n      this.updateVisibility();\r\n\r\n      // Force update header total value\r\n      this.forceUpdateHeaderTotal();\r\n\r\n      // Force update group headers after a small delay to ensure all elements are processed\r\n      setTimeout(() => {\r\n        this.updateGroupHeadersVisibility();\r\n      }, 100);\r\n\r\n      // console.log(`📦 Resultado data loaded from cache: ${this.selectedAssets.size} assets, total: R$ ${Utils.formatCurrency(this.totalPatrimonio)}`);\r\n    } catch (error) {\r\n      console.warn('Error loading resultado data from cache:', error);\r\n      // On error, ensure containers are hidden\r\n      this.updateMainContainersVisibility(false);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear resultado cache\r\n   */\r\n  clearCache() {\r\n    try {\r\n      this.CacheManager.remove(this.cacheKey);\r\n    } catch (error) {\r\n      console.warn('Error clearing resultado cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current display values for caching\r\n   */\r\n  getCurrentDisplayValues() {\r\n    const displayValues = {};\r\n\r\n    // Cache patrimonio display values\r\n    this.resultadoPatrimonioItems.forEach((item, key) => {\r\n      if (item.valorElement && item.percentageElement) {\r\n        displayValues[key] = {\r\n          valor: item.valorElement.textContent,\r\n          percentage: item.percentageElement.textContent,\r\n        };\r\n      }\r\n    });\r\n\r\n    // Cache comissao display values\r\n    this.resultadoComissaoItems.forEach((item, key) => {\r\n      if (\r\n        item.taxaMinimaElement &&\r\n        item.taxaMaximaElement &&\r\n        item.valorMinimoElement &&\r\n        item.valorMaximoElement\r\n      ) {\r\n        displayValues[`${key}_comissao`] = {\r\n          taxaMinima: item.taxaMinimaElement.textContent,\r\n          taxaMaxima: item.taxaMaximaElement.textContent,\r\n          valorMinimo: item.valorMinimoElement.textContent,\r\n          valorMaximo: item.valorMaximoElement.textContent,\r\n        };\r\n      }\r\n    });\r\n\r\n    return displayValues;\r\n  }\r\n\r\n  /**\r\n   * Restore display values from cache\r\n   */\r\n  restoreDisplayValues(displayValues) {\r\n    // Restore patrimonio display values\r\n    this.resultadoPatrimonioItems.forEach((item, key) => {\r\n      const cachedValue = displayValues[key];\r\n      if (cachedValue && item.valorElement && item.percentageElement) {\r\n        item.valorElement.textContent = cachedValue.valor;\r\n        item.percentageElement.textContent = cachedValue.percentage;\r\n      }\r\n    });\r\n\r\n    // Restore comissao display values\r\n    this.resultadoComissaoItems.forEach((item, key) => {\r\n      const cachedValue = displayValues[`${key}_comissao`];\r\n      if (\r\n        cachedValue &&\r\n        item.taxaMinimaElement &&\r\n        item.taxaMaximaElement &&\r\n        item.valorMinimoElement &&\r\n        item.valorMaximoElement\r\n      ) {\r\n        item.taxaMinimaElement.textContent = cachedValue.taxaMinima;\r\n        item.taxaMaximaElement.textContent = cachedValue.taxaMaxima;\r\n        item.valorMinimoElement.textContent = cachedValue.valorMinimo;\r\n        item.valorMaximoElement.textContent = cachedValue.valorMaximo;\r\n      }\r\n    });\r\n  }\r\n\r\n  initialSync() {\r\n    // Get total patrimonio from PatrimonySync system\r\n    if (window.PatrimonySync && typeof window.PatrimonySync.getMainValue === 'function') {\r\n      this.totalPatrimonio = window.PatrimonySync.getMainValue();\r\n    } else {\r\n      // Fallback: obtém diretamente do input principal\r\n      const mainInput = document.querySelector('.currency-input.main');\r\n      if (mainInput) {\r\n        this.totalPatrimonio = Utils.parseCurrencyValue(mainInput.value);\r\n      }\r\n    }\r\n\r\n    this.syncAllValues();\r\n    this.updateVisibility();\r\n\r\n    // Force update header total value\r\n    this.forceUpdateHeaderTotal();\r\n  }\r\n\r\n  /**\r\n   * Force update the header total patrimony value\r\n   * This ensures the header-and-patrimonio section displays the correct value\r\n   */\r\n  forceUpdateHeaderTotal() {\r\n    const headerTotalEl = document.querySelector('[data-patrimonio-total=\"true\"]');\r\n    if (headerTotalEl) {\r\n      const currentValue = this.totalPatrimonio || 0;\r\n      headerTotalEl.textContent = Utils.formatCurrency(currentValue);\r\n    }\r\n  }\r\n\r\n  syncAllValues() {\r\n    // Sync total patrimonio first\r\n    this.syncTotalPatrimonio();\r\n\r\n    this.patrimonioItems.forEach((_, key) => {\r\n      this.syncPatrimonioItem(key);\r\n      this.syncComissaoItem(key);\r\n    });\r\n  }\r\n\r\n  syncTotalPatrimonio() {\r\n    // Usa exatamente o mesmo código do patrimony-sync, mas com o atributo\r\n    const totalEl = document.querySelector('[data-patrimonio-total=\"true\"]');\r\n    if (totalEl && window.PatrimonySync) {\r\n      const mainValue = window.PatrimonySync.getMainValue();\r\n      totalEl.textContent = Utils.formatCurrency(mainValue);\r\n    }\r\n  }\r\n\r\n  syncPatrimonioItem(key) {\r\n    const patrimonioItem = this.patrimonioItems.get(key);\r\n    const resultKey = this.getResultKey(key);\r\n    const resultadoItem = this.resultadoPatrimonioItems.get(resultKey);\r\n\r\n    if (!patrimonioItem || !resultadoItem) {\r\n      return;\r\n    }\r\n\r\n    // Get current values\r\n    const inputValue = patrimonioItem.input\r\n      ? Utils.parseCurrencyValue(patrimonioItem.input.value)\r\n      : 0;\r\n\r\n    const sliderValue = patrimonioItem.slider ? parseFloat(patrimonioItem.slider.value) : 0;\r\n\r\n    const percentage = sliderValue * 100;\r\n\r\n    // Update resultado patrimônio values\r\n    if (resultadoItem.valorElement) {\r\n      resultadoItem.valorElement.textContent = Utils.formatCurrency(inputValue);\r\n    }\r\n\r\n    if (resultadoItem.percentageElement) {\r\n      resultadoItem.percentageElement.textContent = `${percentage.toFixed(1)}%`;\r\n    }\r\n  }\r\n\r\n  syncComissaoItem(key) {\r\n    const patrimonioItem = this.patrimonioItems.get(key);\r\n    const resultKey = this.getResultKey(key);\r\n    const comissaoItem = this.resultadoComissaoItems.get(resultKey);\r\n\r\n    if (!patrimonioItem || !comissaoItem) {\r\n      return;\r\n    }\r\n\r\n    const inputValue = patrimonioItem.input\r\n      ? Utils.parseCurrencyValue(patrimonioItem.input.value)\r\n      : 0;\r\n\r\n    // Get commission rates\r\n    const comissaoData = this.getComissaoData(patrimonioItem.category, patrimonioItem.product);\r\n\r\n    if (comissaoData) {\r\n      // Update percentage rates\r\n      if (comissaoItem.taxaMinimaElement) {\r\n        comissaoItem.taxaMinimaElement.textContent = `${comissaoData.min}%`;\r\n      }\r\n\r\n      if (comissaoItem.taxaMaximaElement) {\r\n        comissaoItem.taxaMaximaElement.textContent = `${comissaoData.max}%`;\r\n      }\r\n\r\n      // Calculate commission values\r\n      const valorMinimo = (inputValue * comissaoData.min) / 100;\r\n      const valorMaximo = (inputValue * comissaoData.max) / 100;\r\n\r\n      // Update commission values\r\n      if (comissaoItem.valorMinimoElement) {\r\n        comissaoItem.valorMinimoElement.textContent = Utils.formatCurrency(valorMinimo);\r\n      }\r\n\r\n      if (comissaoItem.valorMaximoElement) {\r\n        comissaoItem.valorMaximoElement.textContent = Utils.formatCurrency(valorMaximo);\r\n      }\r\n    }\r\n  }\r\n\r\n  getComissaoData(category, product) {\r\n    return ComissoesUtils.getComissaoData(category, product);\r\n  }\r\n\r\n  handlePatrimonioChange(detail) {\r\n    if (detail.key) {\r\n      this.syncPatrimonioItem(detail.key);\r\n      this.syncComissaoItem(detail.key);\r\n      // Save to cache after sync\r\n      this.saveCachedData();\r\n    }\r\n  }\r\n\r\n  handleSliderChange(detail) {\r\n    if (detail.key) {\r\n      this.syncPatrimonioItem(detail.key);\r\n      this.syncComissaoItem(detail.key);\r\n      // Save to cache after sync\r\n      this.saveCachedData();\r\n    }\r\n  }\r\n\r\n  handleAssetSelectionChange(detail) {\r\n    // Convert normalized keys from asset selection to our key format\r\n    const selectedAssets = (detail.selectedAssets || []).map((normalizedKey) => {\r\n      // Convert from \"category|product\" (normalized) to \"Category-Product\" (our format)\r\n      return this.convertNormalizedKeyToResultKey(normalizedKey);\r\n    });\r\n\r\n    this.selectedAssets = new Set(selectedAssets);\r\n    this.updateVisibility();\r\n    // Save to cache after selection change\r\n    this.saveCachedData();\r\n  }\r\n\r\n  /**\r\n   * Convert normalized key from asset selection to resultado key format\r\n   * From: \"renda fixa|cdb\" (normalized, pipe separator)\r\n   * To: \"Renda Fixa-CDB\" (proper case, dash separator)\r\n   */\r\n  convertNormalizedKeyToResultKey(normalizedKey) {\r\n    const [category, product] = normalizedKey.split('|');\r\n\r\n    // Find matching key in our maps by comparing normalized versions\r\n    for (const [resultKey] of this.resultadoPatrimonioItems) {\r\n      const [resultCategory, resultProduct] = resultKey.split('-');\r\n      if (\r\n        this.normalizeString(resultCategory) === category &&\r\n        this.normalizeString(resultProduct) === product\r\n      ) {\r\n        return resultKey;\r\n      }\r\n    }\r\n\r\n    // If not found in patrimonio items, check comissao items\r\n    for (const [resultKey] of this.resultadoComissaoItems) {\r\n      const [resultCategory, resultProduct] = resultKey.split('-');\r\n      if (\r\n        this.normalizeString(resultCategory) === category &&\r\n        this.normalizeString(resultProduct) === product\r\n      ) {\r\n        return resultKey;\r\n      }\r\n    }\r\n\r\n    // Fallback: convert to proper case format\r\n    return `${this.toProperCase(category)}-${this.toProperCase(product)}`;\r\n  }\r\n\r\n  /**\r\n   * Normalize string for comparison (same as asset selection filter)\r\n   */\r\n  normalizeString(str) {\r\n    return str.toLowerCase().trim();\r\n  }\r\n\r\n  /**\r\n   * Convert string to proper case\r\n   */\r\n  toProperCase(str) {\r\n    return str\r\n      .split(' ')\r\n      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\r\n      .join(' ');\r\n  }\r\n\r\n  handlePatrimonyValueChanged(detail) {\r\n    // Handle updates from the patrimony sync system\r\n    if (detail.key) {\r\n      this.syncPatrimonioItem(detail.key);\r\n      this.syncComissaoItem(detail.key);\r\n    } else {\r\n      // If no specific key, sync all\r\n      this.syncAllValues();\r\n    }\r\n    // Save to cache after sync\r\n    this.saveCachedData();\r\n  }\r\n\r\n  handleMainPatrimonioChanged(detail) {\r\n    // Handle main patrimonio value changes\r\n    this.totalPatrimonio = detail.value;\r\n    this.syncTotalPatrimonio();\r\n    this.syncAllValues();\r\n\r\n    // Force update header total value\r\n    this.forceUpdateHeaderTotal();\r\n\r\n    // Save to cache after main value change\r\n    this.saveCachedData();\r\n  }\r\n\r\n  updateVisibility() {\r\n    // Check if any assets are selected\r\n    const hasSelectedAssets = this.selectedAssets.size > 0;\r\n\r\n    // Update main containers visibility\r\n    this.updateMainContainersVisibility(hasSelectedAssets);\r\n\r\n    if (!hasSelectedAssets) {\r\n      // If no assets selected, hide everything and return\r\n      this.hideAllItems();\r\n      return;\r\n    }\r\n\r\n    // Update individual patrimônio items visibility with animation\r\n    this.resultadoPatrimonioItems.forEach((item, key) => {\r\n      const hasValue = this.hasValue(key);\r\n      const isSelected = this.isAssetSelectedForResult(key);\r\n\r\n      // Show if has value OR if asset is selected\r\n      const shouldShow = hasValue || isSelected;\r\n\r\n      this.animateVisibility(item.element, shouldShow);\r\n    });\r\n\r\n    // Update individual comissão items visibility with animation\r\n    this.resultadoComissaoItems.forEach((item, key) => {\r\n      const hasValue = this.hasValue(key);\r\n      const isSelected = this.isAssetSelectedForResult(key);\r\n\r\n      // Show if has value OR if asset is selected\r\n      const shouldShow = hasValue || isSelected;\r\n\r\n      this.animateVisibility(item.element, shouldShow);\r\n    });\r\n\r\n    // Update group headers visibility\r\n    this.updateGroupHeadersVisibility();\r\n  }\r\n\r\n  /**\r\n   * Update main containers visibility based on asset selection\r\n   */\r\n  updateMainContainersVisibility(hasSelectedAssets) {\r\n    // Find main containers\r\n    const patrimonioAtivosGroup = document.querySelector('.patrimonio-ativos-group');\r\n    const ativosContentFloat = document.querySelector('.ativos-content-float');\r\n\r\n    if (patrimonioAtivosGroup) {\r\n      if (hasSelectedAssets) {\r\n        patrimonioAtivosGroup.style.display = '';\r\n        patrimonioAtivosGroup.style.opacity = '1';\r\n      } else {\r\n        patrimonioAtivosGroup.style.display = 'none';\r\n        patrimonioAtivosGroup.style.opacity = '0';\r\n      }\r\n    }\r\n\r\n    if (ativosContentFloat) {\r\n      if (hasSelectedAssets) {\r\n        ativosContentFloat.style.display = '';\r\n        ativosContentFloat.style.opacity = '1';\r\n      } else {\r\n        ativosContentFloat.style.display = 'none';\r\n        ativosContentFloat.style.opacity = '0';\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Hide all individual items when no assets are selected\r\n   */\r\n  hideAllItems() {\r\n    // Hide all patrimônio items\r\n    this.resultadoPatrimonioItems.forEach((item) => {\r\n      this.animateVisibility(item.element, false);\r\n    });\r\n\r\n    // Hide all comissão items\r\n    this.resultadoComissaoItems.forEach((item) => {\r\n      this.animateVisibility(item.element, false);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Check if an asset is selected for result display\r\n   * Handles the key format conversion and mapping\r\n   */\r\n  isAssetSelectedForResult(resultKey) {\r\n    // If no assets are selected, don't show any individual items\r\n    // (the main containers will be hidden)\r\n    if (this.selectedAssets.size === 0) {\r\n      return false;\r\n    }\r\n\r\n    // Direct check first\r\n    if (this.selectedAssets.has(resultKey)) {\r\n      return true;\r\n    }\r\n\r\n    // Check with attribute mapping\r\n    const mappedKey = this.getResultKey(resultKey);\r\n    if (this.selectedAssets.has(mappedKey)) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  hasValue(key) {\r\n    const patrimonioItem = this.patrimonioItems.get(key);\r\n    if (!patrimonioItem || !patrimonioItem.input) return false;\r\n\r\n    const value = Utils.parseCurrencyValue(patrimonioItem.input.value);\r\n    return value > 0;\r\n  }\r\n\r\n  animateVisibility(element, shouldShow) {\r\n    if (!element) return;\r\n\r\n    if (shouldShow) {\r\n      // Show element\r\n      if (element.style.display === 'none') {\r\n        element.style.display = 'block';\r\n        element.style.opacity = '0';\r\n        element.style.transform = 'translateY(-10px)';\r\n\r\n        // Animate in\r\n        const animateIn = () => {\r\n          element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';\r\n          element.style.opacity = '1';\r\n          element.style.transform = 'translateY(0)';\r\n        };\r\n\r\n        if (typeof requestAnimationFrame !== 'undefined') {\r\n          requestAnimationFrame(animateIn);\r\n        } else {\r\n          setTimeout(animateIn, 16);\r\n        }\r\n      }\r\n    } else {\r\n      // Hide element\r\n      if (element.style.display !== 'none') {\r\n        element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';\r\n        element.style.opacity = '0';\r\n        element.style.transform = 'translateY(-10px)';\r\n\r\n        setTimeout(() => {\r\n          if (element.style.opacity === '0') {\r\n            element.style.display = 'none';\r\n          }\r\n        }, 300);\r\n      }\r\n    }\r\n  }\r\n\r\n  updateGroupHeadersVisibility() {\r\n    // If no assets are selected, hide all group headers\r\n    if (this.selectedAssets.size === 0) {\r\n      this.hideAllGroupHeaders();\r\n      return;\r\n    }\r\n\r\n    // Get all group containers\r\n    const patrimonioGroups = document.querySelectorAll(\r\n      '.patrimonio-ativos-group .ativos-group-produtos'\r\n    );\r\n    const comissaoGroups = document.querySelectorAll('.ativos-content-float .ativos-group');\r\n\r\n    // Update patrimônio groups\r\n    patrimonioGroups.forEach((group) => {\r\n      const shouldShow = this.shouldShowPatrimonioGroup(group);\r\n      this.animateVisibility(group, shouldShow);\r\n    });\r\n\r\n    // Update comissão groups\r\n    comissaoGroups.forEach((group) => {\r\n      const shouldShow = this.shouldShowComissaoGroup(group);\r\n      this.animateVisibility(group, shouldShow);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Check if a patrimonio group should be visible based on selected assets and values\r\n   */\r\n  shouldShowPatrimonioGroup(group) {\r\n    const items = group.querySelectorAll('.ativos-produtos-item');\r\n\r\n    for (const item of items) {\r\n      const category = item.getAttribute('ativo-category');\r\n      const product = item.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        const key = `${category}-${product}`;\r\n        const mappedKey = this.getResultKey(key);\r\n\r\n        // Check if this asset is selected and has value or should be shown\r\n        const isSelected = this.isAssetSelectedForResult(mappedKey);\r\n        const hasValue = this.hasValue(mappedKey);\r\n\r\n        if (isSelected || hasValue) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if a comissao group should be visible based on selected assets and values\r\n   */\r\n  shouldShowComissaoGroup(group) {\r\n    const items = group.querySelectorAll('.ativos-produtos-item');\r\n\r\n    for (const item of items) {\r\n      const category = item.getAttribute('ativo-category');\r\n      const product = item.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        const key = `${category}-${product}`;\r\n        const mappedKey = this.getResultKey(key);\r\n\r\n        // Check if this asset is selected and has value or should be shown\r\n        const isSelected = this.isAssetSelectedForResult(mappedKey);\r\n        const hasValue = this.hasValue(mappedKey);\r\n\r\n        if (isSelected || hasValue) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Hide all group headers when no assets are selected\r\n   */\r\n  hideAllGroupHeaders() {\r\n    const patrimonioGroups = document.querySelectorAll(\r\n      '.patrimonio-ativos-group .ativos-group-produtos'\r\n    );\r\n    const comissaoGroups = document.querySelectorAll('.ativos-content-float .ativos-group');\r\n\r\n    patrimonioGroups.forEach((group) => {\r\n      this.animateVisibility(group, false);\r\n    });\r\n\r\n    comissaoGroups.forEach((group) => {\r\n      this.animateVisibility(group, false);\r\n    });\r\n  }\r\n\r\n  // Public API methods\r\n  updateTotalPatrimonio(value) {\r\n    this.totalPatrimonio = value;\r\n    this.syncAllValues();\r\n  }\r\n\r\n  getResultadoData() {\r\n    const data = {\r\n      patrimonio: {},\r\n      comissoes: {},\r\n      total: this.totalPatrimonio,\r\n    };\r\n\r\n    this.patrimonioItems.forEach((item, key) => {\r\n      const inputValue = item.input ? Utils.parseCurrencyValue(item.input.value) : 0;\r\n      const sliderValue = item.slider ? parseFloat(item.slider.value) : 0;\r\n\r\n      data.patrimonio[key] = {\r\n        valor: inputValue,\r\n        percentual: sliderValue * 100,\r\n        category: item.category,\r\n        product: item.product,\r\n      };\r\n\r\n      const comissaoData = this.getComissaoData(item.category, item.product);\r\n      if (comissaoData) {\r\n        data.comissoes[key] = {\r\n          taxaMin: comissaoData.min,\r\n          taxaMax: comissaoData.max,\r\n          valorMin: (inputValue * comissaoData.min) / 100,\r\n          valorMax: (inputValue * comissaoData.max) / 100,\r\n        };\r\n      }\r\n    });\r\n\r\n    return data;\r\n  }\r\n\r\n  forceSync() {\r\n    this.syncAllValues();\r\n    this.updateVisibility();\r\n  }\r\n\r\n  // Debug methods\r\n  debugInfo() {\r\n    const patrimonioKeys = Array.from(this.patrimonioItems.keys());\r\n    const resultadoPatrimonioKeys = Array.from(this.resultadoPatrimonioItems.keys());\r\n    const resultadoComissaoKeys = Array.from(this.resultadoComissaoItems.keys());\r\n\r\n    // Check mappings\r\n    const mappingStatus = {};\r\n    patrimonioKeys.forEach((key) => {\r\n      const resultKey = this.getResultKey(key);\r\n      const hasPatrimonioMatch = this.resultadoPatrimonioItems.has(resultKey);\r\n      const hasComissaoMatch = this.resultadoComissaoItems.has(resultKey);\r\n\r\n      mappingStatus[key] = {\r\n        resultKey,\r\n        hasPatrimonioMatch,\r\n        hasComissaoMatch,\r\n        isMapped: key !== resultKey,\r\n      };\r\n    });\r\n\r\n    return {\r\n      isInitialized: this.isInitialized,\r\n      patrimonioItems: this.patrimonioItems.size,\r\n      resultadoPatrimonioItems: this.resultadoPatrimonioItems.size,\r\n      resultadoComissaoItems: this.resultadoComissaoItems.size,\r\n      selectedAssets: this.selectedAssets.size,\r\n      totalPatrimonio: this.totalPatrimonio,\r\n      patrimonioKeys,\r\n      resultadoPatrimonioKeys,\r\n      resultadoComissaoKeys,\r\n      mappingStatus,\r\n      attributeMapping: this.attributeMapping,\r\n    };\r\n  }\r\n\r\n  testSync() {\r\n    // Test total patrimonio sync\r\n    this.syncTotalPatrimonio();\r\n\r\n    // Test with first patrimonio item\r\n    const firstKey = this.patrimonioItems.keys().next().value;\r\n    if (firstKey) {\r\n      this.syncPatrimonioItem(firstKey);\r\n      this.syncComissaoItem(firstKey);\r\n    }\r\n\r\n    // Test visibility\r\n    this.updateVisibility();\r\n  }\r\n\r\n  /**\r\n   * Reset the entire resultado system and clear cache\r\n   */\r\n  resetSystem() {\r\n    try {\r\n      // Clear cache\r\n      this.clearCache();\r\n\r\n      // Reset internal state\r\n      this.selectedAssets.clear();\r\n      this.totalPatrimonio = 0;\r\n\r\n      // Reset all display values to zero\r\n      this.resetAllDisplayValues();\r\n\r\n      // Update visibility\r\n      this.updateVisibility();\r\n\r\n      // Dispatch reset event\r\n      document.dispatchEvent(\r\n        new CustomEvent('resultadoSyncReset', {\r\n          detail: {\r\n            timestamp: Date.now(),\r\n          },\r\n        })\r\n      );\r\n    } catch (error) {\r\n      console.warn('Error resetting resultado system:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset all display values to zero\r\n   */\r\n  resetAllDisplayValues() {\r\n    // Reset patrimonio display values\r\n    this.resultadoPatrimonioItems.forEach((item) => {\r\n      if (item.valorElement) {\r\n        item.valorElement.textContent = '0,00';\r\n      }\r\n      if (item.percentageElement) {\r\n        item.percentageElement.textContent = '0%';\r\n      }\r\n    });\r\n\r\n    // Reset comissao display values\r\n    this.resultadoComissaoItems.forEach((item) => {\r\n      if (item.taxaMinimaElement) {\r\n        item.taxaMinimaElement.textContent = '0%';\r\n      }\r\n      if (item.taxaMaximaElement) {\r\n        item.taxaMaximaElement.textContent = '0%';\r\n      }\r\n      if (item.valorMinimoElement) {\r\n        item.valorMinimoElement.textContent = '0,00';\r\n      }\r\n      if (item.valorMaximoElement) {\r\n        item.valorMaximoElement.textContent = '0,00';\r\n      }\r\n    });\r\n\r\n    // Hide main containers when resetting\r\n    this.updateMainContainersVisibility(false);\r\n  }\r\n\r\n  /**\r\n   * Get cache information for debugging\r\n   */\r\n  getCacheInfo() {\r\n    try {\r\n      const cachedData = this.CacheManager.get(this.cacheKey);\r\n      return {\r\n        hasCachedData: !!cachedData,\r\n        totalPatrimonio: cachedData?.totalPatrimonio || 0,\r\n        selectedAssetsCount: cachedData?.selectedAssets?.length || 0,\r\n        timestamp: cachedData?.timestamp || null,\r\n        currentTotalPatrimonio: this.totalPatrimonio,\r\n        currentSelectedAssets: this.selectedAssets.size,\r\n        displayValuesCount: cachedData?.displayValues\r\n          ? Object.keys(cachedData.displayValues).length\r\n          : 0,\r\n      };\r\n    } catch (error) {\r\n      console.warn('Error getting cache info:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Debug method to check key mappings and visibility\r\n   */\r\n  debugKeyMappings() {\r\n    console.warn('=== RESULTADO SYNC DEBUG ===');\r\n    console.warn('Selected Assets:', Array.from(this.selectedAssets));\r\n    console.warn('Has Selected Assets:', this.selectedAssets.size > 0);\r\n\r\n    // Check main containers visibility\r\n    const patrimonioAtivosGroup = document.querySelector('.patrimonio-ativos-group');\r\n    const ativosContentFloat = document.querySelector('.ativos-content-float');\r\n\r\n    console.warn('\\n--- MAIN CONTAINERS ---');\r\n    console.warn('patrimonio-ativos-group:', {\r\n      exists: !!patrimonioAtivosGroup,\r\n      display: patrimonioAtivosGroup?.style.display || 'default',\r\n      opacity: patrimonioAtivosGroup?.style.opacity || 'default',\r\n    });\r\n    console.warn('ativos-content-float:', {\r\n      exists: !!ativosContentFloat,\r\n      display: ativosContentFloat?.style.display || 'default',\r\n      opacity: ativosContentFloat?.style.opacity || 'default',\r\n    });\r\n\r\n    console.warn('\\n--- PATRIMONIO ITEMS ---');\r\n    this.resultadoPatrimonioItems.forEach((item, key) => {\r\n      const hasValue = this.hasValue(key);\r\n      const isSelected = this.isAssetSelectedForResult(key);\r\n      const shouldShow = hasValue || isSelected;\r\n      const isVisible = item.element.style.display !== 'none';\r\n\r\n      console.warn(`${key}:`, {\r\n        hasValue,\r\n        isSelected,\r\n        shouldShow,\r\n        isVisible,\r\n        category: item.category,\r\n        product: item.product,\r\n      });\r\n    });\r\n\r\n    console.warn('\\n--- COMISSAO ITEMS ---');\r\n    this.resultadoComissaoItems.forEach((item, key) => {\r\n      const hasValue = this.hasValue(key);\r\n      const isSelected = this.isAssetSelectedForResult(key);\r\n      const shouldShow = hasValue || isSelected;\r\n      const isVisible = item.element.style.display !== 'none';\r\n\r\n      console.warn(`${key}:`, {\r\n        hasValue,\r\n        isSelected,\r\n        shouldShow,\r\n        isVisible,\r\n        category: item.category,\r\n        product: item.product,\r\n      });\r\n    });\r\n\r\n    console.warn('\\n--- GROUP HEADERS ---');\r\n    const patrimonioGroups = document.querySelectorAll(\r\n      '.patrimonio-ativos-group .ativos-group-produtos'\r\n    );\r\n    const comissaoGroups = document.querySelectorAll('.ativos-content-float .ativos-group');\r\n\r\n    console.warn('Patrimonio Groups:');\r\n    patrimonioGroups.forEach((group, index) => {\r\n      const shouldShow = this.shouldShowPatrimonioGroup(group);\r\n      const isVisible = group.style.display !== 'none';\r\n      const header = group.querySelector('.ativos-group-header');\r\n      const headerText = header?.textContent?.trim() || 'No header';\r\n\r\n      console.warn(`  Group ${index} (${headerText}):`, {\r\n        shouldShow,\r\n        isVisible,\r\n        display: group.style.display || 'default',\r\n      });\r\n    });\r\n\r\n    console.warn('Comissao Groups:');\r\n    comissaoGroups.forEach((group, index) => {\r\n      const shouldShow = this.shouldShowComissaoGroup(group);\r\n      const isVisible = group.style.display !== 'none';\r\n      const header = group.querySelector('.ativos-group-header');\r\n      const headerText = header?.textContent?.trim() || 'No header';\r\n\r\n      console.warn(`  Group ${index} (${headerText}):`, {\r\n        shouldShow,\r\n        isVisible,\r\n        display: group.style.display || 'default',\r\n      });\r\n    });\r\n\r\n    console.warn('\\n--- ATTRIBUTE MAPPING ---');\r\n    Object.entries(this.attributeMapping).forEach(([original, mapped]) => {\r\n      console.warn(`${original} -> ${mapped}`);\r\n    });\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nconst resultadoSync = new ResultadoSyncSystem();\r\n\r\n// Export both class and instance\r\nexport { resultadoSync };\r\nexport default ResultadoSyncSystem;\r\n\r\n// Expose debug methods globally for testing\r\nif (typeof window !== 'undefined') {\r\n  window.debugResultadoSync = () => {\r\n    const system = window.AppCalcReino?.getModule?.('resultadoSync');\r\n    if (system) {\r\n      return system.debugInfo();\r\n    }\r\n    return null;\r\n  };\r\n\r\n  window.testResultadoSync = () => {\r\n    const system = window.AppCalcReino?.getModule?.('resultadoSync');\r\n    if (system) {\r\n      system.testSync();\r\n    }\r\n  };\r\n\r\n  window.forceResultadoSync = () => {\r\n    const system = window.AppCalcReino?.getModule?.('resultadoSync');\r\n    if (system) {\r\n      system.forceSync();\r\n    }\r\n  };\r\n\r\n  window.debugResultadoKeyMappings = () => {\r\n    const system = window.AppCalcReino?.getModule?.('resultadoSync');\r\n    if (system) {\r\n      system.debugKeyMappings();\r\n    }\r\n  };\r\n\r\n  window.forceUpdateGroupHeaders = () => {\r\n    const system = window.AppCalcReino?.getModule?.('resultadoSync');\r\n    if (system) {\r\n      system.updateGroupHeadersVisibility();\r\n      console.warn('Group headers visibility updated manually');\r\n    }\r\n  };\r\n\r\n  window.forceUpdateHeaderTotal = () => {\r\n    const system = window.AppCalcReino?.getModule?.('resultadoSync');\r\n    if (system) {\r\n      system.forceUpdateHeaderTotal();\r\n      console.warn('Header total value updated manually');\r\n    }\r\n  };\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACMtF,MAAM,mBAAmB;AAAA,IAC9B,cAAc;AAAA,MACZ,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,0BAAoB;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB,eAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,qBAAkB;AAAA,MAChB,eAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,yBAAyB;AAAA,QACvB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,eAAY;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,kBAAe;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,aAAU;AAAA,QACR,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,gCAA0B;AAAA,QACxB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAKO,MAAM,iBAAN,MAAqB;AAAA,IA9G5B,OA8G4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAO1B,OAAO,gBAAgB,UAAU,SAAS;AACxC,aAAO,iBAAiB,QAAQ,IAAI,OAAO,KAAK;AAAA,IAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,OAAO,iBAAiB,OAAO,UAAU,SAAS;AAChD,YAAM,eAAe,KAAK,gBAAgB,UAAU,OAAO;AAE3D,UAAI,CAAC,gBAAgB,SAAS,GAAG;AAC/B,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,aAAc,QAAQ,aAAa,MAAO;AAAA,QAC1C,aAAc,QAAQ,aAAa,MAAO;AAAA,QAC1C,YAAY,aAAa;AAAA,QACzB,YAAY,aAAa;AAAA,QACzB,aAAa,aAAa;AAAA,QAC1B,OAAO,aAAa;AAAA,MACtB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,OAAO,gBAAgB;AACrB,aAAO,OAAO,KAAK,gBAAgB;AAAA,IACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,OAAO,YAAY,UAAU;AAC3B,aAAO,OAAO,KAAK,iBAAiB,QAAQ,KAAK,CAAC,CAAC;AAAA,IACrD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,OAAO,kBAAkB;AACvB,YAAM,QAAQ;AAAA,QACZ,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAEA,UAAI,aAAa;AACjB,UAAI,aAAa;AAEjB,aAAO,KAAK,gBAAgB,EAAE,QAAQ,cAAY;AAChD,cAAM;AAEN,eAAO,KAAK,iBAAiB,QAAQ,CAAC,EAAE,QAAQ,aAAW;AACzD,gBAAM;AAEN,gBAAM,OAAO,iBAAiB,QAAQ,EAAE,OAAO;AAG/C,cAAI,KAAK,MAAM,MAAM,UAAW,OAAM,YAAY,KAAK;AACvD,cAAI,KAAK,MAAM,MAAM,UAAW,OAAM,YAAY,KAAK;AAGvD,wBAAc,KAAK,MAAM,KAAK;AAC9B,wBAAc;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAED,YAAM,YAAY,aAAa,IAAI,aAAa,aAAa;AAE7D,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,OAAO,QAAQ,UAAU,SAAS;AAChC,aAAO,CAAC,CAAE,iBAAiB,QAAQ,IAAI,OAAO;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,OAAO,oBAAoB,OAAO;AAChC,YAAM,WAAW,CAAC;AAElB,aAAO,KAAK,gBAAgB,EAAE,QAAQ,cAAY;AAChD,eAAO,KAAK,iBAAiB,QAAQ,CAAC,EAAE,QAAQ,aAAW;AACzD,gBAAM,OAAO,iBAAiB,QAAQ,EAAE,OAAO;AAC/C,cAAI,KAAK,UAAU,OAAO;AACxB,qBAAS,KAAK;AAAA,cACZ;AAAA,cACA;AAAA,cACA,GAAG;AAAA,YACL,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,OAAO,mBAAmB,QAAQ;AAChC,UAAI,cAAc;AAClB,UAAI,cAAc;AAElB,aAAO,QAAQ,WAAS;AACtB,cAAM,WAAW,KAAK,iBAAiB,MAAM,OAAO,MAAM,UAAU,MAAM,OAAO;AACjF,YAAI,UAAU;AACZ,yBAAe,SAAS;AACxB,yBAAe,SAAS;AAAA,QAC1B;AAAA,MACF,CAAC;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAU,cAAc;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,MAAO,2BAAQ;;;AC3Pf,MAAM,QAAQ;AAAA,IACZ,eAAe,OAAO;AACpB,aAAO,IAAI,KAAK,aAAa,SAAS;AAAA,QACpC,uBAAuB;AAAA,QACvB,uBAAuB;AAAA,MACzB,CAAC,EAAE,OAAO,KAAK;AAAA,IACjB;AAAA,IAEA,mBAAmB,OAAO;AACxB,UAAI,CAAC,SAAS,OAAO,UAAU,SAAU,QAAO;AAChD,YAAM,aAAa,MAAM,QAAQ,WAAW,EAAE,EAAE,QAAQ,KAAK,GAAG;AAChE,aAAO,WAAW,UAAU,KAAK;AAAA,IACnC;AAAA,IAEA,oBAAoB,OAAO,OAAO;AAChC,UAAI,CAAC,SAAS,UAAU,EAAG,QAAO;AAClC,aAAQ,QAAQ,QAAS;AAAA,IAC3B;AAAA,IAEA,iBAAiB,OAAO;AACtB,aAAO,GAAG,MAAM,QAAQ,CAAC,CAAC;AAAA,IAC5B;AAAA,IAEA,SAAS,MAAM,MAAM;AACnB,UAAI;AACJ,aAAO,gCAAS,oBAAoB,MAAM;AACxC,cAAM,QAAQ,6BAAM;AAClB,uBAAa,OAAO;AACpB,eAAK,GAAG,IAAI;AAAA,QACd,GAHc;AAId,qBAAa,OAAO;AACpB,kBAAU,WAAW,OAAO,IAAI;AAAA,MAClC,GAPO;AAAA,IAQT;AAAA,EACF;AAIO,MAAM,sBAAN,MAA0B;AAAA,IA/CjC,OA+CiC;AAAA;AAAA;AAAA,IAC/B,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,kBAAkB,oBAAI,IAAI;AAC/B,WAAK,2BAA2B,oBAAI,IAAI;AACxC,WAAK,yBAAyB,oBAAI,IAAI;AACtC,WAAK,iBAAiB,oBAAI,IAAI;AAC9B,WAAK,kBAAkB;AAGvB,WAAK,WAAW;AAGhB,WAAK,mBAAmB;AAAA;AAAA,QAEtB,uBAAoB;AAAA;AAAA,QACpB,6CAAuC;AAAA;AAAA,QACvC,kCAAkC;AAAA;AAAA,QAClC,qCAA+B;AAAA,QAC/B,oCAAoC;AAAA,QACpC,iCAAwB;AAAA;AAAA,QACxB,iCAA8B;AAAA,QAC9B,2CAAwC;AAAA,MAC1C;AAAA,IACF;AAAA;AAAA,IAGA,IAAI,eAAe;AACjB,aAAO;AAAA,QACL,KAAK,wBAAC,KAAK,UAAU;AACnB,cAAI;AACF,mBAAO,aAAa,QAAQ,KAAK,KAAK,UAAU,KAAK,CAAC;AAAA,UACxD,QAAQ;AAAA,UAER;AAAA,QACF,GANK;AAAA,QAOL,KAAK,wBAAC,QAAQ;AACZ,cAAI;AACF,kBAAM,QAAQ,OAAO,aAAa,QAAQ,GAAG;AAC7C,mBAAO,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,UACrC,QAAQ;AACN,mBAAO;AAAA,UACT;AAAA,QACF,GAPK;AAAA,QAQL,QAAQ,wBAAC,QAAQ;AACf,cAAI;AACF,mBAAO,aAAa,WAAW,GAAG;AAAA,UACpC,QAAQ;AAAA,UAER;AAAA,QACF,GANQ;AAAA,MAOV;AAAA,IACF;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,WAAK,WAAW,EAAE,KAAK,MAAM;AAE3B,aAAK,oBAAoB,EAAE,KAAK,MAAM;AACpC,eAAK,cAAc;AACnB,eAAK,oBAAoB;AACzB,eAAK,oBAAoB;AACzB,eAAK,eAAe;AACpB,eAAK,YAAY;AACjB,eAAK,gBAAgB;AAGrB,mBAAS;AAAA,YACP,IAAI,YAAY,sBAAsB;AAAA,cACpC,QAAQ,EAAE,QAAQ,KAAK;AAAA,YACzB,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,IAEA,aAAa;AACX,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAI,SAAS,eAAe,WAAW;AACrC,mBAAS,iBAAiB,oBAAoB,OAAO;AAAA,QACvD,OAAO;AACL,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,aAAa,UAAU,SAAS;AAC9B,YAAM,cAAc,GAAG,QAAQ,IAAI,OAAO;AAC1C,aAAO,KAAK,iBAAiB,WAAW,KAAK;AAAA,IAC/C;AAAA;AAAA,IAGA,aAAa,eAAe;AAC1B,aAAO,KAAK,iBAAiB,aAAa,KAAK;AAAA,IACjD;AAAA,IAEA,sBAAsB;AACpB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,cAAM,oBAAoB,6BAAM;AAE9B,gBAAM,qBAAqB,SAAS,cAAc,8BAA8B;AAEhF,gBAAM,mBAAmB,SAAS,cAAc,uBAAuB;AAEvE,cAAI,sBAAsB,kBAAkB;AAC1C,oBAAQ;AAAA,UACV,OAAO;AACL,uBAAW,mBAAmB,GAAG;AAAA,UACnC;AAAA,QACF,GAX0B;AAY1B,0BAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,IAEA,gBAAgB;AAEd,WAAK,yBAAyB,SAAS,cAAc,gCAAgC;AAGrF,YAAM,qBAAqB,SAAS,iBAAiB,8BAA8B;AAEnF,yBAAmB,QAAQ,CAAC,YAAY;AACtC,cAAM,WAAW,QAAQ,aAAa,gBAAgB;AACtD,cAAM,UAAU,QAAQ,aAAa,eAAe;AAEpD,YAAI,YAAY,SAAS;AACvB,gBAAM,MAAM,GAAG,QAAQ,IAAI,OAAO;AAClC,gBAAM,QAAQ,QAAQ,cAAc,4BAA4B;AAChE,gBAAM,SAAS,QAAQ,cAAc,cAAc;AACnD,gBAAM,aAAa,QAAQ,cAAc,0BAA0B;AAEnE,eAAK,gBAAgB,IAAI,KAAK;AAAA,YAC5B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAGD,YAAM,8BAA8B,SAAS;AAAA,QAC3C;AAAA,MACF;AAEA,kCAA4B,QAAQ,CAAC,YAAY;AAC/C,cAAM,WAAW,QAAQ,aAAa,gBAAgB;AACtD,cAAM,UAAU,QAAQ,aAAa,eAAe;AAEpD,YAAI,YAAY,SAAS;AACvB,gBAAM,MAAM,GAAG,QAAQ,IAAI,OAAO;AAClC,gBAAM,eAAe,QAAQ,cAAc,+BAA+B;AAC1E,gBAAM,oBAAoB,QAAQ,cAAc,6BAA6B;AAE7E,eAAK,yBAAyB,IAAI,KAAK;AAAA,YACrC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAGD,YAAM,4BAA4B,SAAS;AAAA,QACzC;AAAA,MACF;AACA,gCAA0B,QAAQ,CAAC,YAAY;AAC7C,cAAM,WAAW,QAAQ,aAAa,gBAAgB;AACtD,cAAM,UAAU,QAAQ,aAAa,eAAe;AAEpD,YAAI,YAAY,SAAS;AACvB,gBAAM,MAAM,GAAG,QAAQ,IAAI,OAAO;AAClC,gBAAM,oBAAoB,QAAQ,cAAc,0CAA0C;AAC1F,gBAAM,oBAAoB,QAAQ,cAAc,0CAA0C;AAC1F,gBAAM,qBAAqB,QAAQ;AAAA,YACjC;AAAA,UACF;AACA,gBAAM,qBAAqB,QAAQ;AAAA,YACjC;AAAA,UACF;AAEA,eAAK,uBAAuB,IAAI,KAAK;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,sBAAsB;AAEpB,eAAS,iBAAiB,sBAAsB,MAAM;AACpD,aAAK,cAAc;AAAA,MACrB,CAAC;AAGD,eAAS,iBAAiB,wBAAwB,CAAC,UAAU;AAC3D,aAAK,uBAAuB,MAAM,MAAM;AAAA,MAC1C,CAAC;AAGD,eAAS,iBAAiB,iBAAiB,CAAC,UAAU;AACpD,aAAK,mBAAmB,MAAM,MAAM;AAAA,MACtC,CAAC;AAGD,eAAS,iBAAiB,yBAAyB,CAAC,UAAU;AAC5D,aAAK,2BAA2B,MAAM,MAAM;AAAA,MAC9C,CAAC;AAGD,eAAS,iBAAiB,6BAA6B,CAAC,UAAU;AAChE,aAAK,4BAA4B,MAAM,MAAM;AAAA,MAC/C,CAAC;AAGD,eAAS,iBAAiB,qBAAqB,MAAM;AACnD,aAAK,cAAc;AAAA,MACrB,CAAC;AAGD,eAAS,iBAAiB,2BAA2B,MAAM;AACzD,aAAK,cAAc;AAAA,MACrB,CAAC;AAGD,eAAS,iBAAiB,yBAAyB,CAAC,UAAU;AAC5D,aAAK,4BAA4B,MAAM,MAAM;AAAA,MAC/C,CAAC;AAGD,WAAK,0BAA0B;AAAA,IACjC;AAAA,IAEA,4BAA4B;AAE1B,YAAM,YAAY,SAAS,cAAc,sBAAsB;AAC/D,UAAI,WAAW;AACb,kBAAU;AAAA,UACR;AAAA,UACA,MAAM,SAAS,MAAM;AACnB,iBAAK,oBAAoB;AAAA,UAC3B,GAAG,GAAG;AAAA,QACR;AAEA,kBAAU,iBAAiB,kBAAkB,MAAM;AACjD,eAAK,oBAAoB;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,WAAK,gBAAgB,QAAQ,CAAC,MAAM,QAAQ;AAC1C,YAAI,KAAK,OAAO;AAEd,eAAK,MAAM;AAAA,YACT;AAAA,YACA,MAAM,SAAS,MAAM;AACnB,mBAAK,mBAAmB,GAAG;AAC3B,mBAAK,iBAAiB,GAAG;AAAA,YAC3B,GAAG,GAAG;AAAA,UACR;AAGA,eAAK,MAAM,iBAAiB,kBAAkB,MAAM;AAClD,iBAAK,mBAAmB,GAAG;AAC3B,iBAAK,iBAAiB,GAAG;AAAA,UAC3B,CAAC;AAAA,QACH;AAEA,YAAI,KAAK,QAAQ;AAEf,eAAK,OAAO,iBAAiB,SAAS,MAAM;AAC1C,iBAAK,mBAAmB,GAAG;AAC3B,iBAAK,iBAAiB,GAAG;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,sBAAsB;AAEpB,eAAS,iBAAiB,sBAAsB,MAAM;AACpD,aAAK,WAAW;AAAA,MAClB,CAAC;AAGD,eAAS,iBAAiB,6BAA6B,MAAM;AAC3D,aAAK,WAAW;AAAA,MAClB,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB;AACf,UAAI;AACF,cAAM,YAAY;AAAA,UAChB,iBAAiB,KAAK;AAAA,UACtB,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,UAC9C,WAAW,KAAK,IAAI;AAAA;AAAA,UAEpB,eAAe,KAAK,wBAAwB;AAAA,QAC9C;AACA,aAAK,aAAa,IAAI,KAAK,UAAU,SAAS;AAAA,MAChD,SAAS,OAAO;AACd,gBAAQ,KAAK,yCAAyC,KAAK;AAAA,MAC7D;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB;AACf,UAAI;AACF,cAAM,aAAa,KAAK,aAAa,IAAI,KAAK,QAAQ;AACtD,YAAI,CAAC,YAAY;AAEf,eAAK,+BAA+B,KAAK;AACzC;AAAA,QACF;AAGA,aAAK,kBAAkB,WAAW,mBAAmB;AACrD,aAAK,iBAAiB,IAAI,IAAI,WAAW,kBAAkB,CAAC,CAAC;AAG7D,YAAI,WAAW,eAAe;AAC5B,eAAK,qBAAqB,WAAW,aAAa;AAAA,QACpD;AAGA,aAAK,iBAAiB;AAGtB,aAAK,uBAAuB;AAG5B,mBAAW,MAAM;AACf,eAAK,6BAA6B;AAAA,QACpC,GAAG,GAAG;AAAA,MAGR,SAAS,OAAO;AACd,gBAAQ,KAAK,4CAA4C,KAAK;AAE9D,aAAK,+BAA+B,KAAK;AAAA,MAC3C;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa;AACX,UAAI;AACF,aAAK,aAAa,OAAO,KAAK,QAAQ;AAAA,MACxC,SAAS,OAAO;AACd,gBAAQ,KAAK,mCAAmC,KAAK;AAAA,MACvD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,0BAA0B;AACxB,YAAM,gBAAgB,CAAC;AAGvB,WAAK,yBAAyB,QAAQ,CAAC,MAAM,QAAQ;AACnD,YAAI,KAAK,gBAAgB,KAAK,mBAAmB;AAC/C,wBAAc,GAAG,IAAI;AAAA,YACnB,OAAO,KAAK,aAAa;AAAA,YACzB,YAAY,KAAK,kBAAkB;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAGD,WAAK,uBAAuB,QAAQ,CAAC,MAAM,QAAQ;AACjD,YACE,KAAK,qBACL,KAAK,qBACL,KAAK,sBACL,KAAK,oBACL;AACA,wBAAc,GAAG,GAAG,WAAW,IAAI;AAAA,YACjC,YAAY,KAAK,kBAAkB;AAAA,YACnC,YAAY,KAAK,kBAAkB;AAAA,YACnC,aAAa,KAAK,mBAAmB;AAAA,YACrC,aAAa,KAAK,mBAAmB;AAAA,UACvC;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAKA,qBAAqB,eAAe;AAElC,WAAK,yBAAyB,QAAQ,CAAC,MAAM,QAAQ;AACnD,cAAM,cAAc,cAAc,GAAG;AACrC,YAAI,eAAe,KAAK,gBAAgB,KAAK,mBAAmB;AAC9D,eAAK,aAAa,cAAc,YAAY;AAC5C,eAAK,kBAAkB,cAAc,YAAY;AAAA,QACnD;AAAA,MACF,CAAC;AAGD,WAAK,uBAAuB,QAAQ,CAAC,MAAM,QAAQ;AACjD,cAAM,cAAc,cAAc,GAAG,GAAG,WAAW;AACnD,YACE,eACA,KAAK,qBACL,KAAK,qBACL,KAAK,sBACL,KAAK,oBACL;AACA,eAAK,kBAAkB,cAAc,YAAY;AACjD,eAAK,kBAAkB,cAAc,YAAY;AACjD,eAAK,mBAAmB,cAAc,YAAY;AAClD,eAAK,mBAAmB,cAAc,YAAY;AAAA,QACpD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,cAAc;AAEZ,UAAI,OAAO,iBAAiB,OAAO,OAAO,cAAc,iBAAiB,YAAY;AACnF,aAAK,kBAAkB,OAAO,cAAc,aAAa;AAAA,MAC3D,OAAO;AAEL,cAAM,YAAY,SAAS,cAAc,sBAAsB;AAC/D,YAAI,WAAW;AACb,eAAK,kBAAkB,MAAM,mBAAmB,UAAU,KAAK;AAAA,QACjE;AAAA,MACF;AAEA,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAGtB,WAAK,uBAAuB;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,yBAAyB;AACvB,YAAM,gBAAgB,SAAS,cAAc,gCAAgC;AAC7E,UAAI,eAAe;AACjB,cAAM,eAAe,KAAK,mBAAmB;AAC7C,sBAAc,cAAc,MAAM,eAAe,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IAEA,gBAAgB;AAEd,WAAK,oBAAoB;AAEzB,WAAK,gBAAgB,QAAQ,CAAC,GAAG,QAAQ;AACvC,aAAK,mBAAmB,GAAG;AAC3B,aAAK,iBAAiB,GAAG;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,IAEA,sBAAsB;AAEpB,YAAM,UAAU,SAAS,cAAc,gCAAgC;AACvE,UAAI,WAAW,OAAO,eAAe;AACnC,cAAM,YAAY,OAAO,cAAc,aAAa;AACpD,gBAAQ,cAAc,MAAM,eAAe,SAAS;AAAA,MACtD;AAAA,IACF;AAAA,IAEA,mBAAmB,KAAK;AACtB,YAAM,iBAAiB,KAAK,gBAAgB,IAAI,GAAG;AACnD,YAAM,YAAY,KAAK,aAAa,GAAG;AACvC,YAAM,gBAAgB,KAAK,yBAAyB,IAAI,SAAS;AAEjE,UAAI,CAAC,kBAAkB,CAAC,eAAe;AACrC;AAAA,MACF;AAGA,YAAM,aAAa,eAAe,QAC9B,MAAM,mBAAmB,eAAe,MAAM,KAAK,IACnD;AAEJ,YAAM,cAAc,eAAe,SAAS,WAAW,eAAe,OAAO,KAAK,IAAI;AAEtF,YAAM,aAAa,cAAc;AAGjC,UAAI,cAAc,cAAc;AAC9B,sBAAc,aAAa,cAAc,MAAM,eAAe,UAAU;AAAA,MAC1E;AAEA,UAAI,cAAc,mBAAmB;AACnC,sBAAc,kBAAkB,cAAc,GAAG,WAAW,QAAQ,CAAC,CAAC;AAAA,MACxE;AAAA,IACF;AAAA,IAEA,iBAAiB,KAAK;AACpB,YAAM,iBAAiB,KAAK,gBAAgB,IAAI,GAAG;AACnD,YAAM,YAAY,KAAK,aAAa,GAAG;AACvC,YAAM,eAAe,KAAK,uBAAuB,IAAI,SAAS;AAE9D,UAAI,CAAC,kBAAkB,CAAC,cAAc;AACpC;AAAA,MACF;AAEA,YAAM,aAAa,eAAe,QAC9B,MAAM,mBAAmB,eAAe,MAAM,KAAK,IACnD;AAGJ,YAAM,eAAe,KAAK,gBAAgB,eAAe,UAAU,eAAe,OAAO;AAEzF,UAAI,cAAc;AAEhB,YAAI,aAAa,mBAAmB;AAClC,uBAAa,kBAAkB,cAAc,GAAG,aAAa,GAAG;AAAA,QAClE;AAEA,YAAI,aAAa,mBAAmB;AAClC,uBAAa,kBAAkB,cAAc,GAAG,aAAa,GAAG;AAAA,QAClE;AAGA,cAAM,cAAe,aAAa,aAAa,MAAO;AACtD,cAAM,cAAe,aAAa,aAAa,MAAO;AAGtD,YAAI,aAAa,oBAAoB;AACnC,uBAAa,mBAAmB,cAAc,MAAM,eAAe,WAAW;AAAA,QAChF;AAEA,YAAI,aAAa,oBAAoB;AACnC,uBAAa,mBAAmB,cAAc,MAAM,eAAe,WAAW;AAAA,QAChF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,gBAAgB,UAAU,SAAS;AACjC,aAAO,eAAe,gBAAgB,UAAU,OAAO;AAAA,IACzD;AAAA,IAEA,uBAAuB,QAAQ;AAC7B,UAAI,OAAO,KAAK;AACd,aAAK,mBAAmB,OAAO,GAAG;AAClC,aAAK,iBAAiB,OAAO,GAAG;AAEhC,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,IAEA,mBAAmB,QAAQ;AACzB,UAAI,OAAO,KAAK;AACd,aAAK,mBAAmB,OAAO,GAAG;AAClC,aAAK,iBAAiB,OAAO,GAAG;AAEhC,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,IAEA,2BAA2B,QAAQ;AAEjC,YAAM,kBAAkB,OAAO,kBAAkB,CAAC,GAAG,IAAI,CAAC,kBAAkB;AAE1E,eAAO,KAAK,gCAAgC,aAAa;AAAA,MAC3D,CAAC;AAED,WAAK,iBAAiB,IAAI,IAAI,cAAc;AAC5C,WAAK,iBAAiB;AAEtB,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,gCAAgC,eAAe;AAC7C,YAAM,CAAC,UAAU,OAAO,IAAI,cAAc,MAAM,GAAG;AAGnD,iBAAW,CAAC,SAAS,KAAK,KAAK,0BAA0B;AACvD,cAAM,CAAC,gBAAgB,aAAa,IAAI,UAAU,MAAM,GAAG;AAC3D,YACE,KAAK,gBAAgB,cAAc,MAAM,YACzC,KAAK,gBAAgB,aAAa,MAAM,SACxC;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,iBAAW,CAAC,SAAS,KAAK,KAAK,wBAAwB;AACrD,cAAM,CAAC,gBAAgB,aAAa,IAAI,UAAU,MAAM,GAAG;AAC3D,YACE,KAAK,gBAAgB,cAAc,MAAM,YACzC,KAAK,gBAAgB,aAAa,MAAM,SACxC;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,aAAO,GAAG,KAAK,aAAa,QAAQ,CAAC,IAAI,KAAK,aAAa,OAAO,CAAC;AAAA,IACrE;AAAA;AAAA;AAAA;AAAA,IAKA,gBAAgB,KAAK;AACnB,aAAO,IAAI,YAAY,EAAE,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa,KAAK;AAChB,aAAO,IACJ,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC,EAAE,YAAY,CAAC,EACxE,KAAK,GAAG;AAAA,IACb;AAAA,IAEA,4BAA4B,QAAQ;AAElC,UAAI,OAAO,KAAK;AACd,aAAK,mBAAmB,OAAO,GAAG;AAClC,aAAK,iBAAiB,OAAO,GAAG;AAAA,MAClC,OAAO;AAEL,aAAK,cAAc;AAAA,MACrB;AAEA,WAAK,eAAe;AAAA,IACtB;AAAA,IAEA,4BAA4B,QAAQ;AAElC,WAAK,kBAAkB,OAAO;AAC9B,WAAK,oBAAoB;AACzB,WAAK,cAAc;AAGnB,WAAK,uBAAuB;AAG5B,WAAK,eAAe;AAAA,IACtB;AAAA,IAEA,mBAAmB;AAEjB,YAAM,oBAAoB,KAAK,eAAe,OAAO;AAGrD,WAAK,+BAA+B,iBAAiB;AAErD,UAAI,CAAC,mBAAmB;AAEtB,aAAK,aAAa;AAClB;AAAA,MACF;AAGA,WAAK,yBAAyB,QAAQ,CAAC,MAAM,QAAQ;AACnD,cAAM,WAAW,KAAK,SAAS,GAAG;AAClC,cAAM,aAAa,KAAK,yBAAyB,GAAG;AAGpD,cAAM,aAAa,YAAY;AAE/B,aAAK,kBAAkB,KAAK,SAAS,UAAU;AAAA,MACjD,CAAC;AAGD,WAAK,uBAAuB,QAAQ,CAAC,MAAM,QAAQ;AACjD,cAAM,WAAW,KAAK,SAAS,GAAG;AAClC,cAAM,aAAa,KAAK,yBAAyB,GAAG;AAGpD,cAAM,aAAa,YAAY;AAE/B,aAAK,kBAAkB,KAAK,SAAS,UAAU;AAAA,MACjD,CAAC;AAGD,WAAK,6BAA6B;AAAA,IACpC;AAAA;AAAA;AAAA;AAAA,IAKA,+BAA+B,mBAAmB;AAEhD,YAAM,wBAAwB,SAAS,cAAc,0BAA0B;AAC/E,YAAM,qBAAqB,SAAS,cAAc,uBAAuB;AAEzE,UAAI,uBAAuB;AACzB,YAAI,mBAAmB;AACrB,gCAAsB,MAAM,UAAU;AACtC,gCAAsB,MAAM,UAAU;AAAA,QACxC,OAAO;AACL,gCAAsB,MAAM,UAAU;AACtC,gCAAsB,MAAM,UAAU;AAAA,QACxC;AAAA,MACF;AAEA,UAAI,oBAAoB;AACtB,YAAI,mBAAmB;AACrB,6BAAmB,MAAM,UAAU;AACnC,6BAAmB,MAAM,UAAU;AAAA,QACrC,OAAO;AACL,6BAAmB,MAAM,UAAU;AACnC,6BAAmB,MAAM,UAAU;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,eAAe;AAEb,WAAK,yBAAyB,QAAQ,CAAC,SAAS;AAC9C,aAAK,kBAAkB,KAAK,SAAS,KAAK;AAAA,MAC5C,CAAC;AAGD,WAAK,uBAAuB,QAAQ,CAAC,SAAS;AAC5C,aAAK,kBAAkB,KAAK,SAAS,KAAK;AAAA,MAC5C,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,yBAAyB,WAAW;AAGlC,UAAI,KAAK,eAAe,SAAS,GAAG;AAClC,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,eAAe,IAAI,SAAS,GAAG;AACtC,eAAO;AAAA,MACT;AAGA,YAAM,YAAY,KAAK,aAAa,SAAS;AAC7C,UAAI,KAAK,eAAe,IAAI,SAAS,GAAG;AACtC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,SAAS,KAAK;AACZ,YAAM,iBAAiB,KAAK,gBAAgB,IAAI,GAAG;AACnD,UAAI,CAAC,kBAAkB,CAAC,eAAe,MAAO,QAAO;AAErD,YAAM,QAAQ,MAAM,mBAAmB,eAAe,MAAM,KAAK;AACjE,aAAO,QAAQ;AAAA,IACjB;AAAA,IAEA,kBAAkB,SAAS,YAAY;AACrC,UAAI,CAAC,QAAS;AAEd,UAAI,YAAY;AAEd,YAAI,QAAQ,MAAM,YAAY,QAAQ;AACpC,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,YAAY;AAG1B,gBAAM,YAAY,6BAAM;AACtB,oBAAQ,MAAM,aAAa;AAC3B,oBAAQ,MAAM,UAAU;AACxB,oBAAQ,MAAM,YAAY;AAAA,UAC5B,GAJkB;AAMlB,cAAI,OAAO,0BAA0B,aAAa;AAChD,kCAAsB,SAAS;AAAA,UACjC,OAAO;AACL,uBAAW,WAAW,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,OAAO;AAEL,YAAI,QAAQ,MAAM,YAAY,QAAQ;AACpC,kBAAQ,MAAM,aAAa;AAC3B,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,YAAY;AAE1B,qBAAW,MAAM;AACf,gBAAI,QAAQ,MAAM,YAAY,KAAK;AACjC,sBAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,UACF,GAAG,GAAG;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,IAEA,+BAA+B;AAE7B,UAAI,KAAK,eAAe,SAAS,GAAG;AAClC,aAAK,oBAAoB;AACzB;AAAA,MACF;AAGA,YAAM,mBAAmB,SAAS;AAAA,QAChC;AAAA,MACF;AACA,YAAM,iBAAiB,SAAS,iBAAiB,qCAAqC;AAGtF,uBAAiB,QAAQ,CAAC,UAAU;AAClC,cAAM,aAAa,KAAK,0BAA0B,KAAK;AACvD,aAAK,kBAAkB,OAAO,UAAU;AAAA,MAC1C,CAAC;AAGD,qBAAe,QAAQ,CAAC,UAAU;AAChC,cAAM,aAAa,KAAK,wBAAwB,KAAK;AACrD,aAAK,kBAAkB,OAAO,UAAU;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA,IAKA,0BAA0B,OAAO;AAC/B,YAAM,QAAQ,MAAM,iBAAiB,uBAAuB;AAE5D,iBAAW,QAAQ,OAAO;AACxB,cAAM,WAAW,KAAK,aAAa,gBAAgB;AACnD,cAAM,UAAU,KAAK,aAAa,eAAe;AAEjD,YAAI,YAAY,SAAS;AACvB,gBAAM,MAAM,GAAG,QAAQ,IAAI,OAAO;AAClC,gBAAM,YAAY,KAAK,aAAa,GAAG;AAGvC,gBAAM,aAAa,KAAK,yBAAyB,SAAS;AAC1D,gBAAM,WAAW,KAAK,SAAS,SAAS;AAExC,cAAI,cAAc,UAAU;AAC1B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAKA,wBAAwB,OAAO;AAC7B,YAAM,QAAQ,MAAM,iBAAiB,uBAAuB;AAE5D,iBAAW,QAAQ,OAAO;AACxB,cAAM,WAAW,KAAK,aAAa,gBAAgB;AACnD,cAAM,UAAU,KAAK,aAAa,eAAe;AAEjD,YAAI,YAAY,SAAS;AACvB,gBAAM,MAAM,GAAG,QAAQ,IAAI,OAAO;AAClC,gBAAM,YAAY,KAAK,aAAa,GAAG;AAGvC,gBAAM,aAAa,KAAK,yBAAyB,SAAS;AAC1D,gBAAM,WAAW,KAAK,SAAS,SAAS;AAExC,cAAI,cAAc,UAAU;AAC1B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAKA,sBAAsB;AACpB,YAAM,mBAAmB,SAAS;AAAA,QAChC;AAAA,MACF;AACA,YAAM,iBAAiB,SAAS,iBAAiB,qCAAqC;AAEtF,uBAAiB,QAAQ,CAAC,UAAU;AAClC,aAAK,kBAAkB,OAAO,KAAK;AAAA,MACrC,CAAC;AAED,qBAAe,QAAQ,CAAC,UAAU;AAChC,aAAK,kBAAkB,OAAO,KAAK;AAAA,MACrC,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,sBAAsB,OAAO;AAC3B,WAAK,kBAAkB;AACvB,WAAK,cAAc;AAAA,IACrB;AAAA,IAEA,mBAAmB;AACjB,YAAM,OAAO;AAAA,QACX,YAAY,CAAC;AAAA,QACb,WAAW,CAAC;AAAA,QACZ,OAAO,KAAK;AAAA,MACd;AAEA,WAAK,gBAAgB,QAAQ,CAAC,MAAM,QAAQ;AAC1C,cAAM,aAAa,KAAK,QAAQ,MAAM,mBAAmB,KAAK,MAAM,KAAK,IAAI;AAC7E,cAAM,cAAc,KAAK,SAAS,WAAW,KAAK,OAAO,KAAK,IAAI;AAElE,aAAK,WAAW,GAAG,IAAI;AAAA,UACrB,OAAO;AAAA,UACP,YAAY,cAAc;AAAA,UAC1B,UAAU,KAAK;AAAA,UACf,SAAS,KAAK;AAAA,QAChB;AAEA,cAAM,eAAe,KAAK,gBAAgB,KAAK,UAAU,KAAK,OAAO;AACrE,YAAI,cAAc;AAChB,eAAK,UAAU,GAAG,IAAI;AAAA,YACpB,SAAS,aAAa;AAAA,YACtB,SAAS,aAAa;AAAA,YACtB,UAAW,aAAa,aAAa,MAAO;AAAA,YAC5C,UAAW,aAAa,aAAa,MAAO;AAAA,UAC9C;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA,IAEA,YAAY;AACV,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAAA,IACxB;AAAA;AAAA,IAGA,YAAY;AACV,YAAM,iBAAiB,MAAM,KAAK,KAAK,gBAAgB,KAAK,CAAC;AAC7D,YAAM,0BAA0B,MAAM,KAAK,KAAK,yBAAyB,KAAK,CAAC;AAC/E,YAAM,wBAAwB,MAAM,KAAK,KAAK,uBAAuB,KAAK,CAAC;AAG3E,YAAM,gBAAgB,CAAC;AACvB,qBAAe,QAAQ,CAAC,QAAQ;AAC9B,cAAM,YAAY,KAAK,aAAa,GAAG;AACvC,cAAM,qBAAqB,KAAK,yBAAyB,IAAI,SAAS;AACtE,cAAM,mBAAmB,KAAK,uBAAuB,IAAI,SAAS;AAElE,sBAAc,GAAG,IAAI;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,QAAQ;AAAA,QACpB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,QACL,eAAe,KAAK;AAAA,QACpB,iBAAiB,KAAK,gBAAgB;AAAA,QACtC,0BAA0B,KAAK,yBAAyB;AAAA,QACxD,wBAAwB,KAAK,uBAAuB;AAAA,QACpD,gBAAgB,KAAK,eAAe;AAAA,QACpC,iBAAiB,KAAK;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkB,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,IAEA,WAAW;AAET,WAAK,oBAAoB;AAGzB,YAAM,WAAW,KAAK,gBAAgB,KAAK,EAAE,KAAK,EAAE;AACpD,UAAI,UAAU;AACZ,aAAK,mBAAmB,QAAQ;AAChC,aAAK,iBAAiB,QAAQ;AAAA,MAChC;AAGA,WAAK,iBAAiB;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc;AACZ,UAAI;AAEF,aAAK,WAAW;AAGhB,aAAK,eAAe,MAAM;AAC1B,aAAK,kBAAkB;AAGvB,aAAK,sBAAsB;AAG3B,aAAK,iBAAiB;AAGtB,iBAAS;AAAA,UACP,IAAI,YAAY,sBAAsB;AAAA,YACpC,QAAQ;AAAA,cACN,WAAW,KAAK,IAAI;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,qCAAqC,KAAK;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,wBAAwB;AAEtB,WAAK,yBAAyB,QAAQ,CAAC,SAAS;AAC9C,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa,cAAc;AAAA,QAClC;AACA,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB,cAAc;AAAA,QACvC;AAAA,MACF,CAAC;AAGD,WAAK,uBAAuB,QAAQ,CAAC,SAAS;AAC5C,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB,cAAc;AAAA,QACvC;AACA,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB,cAAc;AAAA,QACvC;AACA,YAAI,KAAK,oBAAoB;AAC3B,eAAK,mBAAmB,cAAc;AAAA,QACxC;AACA,YAAI,KAAK,oBAAoB;AAC3B,eAAK,mBAAmB,cAAc;AAAA,QACxC;AAAA,MACF,CAAC;AAGD,WAAK,+BAA+B,KAAK;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA,IAKA,eAAe;AACb,UAAI;AACF,cAAM,aAAa,KAAK,aAAa,IAAI,KAAK,QAAQ;AACtD,eAAO;AAAA,UACL,eAAe,CAAC,CAAC;AAAA,UACjB,iBAAiB,YAAY,mBAAmB;AAAA,UAChD,qBAAqB,YAAY,gBAAgB,UAAU;AAAA,UAC3D,WAAW,YAAY,aAAa;AAAA,UACpC,wBAAwB,KAAK;AAAA,UAC7B,uBAAuB,KAAK,eAAe;AAAA,UAC3C,oBAAoB,YAAY,gBAC5B,OAAO,KAAK,WAAW,aAAa,EAAE,SACtC;AAAA,QACN;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,6BAA6B,KAAK;AAC/C,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB;AACjB,cAAQ,KAAK,8BAA8B;AAC3C,cAAQ,KAAK,oBAAoB,MAAM,KAAK,KAAK,cAAc,CAAC;AAChE,cAAQ,KAAK,wBAAwB,KAAK,eAAe,OAAO,CAAC;AAGjE,YAAM,wBAAwB,SAAS,cAAc,0BAA0B;AAC/E,YAAM,qBAAqB,SAAS,cAAc,uBAAuB;AAEzE,cAAQ,KAAK,2BAA2B;AACxC,cAAQ,KAAK,4BAA4B;AAAA,QACvC,QAAQ,CAAC,CAAC;AAAA,QACV,SAAS,uBAAuB,MAAM,WAAW;AAAA,QACjD,SAAS,uBAAuB,MAAM,WAAW;AAAA,MACnD,CAAC;AACD,cAAQ,KAAK,yBAAyB;AAAA,QACpC,QAAQ,CAAC,CAAC;AAAA,QACV,SAAS,oBAAoB,MAAM,WAAW;AAAA,QAC9C,SAAS,oBAAoB,MAAM,WAAW;AAAA,MAChD,CAAC;AAED,cAAQ,KAAK,4BAA4B;AACzC,WAAK,yBAAyB,QAAQ,CAAC,MAAM,QAAQ;AACnD,cAAM,WAAW,KAAK,SAAS,GAAG;AAClC,cAAM,aAAa,KAAK,yBAAyB,GAAG;AACpD,cAAM,aAAa,YAAY;AAC/B,cAAM,YAAY,KAAK,QAAQ,MAAM,YAAY;AAEjD,gBAAQ,KAAK,GAAG,GAAG,KAAK;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,KAAK;AAAA,UACf,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAED,cAAQ,KAAK,0BAA0B;AACvC,WAAK,uBAAuB,QAAQ,CAAC,MAAM,QAAQ;AACjD,cAAM,WAAW,KAAK,SAAS,GAAG;AAClC,cAAM,aAAa,KAAK,yBAAyB,GAAG;AACpD,cAAM,aAAa,YAAY;AAC/B,cAAM,YAAY,KAAK,QAAQ,MAAM,YAAY;AAEjD,gBAAQ,KAAK,GAAG,GAAG,KAAK;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,KAAK;AAAA,UACf,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAED,cAAQ,KAAK,yBAAyB;AACtC,YAAM,mBAAmB,SAAS;AAAA,QAChC;AAAA,MACF;AACA,YAAM,iBAAiB,SAAS,iBAAiB,qCAAqC;AAEtF,cAAQ,KAAK,oBAAoB;AACjC,uBAAiB,QAAQ,CAAC,OAAO,UAAU;AACzC,cAAM,aAAa,KAAK,0BAA0B,KAAK;AACvD,cAAM,YAAY,MAAM,MAAM,YAAY;AAC1C,cAAM,SAAS,MAAM,cAAc,sBAAsB;AACzD,cAAM,aAAa,QAAQ,aAAa,KAAK,KAAK;AAElD,gBAAQ,KAAK,WAAW,KAAK,KAAK,UAAU,MAAM;AAAA,UAChD;AAAA,UACA;AAAA,UACA,SAAS,MAAM,MAAM,WAAW;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AAED,cAAQ,KAAK,kBAAkB;AAC/B,qBAAe,QAAQ,CAAC,OAAO,UAAU;AACvC,cAAM,aAAa,KAAK,wBAAwB,KAAK;AACrD,cAAM,YAAY,MAAM,MAAM,YAAY;AAC1C,cAAM,SAAS,MAAM,cAAc,sBAAsB;AACzD,cAAM,aAAa,QAAQ,aAAa,KAAK,KAAK;AAElD,gBAAQ,KAAK,WAAW,KAAK,KAAK,UAAU,MAAM;AAAA,UAChD;AAAA,UACA;AAAA,UACA,SAAS,MAAM,MAAM,WAAW;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AAED,cAAQ,KAAK,6BAA6B;AAC1C,aAAO,QAAQ,KAAK,gBAAgB,EAAE,QAAQ,CAAC,CAAC,UAAU,MAAM,MAAM;AACpE,gBAAQ,KAAK,GAAG,QAAQ,OAAO,MAAM,EAAE;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,EACF;AAGA,MAAM,gBAAgB,IAAI,oBAAoB;AAI9C,MAAO,yBAAQ;AAGf,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,qBAAqB,MAAM;AAChC,YAAM,SAAS,OAAO,cAAc,YAAY,eAAe;AAC/D,UAAI,QAAQ;AACV,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AAEA,WAAO,oBAAoB,MAAM;AAC/B,YAAM,SAAS,OAAO,cAAc,YAAY,eAAe;AAC/D,UAAI,QAAQ;AACV,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAEA,WAAO,qBAAqB,MAAM;AAChC,YAAM,SAAS,OAAO,cAAc,YAAY,eAAe;AAC/D,UAAI,QAAQ;AACV,eAAO,UAAU;AAAA,MACnB;AAAA,IACF;AAEA,WAAO,4BAA4B,MAAM;AACvC,YAAM,SAAS,OAAO,cAAc,YAAY,eAAe;AAC/D,UAAI,QAAQ;AACV,eAAO,iBAAiB;AAAA,MAC1B;AAAA,IACF;AAEA,WAAO,0BAA0B,MAAM;AACrC,YAAM,SAAS,OAAO,cAAc,YAAY,eAAe;AAC/D,UAAI,QAAQ;AACV,eAAO,6BAA6B;AACpC,gBAAQ,KAAK,2CAA2C;AAAA,MAC1D;AAAA,IACF;AAEA,WAAO,yBAAyB,MAAM;AACpC,YAAM,SAAS,OAAO,cAAc,YAAY,eAAe;AAC/D,UAAI,QAAQ;AACV,eAAO,uBAAuB;AAC9B,gBAAQ,KAAK,qCAAqC;AAAA,MACpD;AAAA,IACF;AAAA,EACF;", "names": []}