{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/event-coordinator.js", "../../src/modules/currency-control.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * EventCoordinator - Sistema centralizado CORRIGIDO para gerenciar eventos do input principal\r\n * Evita conflitos entre múltiplos módulos e memory leaks\r\n */\r\n\r\nexport class EventCoordinator {\r\n  constructor() {\r\n    this.input = null;\r\n    this.listeners = new Map();\r\n    this.isProcessing = false;\r\n    this.eventQueue = [];\r\n    this.boundHandlers = new Map(); // Para rastrear handlers bound\r\n    this.isDestroyed = false;\r\n\r\n    this.init();\r\n  }\r\n\r\n  init() {\r\n    // Aguarda o DOM estar pronto\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => this.findAndSetupInput());\r\n    } else {\r\n      this.findAndSetupInput();\r\n    }\r\n  }\r\n\r\n  findAndSetupInput() {\r\n    this.input = document.querySelector('[is-main=\"true\"]');\r\n    if (this.input && !this.isDestroyed) {\r\n      this.setupMainListeners();\r\n    }\r\n  }\r\n\r\n  setupMainListeners() {\r\n    if (!this.input || this.boundHandlers.has('main')) return;\r\n\r\n    // Cria handlers bound uma única vez\r\n    const inputHandler = (e) => this.handleInputEvent(e);\r\n    const focusHandler = (e) => this.processFocusEvent(e);\r\n    const blurHandler = (e) => this.processBlurEvent(e);\r\n    const changeHandler = (e) => this.processChangeEvent(e);\r\n\r\n    // Armazena referências para cleanup posterior\r\n    this.boundHandlers.set('main', {\r\n      input: inputHandler,\r\n      focus: focusHandler,\r\n      blur: blurHandler,\r\n      change: changeHandler,\r\n    });\r\n\r\n    // Adiciona listeners apenas uma vez\r\n    this.input.addEventListener('input', inputHandler, { passive: true });\r\n    this.input.addEventListener('focus', focusHandler, { passive: true });\r\n    this.input.addEventListener('blur', blurHandler, { passive: true });\r\n    this.input.addEventListener('change', changeHandler, { passive: true });\r\n  }\r\n\r\n  handleInputEvent(e) {\r\n    if (this.isProcessing || this.isDestroyed) {\r\n      return;\r\n    }\r\n\r\n    this.isProcessing = true;\r\n\r\n    // Usa requestAnimationFrame para melhor performance\r\n    requestAnimationFrame(() => {\r\n      this.processInputEvent(e);\r\n      this.isProcessing = false;\r\n\r\n      // Processa queue se houver\r\n      if (this.eventQueue.length > 0) {\r\n        const nextEvent = this.eventQueue.shift();\r\n        requestAnimationFrame(() => this.handleInputEvent(nextEvent));\r\n      }\r\n    });\r\n  }\r\n\r\n  // Registra um listener para um módulo específico\r\n  registerListener(moduleId, eventType, callback) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    // Remove listener anterior se existir (evita duplicação)\r\n    this.unregisterListener(moduleId, eventType);\r\n\r\n    if (!this.listeners.has(key)) {\r\n      this.listeners.set(key, []);\r\n    }\r\n\r\n    this.listeners.get(key).push(callback);\r\n  }\r\n\r\n  // Remove listener de um módulo\r\n  unregisterListener(moduleId, eventType, specificCallback = null) {\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    if (this.listeners.has(key)) {\r\n      if (specificCallback) {\r\n        const callbacks = this.listeners.get(key);\r\n        const index = callbacks.indexOf(specificCallback);\r\n        if (index > -1) {\r\n          callbacks.splice(index, 1);\r\n        }\r\n      } else {\r\n        // Remove todos os callbacks do módulo para este evento\r\n        this.listeners.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove todos os listeners de um módulo\r\n  unregisterModule(moduleId) {\r\n    const keysToRemove = [];\r\n    for (const key of this.listeners.keys()) {\r\n      if (key.startsWith(`${moduleId}_`)) {\r\n        keysToRemove.push(key);\r\n      }\r\n    }\r\n\r\n    keysToRemove.forEach((key) => this.listeners.delete(key));\r\n  }\r\n\r\n  processInputEvent(e) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const inputCallbacks = this.getCallbacksForEvent('input');\r\n\r\n    // Executa callbacks em ordem de prioridade\r\n    const priorityOrder = ['currency-formatting', 'motion-animation', 'patrimony-sync'];\r\n\r\n    for (const moduleId of priorityOrder) {\r\n      const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);\r\n      for (const callbackInfo of moduleCallbacks) {\r\n        try {\r\n          callbackInfo.callback(e);\r\n        } catch (error) {\r\n          console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  processFocusEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('focus', e);\r\n  }\r\n\r\n  processBlurEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('blur', e);\r\n  }\r\n\r\n  processChangeEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('change', e);\r\n  }\r\n\r\n  executeCallbacksForEvent(eventType, e) {\r\n    const callbacks = this.getCallbacksForEvent(eventType);\r\n    callbacks.forEach(({ callback, moduleId }) => {\r\n      try {\r\n        callback(e);\r\n      } catch (error) {\r\n        console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);\r\n      }\r\n    });\r\n  }\r\n\r\n  getCallbacksForEvent(eventType) {\r\n    const callbacks = [];\r\n    for (const [key, callbackList] of this.listeners.entries()) {\r\n      if (key.endsWith(`_${eventType}`)) {\r\n        const moduleId = key.replace(`_${eventType}`, '');\r\n        callbackList.forEach((callback) => {\r\n          callbacks.push({ moduleId, callback });\r\n        });\r\n      }\r\n    }\r\n    return callbacks;\r\n  }\r\n\r\n  // Método para disparar eventos programaticamente\r\n  dispatchInputEvent(sourceModule = 'unknown') {\r\n    if (this.isProcessing || this.isDestroyed || !this.input) {\r\n      return;\r\n    }\r\n\r\n    const event = new Event('input', { bubbles: true });\r\n    event.sourceModule = sourceModule;\r\n    this.input.dispatchEvent(event);\r\n  }\r\n\r\n  // Método para atualizar valor sem disparar eventos\r\n  setSilentValue(value) {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.isProcessing = true;\r\n    this.input.value = value;\r\n\r\n    // Usa requestAnimationFrame para reset mais confiável\r\n    requestAnimationFrame(() => {\r\n      this.isProcessing = false;\r\n    });\r\n  }\r\n\r\n  // Getter para o valor atual\r\n  getValue() {\r\n    return this.input ? this.input.value : '';\r\n  }\r\n\r\n  // Setter que dispara eventos controlados\r\n  setValue(value, sourceModule = 'unknown') {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.input.value = value;\r\n    this.dispatchInputEvent(sourceModule);\r\n  }\r\n\r\n  // Método de cleanup para prevenir memory leaks\r\n  destroy() {\r\n    this.isDestroyed = true;\r\n\r\n    // Remove todos os event listeners\r\n    if (this.input && this.boundHandlers.has('main')) {\r\n      const handlers = this.boundHandlers.get('main');\r\n      this.input.removeEventListener('input', handlers.input);\r\n      this.input.removeEventListener('focus', handlers.focus);\r\n      this.input.removeEventListener('blur', handlers.blur);\r\n      this.input.removeEventListener('change', handlers.change);\r\n    }\r\n\r\n    // Limpa todas as referências\r\n    this.listeners.clear();\r\n    this.boundHandlers.clear();\r\n    this.eventQueue.length = 0;\r\n    this.input = null;\r\n    this.isProcessing = false;\r\n  }\r\n\r\n  // Método para reinicializar se necessário\r\n  reinitialize() {\r\n    this.destroy();\r\n    this.isDestroyed = false;\r\n    this.init();\r\n  }\r\n}\r\n\r\n// Instância singleton\r\nexport const eventCoordinator = new EventCoordinator();\r\n\r\n// Cleanup automático quando a página é descarregada\r\nwindow.addEventListener('beforeunload', () => {\r\n  eventCoordinator.destroy();\r\n});\r\n", "import { eventCoordinator } from './event-coordinator.js';\r\n\r\n/**\r\n * Currency Control System\r\n * Handles currency input controls (increase/decrease buttons)\r\n * Uses EventCoordinator to prevent infinite loops\r\n */\r\nexport class CurrencyControlSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.initializeCurrencyControls();\r\n    });\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  initializeCurrencyControls() {\r\n    const input = document.querySelector('[is-main=\"true\"]');\r\n    if (!input) return;\r\n\r\n    // Função para calcular incremento inteligente\r\n    const getIncrement = (value) => {\r\n      if (value < 1000) return 100;\r\n      if (value < 10000) return 1000;\r\n      if (value < 100000) return 10000;\r\n      if (value < 1000000) return 50000;\r\n      return 100000;\r\n    };\r\n\r\n    // Função para atualizar valor usando EventCoordinator\r\n    const updateValue = (newValue) => {\r\n      const formattedValue = new Intl.NumberFormat('pt-BR', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2,\r\n      }).format(newValue);\r\n\r\n      // Usa EventCoordinator para evitar loops infinitos\r\n      eventCoordinator.setValue(formattedValue, 'currency-control');\r\n    };\r\n\r\n    // Decrease buttons\r\n    document.querySelectorAll('[currency-control=\"decrease\"]').forEach((btn) => {\r\n      btn.addEventListener('click', (e) => {\r\n        e.preventDefault();\r\n        const current = parseFloat(input.value.replace(/[^\\d,]/g, '').replace(',', '.')) || 0;\r\n        updateValue(Math.max(0, current - getIncrement(current)));\r\n      });\r\n    });\r\n\r\n    // Increase buttons\r\n    document.querySelectorAll('[currency-control=\"increase\"]').forEach((btn) => {\r\n      btn.addEventListener('click', (e) => {\r\n        e.preventDefault();\r\n        const current = parseFloat(input.value.replace(/[^\\d,]/g, '').replace(',', '.')) || 0;\r\n        updateValue(current + getIncrement(current));\r\n      });\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACKtF,MAAM,mBAAN,MAAuB;AAAA,IAL9B,OAK8B;AAAA;AAAA;AAAA,IAC5B,cAAc;AACZ,WAAK,QAAQ;AACb,WAAK,YAAY,oBAAI,IAAI;AACzB,WAAK,eAAe;AACpB,WAAK,aAAa,CAAC;AACnB,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,cAAc;AAEnB,WAAK,KAAK;AAAA,IACZ;AAAA,IAEA,OAAO;AAEL,UAAI,SAAS,eAAe,WAAW;AACrC,iBAAS,iBAAiB,oBAAoB,MAAM,KAAK,kBAAkB,CAAC;AAAA,MAC9E,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,WAAK,QAAQ,SAAS,cAAc,kBAAkB;AACtD,UAAI,KAAK,SAAS,CAAC,KAAK,aAAa;AACnC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,IAEA,qBAAqB;AACnB,UAAI,CAAC,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,EAAG;AAGnD,YAAM,eAAe,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACrB,YAAM,eAAe,wBAAC,MAAM,KAAK,kBAAkB,CAAC,GAA/B;AACrB,YAAM,cAAc,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACpB,YAAM,gBAAgB,wBAAC,MAAM,KAAK,mBAAmB,CAAC,GAAhC;AAGtB,WAAK,cAAc,IAAI,QAAQ;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAGD,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,QAAQ,aAAa,EAAE,SAAS,KAAK,CAAC;AAClE,WAAK,MAAM,iBAAiB,UAAU,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,IACxE;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,gBAAgB,KAAK,aAAa;AACzC;AAAA,MACF;AAEA,WAAK,eAAe;AAGpB,4BAAsB,MAAM;AAC1B,aAAK,kBAAkB,CAAC;AACxB,aAAK,eAAe;AAGpB,YAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,gBAAM,YAAY,KAAK,WAAW,MAAM;AACxC,gCAAsB,MAAM,KAAK,iBAAiB,SAAS,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,iBAAiB,UAAU,WAAW,UAAU;AAC9C,UAAI,KAAK,YAAa;AAEtB,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAGpC,WAAK,mBAAmB,UAAU,SAAS;AAE3C,UAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,aAAK,UAAU,IAAI,KAAK,CAAC,CAAC;AAAA,MAC5B;AAEA,WAAK,UAAU,IAAI,GAAG,EAAE,KAAK,QAAQ;AAAA,IACvC;AAAA;AAAA,IAGA,mBAAmB,UAAU,WAAW,mBAAmB,MAAM;AAC/D,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAEpC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,YAAI,kBAAkB;AACpB,gBAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,gBAAM,QAAQ,UAAU,QAAQ,gBAAgB;AAChD,cAAI,QAAQ,IAAI;AACd,sBAAU,OAAO,OAAO,CAAC;AAAA,UAC3B;AAAA,QACF,OAAO;AAEL,eAAK,UAAU,OAAO,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,iBAAiB,UAAU;AACzB,YAAM,eAAe,CAAC;AACtB,iBAAW,OAAO,KAAK,UAAU,KAAK,GAAG;AACvC,YAAI,IAAI,WAAW,GAAG,QAAQ,GAAG,GAAG;AAClC,uBAAa,KAAK,GAAG;AAAA,QACvB;AAAA,MACF;AAEA,mBAAa,QAAQ,CAAC,QAAQ,KAAK,UAAU,OAAO,GAAG,CAAC;AAAA,IAC1D;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AAEtB,YAAM,iBAAiB,KAAK,qBAAqB,OAAO;AAGxD,YAAM,gBAAgB,CAAC,uBAAuB,oBAAoB,gBAAgB;AAElF,iBAAW,YAAY,eAAe;AACpC,cAAM,kBAAkB,eAAe,OAAO,CAAC,OAAO,GAAG,aAAa,QAAQ;AAC9E,mBAAW,gBAAgB,iBAAiB;AAC1C,cAAI;AACF,yBAAa,SAAS,CAAC;AAAA,UACzB,SAAS,OAAO;AACd,oBAAQ,MAAM,8BAA8B,QAAQ,cAAc,KAAK;AAAA,UACzE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,SAAS,CAAC;AAAA,IAC1C;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,QAAQ,CAAC;AAAA,IACzC;AAAA,IAEA,mBAAmB,GAAG;AACpB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,UAAU,CAAC;AAAA,IAC3C;AAAA,IAEA,yBAAyB,WAAW,GAAG;AACrC,YAAM,YAAY,KAAK,qBAAqB,SAAS;AACrD,gBAAU,QAAQ,CAAC,EAAE,UAAU,SAAS,MAAM;AAC5C,YAAI;AACF,mBAAS,CAAC;AAAA,QACZ,SAAS,OAAO;AACd,kBAAQ,MAAM,8BAA8B,QAAQ,IAAI,SAAS,cAAc,KAAK;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,qBAAqB,WAAW;AAC9B,YAAM,YAAY,CAAC;AACnB,iBAAW,CAAC,KAAK,YAAY,KAAK,KAAK,UAAU,QAAQ,GAAG;AAC1D,YAAI,IAAI,SAAS,IAAI,SAAS,EAAE,GAAG;AACjC,gBAAM,WAAW,IAAI,QAAQ,IAAI,SAAS,IAAI,EAAE;AAChD,uBAAa,QAAQ,CAAC,aAAa;AACjC,sBAAU,KAAK,EAAE,UAAU,SAAS,CAAC;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,mBAAmB,eAAe,WAAW;AAC3C,UAAI,KAAK,gBAAgB,KAAK,eAAe,CAAC,KAAK,OAAO;AACxD;AAAA,MACF;AAEA,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC;AAClD,YAAM,eAAe;AACrB,WAAK,MAAM,cAAc,KAAK;AAAA,IAChC;AAAA;AAAA,IAGA,eAAe,OAAO;AACpB,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,eAAe;AACpB,WAAK,MAAM,QAAQ;AAGnB,4BAAsB,MAAM;AAC1B,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,WAAW;AACT,aAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ;AAAA,IACzC;AAAA;AAAA,IAGA,SAAS,OAAO,eAAe,WAAW;AACxC,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,YAAY;AAAA,IACtC;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,cAAc;AAGnB,UAAI,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,GAAG;AAChD,cAAM,WAAW,KAAK,cAAc,IAAI,MAAM;AAC9C,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,QAAQ,SAAS,IAAI;AACpD,aAAK,MAAM,oBAAoB,UAAU,SAAS,MAAM;AAAA,MAC1D;AAGA,WAAK,UAAU,MAAM;AACrB,WAAK,cAAc,MAAM;AACzB,WAAK,WAAW,SAAS;AACzB,WAAK,QAAQ;AACb,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,eAAe;AACb,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAGO,MAAM,mBAAmB,IAAI,iBAAiB;AAGrD,SAAO,iBAAiB,gBAAgB,MAAM;AAC5C,qBAAiB,QAAQ;AAAA,EAC3B,CAAC;;;ACvPM,MAAM,wBAAN,MAA4B;AAAA,IAPnC,OAOmC;AAAA;AAAA;AAAA,IACjC,cAAc;AACZ,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,2BAA2B;AAAA,MAClC,CAAC;AAED,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,6BAA6B;AAC3B,YAAM,QAAQ,SAAS,cAAc,kBAAkB;AACvD,UAAI,CAAC,MAAO;AAGZ,YAAM,eAAe,wBAAC,UAAU;AAC9B,YAAI,QAAQ,IAAM,QAAO;AACzB,YAAI,QAAQ,IAAO,QAAO;AAC1B,YAAI,QAAQ,IAAQ,QAAO;AAC3B,YAAI,QAAQ,IAAS,QAAO;AAC5B,eAAO;AAAA,MACT,GANqB;AASrB,YAAM,cAAc,wBAAC,aAAa;AAChC,cAAM,iBAAiB,IAAI,KAAK,aAAa,SAAS;AAAA,UACpD,uBAAuB;AAAA,UACvB,uBAAuB;AAAA,QACzB,CAAC,EAAE,OAAO,QAAQ;AAGlB,yBAAiB,SAAS,gBAAgB,kBAAkB;AAAA,MAC9D,GARoB;AAWpB,eAAS,iBAAiB,+BAA+B,EAAE,QAAQ,CAAC,QAAQ;AAC1E,YAAI,iBAAiB,SAAS,CAAC,MAAM;AACnC,YAAE,eAAe;AACjB,gBAAM,UAAU,WAAW,MAAM,MAAM,QAAQ,WAAW,EAAE,EAAE,QAAQ,KAAK,GAAG,CAAC,KAAK;AACpF,sBAAY,KAAK,IAAI,GAAG,UAAU,aAAa,OAAO,CAAC,CAAC;AAAA,QAC1D,CAAC;AAAA,MACH,CAAC;AAGD,eAAS,iBAAiB,+BAA+B,EAAE,QAAQ,CAAC,QAAQ;AAC1E,YAAI,iBAAiB,SAAS,CAAC,MAAM;AACnC,YAAE,eAAe;AACjB,gBAAM,UAAU,WAAW,MAAM,MAAM,QAAQ,WAAW,EAAE,EAAE,QAAQ,KAAK,GAAG,CAAC,KAAK;AACpF,sBAAY,UAAU,aAAa,OAAO,CAAC;AAAA,QAC7C,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;", "names": []}