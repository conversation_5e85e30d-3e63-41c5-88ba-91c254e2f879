{"version": 3, "sources": ["../../bin/live-reload.js", "../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/browser.js", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/PostgrestError.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/PostgrestBuilder.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/PostgrestTransformBuilder.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/PostgrestFilterBuilder.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/PostgrestQueryBuilder.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/version.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/constants.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/PostgrestClient.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/src/index.ts", "../../src/modules/salesforce-sync.js", "../../src/config/salesforce.js", "../../src/config/supabase.js", "../../node_modules/.pnpm/@supabase+supabase-js@2.53.0/node_modules/@supabase/supabase-js/src/index.ts", "../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/src/index.ts", "../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/src/helper.ts", "../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/src/types.ts", "../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/index.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/RealtimeClient.ts", "../../node_modules/.pnpm/isows@1.0.7_ws@8.18.3/node_modules/isows/native.ts", "../../node_modules/.pnpm/isows@1.0.7_ws@8.18.3/node_modules/isows/utils.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/lib/constants.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/lib/version.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/lib/serializer.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/lib/timer.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/lib/transformers.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/RealtimeChannel.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/lib/push.ts", "../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/src/RealtimePresence.ts", "../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/src/index.ts", "../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/src/StorageClient.ts", "../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/src/lib/errors.ts", "../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/src/lib/helpers.ts", "../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/src/lib/constants.ts", "../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/src/lib/version.ts", "../../node_modules/.pnpm/@supabase+supabase-js@2.53.0/node_modules/@supabase/supabase-js/src/lib/constants.ts", "../../node_modules/.pnpm/@supabase+supabase-js@2.53.0/node_modules/@supabase/supabase-js/src/lib/version.ts", "../../node_modules/.pnpm/@supabase+supabase-js@2.53.0/node_modules/@supabase/supabase-js/src/lib/SupabaseAuthClient.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/index.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/constants.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/version.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/helpers.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/errors.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/base64url.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/fetch.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/types.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/GoTrueClient.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/local-storage.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/polyfills.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/lib/locks.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/AuthAdminApi.ts", "../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/src/AuthClient.ts"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "\"use strict\";\n\n// ref: https://github.com/tc39/proposal-global\nvar getGlobal = function() {\n    // the only reliable means to get the global object is\n    // `Function('return this')()`\n    // However, this causes CSP violations in Chrome apps.\n    if (typeof self !== 'undefined') { return self; }\n    if (typeof window !== 'undefined') { return window; }\n    if (typeof global !== 'undefined') { return global; }\n    throw new Error('unable to locate global object');\n}\n\nvar globalObject = getGlobal();\n\nexport const fetch = globalObject.fetch;\n\nexport default globalObject.fetch.bind(globalObject);\n\nexport const Headers = globalObject.Headers;\nexport const Request = globalObject.Request;\nexport const Response = globalObject.Response;\n", "/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nexport default class PostgrestError extends Error {\n  details: string\n  hint: string\n  code: string\n\n  constructor(context: { message: string; details: string; hint: string; code: string }) {\n    super(context.message)\n    this.name = 'PostgrestError'\n    this.details = context.details\n    this.hint = context.hint\n    this.code = context.code\n  }\n}\n", "// @ts-ignore\nimport nodeFetch from '@supabase/node-fetch'\n\nimport type {\n  Fetch,\n  PostgrestSingleResponse,\n  PostgrestResponseSuccess,\n  CheckMatchingArrayTypes,\n  MergePartialResult,\n  IsValidResultOverride,\n} from './types'\nimport PostgrestError from './PostgrestError'\nimport { ContainsNull } from './select-query-parser/types'\n\nexport default abstract class PostgrestBuilder<Result, ThrowOnError extends boolean = false>\n  implements\n    PromiseLike<\n      ThrowOnError extends true ? PostgrestResponseSuccess<Result> : PostgrestSingleResponse<Result>\n    >\n{\n  protected method: 'GET' | 'HEAD' | 'POST' | 'PATCH' | 'DELETE'\n  protected url: URL\n  protected headers: Record<string, string>\n  protected schema?: string\n  protected body?: unknown\n  protected shouldThrowOnError = false\n  protected signal?: AbortSignal\n  protected fetch: Fetch\n  protected isMaybeSingle: boolean\n\n  constructor(builder: PostgrestBuilder<Result>) {\n    this.method = builder.method\n    this.url = builder.url\n    this.headers = builder.headers\n    this.schema = builder.schema\n    this.body = builder.body\n    this.shouldThrowOnError = builder.shouldThrowOnError\n    this.signal = builder.signal\n    this.isMaybeSingle = builder.isMaybeSingle\n\n    if (builder.fetch) {\n      this.fetch = builder.fetch\n    } else if (typeof fetch === 'undefined') {\n      this.fetch = nodeFetch\n    } else {\n      this.fetch = fetch\n    }\n  }\n\n  /**\n   * If there's an error with the query, throwOnError will reject the promise by\n   * throwing the error instead of returning it as part of a successful response.\n   *\n   * {@link https://github.com/supabase/supabase-js/issues/92}\n   */\n  throwOnError(): this & PostgrestBuilder<Result, true> {\n    this.shouldThrowOnError = true\n    return this as this & PostgrestBuilder<Result, true>\n  }\n\n  /**\n   * Set an HTTP header for the request.\n   */\n  setHeader(name: string, value: string): this {\n    this.headers = { ...this.headers }\n    this.headers[name] = value\n    return this\n  }\n\n  then<\n    TResult1 = ThrowOnError extends true\n      ? PostgrestResponseSuccess<Result>\n      : PostgrestSingleResponse<Result>,\n    TResult2 = never\n  >(\n    onfulfilled?:\n      | ((\n          value: ThrowOnError extends true\n            ? PostgrestResponseSuccess<Result>\n            : PostgrestSingleResponse<Result>\n        ) => TResult1 | PromiseLike<TResult1>)\n      | undefined\n      | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null\n  ): PromiseLike<TResult1 | TResult2> {\n    // https://postgrest.org/en/stable/api.html#switching-schemas\n    if (this.schema === undefined) {\n      // skip\n    } else if (['GET', 'HEAD'].includes(this.method)) {\n      this.headers['Accept-Profile'] = this.schema\n    } else {\n      this.headers['Content-Profile'] = this.schema\n    }\n    if (this.method !== 'GET' && this.method !== 'HEAD') {\n      this.headers['Content-Type'] = 'application/json'\n    }\n\n    // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n    // https://github.com/supabase/postgrest-js/pull/247\n    const _fetch = this.fetch\n    let res = _fetch(this.url.toString(), {\n      method: this.method,\n      headers: this.headers,\n      body: JSON.stringify(this.body),\n      signal: this.signal,\n    }).then(async (res) => {\n      let error = null\n      let data = null\n      let count: number | null = null\n      let status = res.status\n      let statusText = res.statusText\n\n      if (res.ok) {\n        if (this.method !== 'HEAD') {\n          const body = await res.text()\n          if (body === '') {\n            // Prefer: return=minimal\n          } else if (this.headers['Accept'] === 'text/csv') {\n            data = body\n          } else if (\n            this.headers['Accept'] &&\n            this.headers['Accept'].includes('application/vnd.pgrst.plan+text')\n          ) {\n            data = body\n          } else {\n            data = JSON.parse(body)\n          }\n        }\n\n        const countHeader = this.headers['Prefer']?.match(/count=(exact|planned|estimated)/)\n        const contentRange = res.headers.get('content-range')?.split('/')\n        if (countHeader && contentRange && contentRange.length > 1) {\n          count = parseInt(contentRange[1])\n        }\n\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n          if (data.length > 1) {\n            error = {\n              // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n              code: 'PGRST116',\n              details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n              hint: null,\n              message: 'JSON object requested, multiple (or no) rows returned',\n            }\n            data = null\n            count = null\n            status = 406\n            statusText = 'Not Acceptable'\n          } else if (data.length === 1) {\n            data = data[0]\n          } else {\n            data = null\n          }\n        }\n      } else {\n        const body = await res.text()\n\n        try {\n          error = JSON.parse(body)\n\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (Array.isArray(error) && res.status === 404) {\n            data = []\n            error = null\n            status = 200\n            statusText = 'OK'\n          }\n        } catch {\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (res.status === 404 && body === '') {\n            status = 204\n            statusText = 'No Content'\n          } else {\n            error = {\n              message: body,\n            }\n          }\n        }\n\n        if (error && this.isMaybeSingle && error?.details?.includes('0 rows')) {\n          error = null\n          status = 200\n          statusText = 'OK'\n        }\n\n        if (error && this.shouldThrowOnError) {\n          throw new PostgrestError(error)\n        }\n      }\n\n      const postgrestResponse = {\n        error,\n        data,\n        count,\n        status,\n        statusText,\n      }\n\n      return postgrestResponse\n    })\n    if (!this.shouldThrowOnError) {\n      res = res.catch((fetchError) => ({\n        error: {\n          message: `${fetchError?.name ?? 'FetchError'}: ${fetchError?.message}`,\n          details: `${fetchError?.stack ?? ''}`,\n          hint: '',\n          code: `${fetchError?.code ?? ''}`,\n        },\n        data: null,\n        count: null,\n        status: 0,\n        statusText: '',\n      }))\n    }\n\n    return res.then(onfulfilled, onrejected)\n  }\n\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns<NewResult>(): PostgrestBuilder<CheckMatchingArrayTypes<Result, NewResult>, ThrowOnError> {\n    /* istanbul ignore next */\n    return this as unknown as PostgrestBuilder<\n      CheckMatchingArrayTypes<Result, NewResult>,\n      ThrowOnError\n    >\n  }\n\n  /**\n   * Override the type of the returned `data` field in the response.\n   *\n   * @typeParam NewResult - The new type to cast the response data to\n   * @typeParam Options - Optional type configuration (defaults to { merge: true })\n   * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n   * @example\n   * ```typescript\n   * // Merge with existing types (default behavior)\n   * const query = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ custom_field: string }>()\n   *\n   * // Replace existing types completely\n   * const replaceQuery = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n   * ```\n   * @returns A PostgrestBuilder instance with the new type\n   */\n  overrideTypes<\n    NewResult,\n    Options extends { merge?: boolean } = { merge: true }\n  >(): PostgrestBuilder<\n    IsValidResultOverride<Result, NewResult, false, false> extends true\n      ? // Preserve the optionality of the result if the overriden type is an object (case of chaining with `maybeSingle`)\n        ContainsNull<Result> extends true\n        ? MergePartialResult<NewResult, NonNullable<Result>, Options> | null\n        : MergePartialResult<NewResult, Result, Options>\n      : CheckMatchingArrayTypes<Result, NewResult>,\n    ThrowOnError\n  > {\n    return this as unknown as PostgrestBuilder<\n      IsValidResultOverride<Result, NewResult, false, false> extends true\n        ? // Preserve the optionality of the result if the overriden type is an object (case of chaining with `maybeSingle`)\n          ContainsNull<Result> extends true\n          ? MergePartialResult<NewResult, NonNullable<Result>, Options> | null\n          : MergePartialResult<NewResult, Result, Options>\n        : CheckMatchingArrayTypes<Result, NewResult>,\n      ThrowOnError\n    >\n  }\n}\n", "import PostgrestBuilder from './PostgrestBuilder'\nimport { GetResult } from './select-query-parser/result'\nimport { GenericSchema, CheckMatchingArrayTypes } from './types'\n\nexport default class PostgrestTransformBuilder<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  Result,\n  RelationName = unknown,\n  Relationships = unknown\n> extends PostgrestBuilder<Result> {\n  /**\n   * Perform a SELECT on the query result.\n   *\n   * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n   * return modified rows. By calling this method, modified rows are returned in\n   * `data`.\n   *\n   * @param columns - The columns to retrieve, separated by commas\n   */\n  select<\n    Query extends string = '*',\n    NewResultOne = GetResult<Schema, Row, RelationName, Relationships, Query>\n  >(\n    columns?: Query\n  ): PostgrestTransformBuilder<Schema, Row, NewResultOne[], RelationName, Relationships> {\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (this.headers['Prefer']) {\n      this.headers['Prefer'] += ','\n    }\n    this.headers['Prefer'] += 'return=representation'\n    return this as unknown as PostgrestTransformBuilder<\n      Schema,\n      Row,\n      NewResultOne[],\n      RelationName,\n      Relationships\n    >\n  }\n\n  order<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    options?: { ascending?: boolean; nullsFirst?: boolean; referencedTable?: undefined }\n  ): this\n  order(\n    column: string,\n    options?: { ascending?: boolean; nullsFirst?: boolean; referencedTable?: string }\n  ): this\n  /**\n   * @deprecated Use `options.referencedTable` instead of `options.foreignTable`\n   */\n  order<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: undefined }\n  ): this\n  /**\n   * @deprecated Use `options.referencedTable` instead of `options.foreignTable`\n   */\n  order(\n    column: string,\n    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: string }\n  ): this\n  /**\n   * Order the query result by `column`.\n   *\n   * You can call this method multiple times to order by multiple columns.\n   *\n   * You can order referenced tables, but it only affects the ordering of the\n   * parent table if you use `!inner` in the query.\n   *\n   * @param column - The column to order by\n   * @param options - Named parameters\n   * @param options.ascending - If `true`, the result will be in ascending order\n   * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n   * `null`s appear last.\n   * @param options.referencedTable - Set this to order a referenced table by\n   * its columns\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  order(\n    column: string,\n    {\n      ascending = true,\n      nullsFirst,\n      foreignTable,\n      referencedTable = foreignTable,\n    }: {\n      ascending?: boolean\n      nullsFirst?: boolean\n      foreignTable?: string\n      referencedTable?: string\n    } = {}\n  ): this {\n    const key = referencedTable ? `${referencedTable}.order` : 'order'\n    const existingOrder = this.url.searchParams.get(key)\n\n    this.url.searchParams.set(\n      key,\n      `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${\n        nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'\n      }`\n    )\n    return this\n  }\n\n  /**\n   * Limit the query result by `count`.\n   *\n   * @param count - The maximum number of rows to return\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  limit(\n    count: number,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`\n    this.url.searchParams.set(key, `${count}`)\n    return this\n  }\n\n  /**\n   * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n   * Only records within this range are returned.\n   * This respects the query order and if there is no order clause the range could behave unexpectedly.\n   * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n   * and fourth rows of the query.\n   *\n   * @param from - The starting index from which to limit the result\n   * @param to - The last index to which to limit the result\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  range(\n    from: number,\n    to: number,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const keyOffset =\n      typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`\n    const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`\n    this.url.searchParams.set(keyOffset, `${from}`)\n    // Range is inclusive, so add 1\n    this.url.searchParams.set(keyLimit, `${to - from + 1}`)\n    return this\n  }\n\n  /**\n   * Set the AbortSignal for the fetch request.\n   *\n   * @param signal - The AbortSignal to use for the fetch request\n   */\n  abortSignal(signal: AbortSignal): this {\n    this.signal = signal\n    return this\n  }\n\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n   * returns an error.\n   */\n  single<\n    ResultOne = Result extends (infer ResultOne)[] ? ResultOne : never\n  >(): PostgrestBuilder<ResultOne> {\n    this.headers['Accept'] = 'application/vnd.pgrst.object+json'\n    return this as unknown as PostgrestBuilder<ResultOne>\n  }\n\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n   * this returns an error.\n   */\n  maybeSingle<\n    ResultOne = Result extends (infer ResultOne)[] ? ResultOne : never\n  >(): PostgrestBuilder<ResultOne | null> {\n    // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n    // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n    if (this.method === 'GET') {\n      this.headers['Accept'] = 'application/json'\n    } else {\n      this.headers['Accept'] = 'application/vnd.pgrst.object+json'\n    }\n    this.isMaybeSingle = true\n    return this as unknown as PostgrestBuilder<ResultOne | null>\n  }\n\n  /**\n   * Return `data` as a string in CSV format.\n   */\n  csv(): PostgrestBuilder<string> {\n    this.headers['Accept'] = 'text/csv'\n    return this as unknown as PostgrestBuilder<string>\n  }\n\n  /**\n   * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n   */\n  geojson(): PostgrestBuilder<Record<string, unknown>> {\n    this.headers['Accept'] = 'application/geo+json'\n    return this as unknown as PostgrestBuilder<Record<string, unknown>>\n  }\n\n  /**\n   * Return `data` as the EXPLAIN plan for the query.\n   *\n   * You need to enable the\n   * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n   * setting before using this method.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.analyze - If `true`, the query will be executed and the\n   * actual run time will be returned\n   *\n   * @param options.verbose - If `true`, the query identifier will be returned\n   * and `data` will include the output columns of the query\n   *\n   * @param options.settings - If `true`, include information on configuration\n   * parameters that affect query planning\n   *\n   * @param options.buffers - If `true`, include information on buffer usage\n   *\n   * @param options.wal - If `true`, include information on WAL record generation\n   *\n   * @param options.format - The format of the output, can be `\"text\"` (default)\n   * or `\"json\"`\n   */\n  explain({\n    analyze = false,\n    verbose = false,\n    settings = false,\n    buffers = false,\n    wal = false,\n    format = 'text',\n  }: {\n    analyze?: boolean\n    verbose?: boolean\n    settings?: boolean\n    buffers?: boolean\n    wal?: boolean\n    format?: 'json' | 'text'\n  } = {}): PostgrestBuilder<Record<string, unknown>[]> | PostgrestBuilder<string> {\n    const options = [\n      analyze ? 'analyze' : null,\n      verbose ? 'verbose' : null,\n      settings ? 'settings' : null,\n      buffers ? 'buffers' : null,\n      wal ? 'wal' : null,\n    ]\n      .filter(Boolean)\n      .join('|')\n    // An Accept header can carry multiple media types but postgrest-js always sends one\n    const forMediatype = this.headers['Accept'] ?? 'application/json'\n    this.headers[\n      'Accept'\n    ] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`\n    if (format === 'json') return this as unknown as PostgrestBuilder<Record<string, unknown>[]>\n    else return this as unknown as PostgrestBuilder<string>\n  }\n\n  /**\n   * Rollback the query.\n   *\n   * `data` will still be returned, but the query is not committed.\n   */\n  rollback(): this {\n    if ((this.headers['Prefer'] ?? '').trim().length > 0) {\n      this.headers['Prefer'] += ',tx=rollback'\n    } else {\n      this.headers['Prefer'] = 'tx=rollback'\n    }\n    return this\n  }\n\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns<NewResult>(): PostgrestTransformBuilder<\n    Schema,\n    Row,\n    CheckMatchingArrayTypes<Result, NewResult>,\n    RelationName,\n    Relationships\n  > {\n    return this as unknown as PostgrestTransformBuilder<\n      Schema,\n      Row,\n      CheckMatchingArrayTypes<Result, NewResult>,\n      RelationName,\n      Relationships\n    >\n  }\n}\n", "import PostgrestTransformBuilder from './PostgrestTransformBuilder'\nimport { JsonPathToAccessor, JsonPathToType } from './select-query-parser/utils'\nimport { GenericSchema } from './types'\n\ntype FilterOperator =\n  | 'eq'\n  | 'neq'\n  | 'gt'\n  | 'gte'\n  | 'lt'\n  | 'lte'\n  | 'like'\n  | 'ilike'\n  | 'is'\n  | 'in'\n  | 'cs'\n  | 'cd'\n  | 'sl'\n  | 'sr'\n  | 'nxl'\n  | 'nxr'\n  | 'adj'\n  | 'ov'\n  | 'fts'\n  | 'plfts'\n  | 'phfts'\n  | 'wfts'\n\nexport type IsStringOperator<Path extends string> = Path extends `${string}->>${string}`\n  ? true\n  : false\n\n// Match relationship filters with `table.column` syntax and resolve underlying\n// column value. If not matched, fallback to generic type.\n// TODO: Validate the relationship itself ala select-query-parser. Currently we\n// assume that all tables have valid relationships to each other, despite\n// nonexistent foreign keys.\ntype ResolveFilterValue<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  ColumnName extends string\n> = ColumnName extends `${infer RelationshipTable}.${infer Remainder}`\n  ? Remainder extends `${infer _}.${infer _}`\n    ? ResolveFilterValue<Schema, Row, Remainder>\n    : ResolveFilterRelationshipValue<Schema, RelationshipTable, Remainder>\n  : ColumnName extends keyof Row\n  ? Row[ColumnName]\n  : // If the column selection is a jsonpath like `data->value` or `data->>value` we attempt to match\n  // the expected type with the parsed custom json type\n  IsStringOperator<ColumnName> extends true\n  ? string\n  : JsonPathToType<Row, JsonPathToAccessor<ColumnName>> extends infer JsonPathValue\n  ? JsonPathValue extends never\n    ? never\n    : JsonPathValue\n  : never\n\ntype ResolveFilterRelationshipValue<\n  Schema extends GenericSchema,\n  RelationshipTable extends string,\n  RelationshipColumn extends string\n> = Schema['Tables'] & Schema['Views'] extends infer TablesAndViews\n  ? RelationshipTable extends keyof TablesAndViews\n    ? 'Row' extends keyof TablesAndViews[RelationshipTable]\n      ? RelationshipColumn extends keyof TablesAndViews[RelationshipTable]['Row']\n        ? TablesAndViews[RelationshipTable]['Row'][RelationshipColumn]\n        : unknown\n      : unknown\n    : unknown\n  : never\n\nexport default class PostgrestFilterBuilder<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  Result,\n  RelationName = unknown,\n  Relationships = unknown\n> extends PostgrestTransformBuilder<Schema, Row, Result, RelationName, Relationships> {\n  /**\n   * Match only rows where `column` is equal to `value`.\n   *\n   * To check if the value of `column` is NULL, you should use `.is()` instead.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  eq<ColumnName extends string>(\n    column: ColumnName,\n    value: ResolveFilterValue<Schema, Row, ColumnName> extends never\n      ? NonNullable<unknown>\n      : // We want to infer the type before wrapping it into a `NonNullable` to avoid too deep\n      // type resolution error\n      ResolveFilterValue<Schema, Row, ColumnName> extends infer ResolvedFilterValue\n      ? NonNullable<ResolvedFilterValue>\n      : // We should never enter this case as all the branches are covered above\n        never\n  ): this {\n    this.url.searchParams.append(column, `eq.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows where `column` is not equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  neq<ColumnName extends string>(\n    column: ColumnName,\n    value: ResolveFilterValue<Schema, Row, ColumnName> extends never\n      ? unknown\n      : ResolveFilterValue<Schema, Row, ColumnName> extends infer ResolvedFilterValue\n      ? ResolvedFilterValue\n      : never\n  ): this {\n    this.url.searchParams.append(column, `neq.${value}`)\n    return this\n  }\n\n  gt<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  gt(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is greater than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gt(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `gt.${value}`)\n    return this\n  }\n\n  gte<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  gte(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is greater than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gte(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `gte.${value}`)\n    return this\n  }\n\n  lt<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  lt(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is less than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lt(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `lt.${value}`)\n    return this\n  }\n\n  lte<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  lte(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is less than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lte(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `lte.${value}`)\n    return this\n  }\n\n  like<ColumnName extends string & keyof Row>(column: ColumnName, pattern: string): this\n  like(column: string, pattern: string): this\n  /**\n   * Match only rows where `column` matches `pattern` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  like(column: string, pattern: string): this {\n    this.url.searchParams.append(column, `like.${pattern}`)\n    return this\n  }\n\n  likeAllOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  likeAllOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches all of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAllOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`)\n    return this\n  }\n\n  likeAnyOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  likeAnyOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches any of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAnyOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`)\n    return this\n  }\n\n  ilike<ColumnName extends string & keyof Row>(column: ColumnName, pattern: string): this\n  ilike(column: string, pattern: string): this\n  /**\n   * Match only rows where `column` matches `pattern` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  ilike(column: string, pattern: string): this {\n    this.url.searchParams.append(column, `ilike.${pattern}`)\n    return this\n  }\n\n  ilikeAllOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  ilikeAllOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches all of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAllOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`)\n    return this\n  }\n\n  ilikeAnyOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  ilikeAnyOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches any of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAnyOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`)\n    return this\n  }\n\n  is<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: Row[ColumnName] & (boolean | null)\n  ): this\n  is(column: string, value: boolean | null): this\n  /**\n   * Match only rows where `column` IS `value`.\n   *\n   * For non-boolean columns, this is only relevant for checking if the value of\n   * `column` is NULL by setting `value` to `null`.\n   *\n   * For boolean columns, you can also set `value` to `true` or `false` and it\n   * will behave the same way as `.eq()`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  is(column: string, value: boolean | null): this {\n    this.url.searchParams.append(column, `is.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows where `column` is included in the `values` array.\n   *\n   * @param column - The column to filter on\n   * @param values - The values array to filter with\n   */\n  in<ColumnName extends string>(\n    column: ColumnName,\n    values: ReadonlyArray<\n      ResolveFilterValue<Schema, Row, ColumnName> extends never\n        ? unknown\n        : // We want to infer the type before wrapping it into a `NonNullable` to avoid too deep\n        // type resolution error\n        ResolveFilterValue<Schema, Row, ColumnName> extends infer ResolvedFilterValue\n        ? ResolvedFilterValue\n        : // We should never enter this case as all the branches are covered above\n          never\n    >\n  ): this {\n    const cleanedValues = Array.from(new Set(values))\n      .map((s) => {\n        // handle postgrest reserved characters\n        // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n        if (typeof s === 'string' && new RegExp('[,()]').test(s)) return `\"${s}\"`\n        else return `${s}`\n      })\n      .join(',')\n    this.url.searchParams.append(column, `in.(${cleanedValues})`)\n    return this\n  }\n\n  contains<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]> | Record<string, unknown>\n  ): this\n  contains(column: string, value: string | readonly unknown[] | Record<string, unknown>): this\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * `column` contains every element appearing in `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  contains(column: string, value: string | readonly unknown[] | Record<string, unknown>): this {\n    if (typeof value === 'string') {\n      // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n      // keep it simple and accept a string\n      this.url.searchParams.append(column, `cs.${value}`)\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cs.{${value.join(',')}}`)\n    } else {\n      // json\n      this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`)\n    }\n    return this\n  }\n\n  containedBy<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]> | Record<string, unknown>\n  ): this\n  containedBy(column: string, value: string | readonly unknown[] | Record<string, unknown>): this\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * every element appearing in `column` is contained by `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  containedBy(column: string, value: string | readonly unknown[] | Record<string, unknown>): this {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `cd.${value}`)\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cd.{${value.join(',')}}`)\n    } else {\n      // json\n      this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`)\n    }\n    return this\n  }\n\n  rangeGt<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeGt(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is greater than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGt(column: string, range: string): this {\n    this.url.searchParams.append(column, `sr.${range}`)\n    return this\n  }\n\n  rangeGte<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeGte(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or greater than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGte(column: string, range: string): this {\n    this.url.searchParams.append(column, `nxl.${range}`)\n    return this\n  }\n\n  rangeLt<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeLt(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is less than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLt(column: string, range: string): this {\n    this.url.searchParams.append(column, `sl.${range}`)\n    return this\n  }\n\n  rangeLte<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeLte(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or less than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLte(column: string, range: string): this {\n    this.url.searchParams.append(column, `nxr.${range}`)\n    return this\n  }\n\n  rangeAdjacent<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeAdjacent(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where `column` is\n   * mutually exclusive to `range` and there can be no element between the two\n   * ranges.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeAdjacent(column: string, range: string): this {\n    this.url.searchParams.append(column, `adj.${range}`)\n    return this\n  }\n\n  overlaps<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]>\n  ): this\n  overlaps(column: string, value: string | readonly unknown[]): this\n  /**\n   * Only relevant for array and range columns. Match only rows where\n   * `column` and `value` have an element in common.\n   *\n   * @param column - The array or range column to filter on\n   * @param value - The array or range value to filter with\n   */\n  overlaps(column: string, value: string | readonly unknown[]): this {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `ov.${value}`)\n    } else {\n      // array\n      this.url.searchParams.append(column, `ov.{${value.join(',')}}`)\n    }\n    return this\n  }\n\n  textSearch<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    query: string,\n    options?: { config?: string; type?: 'plain' | 'phrase' | 'websearch' }\n  ): this\n  textSearch(\n    column: string,\n    query: string,\n    options?: { config?: string; type?: 'plain' | 'phrase' | 'websearch' }\n  ): this\n  /**\n   * Only relevant for text and tsvector columns. Match only rows where\n   * `column` matches the query string in `query`.\n   *\n   * @param column - The text or tsvector column to filter on\n   * @param query - The query text to match with\n   * @param options - Named parameters\n   * @param options.config - The text search configuration to use\n   * @param options.type - Change how the `query` text is interpreted\n   */\n  textSearch(\n    column: string,\n    query: string,\n    { config, type }: { config?: string; type?: 'plain' | 'phrase' | 'websearch' } = {}\n  ): this {\n    let typePart = ''\n    if (type === 'plain') {\n      typePart = 'pl'\n    } else if (type === 'phrase') {\n      typePart = 'ph'\n    } else if (type === 'websearch') {\n      typePart = 'w'\n    }\n    const configPart = config === undefined ? '' : `(${config})`\n    this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`)\n    return this\n  }\n\n  match<ColumnName extends string & keyof Row>(query: Record<ColumnName, Row[ColumnName]>): this\n  match(query: Record<string, unknown>): this\n  /**\n   * Match only rows where each column in `query` keys is equal to its\n   * associated value. Shorthand for multiple `.eq()`s.\n   *\n   * @param query - The object to filter with, with column names as keys mapped\n   * to their filter values\n   */\n  match(query: Record<string, unknown>): this {\n    Object.entries(query).forEach(([column, value]) => {\n      this.url.searchParams.append(column, `eq.${value}`)\n    })\n    return this\n  }\n\n  not<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    operator: FilterOperator,\n    value: Row[ColumnName]\n  ): this\n  not(column: string, operator: string, value: unknown): this\n  /**\n   * Match only rows which doesn't satisfy the filter.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to be negated to filter with, following\n   * PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  not(column: string, operator: string, value: unknown): this {\n    this.url.searchParams.append(column, `not.${operator}.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows which satisfy at least one of the filters.\n   *\n   * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure it's properly sanitized.\n   *\n   * It's currently not possible to do an `.or()` filter across multiple tables.\n   *\n   * @param filters - The filters to use, following PostgREST syntax\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to filter on referenced tables\n   * instead of the parent table\n   * @param options.foreignTable - Deprecated, use `referencedTable` instead\n   */\n  or(\n    filters: string,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const key = referencedTable ? `${referencedTable}.or` : 'or'\n    this.url.searchParams.append(key, `(${filters})`)\n    return this\n  }\n\n  filter<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    operator: `${'' | 'not.'}${FilterOperator}`,\n    value: unknown\n  ): this\n  filter(column: string, operator: string, value: unknown): this\n  /**\n   * Match only rows which satisfy the filter. This is an escape hatch - you\n   * should use the specific filter methods wherever possible.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to filter with, following PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  filter(column: string, operator: string, value: unknown): this {\n    this.url.searchParams.append(column, `${operator}.${value}`)\n    return this\n  }\n}\n", "import PostgrestBuilder from './PostgrestBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport { GetResult } from './select-query-parser/result'\nimport { Fetch, GenericSchema, GenericTable, GenericView } from './types'\n\nexport default class PostgrestQueryBuilder<\n  Schema extends GenericSchema,\n  Relation extends GenericTable | GenericView,\n  RelationName = unknown,\n  Relationships = Relation extends { Relationships: infer R } ? R : unknown\n> {\n  url: URL\n  headers: Record<string, string>\n  schema?: string\n  signal?: AbortSignal\n  fetch?: Fetch\n\n  constructor(\n    url: URL,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: string\n      fetch?: Fetch\n    }\n  ) {\n    this.url = url\n    this.headers = headers\n    this.schema = schema\n    this.fetch = fetch\n  }\n\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select<\n    Query extends string = '*',\n    ResultOne = GetResult<Schema, Relation['Row'], RelationName, Relationships, Query>\n  >(\n    columns?: Query,\n    {\n      head = false,\n      count,\n    }: {\n      head?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], ResultOne[], RelationName, Relationships> {\n    const method = head ? 'HEAD' : 'GET'\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<ResultOne[]>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk inserts.\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      count,\n      defaultToNull = true,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk upserts.\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      onConflict,\n      ignoreDuplicates = false,\n      count,\n      defaultToNull = true,\n    }: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`]\n\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict)\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update<Row extends Relation extends { Update: unknown } ? Relation['Update'] : never>(\n    values: Row,\n    {\n      count,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'PATCH'\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete({\n    count,\n  }: {\n    count?: 'exact' | 'planned' | 'estimated'\n  } = {}): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'DELETE'\n    const prefersHeaders = []\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer'])\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n}\n", "export const version = '1.19.4'\n", "import { version } from './version'\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version}` }\n", "import PostgrestQueryBuilder from './PostgrestQueryBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport PostgrestBuilder from './PostgrestBuilder'\nimport { DEFAULT_HEADERS } from './constants'\nimport { Fetch, GenericSchema } from './types'\n\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nexport default class PostgrestClient<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n> {\n  url: string\n  headers: Record<string, string>\n  schemaName?: SchemaName\n  fetch?: Fetch\n\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(\n    url: string,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: SchemaName\n      fetch?: Fetch\n    } = {}\n  ) {\n    this.url = url\n    this.headers = { ...DEFAULT_HEADERS, ...headers }\n    this.schemaName = schema\n    this.fetch = fetch\n  }\n\n  from<\n    TableName extends string & keyof Schema['Tables'],\n    Table extends Schema['Tables'][TableName]\n  >(relation: TableName): PostgrestQueryBuilder<Schema, Table, TableName>\n  from<ViewName extends string & keyof Schema['Views'], View extends Schema['Views'][ViewName]>(\n    relation: ViewName\n  ): PostgrestQueryBuilder<Schema, View, ViewName>\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation: string): PostgrestQueryBuilder<Schema, any, any> {\n    const url = new URL(`${this.url}/${relation}`)\n    return new PostgrestQueryBuilder(url, {\n      headers: { ...this.headers },\n      schema: this.schemaName,\n      fetch: this.fetch,\n    })\n  }\n\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema<DynamicSchema extends string & keyof Database>(\n    schema: DynamicSchema\n  ): PostgrestClient<\n    Database,\n    DynamicSchema,\n    Database[DynamicSchema] extends GenericSchema ? Database[DynamicSchema] : any\n  > {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch,\n    })\n  }\n\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc<FnName extends string & keyof Schema['Functions'], Fn extends Schema['Functions'][FnName]>(\n    fn: FnName,\n    args: Fn['Args'] = {},\n    {\n      head = false,\n      get = false,\n      count,\n    }: {\n      head?: boolean\n      get?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<\n    Schema,\n    Fn['Returns'] extends any[]\n      ? Fn['Returns'][number] extends Record<string, unknown>\n        ? Fn['Returns'][number]\n        : never\n      : never,\n    Fn['Returns'],\n    FnName,\n    null\n  > {\n    let method: 'HEAD' | 'GET' | 'POST'\n    const url = new URL(`${this.url}/rpc/${fn}`)\n    let body: unknown | undefined\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET'\n      Object.entries(args)\n        // params with undefined value needs to be filtered out, otherwise it'll\n        // show up as `?param=undefined`\n        .filter(([_, value]) => value !== undefined)\n        // array values need special syntax\n        .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n        .forEach(([name, value]) => {\n          url.searchParams.append(name, value)\n        })\n    } else {\n      method = 'POST'\n      body = args\n    }\n\n    const headers = { ...this.headers }\n    if (count) {\n      headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<Fn['Returns']>)\n  }\n}\n", "// Always update wrapper.mjs when updating this file.\nimport PostgrestClient from './PostgrestClient'\nimport PostgrestQueryBuilder from './PostgrestQueryBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport PostgrestTransformBuilder from './PostgrestTransformBuilder'\nimport PostgrestBuilder from './PostgrestBuilder'\nimport PostgrestError from './PostgrestError'\n\nexport {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\nexport type {\n  PostgrestResponse,\n  PostgrestResponseFailure,\n  PostgrestResponseSuccess,\n  PostgrestSingleResponse,\n  PostgrestMaybeSingleResponse,\n} from './types'\n// https://github.com/supabase/postgrest-js/issues/551\n// To be replaced with a helper type that only uses public types\nexport type { GetResult as UnstableGetResult } from './select-query-parser/result'\n", "/**\r\n * Salesforce Sync System\r\n * Handles synchronization of data from Supabase to Salesforce\r\n */\r\nimport {\r\n  SALESFORCE_CONFIG,\r\n  salesforceAPI,\r\n  validateSalesforceConfig,\r\n} from '../config/salesforce.js';\r\nimport { supabase, TABLE_NAME } from '../config/supabase.js';\r\n\r\nexport class SalesforceSyncSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.debugMode = false;\r\n    this.syncQueue = [];\r\n    this.isProcessing = false;\r\n    this.retryAttempts = 3;\r\n    this.retryDelay = 5000; // 5 seconds\r\n  }\r\n\r\n  async init() {\r\n    if (this.isInitialized) return;\r\n\r\n    try {\r\n      // Enable debug mode if in development\r\n      this.debugMode =\r\n        window.location.hostname === 'localhost' ||\r\n        window.location.hostname === '127.0.0.1' ||\r\n        window.location.search.includes('debug=true');\r\n\r\n      // Validate Salesforce configuration\r\n      const configValidation = validateSalesforceConfig();\r\n      if (!configValidation.isValid) {\r\n        console.warn('⚠️ Salesforce integration disabled:', configValidation.message);\r\n        return;\r\n      }\r\n\r\n      // Initialize Salesforce API\r\n      const initialized = await salesforceAPI.init();\r\n      if (!initialized) {\r\n        console.error('❌ Failed to initialize Salesforce API');\r\n        return;\r\n      }\r\n\r\n      // Setup Supabase listener for new submissions\r\n      this.setupSupabaseListener();\r\n\r\n      // Process any existing queue\r\n      this.processQueue();\r\n\r\n      this.isInitialized = true;\r\n\r\n      if (this.debugMode) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ Salesforce Sync System initialized');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Salesforce Sync System init failed:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup real-time listener for new Supabase submissions\r\n   */\r\n  setupSupabaseListener() {\r\n    // Listen for INSERT events on calculator_submissions table\r\n    const subscription = supabase\r\n      .channel('calculator_submissions_sync')\r\n      .on(\r\n        'postgres_changes',\r\n        {\r\n          event: 'INSERT',\r\n          schema: 'public',\r\n          table: TABLE_NAME,\r\n        },\r\n        (payload) => {\r\n          if (this.debugMode) {\r\n            // eslint-disable-next-line no-console\r\n            console.log('📩 New submission detected:', payload.new);\r\n          }\r\n          this.queueForSync(payload.new);\r\n        }\r\n      )\r\n      .subscribe();\r\n\r\n    if (this.debugMode) {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🔄 Supabase real-time listener setup for table:', TABLE_NAME);\r\n    }\r\n\r\n    // Store subscription for cleanup\r\n    this.subscription = subscription;\r\n  }\r\n\r\n  /**\r\n   * Add submission to sync queue\r\n   * @param {Object} submission - Submission data from Supabase\r\n   */\r\n  queueForSync(submission) {\r\n    const syncItem = {\r\n      id: submission.id,\r\n      data: submission,\r\n      attempts: 0,\r\n      timestamp: new Date().toISOString(),\r\n      status: 'pending',\r\n    };\r\n\r\n    this.syncQueue.push(syncItem);\r\n\r\n    if (this.debugMode) {\r\n      // eslint-disable-next-line no-console\r\n      console.log('📝 Queued for Salesforce sync:', syncItem.id);\r\n    }\r\n\r\n    // Process queue if not already processing\r\n    if (!this.isProcessing) {\r\n      this.processQueue();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process the sync queue\r\n   */\r\n  async processQueue() {\r\n    if (this.isProcessing || this.syncQueue.length === 0) return;\r\n\r\n    this.isProcessing = true;\r\n\r\n    try {\r\n      while (this.syncQueue.length > 0) {\r\n        const item = this.syncQueue[0]; // Get first item\r\n        const success = await this.syncToSalesforce(item);\r\n\r\n        if (success) {\r\n          // Remove successful item from queue\r\n          this.syncQueue.shift();\r\n          if (this.debugMode) {\r\n            // eslint-disable-next-line no-console\r\n            console.log('✅ Successfully synced to Salesforce:', item.id);\r\n          }\r\n        } else {\r\n          // Handle retry logic\r\n          item.attempts += 1;\r\n          if (item.attempts >= this.retryAttempts) {\r\n            // Max retries reached, remove from queue and log error\r\n            this.syncQueue.shift();\r\n            console.error('❌ Max retries reached for sync:', item.id);\r\n\r\n            // Optionally save failed sync for manual review\r\n            this.logFailedSync(item);\r\n          } else {\r\n            // Wait before retry\r\n            if (this.debugMode) {\r\n              // eslint-disable-next-line no-console\r\n              console.log(\r\n                `🔄 Retrying sync for ${item.id} (attempt ${item.attempts}/${this.retryAttempts})`\r\n              );\r\n            }\r\n            await this.delay(this.retryDelay);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error processing sync queue:', error);\r\n    } finally {\r\n      this.isProcessing = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sync single submission to Salesforce\r\n   * @param {Object} syncItem - Sync item with submission data\r\n   * @returns {Promise<boolean>} Success status\r\n   */\r\n  async syncToSalesforce(syncItem) {\r\n    try {\r\n      const { data: submission } = syncItem;\r\n\r\n      // Transform Supabase data to Salesforce format\r\n      const salesforceData = this.transformDataForSalesforce(submission);\r\n\r\n      // Create record in Salesforce\r\n      const result = await salesforceAPI.createRecord(\r\n        SALESFORCE_CONFIG.OBJECTS.CALCULATOR_SUBMISSION,\r\n        salesforceData\r\n      );\r\n\r\n      if (result.success !== false) {\r\n        // Update Supabase record with Salesforce ID\r\n        await this.updateSupabaseWithSalesforceId(submission.id, result.id);\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    } catch (error) {\r\n      console.error('❌ Error syncing to Salesforce:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Transform Supabase data to Salesforce field format\r\n   * @param {Object} submission - Supabase submission data\r\n   * @returns {Object} Salesforce formatted data\r\n   */\r\n  transformDataForSalesforce(submission) {\r\n    // Map Supabase fields to Salesforce custom object fields\r\n    const salesforceData = {\r\n      Name: `Calculadora - ${submission.id}`, // Record name\r\n      Supabase_ID__c: submission.id,\r\n      Patrimonio__c: submission.patrimonio,\r\n      Ativos_Escolhidos__c: JSON.stringify(submission.ativos_escolhidos),\r\n      Alocacao__c: JSON.stringify(submission.alocacao),\r\n      Total_Alocado__c: submission.total_alocado,\r\n      Percentual_Alocado__c: submission.percentual_alocado,\r\n      Patrimonio_Restante__c: submission.patrimonio_restante,\r\n      Data_Submissao__c: submission.submitted_at,\r\n      User_Agent__c: submission.user_agent,\r\n      Session_ID__c: submission.session_id,\r\n      Created_Date__c: submission.created_at,\r\n    };\r\n\r\n    // Add computed fields\r\n    salesforceData.Status__c = 'Novo';\r\n    salesforceData.Source__c = 'Website Calculator';\r\n\r\n    // Extract summary information for easy viewing in Salesforce\r\n    if (submission.ativos_escolhidos && Array.isArray(submission.ativos_escolhidos)) {\r\n      const categories = submission.ativos_escolhidos.map((item) => item.category).filter(Boolean);\r\n      const uniqueCategories = [...new Set(categories)];\r\n      salesforceData.Categorias_Selecionadas__c = uniqueCategories.join(', ');\r\n      salesforceData.Total_Ativos_Selecionados__c = submission.ativos_escolhidos.length;\r\n    }\r\n\r\n    // Calculate allocation summary\r\n    if (submission.alocacao && typeof submission.alocacao === 'object') {\r\n      const allocations = Object.values(submission.alocacao);\r\n      const totalItems = allocations.length;\r\n      const itemsWithValue = allocations.filter((item) => item.value > 0).length;\r\n\r\n      salesforceData.Total_Itens_Alocacao__c = totalItems;\r\n      salesforceData.Itens_Com_Valor__c = itemsWithValue;\r\n      salesforceData.Taxa_Preenchimento__c =\r\n        totalItems > 0 ? (itemsWithValue / totalItems) * 100 : 0;\r\n    }\r\n\r\n    return salesforceData;\r\n  }\r\n\r\n  /**\r\n   * Update Supabase record with Salesforce ID\r\n   * @param {string} supabaseId - Supabase record ID\r\n   * @param {string} salesforceId - Salesforce record ID\r\n   */\r\n  async updateSupabaseWithSalesforceId(supabaseId, salesforceId) {\r\n    try {\r\n      const { error } = await supabase\r\n        .from(TABLE_NAME)\r\n        .update({\r\n          salesforce_id: salesforceId,\r\n          synced_at: new Date().toISOString(),\r\n          sync_status: 'synced',\r\n        })\r\n        .eq('id', supabaseId);\r\n\r\n      if (error) {\r\n        console.error('❌ Error updating Supabase with Salesforce ID:', error);\r\n      } else if (this.debugMode) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ Updated Supabase record with Salesforce ID:', salesforceId);\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error updating Supabase record:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log failed sync for manual review\r\n   * @param {Object} syncItem - Failed sync item\r\n   */\r\n  async logFailedSync(syncItem) {\r\n    try {\r\n      // Update Supabase record to mark as failed\r\n      await supabase\r\n        .from(TABLE_NAME)\r\n        .update({\r\n          sync_status: 'failed',\r\n          sync_error: `Failed after ${syncItem.attempts} attempts`,\r\n          last_sync_attempt: new Date().toISOString(),\r\n        })\r\n        .eq('id', syncItem.data.id);\r\n\r\n      console.error('❌ Logged failed sync for manual review:', syncItem.id);\r\n    } catch (error) {\r\n      console.error('❌ Error logging failed sync:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Manual sync of specific submission\r\n   * @param {string} submissionId - Supabase submission ID\r\n   * @returns {Promise<boolean>} Success status\r\n   */\r\n  async manualSync(submissionId) {\r\n    try {\r\n      // Get submission from Supabase\r\n      const { data: submission, error } = await supabase\r\n        .from(TABLE_NAME)\r\n        .select('*')\r\n        .eq('id', submissionId)\r\n        .single();\r\n\r\n      if (error || !submission) {\r\n        console.error('❌ Submission not found:', submissionId);\r\n        return false;\r\n      }\r\n\r\n      // Create sync item and process\r\n      const syncItem = {\r\n        id: submission.id,\r\n        data: submission,\r\n        attempts: 0,\r\n        timestamp: new Date().toISOString(),\r\n        status: 'manual',\r\n      };\r\n\r\n      const success = await this.syncToSalesforce(syncItem);\r\n\r\n      if (success) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ Manual sync successful:', submissionId);\r\n      } else {\r\n        console.error('❌ Manual sync failed:', submissionId);\r\n      }\r\n\r\n      return success;\r\n    } catch (error) {\r\n      console.error('❌ Error in manual sync:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get sync status for all submissions\r\n   * @returns {Promise<Object>} Sync status summary\r\n   */\r\n  async getSyncStatus() {\r\n    try {\r\n      const { data: submissions, error } = await supabase\r\n        .from(TABLE_NAME)\r\n        .select('id, sync_status, salesforce_id, synced_at, sync_error')\r\n        .order('created_at', { ascending: false })\r\n        .limit(100);\r\n\r\n      if (error) {\r\n        console.error('❌ Error getting sync status:', error);\r\n        return null;\r\n      }\r\n\r\n      const summary = {\r\n        total: submissions.length,\r\n        synced: submissions.filter((s) => s.sync_status === 'synced').length,\r\n        pending: submissions.filter((s) => !s.sync_status || s.sync_status === 'pending').length,\r\n        failed: submissions.filter((s) => s.sync_status === 'failed').length,\r\n        queueLength: this.syncQueue.length,\r\n        isProcessing: this.isProcessing,\r\n        submissions,\r\n      };\r\n\r\n      return summary;\r\n    } catch (error) {\r\n      console.error('❌ Error getting sync status:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retry all failed syncs\r\n   * @returns {Promise<number>} Number of retries queued\r\n   */\r\n  async retryFailedSyncs() {\r\n    try {\r\n      const { data: failedSubmissions, error } = await supabase\r\n        .from(TABLE_NAME)\r\n        .select('*')\r\n        .eq('sync_status', 'failed');\r\n\r\n      if (error) {\r\n        console.error('❌ Error getting failed submissions:', error);\r\n        return 0;\r\n      }\r\n\r\n      let retryCount = 0;\r\n      for (const submission of failedSubmissions) {\r\n        // Reset sync status\r\n        await supabase.from(TABLE_NAME).update({ sync_status: 'pending' }).eq('id', submission.id);\r\n\r\n        // Add to queue\r\n        this.queueForSync(submission);\r\n        retryCount += 1;\r\n      }\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log(`🔄 Queued ${retryCount} failed submissions for retry`);\r\n      return retryCount;\r\n    } catch (error) {\r\n      console.error('❌ Error retrying failed syncs:', error);\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Utility function for delays\r\n   * @param {number} ms - Milliseconds to delay\r\n   * @returns {Promise} Promise that resolves after delay\r\n   */\r\n  delay(ms) {\r\n    return new Promise((resolve) => setTimeout(resolve, ms));\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  destroy() {\r\n    if (this.subscription) {\r\n      this.subscription.unsubscribe();\r\n    }\r\n    this.syncQueue = [];\r\n    this.isProcessing = false;\r\n    this.isInitialized = false;\r\n  }\r\n\r\n  /**\r\n   * Get system status for debugging\r\n   * @returns {Object} System status\r\n   */\r\n  getStatus() {\r\n    return {\r\n      isInitialized: this.isInitialized,\r\n      debugMode: this.debugMode,\r\n      queueLength: this.syncQueue.length,\r\n      isProcessing: this.isProcessing,\r\n      retryAttempts: this.retryAttempts,\r\n      retryDelay: this.retryDelay,\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * Salesforce Integration Configuration\r\n * Handles authentication and API connection with Salesforce\r\n */\r\n\r\n// Salesforce Configuration\r\nconst SALESFORCE_CONFIG = {\r\n  // Sandbox or Production\r\n  INSTANCE_URL: 'https://your-org.my.salesforce.com',\r\n  CLIENT_ID: 'your_connected_app_client_id',\r\n  CLIENT_SECRET: 'your_connected_app_client_secret',\r\n  USERNAME: '<EMAIL>',\r\n  PASSWORD: 'your_password',\r\n  SECURITY_TOKEN: 'your_security_token',\r\n\r\n  // API Configuration\r\n  API_VERSION: '58.0',\r\n  TIMEOUT: 30000, // 30 seconds\r\n\r\n  // Object Mapping\r\n  OBJECTS: {\r\n    LEAD: 'Lead',\r\n    OPPORTUNITY: 'Opportunity',\r\n    ACCOUNT: 'Account',\r\n    CONTACT: 'Contact',\r\n    CALCULATOR_SUBMISSION: 'Calculator_Submission__c', // Custom object\r\n  },\r\n};\r\n\r\n/**\r\n * Salesforce Authentication Client\r\n */\r\nclass SalesforceAuth {\r\n  constructor() {\r\n    this.accessToken = null;\r\n    this.instanceUrl = null;\r\n    this.tokenExpiry = null;\r\n  }\r\n\r\n  /**\r\n   * Authenticate with Salesforce using OAuth 2.0 Username-Password flow\r\n   * @returns {Promise<Object>} Authentication response\r\n   */\r\n  async authenticate() {\r\n    try {\r\n      const authUrl = `${SALESFORCE_CONFIG.INSTANCE_URL}/services/oauth2/token`;\r\n\r\n      const params = new window.URLSearchParams({\r\n        grant_type: 'password',\r\n        client_id: SALESFORCE_CONFIG.CLIENT_ID,\r\n        client_secret: SALESFORCE_CONFIG.CLIENT_SECRET,\r\n        username: SALESFORCE_CONFIG.USERNAME,\r\n        password: SALESFORCE_CONFIG.PASSWORD + SALESFORCE_CONFIG.SECURITY_TOKEN,\r\n      });\r\n\r\n      const response = await window.fetch(authUrl, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/x-www-form-urlencoded',\r\n        },\r\n        body: params.toString(),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.text();\r\n        throw new Error(`Salesforce auth failed: ${response.status} - ${errorData}`);\r\n      }\r\n\r\n      const authData = await response.json();\r\n\r\n      this.accessToken = authData.access_token;\r\n      this.instanceUrl = authData.instance_url;\r\n      this.tokenExpiry = Date.now() + 3600 * 1000; // Token expires in 1 hour\r\n\r\n      return {\r\n        success: true,\r\n        accessToken: this.accessToken,\r\n        instanceUrl: this.instanceUrl,\r\n        tokenType: authData.token_type,\r\n        signature: authData.signature,\r\n        issued_at: authData.issued_at,\r\n      };\r\n    } catch (error) {\r\n      console.error('Salesforce authentication error:', error);\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if current token is valid\r\n   * @returns {boolean} True if token is valid\r\n   */\r\n  isTokenValid() {\r\n    return this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry;\r\n  }\r\n\r\n  /**\r\n   * Get valid access token (refresh if needed)\r\n   * @returns {Promise<string|null>} Access token or null if failed\r\n   */\r\n  async getValidToken() {\r\n    if (this.isTokenValid()) {\r\n      return this.accessToken;\r\n    }\r\n\r\n    const authResult = await this.authenticate();\r\n    return authResult.success ? this.accessToken : null;\r\n  }\r\n\r\n  /**\r\n   * Get authorization headers for API calls\r\n   * @returns {Promise<Object>} Headers object\r\n   */\r\n  async getAuthHeaders() {\r\n    const token = await this.getValidToken();\r\n    if (!token) {\r\n      throw new Error('Failed to get valid Salesforce token');\r\n    }\r\n\r\n    return {\r\n      Authorization: `Bearer ${token}`,\r\n      'Content-Type': 'application/json',\r\n      Accept: 'application/json',\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Salesforce API Client\r\n */\r\nclass SalesforceAPI {\r\n  constructor() {\r\n    this.auth = new SalesforceAuth();\r\n    this.baseUrl = null;\r\n  }\r\n\r\n  /**\r\n   * Initialize the API client\r\n   * @returns {Promise<boolean>} True if initialized successfully\r\n   */\r\n  async init() {\r\n    try {\r\n      const authResult = await this.auth.authenticate();\r\n      if (authResult.success) {\r\n        this.baseUrl = `${this.auth.instanceUrl}/services/data/v${SALESFORCE_CONFIG.API_VERSION}`;\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Salesforce API init error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Make authenticated API request\r\n   * @param {string} endpoint - API endpoint\r\n   * @param {Object} options - Request options\r\n   * @returns {Promise<Object>} Response data\r\n   */\r\n  async request(endpoint, options = {}) {\r\n    try {\r\n      const headers = await this.auth.getAuthHeaders();\r\n\r\n      const response = await window.fetch(`${this.baseUrl}${endpoint}`, {\r\n        ...options,\r\n        headers: {\r\n          ...headers,\r\n          ...options.headers,\r\n        },\r\n        timeout: SALESFORCE_CONFIG.TIMEOUT,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.text();\r\n        throw new Error(`Salesforce API error: ${response.status} - ${errorData}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Salesforce API request error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new record\r\n   * @param {string} objectType - Salesforce object type\r\n   * @param {Object} data - Record data\r\n   * @returns {Promise<Object>} Created record response\r\n   */\r\n  async createRecord(objectType, data) {\r\n    return await this.request(`/sobjects/${objectType}`, {\r\n      method: 'POST',\r\n      body: JSON.stringify(data),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update an existing record\r\n   * @param {string} objectType - Salesforce object type\r\n   * @param {string} recordId - Record ID\r\n   * @param {Object} data - Update data\r\n   * @returns {Promise<Object>} Update response\r\n   */\r\n  async updateRecord(objectType, recordId, data) {\r\n    return await this.request(`/sobjects/${objectType}/${recordId}`, {\r\n      method: 'PATCH',\r\n      body: JSON.stringify(data),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Query records using SOQL\r\n   * @param {string} query - SOQL query\r\n   * @returns {Promise<Object>} Query results\r\n   */\r\n  async query(query) {\r\n    const encodedQuery = encodeURIComponent(query);\r\n    return await this.request(`/query?q=${encodedQuery}`);\r\n  }\r\n\r\n  /**\r\n   * Get record by ID\r\n   * @param {string} objectType - Salesforce object type\r\n   * @param {string} recordId - Record ID\r\n   * @param {string[]} fields - Fields to retrieve\r\n   * @returns {Promise<Object>} Record data\r\n   */\r\n  async getRecord(objectType, recordId, fields = []) {\r\n    const fieldsParam = fields.length > 0 ? `?fields=${fields.join(',')}` : '';\r\n    return await this.request(`/sobjects/${objectType}/${recordId}${fieldsParam}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Validate Salesforce configuration\r\n * @returns {Object} Validation result\r\n */\r\nexport function validateSalesforceConfig() {\r\n  const missing = [];\r\n\r\n  if (!SALESFORCE_CONFIG.INSTANCE_URL || SALESFORCE_CONFIG.INSTANCE_URL.includes('your-org')) {\r\n    missing.push('SALESFORCE_INSTANCE_URL');\r\n  }\r\n  if (!SALESFORCE_CONFIG.CLIENT_ID || SALESFORCE_CONFIG.CLIENT_ID.includes('your_')) {\r\n    missing.push('SALESFORCE_CLIENT_ID');\r\n  }\r\n  if (!SALESFORCE_CONFIG.CLIENT_SECRET || SALESFORCE_CONFIG.CLIENT_SECRET.includes('your_')) {\r\n    missing.push('SALESFORCE_CLIENT_SECRET');\r\n  }\r\n  if (!SALESFORCE_CONFIG.USERNAME || SALESFORCE_CONFIG.USERNAME.includes('your_')) {\r\n    missing.push('SALESFORCE_USERNAME');\r\n  }\r\n  if (!SALESFORCE_CONFIG.PASSWORD || SALESFORCE_CONFIG.PASSWORD.includes('your_')) {\r\n    missing.push('SALESFORCE_PASSWORD');\r\n  }\r\n  if (!SALESFORCE_CONFIG.SECURITY_TOKEN || SALESFORCE_CONFIG.SECURITY_TOKEN.includes('your_')) {\r\n    missing.push('SALESFORCE_SECURITY_TOKEN');\r\n  }\r\n\r\n  return {\r\n    isValid: missing.length === 0,\r\n    missing,\r\n    message:\r\n      missing.length > 0\r\n        ? `Configuração incompleta. Variáveis faltando: ${missing.join(', ')}`\r\n        : 'Configuração válida',\r\n  };\r\n}\r\n\r\n// Create singleton instances\r\nexport const salesforceAuth = new SalesforceAuth();\r\nexport const salesforceAPI = new SalesforceAPI();\r\nexport { SALESFORCE_CONFIG };\r\n", "/**\r\n * Supabase Configuration\r\n * Configure your Supabase project credentials here\r\n */\r\n\r\nimport { createClient } from '@supabase/supabase-js';\r\n\r\n// Supabase project configuration\r\nconst SUPABASE_URL = 'https://dwpsyresppubuxbrwrkc.supabase.co'; // Replace with your project URL\r\nconst SUPABASE_ANON_KEY =\r\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR3cHN5cmVzcHB1YnV4YnJ3cmtjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNjcxNzgsImV4cCI6MjA2ODk0MzE3OH0.Z0sA04rkEBVGnQqmHy8UO7FCzYjCCsG7ENCBuY4Ijbc'; // Replace with your anon key\r\n\r\n// Create Supabase client\r\nexport const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\r\n\r\n// Database table name\r\nexport const TABLE_NAME = 'calculator_submissions';\r\n\r\n// Schema for the data structure\r\nexport const DATA_SCHEMA = {\r\n  // ID automático do Supabase\r\n  id: 'uuid',\r\n\r\n  // Dados básicos\r\n  patrimonio: 'numeric',\r\n  ativos_escolhidos: 'jsonb',\r\n  alocacao: 'jsonb',\r\n\r\n  // Metadados\r\n  submitted_at: 'timestamp with time zone',\r\n  user_agent: 'text',\r\n  session_id: 'text',\r\n\r\n  // Dados calculados\r\n  total_alocado: 'numeric',\r\n  percentual_alocado: 'numeric',\r\n  patrimonio_restante: 'numeric',\r\n};\r\n\r\n// Helper function to validate environment\r\nexport function validateSupabaseConfig() {\r\n  if (SUPABASE_URL === 'YOUR_SUPABASE_URL' || SUPABASE_ANON_KEY === 'YOUR_SUPABASE_ANON_KEY') {\r\n    console.error(\r\n      '❌ Supabase not configured. Please update src/config/supabase.js with your credentials.'\r\n    );\r\n    return false;\r\n  }\r\n  return true;\r\n}\r\n\r\n// Export default client for convenience\r\nexport default supabase;\r\n", "import SupabaseClient from './SupabaseClient'\nimport type { GenericSchema, SupabaseClientOptions } from './lib/types'\n\nexport * from '@supabase/auth-js'\nexport type { User as AuthUser, Session as AuthSession } from '@supabase/auth-js'\nexport {\n  type PostgrestResponse,\n  type PostgrestSingleResponse,\n  type PostgrestMaybeSingleResponse,\n  PostgrestError,\n} from '@supabase/postgrest-js'\nexport {\n  FunctionsHttpError,\n  FunctionsFetchError,\n  FunctionsRelayError,\n  FunctionsError,\n  type FunctionInvokeOptions,\n  FunctionRegion,\n} from '@supabase/functions-js'\nexport * from '@supabase/realtime-js'\nexport { default as SupabaseClient } from './SupabaseClient'\nexport type { SupabaseClientOptions, QueryResult, QueryData, QueryError } from './lib/types'\n\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = <\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n>(\n  supabaseUrl: string,\n  supabaseKey: string,\n  options?: SupabaseClientOptions<SchemaName>\n): SupabaseClient<Database, SchemaName, Schema> => {\n  return new SupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, options)\n}\n\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning(): boolean {\n  if (\n    typeof window !== 'undefined' ||\n    typeof process === 'undefined' ||\n    process.version === undefined ||\n    process.version === null\n  ) {\n    return false\n  }\n\n  const versionMatch = process.version.match(/^v(\\d+)\\./)\n  if (!versionMatch) {\n    return false\n  }\n\n  const majorVersion = parseInt(versionMatch[1], 10)\n  return majorVersion <= 18\n}\n\nif (shouldShowDeprecationWarning()) {\n  console.warn(\n    `⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` +\n      `Please upgrade to Node.js 20 or later. ` +\n      `For more information, visit: https://github.com/orgs/supabase/discussions/37217`\n  )\n}\n", "export { FunctionsClient } from './FunctionsClient'\nexport {\n  type FunctionInvokeOptions,\n  FunctionsError,\n  FunctionsFetchError,\n  FunctionsHttpError,\n  FunctionsRelayError,\n  FunctionRegion,\n  type FunctionsResponse,\n} from './types'\n", "import { Fetch } from './types'\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n", "export type Fetch = typeof fetch\n\n/**\n * Response format\n */\nexport interface FunctionsResponseSuccess<T> {\n  data: T\n  error: null\n  response?: Response\n}\nexport interface FunctionsResponseFailure {\n  data: null\n  error: any\n  response?: Response\n}\nexport type FunctionsResponse<T> = FunctionsResponseSuccess<T> | FunctionsResponseFailure\n\nexport class FunctionsError extends Error {\n  context: any\n  constructor(message: string, name = 'FunctionsError', context?: any) {\n    super(message)\n    this.name = name\n    this.context = context\n  }\n}\n\nexport class FunctionsFetchError extends FunctionsError {\n  constructor(context: any) {\n    super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context)\n  }\n}\n\nexport class FunctionsRelayError extends FunctionsError {\n  constructor(context: any) {\n    super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context)\n  }\n}\n\nexport class FunctionsHttpError extends FunctionsError {\n  constructor(context: any) {\n    super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context)\n  }\n}\n// Define the enum for the 'region' property\nexport enum FunctionRegion {\n  Any = 'any',\n  ApNortheast1 = 'ap-northeast-1',\n  ApNortheast2 = 'ap-northeast-2',\n  ApSouth1 = 'ap-south-1',\n  ApSoutheast1 = 'ap-southeast-1',\n  ApSoutheast2 = 'ap-southeast-2',\n  CaCentral1 = 'ca-central-1',\n  EuCentral1 = 'eu-central-1',\n  EuWest1 = 'eu-west-1',\n  EuWest2 = 'eu-west-2',\n  EuWest3 = 'eu-west-3',\n  SaEast1 = 'sa-east-1',\n  UsEast1 = 'us-east-1',\n  UsWest1 = 'us-west-1',\n  UsWest2 = 'us-west-2',\n}\n\nexport type FunctionInvokeOptions = {\n  /**\n   * Object representing the headers to send with the request.\n   */\n  headers?: { [key: string]: string }\n  /**\n   * The HTTP verb of the request\n   */\n  method?: 'POST' | 'GET' | 'PUT' | 'PATCH' | 'DELETE'\n  /**\n   * The Region to invoke the function in.\n   */\n  region?: FunctionRegion\n  /**\n   * The body of the request.\n   */\n  body?:\n    | File\n    | Blob\n    | ArrayBuffer\n    | FormData\n    | ReadableStream<Uint8Array>\n    | Record<string, any>\n    | string\n}\n", "import index from '../cjs/index.js'\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = index\n\nexport {\n  PostgrestBuilder,\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n  PostgrestTransformBuilder,\n  PostgrestError,\n}\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\n", "import RealtimeClient, {\n  RealtimeClientOptions,\n  RealtimeMessage,\n  RealtimeRemoveChannelResponse,\n} from './RealtimeClient'\nimport RealtimeChannel, {\n  RealtimeChannelOptions,\n  RealtimeChannelSendResponse,\n  RealtimePostgresChangesFilter,\n  RealtimePostgresChangesPayload,\n  RealtimePostgresInsertPayload,\n  RealtimePostgresUpdatePayload,\n  RealtimePostgresDeletePayload,\n  REALTIME_LISTEN_TYPES,\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT,\n  REALTIME_SUBSCRIBE_STATES,\n  REALTIME_CHANNEL_STATES,\n} from './RealtimeChannel'\nimport RealtimePresence, {\n  RealtimePresenceState,\n  RealtimePresenceJoinPayload,\n  RealtimePresenceLeavePayload,\n  REALTIME_PRESENCE_LISTEN_EVENTS,\n} from './RealtimePresence'\n\nexport {\n  RealtimePresence,\n  RealtimeChannel,\n  RealtimeChannelOptions,\n  RealtimeChannelSendResponse,\n  RealtimeClient,\n  RealtimeClientOptions,\n  RealtimeMessage,\n  RealtimePostgresChangesFilter,\n  RealtimePostgresChangesPayload,\n  RealtimePostgresInsertPayload,\n  RealtimePostgresUpdatePayload,\n  RealtimePostgresDeletePayload,\n  RealtimePresenceJoinPayload,\n  RealtimePresenceLeavePayload,\n  RealtimePresenceState,\n  RealtimeRemoveChannelResponse,\n  REALTIME_LISTEN_TYPES,\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT,\n  REALTIME_PRESENCE_LISTEN_EVENTS,\n  REALTIME_SUBSCRIBE_STATES,\n  REALTIME_CHANNEL_STATES,\n}\n", "import { WebSocket } from 'isows'\n\nimport {\n  CHANNEL_EVENTS,\n  CONNECTION_STATE,\n  DEFAULT_VERSION,\n  DEFAULT_TIMEOUT,\n  SOCKET_STATES,\n  TRANSPORTS,\n  VSN,\n  WS_CLOSE_NORMAL,\n} from './lib/constants'\n\nimport Serializer from './lib/serializer'\nimport Timer from './lib/timer'\n\nimport { httpEndpointURL } from './lib/transformers'\nimport RealtimeChannel from './RealtimeChannel'\nimport type { RealtimeChannelOptions } from './RealtimeChannel'\n\ntype Fetch = typeof fetch\n\nexport type Channel = {\n  name: string\n  inserted_at: string\n  updated_at: string\n  id: number\n}\nexport type LogLevel = 'info' | 'warn' | 'error'\n\nexport type RealtimeMessage = {\n  topic: string\n  event: string\n  payload: any\n  ref: string\n  join_ref?: string\n}\n\nexport type RealtimeRemoveChannelResponse = 'ok' | 'timed out' | 'error'\nexport type HeartbeatStatus =\n  | 'sent'\n  | 'ok'\n  | 'error'\n  | 'timeout'\n  | 'disconnected'\n\nconst noop = () => {}\n\nexport interface WebSocketLikeConstructor {\n  new (\n    address: string | URL,\n    subprotocols?: string | string[] | undefined\n  ): WebSocketLike\n}\n\nexport type WebSocketLike = WebSocket\n\nexport interface WebSocketLikeError {\n  error: any\n  message: string\n  type: string\n}\n\nexport type RealtimeClientOptions = {\n  transport?: WebSocketLikeConstructor\n  timeout?: number\n  heartbeatIntervalMs?: number\n  logger?: Function\n  encode?: Function\n  decode?: Function\n  reconnectAfterMs?: Function\n  headers?: { [key: string]: string }\n  params?: { [key: string]: any }\n  //Deprecated: Use it in favour of correct casing `logLevel`\n  log_level?: LogLevel\n  logLevel?: LogLevel\n  fetch?: Fetch\n  worker?: boolean\n  workerUrl?: string\n  accessToken?: () => Promise<string | null>\n}\n\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`\n\nexport default class RealtimeClient {\n  accessTokenValue: string | null = null\n  apiKey: string | null = null\n  channels: RealtimeChannel[] = new Array()\n  endPoint: string = ''\n  httpEndpoint: string = ''\n  /** @deprecated headers cannot be set on websocket connections */\n  headers?: { [key: string]: string } = {}\n  params?: { [key: string]: string } = {}\n  timeout: number = DEFAULT_TIMEOUT\n  transport: WebSocketLikeConstructor | null\n  heartbeatIntervalMs: number = 25000\n  heartbeatTimer: ReturnType<typeof setInterval> | undefined = undefined\n  pendingHeartbeatRef: string | null = null\n  heartbeatCallback: (status: HeartbeatStatus) => void = noop\n  ref: number = 0\n  reconnectTimer: Timer\n  logger: Function = noop\n  logLevel?: LogLevel\n  encode: Function\n  decode: Function\n  reconnectAfterMs: Function\n  conn: WebSocketLike | null = null\n  sendBuffer: Function[] = []\n  serializer: Serializer = new Serializer()\n  stateChangeCallbacks: {\n    open: Function[]\n    close: Function[]\n    error: Function[]\n    message: Function[]\n  } = {\n    open: [],\n    close: [],\n    error: [],\n    message: [],\n  }\n  fetch: Fetch\n  accessToken: (() => Promise<string | null>) | null = null\n  worker?: boolean\n  workerUrl?: string\n  workerRef?: Worker\n\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers Deprecated: headers cannot be set on websocket connections and this option will be removed in the future.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.logLevel Sets the log level for Realtime\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint: string, options?: RealtimeClientOptions) {\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`\n    this.httpEndpoint = httpEndpointURL(endPoint)\n    if (options?.transport) {\n      this.transport = options.transport\n    } else {\n      this.transport = null\n    }\n    if (options?.params) this.params = options.params\n    if (options?.timeout) this.timeout = options.timeout\n    if (options?.logger) this.logger = options.logger\n    if (options?.logLevel || options?.log_level) {\n      this.logLevel = options.logLevel || options.log_level\n      this.params = { ...this.params, log_level: this.logLevel as string }\n    }\n\n    if (options?.heartbeatIntervalMs)\n      this.heartbeatIntervalMs = options.heartbeatIntervalMs\n\n    const accessTokenValue = options?.params?.apikey\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue\n      this.apiKey = accessTokenValue\n    }\n\n    this.reconnectAfterMs = options?.reconnectAfterMs\n      ? options.reconnectAfterMs\n      : (tries: number) => {\n          return [1000, 2000, 5000, 10000][tries - 1] || 10000\n        }\n    this.encode = options?.encode\n      ? options.encode\n      : (payload: JSON, callback: Function) => {\n          return callback(JSON.stringify(payload))\n        }\n    this.decode = options?.decode\n      ? options.decode\n      : this.serializer.decode.bind(this.serializer)\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect()\n      this.connect()\n    }, this.reconnectAfterMs)\n\n    this.fetch = this._resolveFetch(options?.fetch)\n    if (options?.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported')\n      }\n      this.worker = options?.worker || false\n      this.workerUrl = options?.workerUrl\n    }\n    this.accessToken = options?.accessToken || null\n  }\n\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect(): void {\n    if (this.conn) {\n      return\n    }\n    if (!this.transport) {\n      this.transport = WebSocket\n    }\n    if (!this.transport) {\n      throw new Error('No transport provided')\n    }\n    this.conn = new this.transport(this.endpointURL()) as WebSocketLike\n    this.setupConnection()\n  }\n\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL(): string {\n    return this._appendParams(\n      this.endPoint,\n      Object.assign({}, this.params, { vsn: VSN })\n    )\n  }\n\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code?: number, reason?: string): void {\n    if (this.conn) {\n      this.conn.onclose = function () {} // noop\n      if (code) {\n        this.conn.close(code, reason ?? '')\n      } else {\n        this.conn.close()\n      }\n      this.conn = null\n\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.reconnectTimer.reset()\n      this.channels.forEach((channel) => channel.teardown())\n    }\n  }\n\n  /**\n   * Returns all created channels\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.channels\n  }\n\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(\n    channel: RealtimeChannel\n  ): Promise<RealtimeRemoveChannelResponse> {\n    const status = await channel.unsubscribe()\n\n    if (this.channels.length === 0) {\n      this.disconnect()\n    }\n\n    return status\n  }\n\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels(): Promise<RealtimeRemoveChannelResponse[]> {\n    const values_1 = await Promise.all(\n      this.channels.map((channel) => channel.unsubscribe())\n    )\n    this.channels = []\n    this.disconnect()\n    return values_1\n  }\n\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind: string, msg: string, data?: any) {\n    this.logger(kind, msg, data)\n  }\n\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState(): CONNECTION_STATE {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing\n      default:\n        return CONNECTION_STATE.Closed\n    }\n  }\n\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected(): boolean {\n    return this.connectionState() === CONNECTION_STATE.Open\n  }\n\n  channel(\n    topic: string,\n    params: RealtimeChannelOptions = { config: {} }\n  ): RealtimeChannel {\n    const realtimeTopic = `realtime:${topic}`\n    const exists = this.getChannels().find(\n      (c: RealtimeChannel) => c.topic === realtimeTopic\n    )\n\n    if (!exists) {\n      const chan = new RealtimeChannel(`realtime:${topic}`, params, this)\n      this.channels.push(chan)\n\n      return chan\n    } else {\n      return exists\n    }\n  }\n\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data: RealtimeMessage): void {\n    const { topic, event, payload, ref } = data\n    const callback = () => {\n      this.encode(data, (result: any) => {\n        this.conn?.send(result)\n      })\n    }\n    this.log('push', `${topic} ${event} (${ref})`, payload)\n    if (this.isConnected()) {\n      callback()\n    } else {\n      this.sendBuffer.push(callback)\n    }\n  }\n\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token: string | null = null): Promise<void> {\n    let tokenToSend =\n      token ||\n      (this.accessToken && (await this.accessToken())) ||\n      this.accessTokenValue\n\n    if (this.accessTokenValue != tokenToSend) {\n      this.accessTokenValue = tokenToSend\n      this.channels.forEach((channel) => {\n        const payload = {\n          access_token: tokenToSend,\n          version: DEFAULT_VERSION,\n        }\n\n        tokenToSend && channel.updateJoinPayload(payload)\n\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend,\n          })\n        }\n      })\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    if (!this.isConnected()) {\n      this.heartbeatCallback('disconnected')\n      return\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null\n      this.log(\n        'transport',\n        'heartbeat timeout. Attempting to re-establish connection'\n      )\n      this.heartbeatCallback('timeout')\n      this.conn?.close(WS_CLOSE_NORMAL, 'hearbeat timeout')\n      return\n    }\n    this.pendingHeartbeatRef = this._makeRef()\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef,\n    })\n    this.heartbeatCallback('sent')\n    await this.setAuth()\n  }\n\n  onHeartbeat(callback: (status: HeartbeatStatus) => void): void {\n    this.heartbeatCallback = callback\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach((callback) => callback())\n      this.sendBuffer = []\n    }\n  }\n\n  /**\n   * Use either custom fetch, if provided, or default fetch to make HTTP requests\n   *\n   * @internal\n   */\n  _resolveFetch = (customFetch?: Fetch): Fetch => {\n    let _fetch: Fetch\n    if (customFetch) {\n      _fetch = customFetch\n    } else if (typeof fetch === 'undefined') {\n      _fetch = (...args) =>\n        import('@supabase/node-fetch' as any).then(({ default: fetch }) =>\n          fetch(...args)\n        )\n    } else {\n      _fetch = fetch\n    }\n    return (...args) => _fetch(...args)\n  }\n\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef(): string {\n    let newRef = this.ref + 1\n    if (newRef === this.ref) {\n      this.ref = 0\n    } else {\n      this.ref = newRef\n    }\n\n    return this.ref.toString()\n  }\n\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic: string): void {\n    let dupChannel = this.channels.find(\n      (c) => c.topic === topic && (c._isJoined() || c._isJoining())\n    )\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`)\n      dupChannel.unsubscribe()\n    }\n  }\n\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel: RealtimeChannel) {\n    this.channels = this.channels.filter((c) => c.topic !== channel.topic)\n  }\n\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  private setupConnection(): void {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer'\n      this.conn.onopen = () => this._onConnOpen()\n      this.conn.onerror = (error: Event) => this._onConnError(error)\n      this.conn.onmessage = (event: any) => this._onConnMessage(event)\n      this.conn.onclose = (event: any) => this._onConnClose(event)\n    }\n  }\n\n  /** @internal */\n  private _onConnMessage(rawMessage: { data: any }) {\n    this.decode(rawMessage.data, (msg: RealtimeMessage) => {\n      let { topic, event, payload, ref } = msg\n\n      if (topic === 'phoenix' && event === 'phx_reply') {\n        this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error')\n      }\n\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null\n      }\n\n      this.log(\n        'receive',\n        `${payload.status || ''} ${topic} ${event} ${\n          (ref && '(' + ref + ')') || ''\n        }`,\n        payload\n      )\n\n      Array.from(this.channels)\n        .filter((channel: RealtimeChannel) => channel._isMember(topic))\n        .forEach((channel: RealtimeChannel) =>\n          channel._trigger(event, payload, ref)\n        )\n\n      this.stateChangeCallbacks.message.forEach((callback) => callback(msg))\n    })\n  }\n\n  /** @internal */\n  private _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`)\n    this.flushSendBuffer()\n    this.reconnectTimer.reset()\n    if (!this.worker) {\n      this._startHeartbeat()\n    } else {\n      if (!this.workerRef) {\n        this._startWorkerHeartbeat()\n      }\n    }\n\n    this.stateChangeCallbacks.open.forEach((callback) => callback())\n  }\n  /** @internal */\n  private _startHeartbeat() {\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.heartbeatTimer = setInterval(\n      () => this.sendHeartbeat(),\n      this.heartbeatIntervalMs\n    )\n  }\n\n  /** @internal */\n  private _startWorkerHeartbeat() {\n    if (this.workerUrl) {\n      this.log('worker', `starting worker for from ${this.workerUrl}`)\n    } else {\n      this.log('worker', `starting default worker`)\n    }\n    const objectUrl = this._workerObjectUrl(this.workerUrl!)\n    this.workerRef = new Worker(objectUrl)\n    this.workerRef.onerror = (error) => {\n      this.log('worker', 'worker error', (error as ErrorEvent).message)\n      this.workerRef!.terminate()\n    }\n    this.workerRef.onmessage = (event) => {\n      if (event.data.event === 'keepAlive') {\n        this.sendHeartbeat()\n      }\n    }\n    this.workerRef.postMessage({\n      event: 'start',\n      interval: this.heartbeatIntervalMs,\n    })\n  }\n  /** @internal */\n  private _onConnClose(event: any) {\n    this.log('transport', 'close', event)\n    this._triggerChanError()\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.reconnectTimer.scheduleTimeout()\n    this.stateChangeCallbacks.close.forEach((callback) => callback(event))\n  }\n\n  /** @internal */\n  private _onConnError(error: Event) {\n    this.log('transport', `${error}`)\n    this._triggerChanError()\n    this.stateChangeCallbacks.error.forEach((callback) => callback(error))\n  }\n\n  /** @internal */\n  private _triggerChanError() {\n    this.channels.forEach((channel: RealtimeChannel) =>\n      channel._trigger(CHANNEL_EVENTS.error)\n    )\n  }\n\n  /** @internal */\n  private _appendParams(\n    url: string,\n    params: { [key: string]: string }\n  ): string {\n    if (Object.keys(params).length === 0) {\n      return url\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?'\n    const query = new URLSearchParams(params)\n    return `${url}${prefix}${query}`\n  }\n\n  private _workerObjectUrl(url: string | undefined): string {\n    let result_url: string\n    if (url) {\n      result_url = url\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' })\n      result_url = URL.createObjectURL(blob)\n    }\n    return result_url\n  }\n}\n", "import { getNativeWebSocket } from \"./utils.js\";\n\nexport const WebSocket = getNativeWebSocket();\n\ntype MessageEvent_ = MessageEvent;\nexport type { MessageEvent_ as MessageEvent };\n", "export function getNativeWebSocket() {\n  if (typeof WebSocket !== \"undefined\") return WebSocket;\n  if (typeof global.WebSocket !== \"undefined\") return global.WebSocket;\n  if (typeof window.WebSocket !== \"undefined\") return window.WebSocket;\n  if (typeof self.WebSocket !== \"undefined\") return self.WebSocket;\n  throw new Error(\"`WebSocket` is not supported in this environment\");\n}\n", "import { version } from './version'\n\nexport const DEFAULT_VERSION = `realtime-js/${version}`\nexport const VSN: string = '1.0.0'\n\nexport const VERSION = version\n\nexport const DEFAULT_TIMEOUT = 10000\n\nexport const WS_CLOSE_NORMAL = 1000\n\nexport enum SOCKET_STATES {\n  connecting = 0,\n  open = 1,\n  closing = 2,\n  closed = 3,\n}\n\nexport enum CHANNEL_STATES {\n  closed = 'closed',\n  errored = 'errored',\n  joined = 'joined',\n  joining = 'joining',\n  leaving = 'leaving',\n}\n\nexport enum CHANNEL_EVENTS {\n  close = 'phx_close',\n  error = 'phx_error',\n  join = 'phx_join',\n  reply = 'phx_reply',\n  leave = 'phx_leave',\n  access_token = 'access_token',\n}\n\nexport enum TRANSPORTS {\n  websocket = 'websocket',\n}\n\nexport enum CONNECTION_STATE {\n  Connecting = 'connecting',\n  Open = 'open',\n  Closing = 'closing',\n  Closed = 'closed',\n}\n", "export const version = '2.11.15'\n", "// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\n\nexport default class Serializer {\n  HEADER_LENGTH = 1\n\n  decode(rawPayload: ArrayBuffer | string, callback: Function) {\n    if (rawPayload.constructor === ArrayBuffer) {\n      return callback(this._binaryDecode(rawPayload))\n    }\n\n    if (typeof rawPayload === 'string') {\n      return callback(JSON.parse(rawPayload))\n    }\n\n    return callback({})\n  }\n\n  private _binaryDecode(buffer: ArrayBuffer) {\n    const view = new DataView(buffer)\n    const decoder = new TextDecoder()\n\n    return this._decodeBroadcast(buffer, view, decoder)\n  }\n\n  private _decodeBroadcast(\n    buffer: <PERSON><PERSON><PERSON><PERSON><PERSON>er,\n    view: DataView,\n    decoder: TextDecoder\n  ): {\n    ref: null\n    topic: string\n    event: string\n    payload: { [key: string]: any }\n  } {\n    const topicSize = view.getUint8(1)\n    const eventSize = view.getUint8(2)\n    let offset = this.HEADER_LENGTH + 2\n    const topic = decoder.decode(buffer.slice(offset, offset + topicSize))\n    offset = offset + topicSize\n    const event = decoder.decode(buffer.slice(offset, offset + eventSize))\n    offset = offset + eventSize\n    const data = JSON.parse(\n      decoder.decode(buffer.slice(offset, buffer.byteLength))\n    )\n\n    return { ref: null, topic: topic, event: event, payload: data }\n  }\n}\n", "/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nexport default class Timer {\n  timer: number | undefined = undefined\n  tries: number = 0\n\n  constructor(public callback: Function, public timerCalc: Function) {\n    this.callback = callback\n    this.timerCalc = timerCalc\n  }\n\n  reset() {\n    this.tries = 0\n    clearTimeout(this.timer)\n  }\n\n  // Cancels any previous scheduleTimeout and schedules callback\n  scheduleTimeout() {\n    clearTimeout(this.timer)\n\n    this.timer = <any>setTimeout(() => {\n      this.tries = this.tries + 1\n      this.callback()\n    }, this.timerCalc(this.tries + 1))\n  }\n}\n", "/**\n * Helpers to convert the change Payload into native JS types.\n */\n\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\n\nexport enum PostgresTypes {\n  abstime = 'abstime',\n  bool = 'bool',\n  date = 'date',\n  daterange = 'daterange',\n  float4 = 'float4',\n  float8 = 'float8',\n  int2 = 'int2',\n  int4 = 'int4',\n  int4range = 'int4range',\n  int8 = 'int8',\n  int8range = 'int8range',\n  json = 'json',\n  jsonb = 'jsonb',\n  money = 'money',\n  numeric = 'numeric',\n  oid = 'oid',\n  reltime = 'reltime',\n  text = 'text',\n  time = 'time',\n  timestamp = 'timestamp',\n  timestamptz = 'timestamptz',\n  timetz = 'timetz',\n  tsrange = 'tsrange',\n  tstzrange = 'tstzrange',\n}\n\ntype Columns = {\n  name: string // the column name. eg: \"user_id\"\n  type: string // the column type. eg: \"uuid\"\n  flags?: string[] // any special flags for the column. eg: [\"key\"]\n  type_modifier?: number // the type modifier. eg: **********\n}[]\n\ntype BaseValue = null | string | number | boolean\ntype RecordValue = BaseValue | BaseValue[]\n\ntype Record = {\n  [key: string]: RecordValue\n}\n\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nexport const convertChangeData = (\n  columns: Columns,\n  record: Record,\n  options: { skipTypes?: string[] } = {}\n): Record => {\n  const skipTypes = options.skipTypes ?? []\n\n  return Object.keys(record).reduce((acc, rec_key) => {\n    acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes)\n    return acc\n  }, {} as Record)\n}\n\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nexport const convertColumn = (\n  columnName: string,\n  columns: Columns,\n  record: Record,\n  skipTypes: string[]\n): RecordValue => {\n  const column = columns.find((x) => x.name === columnName)\n  const colType = column?.type\n  const value = record[columnName]\n\n  if (colType && !skipTypes.includes(colType)) {\n    return convertCell(colType, value)\n  }\n\n  return noop(value)\n}\n\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nexport const convertCell = (type: string, value: RecordValue): RecordValue => {\n  // if data type is an array\n  if (type.charAt(0) === '_') {\n    const dataType = type.slice(1, type.length)\n    return toArray(value, dataType)\n  }\n\n  // If not null, convert to correct type.\n  switch (type) {\n    case PostgresTypes.bool:\n      return toBoolean(value)\n    case PostgresTypes.float4:\n    case PostgresTypes.float8:\n    case PostgresTypes.int2:\n    case PostgresTypes.int4:\n    case PostgresTypes.int8:\n    case PostgresTypes.numeric:\n    case PostgresTypes.oid:\n      return toNumber(value)\n    case PostgresTypes.json:\n    case PostgresTypes.jsonb:\n      return toJson(value)\n    case PostgresTypes.timestamp:\n      return toTimestampString(value) // Format to be consistent with PostgREST\n    case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n    case PostgresTypes.date: // To allow users to cast it based on Timezone\n    case PostgresTypes.daterange:\n    case PostgresTypes.int4range:\n    case PostgresTypes.int8range:\n    case PostgresTypes.money:\n    case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n    case PostgresTypes.text:\n    case PostgresTypes.time: // To allow users to cast it based on Timezone\n    case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n    case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n    case PostgresTypes.tsrange:\n    case PostgresTypes.tstzrange:\n      return noop(value)\n    default:\n      // Return the value for remaining types\n      return noop(value)\n  }\n}\n\nconst noop = (value: RecordValue): RecordValue => {\n  return value\n}\nexport const toBoolean = (value: RecordValue): RecordValue => {\n  switch (value) {\n    case 't':\n      return true\n    case 'f':\n      return false\n    default:\n      return value\n  }\n}\nexport const toNumber = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    const parsedValue = parseFloat(value)\n    if (!Number.isNaN(parsedValue)) {\n      return parsedValue\n    }\n  }\n  return value\n}\nexport const toJson = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    try {\n      return JSON.parse(value)\n    } catch (error) {\n      console.log(`JSON parse error: ${error}`)\n      return value\n    }\n  }\n  return value\n}\n\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nexport const toArray = (value: RecordValue, type: string): RecordValue => {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  const lastIdx = value.length - 1\n  const closeBrace = value[lastIdx]\n  const openBrace = value[0]\n\n  // Confirm value is a Postgres array by checking curly brackets\n  if (openBrace === '{' && closeBrace === '}') {\n    let arr\n    const valTrim = value.slice(1, lastIdx)\n\n    // TODO: find a better solution to separate Postgres array data\n    try {\n      arr = JSON.parse('[' + valTrim + ']')\n    } catch (_) {\n      // WARNING: splitting on comma does not cover all edge cases\n      arr = valTrim ? valTrim.split(',') : []\n    }\n\n    return arr.map((val: BaseValue) => convertCell(type, val))\n  }\n\n  return value\n}\n\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nexport const toTimestampString = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    return value.replace(' ', 'T')\n  }\n\n  return value\n}\n\nexport const httpEndpointURL = (socketUrl: string): string => {\n  let url = socketUrl\n  url = url.replace(/^ws/i, 'http')\n  url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '')\n  return url.replace(/\\/+$/, '')\n}\n", "import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants'\nimport Push from './lib/push'\nimport type RealtimeClient from './RealtimeClient'\nimport Timer from './lib/timer'\nimport RealtimePresence, {\n  REALTIME_PRESENCE_LISTEN_EVENTS,\n} from './RealtimePresence'\nimport type {\n  RealtimePresenceJoinPayload,\n  RealtimePresenceLeavePayload,\n  RealtimePresenceState,\n} from './RealtimePresence'\nimport * as Transformers from './lib/transformers'\nimport { httpEndpointURL } from './lib/transformers'\n\nexport type RealtimeChannelOptions = {\n  config: {\n    /**\n     * self option enables client to receive message it broadcast\n     * ack option instructs server to acknowledge that broadcast message was received\n     */\n    broadcast?: { self?: boolean; ack?: boolean }\n    /**\n     * key option is used to track presence payload across clients\n     */\n    presence?: { key?: string }\n    /**\n     * defines if the channel is private or not and if RLS policies will be used to check data\n     */\n    private?: boolean\n  }\n}\n\ntype RealtimePostgresChangesPayloadBase = {\n  schema: string\n  table: string\n  commit_timestamp: string\n  errors: string[]\n}\n\nexport type RealtimePostgresInsertPayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`\n    new: T\n    old: {}\n  }\n\nexport type RealtimePostgresUpdatePayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`\n    new: T\n    old: Partial<T>\n  }\n\nexport type RealtimePostgresDeletePayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE}`\n    new: {}\n    old: Partial<T>\n  }\n\nexport type RealtimePostgresChangesPayload<T extends { [key: string]: any }> =\n  | RealtimePostgresInsertPayload<T>\n  | RealtimePostgresUpdatePayload<T>\n  | RealtimePostgresDeletePayload<T>\n\nexport type RealtimePostgresChangesFilter<\n  T extends `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT}`\n> = {\n  /**\n   * The type of database change to listen to.\n   */\n  event: T\n  /**\n   * The database schema to listen to.\n   */\n  schema: string\n  /**\n   * The database table to listen to.\n   */\n  table?: string\n  /**\n   * Receive database changes when filter is matched.\n   */\n  filter?: string\n}\n\nexport type RealtimeChannelSendResponse = 'ok' | 'timed out' | 'error'\n\nexport enum REALTIME_POSTGRES_CHANGES_LISTEN_EVENT {\n  ALL = '*',\n  INSERT = 'INSERT',\n  UPDATE = 'UPDATE',\n  DELETE = 'DELETE',\n}\n\nexport enum REALTIME_LISTEN_TYPES {\n  BROADCAST = 'broadcast',\n  PRESENCE = 'presence',\n  POSTGRES_CHANGES = 'postgres_changes',\n  SYSTEM = 'system',\n}\n\nexport enum REALTIME_SUBSCRIBE_STATES {\n  SUBSCRIBED = 'SUBSCRIBED',\n  TIMED_OUT = 'TIMED_OUT',\n  CLOSED = 'CLOSED',\n  CHANNEL_ERROR = 'CHANNEL_ERROR',\n}\n\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES\n\ninterface PostgresChangesFilters {\n  postgres_changes: {\n    id: string\n    event: string\n    schema?: string\n    table?: string\n    filter?: string\n  }[]\n}\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n  bindings: {\n    [key: string]: {\n      type: string\n      filter: { [key: string]: any }\n      callback: Function\n      id?: string\n    }[]\n  } = {}\n  timeout: number\n  state: CHANNEL_STATES = CHANNEL_STATES.closed\n  joinedOnce = false\n  joinPush: Push\n  rejoinTimer: Timer\n  pushBuffer: Push[] = []\n  presence: RealtimePresence\n  broadcastEndpointURL: string\n  subTopic: string\n  private: boolean\n\n  constructor(\n    /** Topic name can be any string. */\n    public topic: string,\n    public params: RealtimeChannelOptions = { config: {} },\n    public socket: RealtimeClient\n  ) {\n    this.subTopic = topic.replace(/^realtime:/i, '')\n    this.params.config = {\n      ...{\n        broadcast: { ack: false, self: false },\n        presence: { key: '' },\n        private: false,\n      },\n      ...params.config,\n    }\n    this.timeout = this.socket.timeout\n    this.joinPush = new Push(\n      this,\n      CHANNEL_EVENTS.join,\n      this.params,\n      this.timeout\n    )\n    this.rejoinTimer = new Timer(\n      () => this._rejoinUntilConnected(),\n      this.socket.reconnectAfterMs\n    )\n    this.joinPush.receive('ok', () => {\n      this.state = CHANNEL_STATES.joined\n      this.rejoinTimer.reset()\n      this.pushBuffer.forEach((pushEvent: Push) => pushEvent.send())\n      this.pushBuffer = []\n    })\n    this._onClose(() => {\n      this.rejoinTimer.reset()\n      this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`)\n      this.state = CHANNEL_STATES.closed\n      this.socket._remove(this)\n    })\n    this._onError((reason: string) => {\n      if (this._isLeaving() || this._isClosed()) {\n        return\n      }\n      this.socket.log('channel', `error ${this.topic}`, reason)\n      this.state = CHANNEL_STATES.errored\n      this.rejoinTimer.scheduleTimeout()\n    })\n    this.joinPush.receive('timeout', () => {\n      if (!this._isJoining()) {\n        return\n      }\n      this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout)\n      this.state = CHANNEL_STATES.errored\n      this.rejoinTimer.scheduleTimeout()\n    })\n    this._on(CHANNEL_EVENTS.reply, {}, (payload: any, ref: string) => {\n      this._trigger(this._replyEventName(ref), payload)\n    })\n\n    this.presence = new RealtimePresence(this)\n\n    this.broadcastEndpointURL =\n      httpEndpointURL(this.socket.endPoint) + '/api/broadcast'\n    this.private = this.params.config.private || false\n  }\n\n  /** Subscribe registers your client with the server */\n  subscribe(\n    callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) => void,\n    timeout = this.timeout\n  ): RealtimeChannel {\n    if (!this.socket.isConnected()) {\n      this.socket.connect()\n    }\n    if (this.state == CHANNEL_STATES.closed) {\n      const {\n        config: { broadcast, presence, private: isPrivate },\n      } = this.params\n\n      this._onError((e: Error) =>\n        callback?.(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e)\n      )\n      this._onClose(() => callback?.(REALTIME_SUBSCRIBE_STATES.CLOSED))\n\n      const accessTokenPayload: { access_token?: string } = {}\n      const config = {\n        broadcast,\n        presence,\n        postgres_changes:\n          this.bindings.postgres_changes?.map((r) => r.filter) ?? [],\n        private: isPrivate,\n      }\n\n      if (this.socket.accessTokenValue) {\n        accessTokenPayload.access_token = this.socket.accessTokenValue\n      }\n\n      this.updateJoinPayload({ ...{ config }, ...accessTokenPayload })\n\n      this.joinedOnce = true\n      this._rejoin(timeout)\n\n      this.joinPush\n        .receive('ok', async ({ postgres_changes }: PostgresChangesFilters) => {\n          this.socket.setAuth()\n          if (postgres_changes === undefined) {\n            callback?.(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED)\n            return\n          } else {\n            const clientPostgresBindings = this.bindings.postgres_changes\n            const bindingsLen = clientPostgresBindings?.length ?? 0\n            const newPostgresBindings = []\n\n            for (let i = 0; i < bindingsLen; i++) {\n              const clientPostgresBinding = clientPostgresBindings[i]\n              const {\n                filter: { event, schema, table, filter },\n              } = clientPostgresBinding\n              const serverPostgresFilter =\n                postgres_changes && postgres_changes[i]\n\n              if (\n                serverPostgresFilter &&\n                serverPostgresFilter.event === event &&\n                serverPostgresFilter.schema === schema &&\n                serverPostgresFilter.table === table &&\n                serverPostgresFilter.filter === filter\n              ) {\n                newPostgresBindings.push({\n                  ...clientPostgresBinding,\n                  id: serverPostgresFilter.id,\n                })\n              } else {\n                this.unsubscribe()\n                this.state = CHANNEL_STATES.errored\n\n                callback?.(\n                  REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR,\n                  new Error(\n                    'mismatch between server and client bindings for postgres changes'\n                  )\n                )\n                return\n              }\n            }\n\n            this.bindings.postgres_changes = newPostgresBindings\n\n            callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED)\n            return\n          }\n        })\n        .receive('error', (error: { [key: string]: any }) => {\n          this.state = CHANNEL_STATES.errored\n          callback?.(\n            REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR,\n            new Error(\n              JSON.stringify(Object.values(error).join(', ') || 'error')\n            )\n          )\n          return\n        })\n        .receive('timeout', () => {\n          callback?.(REALTIME_SUBSCRIBE_STATES.TIMED_OUT)\n          return\n        })\n    }\n    return this\n  }\n\n  presenceState<\n    T extends { [key: string]: any } = {}\n  >(): RealtimePresenceState<T> {\n    return this.presence.state as RealtimePresenceState<T>\n  }\n\n  async track(\n    payload: { [key: string]: any },\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    return await this.send(\n      {\n        type: 'presence',\n        event: 'track',\n        payload,\n      },\n      opts.timeout || this.timeout\n    )\n  }\n\n  async untrack(\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    return await this.send(\n      {\n        type: 'presence',\n        event: 'untrack',\n      },\n      opts\n    )\n  }\n\n  /**\n   * Creates an event handler that listens to changes.\n   */\n  on(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.SYNC}` },\n    callback: () => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}` },\n    callback: (payload: RealtimePresenceJoinPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}` },\n    callback: (payload: RealtimePresenceLeavePayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.ALL}`>,\n    callback: (payload: RealtimePostgresChangesPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`>,\n    callback: (payload: RealtimePostgresInsertPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`>,\n    callback: (payload: RealtimePostgresUpdatePayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE}`>,\n    callback: (payload: RealtimePostgresDeletePayload<T>) => void\n  ): RealtimeChannel\n  /**\n   * The following is placed here to display on supabase.com/docs/reference/javascript/subscribe.\n   * @param type One of \"broadcast\", \"presence\", or \"postgres_changes\".\n   * @param filter Custom object specific to the Realtime feature detailing which payloads to receive.\n   * @param callback Function to be invoked when event handler is triggered.\n   */\n  on(\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`,\n    filter: { event: string },\n    callback: (payload: {\n      type: `${REALTIME_LISTEN_TYPES.BROADCAST}`\n      event: string\n      [key: string]: any\n    }) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`,\n    filter: { event: string },\n    callback: (payload: {\n      type: `${REALTIME_LISTEN_TYPES.BROADCAST}`\n      event: string\n      payload: T\n    }) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.SYSTEM}`,\n    filter: {},\n    callback: (payload: any) => void\n  ): RealtimeChannel\n  on(\n    type: `${REALTIME_LISTEN_TYPES}`,\n    filter: { event: string; [key: string]: string },\n    callback: (payload: any) => void\n  ): RealtimeChannel {\n    return this._on(type, filter, callback)\n  }\n  /**\n   * Sends a message into the channel.\n   *\n   * @param args Arguments to send to channel\n   * @param args.type The type of event to send\n   * @param args.event The name of the event being sent\n   * @param args.payload Payload to be sent\n   * @param opts Options to be used during the send process\n   */\n  async send(\n    args: {\n      type: 'broadcast' | 'presence' | 'postgres_changes'\n      event: string\n      payload?: any\n      [key: string]: any\n    },\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    if (!this._canPush() && args.type === 'broadcast') {\n      const { event, payload: endpoint_payload } = args\n      const authorization = this.socket.accessTokenValue\n        ? `Bearer ${this.socket.accessTokenValue}`\n        : ''\n      const options = {\n        method: 'POST',\n        headers: {\n          Authorization: authorization,\n          apikey: this.socket.apiKey ? this.socket.apiKey : '',\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          messages: [\n            {\n              topic: this.subTopic,\n              event,\n              payload: endpoint_payload,\n              private: this.private,\n            },\n          ],\n        }),\n      }\n\n      try {\n        const response = await this._fetchWithTimeout(\n          this.broadcastEndpointURL,\n          options,\n          opts.timeout ?? this.timeout\n        )\n\n        await response.body?.cancel()\n        return response.ok ? 'ok' : 'error'\n      } catch (error: any) {\n        if (error.name === 'AbortError') {\n          return 'timed out'\n        } else {\n          return 'error'\n        }\n      }\n    } else {\n      return new Promise((resolve) => {\n        const push = this._push(args.type, args, opts.timeout || this.timeout)\n\n        if (args.type === 'broadcast' && !this.params?.config?.broadcast?.ack) {\n          resolve('ok')\n        }\n\n        push.receive('ok', () => resolve('ok'))\n        push.receive('error', () => resolve('error'))\n        push.receive('timeout', () => resolve('timed out'))\n      })\n    }\n  }\n\n  updateJoinPayload(payload: { [key: string]: any }): void {\n    this.joinPush.updatePayload(payload)\n  }\n\n  /**\n   * Leaves the channel.\n   *\n   * Unsubscribes from server events, and instructs channel to terminate on server.\n   * Triggers onClose() hooks.\n   *\n   * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n   * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n   */\n  unsubscribe(timeout = this.timeout): Promise<'ok' | 'timed out' | 'error'> {\n    this.state = CHANNEL_STATES.leaving\n    const onClose = () => {\n      this.socket.log('channel', `leave ${this.topic}`)\n      this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef())\n    }\n\n    this.joinPush.destroy()\n\n    let leavePush: Push | null = null\n\n    return new Promise<RealtimeChannelSendResponse>((resolve) => {\n      leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout)\n      leavePush\n        .receive('ok', () => {\n          onClose()\n          resolve('ok')\n        })\n        .receive('timeout', () => {\n          onClose()\n          resolve('timed out')\n        })\n        .receive('error', () => {\n          resolve('error')\n        })\n\n      leavePush.send()\n      if (!this._canPush()) {\n        leavePush.trigger('ok', {})\n      }\n    }).finally(() => {\n      leavePush?.destroy()\n    })\n  }\n  /**\n   * Teardown the channel.\n   *\n   * Destroys and stops related timers.\n   */\n  teardown() {\n    this.pushBuffer.forEach((push: Push) => push.destroy())\n    this.rejoinTimer && clearTimeout(this.rejoinTimer.timer)\n    this.joinPush.destroy()\n  }\n\n  /** @internal */\n\n  async _fetchWithTimeout(\n    url: string,\n    options: { [key: string]: any },\n    timeout: number\n  ) {\n    const controller = new AbortController()\n    const id = setTimeout(() => controller.abort(), timeout)\n\n    const response = await this.socket.fetch(url, {\n      ...options,\n      signal: controller.signal,\n    })\n\n    clearTimeout(id)\n\n    return response\n  }\n\n  /** @internal */\n  _push(\n    event: string,\n    payload: { [key: string]: any },\n    timeout = this.timeout\n  ) {\n    if (!this.joinedOnce) {\n      throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`\n    }\n    let pushEvent = new Push(this, event, payload, timeout)\n    if (this._canPush()) {\n      pushEvent.send()\n    } else {\n      pushEvent.startTimeout()\n      this.pushBuffer.push(pushEvent)\n    }\n\n    return pushEvent\n  }\n\n  /**\n   * Overridable message hook\n   *\n   * Receives all events for specialized message handling before dispatching to the channel callbacks.\n   * Must return the payload, modified or unmodified.\n   *\n   * @internal\n   */\n  _onMessage(_event: string, payload: any, _ref?: string) {\n    return payload\n  }\n\n  /** @internal */\n  _isMember(topic: string): boolean {\n    return this.topic === topic\n  }\n\n  /** @internal */\n  _joinRef(): string {\n    return this.joinPush.ref\n  }\n\n  /** @internal */\n  _trigger(type: string, payload?: any, ref?: string) {\n    const typeLower = type.toLocaleLowerCase()\n    const { close, error, leave, join } = CHANNEL_EVENTS\n    const events: string[] = [close, error, leave, join]\n    if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n      return\n    }\n    let handledPayload = this._onMessage(typeLower, payload, ref)\n    if (payload && !handledPayload) {\n      throw 'channel onMessage callbacks must return the payload, modified or unmodified'\n    }\n\n    if (['insert', 'update', 'delete'].includes(typeLower)) {\n      this.bindings.postgres_changes\n        ?.filter((bind) => {\n          return (\n            bind.filter?.event === '*' ||\n            bind.filter?.event?.toLocaleLowerCase() === typeLower\n          )\n        })\n        .map((bind) => bind.callback(handledPayload, ref))\n    } else {\n      this.bindings[typeLower]\n        ?.filter((bind) => {\n          if (\n            ['broadcast', 'presence', 'postgres_changes'].includes(typeLower)\n          ) {\n            if ('id' in bind) {\n              const bindId = bind.id\n              const bindEvent = bind.filter?.event\n              return (\n                bindId &&\n                payload.ids?.includes(bindId) &&\n                (bindEvent === '*' ||\n                  bindEvent?.toLocaleLowerCase() ===\n                    payload.data?.type.toLocaleLowerCase())\n              )\n            } else {\n              const bindEvent = bind?.filter?.event?.toLocaleLowerCase()\n              return (\n                bindEvent === '*' ||\n                bindEvent === payload?.event?.toLocaleLowerCase()\n              )\n            }\n          } else {\n            return bind.type.toLocaleLowerCase() === typeLower\n          }\n        })\n        .map((bind) => {\n          if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n            const postgresChanges = handledPayload.data\n            const { schema, table, commit_timestamp, type, errors } =\n              postgresChanges\n            const enrichedPayload = {\n              schema: schema,\n              table: table,\n              commit_timestamp: commit_timestamp,\n              eventType: type,\n              new: {},\n              old: {},\n              errors: errors,\n            }\n            handledPayload = {\n              ...enrichedPayload,\n              ...this._getPayloadRecords(postgresChanges),\n            }\n          }\n          bind.callback(handledPayload, ref)\n        })\n    }\n  }\n\n  /** @internal */\n  _isClosed(): boolean {\n    return this.state === CHANNEL_STATES.closed\n  }\n\n  /** @internal */\n  _isJoined(): boolean {\n    return this.state === CHANNEL_STATES.joined\n  }\n\n  /** @internal */\n  _isJoining(): boolean {\n    return this.state === CHANNEL_STATES.joining\n  }\n\n  /** @internal */\n  _isLeaving(): boolean {\n    return this.state === CHANNEL_STATES.leaving\n  }\n\n  /** @internal */\n  _replyEventName(ref: string): string {\n    return `chan_reply_${ref}`\n  }\n\n  /** @internal */\n  _on(type: string, filter: { [key: string]: any }, callback: Function) {\n    const typeLower = type.toLocaleLowerCase()\n\n    const binding = {\n      type: typeLower,\n      filter: filter,\n      callback: callback,\n    }\n\n    if (this.bindings[typeLower]) {\n      this.bindings[typeLower].push(binding)\n    } else {\n      this.bindings[typeLower] = [binding]\n    }\n\n    return this\n  }\n\n  /** @internal */\n  _off(type: string, filter: { [key: string]: any }) {\n    const typeLower = type.toLocaleLowerCase()\n\n    this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n      return !(\n        bind.type?.toLocaleLowerCase() === typeLower &&\n        RealtimeChannel.isEqual(bind.filter, filter)\n      )\n    })\n    return this\n  }\n\n  /** @internal */\n  private static isEqual(\n    obj1: { [key: string]: string },\n    obj2: { [key: string]: string }\n  ) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n      return false\n    }\n\n    for (const k in obj1) {\n      if (obj1[k] !== obj2[k]) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  /** @internal */\n  private _rejoinUntilConnected() {\n    this.rejoinTimer.scheduleTimeout()\n    if (this.socket.isConnected()) {\n      this._rejoin()\n    }\n  }\n\n  /**\n   * Registers a callback that will be executed when the channel closes.\n   *\n   * @internal\n   */\n  private _onClose(callback: Function) {\n    this._on(CHANNEL_EVENTS.close, {}, callback)\n  }\n\n  /**\n   * Registers a callback that will be executed when the channel encounteres an error.\n   *\n   * @internal\n   */\n  private _onError(callback: Function) {\n    this._on(CHANNEL_EVENTS.error, {}, (reason: string) => callback(reason))\n  }\n\n  /**\n   * Returns `true` if the socket is connected and the channel has been joined.\n   *\n   * @internal\n   */\n  private _canPush(): boolean {\n    return this.socket.isConnected() && this._isJoined()\n  }\n\n  /** @internal */\n  private _rejoin(timeout = this.timeout): void {\n    if (this._isLeaving()) {\n      return\n    }\n    this.socket._leaveOpenTopic(this.topic)\n    this.state = CHANNEL_STATES.joining\n    this.joinPush.resend(timeout)\n  }\n\n  /** @internal */\n  private _getPayloadRecords(payload: any) {\n    const records = {\n      new: {},\n      old: {},\n    }\n\n    if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n      records.new = Transformers.convertChangeData(\n        payload.columns,\n        payload.record\n      )\n    }\n\n    if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n      records.old = Transformers.convertChangeData(\n        payload.columns,\n        payload.old_record\n      )\n    }\n\n    return records\n  }\n}\n", "import { DEFAULT_TIMEOUT } from '../lib/constants'\nimport type RealtimeChannel from '../RealtimeChannel'\n\nexport default class Push {\n  sent: boolean = false\n  timeoutTimer: number | undefined = undefined\n  ref: string = ''\n  receivedResp: {\n    status: string\n    response: { [key: string]: any }\n  } | null = null\n  recHooks: {\n    status: string\n    callback: Function\n  }[] = []\n  refEvent: string | null = null\n\n  /**\n   * Initializes the Push\n   *\n   * @param channel The Channel\n   * @param event The event, for example `\"phx_join\"`\n   * @param payload The payload, for example `{user_id: 123}`\n   * @param timeout The push timeout in milliseconds\n   */\n  constructor(\n    public channel: RealtimeChannel,\n    public event: string,\n    public payload: { [key: string]: any } = {},\n    public timeout: number = DEFAULT_TIMEOUT\n  ) {}\n\n  resend(timeout: number) {\n    this.timeout = timeout\n    this._cancelRefEvent()\n    this.ref = ''\n    this.refEvent = null\n    this.receivedResp = null\n    this.sent = false\n    this.send()\n  }\n\n  send() {\n    if (this._hasReceived('timeout')) {\n      return\n    }\n    this.startTimeout()\n    this.sent = true\n    this.channel.socket.push({\n      topic: this.channel.topic,\n      event: this.event,\n      payload: this.payload,\n      ref: this.ref,\n      join_ref: this.channel._joinRef(),\n    })\n  }\n\n  updatePayload(payload: { [key: string]: any }): void {\n    this.payload = { ...this.payload, ...payload }\n  }\n\n  receive(status: string, callback: Function) {\n    if (this._hasReceived(status)) {\n      callback(this.receivedResp?.response)\n    }\n\n    this.recHooks.push({ status, callback })\n    return this\n  }\n\n  startTimeout() {\n    if (this.timeoutTimer) {\n      return\n    }\n    this.ref = this.channel.socket._makeRef()\n    this.refEvent = this.channel._replyEventName(this.ref)\n\n    const callback = (payload: any) => {\n      this._cancelRefEvent()\n      this._cancelTimeout()\n      this.receivedResp = payload\n      this._matchReceive(payload)\n    }\n\n    this.channel._on(this.refEvent, {}, callback)\n\n    this.timeoutTimer = <any>setTimeout(() => {\n      this.trigger('timeout', {})\n    }, this.timeout)\n  }\n\n  trigger(status: string, response: any) {\n    if (this.refEvent)\n      this.channel._trigger(this.refEvent, { status, response })\n  }\n\n  destroy() {\n    this._cancelRefEvent()\n    this._cancelTimeout()\n  }\n\n  private _cancelRefEvent() {\n    if (!this.refEvent) {\n      return\n    }\n\n    this.channel._off(this.refEvent, {})\n  }\n\n  private _cancelTimeout() {\n    clearTimeout(this.timeoutTimer)\n    this.timeoutTimer = undefined\n  }\n\n  private _matchReceive({\n    status,\n    response,\n  }: {\n    status: string\n    response: Function\n  }) {\n    this.recHooks\n      .filter((h) => h.status === status)\n      .forEach((h) => h.callback(response))\n  }\n\n  private _hasReceived(status: string) {\n    return this.receivedResp && this.receivedResp.status === status\n  }\n}\n", "/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\n\nimport type {\n  PresenceOpts,\n  PresenceOnJoinCallback,\n  PresenceOnLeaveCallback,\n} from 'phoenix'\nimport type RealtimeChannel from './RealtimeChannel'\n\ntype Presence<T extends { [key: string]: any } = {}> = {\n  presence_ref: string\n} & T\n\nexport type RealtimePresenceState<T extends { [key: string]: any } = {}> = {\n  [key: string]: Presence<T>[]\n}\n\nexport type RealtimePresenceJoinPayload<T extends { [key: string]: any }> = {\n  event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}`\n  key: string\n  currentPresences: Presence<T>[]\n  newPresences: Presence<T>[]\n}\n\nexport type RealtimePresenceLeavePayload<T extends { [key: string]: any }> = {\n  event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}`\n  key: string\n  currentPresences: Presence<T>[]\n  leftPresences: Presence<T>[]\n}\n\nexport enum REALTIME_PRESENCE_LISTEN_EVENTS {\n  SYNC = 'sync',\n  JOIN = 'join',\n  LEAVE = 'leave',\n}\n\ntype PresenceDiff = {\n  joins: RealtimePresenceState\n  leaves: RealtimePresenceState\n}\n\ntype RawPresenceState = {\n  [key: string]: {\n    metas: {\n      phx_ref?: string\n      phx_ref_prev?: string\n      [key: string]: any\n    }[]\n  }\n}\n\ntype RawPresenceDiff = {\n  joins: RawPresenceState\n  leaves: RawPresenceState\n}\n\ntype PresenceChooser<T> = (key: string, presences: Presence[]) => T\n\nexport default class RealtimePresence {\n  state: RealtimePresenceState = {}\n  pendingDiffs: RawPresenceDiff[] = []\n  joinRef: string | null = null\n  caller: {\n    onJoin: PresenceOnJoinCallback\n    onLeave: PresenceOnLeaveCallback\n    onSync: () => void\n  } = {\n    onJoin: () => {},\n    onLeave: () => {},\n    onSync: () => {},\n  }\n\n  /**\n   * Initializes the Presence.\n   *\n   * @param channel - The RealtimeChannel\n   * @param opts - The options,\n   *        for example `{events: {state: 'state', diff: 'diff'}}`\n   */\n  constructor(public channel: RealtimeChannel, opts?: PresenceOpts) {\n    const events = opts?.events || {\n      state: 'presence_state',\n      diff: 'presence_diff',\n    }\n\n    this.channel._on(events.state, {}, (newState: RawPresenceState) => {\n      const { onJoin, onLeave, onSync } = this.caller\n\n      this.joinRef = this.channel._joinRef()\n\n      this.state = RealtimePresence.syncState(\n        this.state,\n        newState,\n        onJoin,\n        onLeave\n      )\n\n      this.pendingDiffs.forEach((diff) => {\n        this.state = RealtimePresence.syncDiff(\n          this.state,\n          diff,\n          onJoin,\n          onLeave\n        )\n      })\n\n      this.pendingDiffs = []\n\n      onSync()\n    })\n\n    this.channel._on(events.diff, {}, (diff: RawPresenceDiff) => {\n      const { onJoin, onLeave, onSync } = this.caller\n\n      if (this.inPendingSyncState()) {\n        this.pendingDiffs.push(diff)\n      } else {\n        this.state = RealtimePresence.syncDiff(\n          this.state,\n          diff,\n          onJoin,\n          onLeave\n        )\n\n        onSync()\n      }\n    })\n\n    this.onJoin((key, currentPresences, newPresences) => {\n      this.channel._trigger('presence', {\n        event: 'join',\n        key,\n        currentPresences,\n        newPresences,\n      })\n    })\n\n    this.onLeave((key, currentPresences, leftPresences) => {\n      this.channel._trigger('presence', {\n        event: 'leave',\n        key,\n        currentPresences,\n        leftPresences,\n      })\n    })\n\n    this.onSync(() => {\n      this.channel._trigger('presence', { event: 'sync' })\n    })\n  }\n\n  /**\n   * Used to sync the list of presences on the server with the\n   * client's state.\n   *\n   * An optional `onJoin` and `onLeave` callback can be provided to\n   * react to changes in the client's local presences across\n   * disconnects and reconnects with the server.\n   *\n   * @internal\n   */\n  private static syncState(\n    currentState: RealtimePresenceState,\n    newState: RawPresenceState | RealtimePresenceState,\n    onJoin: PresenceOnJoinCallback,\n    onLeave: PresenceOnLeaveCallback\n  ): RealtimePresenceState {\n    const state = this.cloneDeep(currentState)\n    const transformedState = this.transformState(newState)\n    const joins: RealtimePresenceState = {}\n    const leaves: RealtimePresenceState = {}\n\n    this.map(state, (key: string, presences: Presence[]) => {\n      if (!transformedState[key]) {\n        leaves[key] = presences\n      }\n    })\n\n    this.map(transformedState, (key, newPresences: Presence[]) => {\n      const currentPresences: Presence[] = state[key]\n\n      if (currentPresences) {\n        const newPresenceRefs = newPresences.map(\n          (m: Presence) => m.presence_ref\n        )\n        const curPresenceRefs = currentPresences.map(\n          (m: Presence) => m.presence_ref\n        )\n        const joinedPresences: Presence[] = newPresences.filter(\n          (m: Presence) => curPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n        const leftPresences: Presence[] = currentPresences.filter(\n          (m: Presence) => newPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n\n        if (joinedPresences.length > 0) {\n          joins[key] = joinedPresences\n        }\n\n        if (leftPresences.length > 0) {\n          leaves[key] = leftPresences\n        }\n      } else {\n        joins[key] = newPresences\n      }\n    })\n\n    return this.syncDiff(state, { joins, leaves }, onJoin, onLeave)\n  }\n\n  /**\n   * Used to sync a diff of presence join and leave events from the\n   * server, as they happen.\n   *\n   * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n   * `onLeave` callbacks to react to a user joining or leaving from a\n   * device.\n   *\n   * @internal\n   */\n  private static syncDiff(\n    state: RealtimePresenceState,\n    diff: RawPresenceDiff | PresenceDiff,\n    onJoin: PresenceOnJoinCallback,\n    onLeave: PresenceOnLeaveCallback\n  ): RealtimePresenceState {\n    const { joins, leaves } = {\n      joins: this.transformState(diff.joins),\n      leaves: this.transformState(diff.leaves),\n    }\n\n    if (!onJoin) {\n      onJoin = () => {}\n    }\n\n    if (!onLeave) {\n      onLeave = () => {}\n    }\n\n    this.map(joins, (key, newPresences: Presence[]) => {\n      const currentPresences: Presence[] = state[key] ?? []\n      state[key] = this.cloneDeep(newPresences)\n\n      if (currentPresences.length > 0) {\n        const joinedPresenceRefs = state[key].map(\n          (m: Presence) => m.presence_ref\n        )\n        const curPresences: Presence[] = currentPresences.filter(\n          (m: Presence) => joinedPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n\n        state[key].unshift(...curPresences)\n      }\n\n      onJoin(key, currentPresences, newPresences)\n    })\n\n    this.map(leaves, (key, leftPresences: Presence[]) => {\n      let currentPresences: Presence[] = state[key]\n\n      if (!currentPresences) return\n\n      const presenceRefsToRemove = leftPresences.map(\n        (m: Presence) => m.presence_ref\n      )\n      currentPresences = currentPresences.filter(\n        (m: Presence) => presenceRefsToRemove.indexOf(m.presence_ref) < 0\n      )\n\n      state[key] = currentPresences\n\n      onLeave(key, currentPresences, leftPresences)\n\n      if (currentPresences.length === 0) delete state[key]\n    })\n\n    return state\n  }\n\n  /** @internal */\n  private static map<T = any>(\n    obj: RealtimePresenceState,\n    func: PresenceChooser<T>\n  ): T[] {\n    return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]))\n  }\n\n  /**\n   * Remove 'metas' key\n   * Change 'phx_ref' to 'presence_ref'\n   * Remove 'phx_ref' and 'phx_ref_prev'\n   *\n   * @example\n   * // returns {\n   *  abc123: [\n   *    { presence_ref: '2', user_id: 1 },\n   *    { presence_ref: '3', user_id: 2 }\n   *  ]\n   * }\n   * RealtimePresence.transformState({\n   *  abc123: {\n   *    metas: [\n   *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n   *      { phx_ref: '3', user_id: 2 }\n   *    ]\n   *  }\n   * })\n   *\n   * @internal\n   */\n  private static transformState(\n    state: RawPresenceState | RealtimePresenceState\n  ): RealtimePresenceState {\n    state = this.cloneDeep(state)\n\n    return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n      const presences = state[key]\n\n      if ('metas' in presences) {\n        newState[key] = presences.metas.map((presence) => {\n          presence['presence_ref'] = presence['phx_ref']\n\n          delete presence['phx_ref']\n          delete presence['phx_ref_prev']\n\n          return presence\n        }) as Presence[]\n      } else {\n        newState[key] = presences\n      }\n\n      return newState\n    }, {} as RealtimePresenceState)\n  }\n\n  /** @internal */\n  private static cloneDeep(obj: { [key: string]: any }) {\n    return JSON.parse(JSON.stringify(obj))\n  }\n\n  /** @internal */\n  private onJoin(callback: PresenceOnJoinCallback): void {\n    this.caller.onJoin = callback\n  }\n\n  /** @internal */\n  private onLeave(callback: PresenceOnLeaveCallback): void {\n    this.caller.onLeave = callback\n  }\n\n  /** @internal */\n  private onSync(callback: () => void): void {\n    this.caller.onSync = callback\n  }\n\n  /** @internal */\n  private inPendingSyncState(): boolean {\n    return !this.joinRef || this.joinRef !== this.channel._joinRef()\n  }\n}\n", "export { StorageClient as StorageClient } from './StorageClient'\nexport * from './lib/types'\nexport * from './lib/errors'\n", "import StorageFileApi from './packages/StorageFileApi'\nimport StorageBucketApi from './packages/StorageBucketApi'\nimport { Fetch } from './lib/fetch'\n\nexport interface StorageClientOptions {\n  useNewHostname?: boolean\n}\n\nexport class StorageClient extends StorageBucketApi {\n  constructor(\n    url: string,\n    headers: { [key: string]: string } = {},\n    fetch?: Fetch,\n    opts?: StorageClientOptions\n  ) {\n    super(url, headers, fetch, opts)\n  }\n\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id: string): StorageFileApi {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch)\n  }\n}\n", "export class StorageError extends Error {\n  protected __isStorageError = true\n\n  constructor(message: string) {\n    super(message)\n    this.name = 'StorageError'\n  }\n}\n\nexport function isStorageError(error: unknown): error is StorageError {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error\n}\n\nexport class StorageApiError extends StorageError {\n  status: number\n  statusCode: string\n\n  constructor(message: string, status: number, statusCode: string) {\n    super(message)\n    this.name = 'StorageApiError'\n    this.status = status\n    this.statusCode = statusCode\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      statusCode: this.statusCode,\n    }\n  }\n}\n\nexport class StorageUnknownError extends StorageError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'StorageUnknownError'\n    this.originalError = originalError\n  }\n}\n", "type Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const resolveResponse = async (): Promise<typeof Response> => {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (await import('@supabase/node-fetch' as any)).Response\n  }\n\n  return Response\n}\n\nexport const recursiveToCamel = (item: Record<string, any>): unknown => {\n  if (Array.isArray(item)) {\n    return item.map((el) => recursiveToCamel(el))\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item\n  }\n\n  const result: Record<string, any> = {}\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''))\n    result[newKey] = recursiveToCamel(value)\n  })\n\n  return result\n}\n\n/**\n * Determine if input is a plain object\n * An object is plain if it's created by either {}, new Object(), or Object.create(null)\n * source: https://github.com/sindresorhus/is-plain-obj\n */\nexport const isPlainObject = (value: object): boolean => {\n  if (typeof value !== 'object' || value === null) {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n  return (\n    (prototype === null ||\n      prototype === Object.prototype ||\n      Object.getPrototypeOf(prototype) === null) &&\n    !(Symbol.toStringTag in value) &&\n    !(Symbol.iterator in value)\n  )\n}\n", "import { version } from './version'\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `storage-js/${version}` }\n", "// generated by genversion\nexport const version = '2.10.4'\n", "// constants.ts\nimport { RealtimeClientOptions } from '@supabase/realtime-js'\nimport { SupabaseAuthClientOptions } from './types'\nimport { version } from './version'\n\nlet JS_ENV = ''\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n  JS_ENV = 'deno'\n} else if (typeof document !== 'undefined') {\n  JS_ENV = 'web'\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n  JS_ENV = 'react-native'\n} else {\n  JS_ENV = 'node'\n}\n\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${version}` }\n\nexport const DEFAULT_GLOBAL_OPTIONS = {\n  headers: DEFAULT_HEADERS,\n}\n\nexport const DEFAULT_DB_OPTIONS = {\n  schema: 'public',\n}\n\nexport const DEFAULT_AUTH_OPTIONS: SupabaseAuthClientOptions = {\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  flowType: 'implicit',\n}\n\nexport const DEFAULT_REALTIME_OPTIONS: RealtimeClientOptions = {}\n", "export const version = '2.53.0'\n", "import { AuthClient } from '@supabase/auth-js'\nimport { SupabaseAuthClientOptions } from './types'\n\nexport class SupabaseAuthClient extends AuthClient {\n  constructor(options: SupabaseAuthClientOptions) {\n    super(options)\n  }\n}\n", "import GoTrueAdminApi from './GoTrueAdminApi'\nimport GoTrueClient from './GoTrueClient'\nimport AuthAdmin<PERSON>pi from './AuthAdminApi'\nimport AuthClient from './AuthClient'\nexport { GoTrueAdminApi, GoTrueClient, AuthAdminApi, AuthClient }\nexport * from './lib/types'\nexport * from './lib/errors'\nexport {\n  navigatorLock,\n  NavigatorLockAcquireTimeoutError,\n  internals as lockInternals,\n  processLock,\n} from './lib/locks'\n", "import { version } from './version'\n\n/** Current session will be checked for refresh at this interval. */\nexport const AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000\n\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nexport const AUTO_REFRESH_TICK_THRESHOLD = 3\n\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nexport const EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS\n\nexport const GOTRUE_URL = 'http://localhost:9999'\nexport const STORAGE_KEY = 'supabase.auth.token'\nexport const AUDIENCE = ''\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${version}` }\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2, // in deciseconds\n}\n\nexport const API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version'\nexport const API_VERSIONS = {\n  '2024-01-01': {\n    timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n    name: '2024-01-01',\n  },\n}\n\nexport const BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i\n\nexport const JWKS_TTL = 10 * 60 * 1000 // 10 minutes\n", "export const version = '2.71.1'\n", "import { API_VERSION_HEADER_NAME, BASE64URL_REGEX } from './constants'\nimport { AuthInvalidJwtError } from './errors'\nimport { base64UrlToUint8Array, stringFromBase64URL } from './base64url'\nimport { JwtHeader, JwtPayload, SupportedStorage, User } from './types'\n\nexport function expiresAt(expiresIn: number) {\n  const timeNow = Math.round(Date.now() / 1000)\n  return timeNow + expiresIn\n}\n\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8\n    return v.toString(16)\n  })\n}\n\nexport const isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined'\n\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false,\n}\n\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false\n  }\n\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false\n  }\n\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable\n  }\n\n  const randomKey = `lswt-${Math.random()}${Math.random()}`\n\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey)\n    globalThis.localStorage.removeItem(randomKey)\n\n    localStorageWriteTests.tested = true\n    localStorageWriteTests.writable = true\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n\n    localStorageWriteTests.tested = true\n    localStorageWriteTests.writable = false\n  }\n\n  return localStorageWriteTests.writable\n}\n\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href: string) {\n  const result: { [parameter: string]: string } = {}\n\n  const url = new URL(href)\n\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1))\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value\n      })\n    } catch (e: any) {\n      // hash is not a query string\n    }\n  }\n\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value\n  })\n\n  return result\n}\n\ntype Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const looksLikeFetchResponse = (maybeResponse: unknown): maybeResponse is Response => {\n  return (\n    typeof maybeResponse === 'object' &&\n    maybeResponse !== null &&\n    'status' in maybeResponse &&\n    'ok' in maybeResponse &&\n    'json' in maybeResponse &&\n    typeof (maybeResponse as any).json === 'function'\n  )\n}\n\n// Storage helpers\nexport const setItemAsync = async (\n  storage: SupportedStorage,\n  key: string,\n  data: any\n): Promise<void> => {\n  await storage.setItem(key, JSON.stringify(data))\n}\n\nexport const getItemAsync = async (storage: SupportedStorage, key: string): Promise<unknown> => {\n  const value = await storage.getItem(key)\n\n  if (!value) {\n    return null\n  }\n\n  try {\n    return JSON.parse(value)\n  } catch {\n    return value\n  }\n}\n\nexport const removeItemAsync = async (storage: SupportedStorage, key: string): Promise<void> => {\n  await storage.removeItem(key)\n}\n\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred<T = any> {\n  public static promiseConstructor: PromiseConstructor = Promise\n\n  public readonly promise!: PromiseLike<T>\n\n  public readonly resolve!: (value?: T | PromiseLike<T>) => void\n\n  public readonly reject!: (reason?: any) => any\n\n  public constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;(this as any).promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;(this as any).resolve = res\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;(this as any).reject = rej\n    })\n  }\n}\n\nexport function decodeJWT(token: string): {\n  header: JwtHeader\n  payload: JwtPayload\n  signature: Uint8Array\n  raw: {\n    header: string\n    payload: string\n  }\n} {\n  const parts = token.split('.')\n\n  if (parts.length !== 3) {\n    throw new AuthInvalidJwtError('Invalid JWT structure')\n  }\n\n  // Regex checks for base64url format\n  for (let i = 0; i < parts.length; i++) {\n    if (!BASE64URL_REGEX.test(parts[i] as string)) {\n      throw new AuthInvalidJwtError('JWT not in base64url format')\n    }\n  }\n  const data = {\n    // using base64url lib\n    header: JSON.parse(stringFromBase64URL(parts[0])),\n    payload: JSON.parse(stringFromBase64URL(parts[1])),\n    signature: base64UrlToUint8Array(parts[2]),\n    raw: {\n      header: parts[0],\n      payload: parts[1],\n    },\n  }\n  return data\n}\n\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time: number): Promise<null> {\n  return await new Promise((accept) => {\n    setTimeout(() => accept(null), time)\n  })\n}\n\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable<T>(\n  fn: (attempt: number) => Promise<T>,\n  isRetryable: (attempt: number, error: any | null, result?: T) => boolean\n): Promise<T> {\n  const promise = new Promise<T>((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;(async () => {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = await fn(attempt)\n\n          if (!isRetryable(attempt, null, result)) {\n            accept(result)\n            return\n          }\n        } catch (e: any) {\n          if (!isRetryable(attempt, e)) {\n            reject(e)\n            return\n          }\n        }\n      }\n    })()\n  })\n\n  return promise\n}\n\nfunction dec2hex(dec: number) {\n  return ('0' + dec.toString(16)).substr(-2)\n}\n\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56\n  const array = new Uint32Array(verifierLength)\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'\n    const charSetLen = charSet.length\n    let verifier = ''\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen))\n    }\n    return verifier\n  }\n  crypto.getRandomValues(array)\n  return Array.from(array, dec2hex).join('')\n}\n\nasync function sha256(randomString: string) {\n  const encoder = new TextEncoder()\n  const encodedData = encoder.encode(randomString)\n  const hash = await crypto.subtle.digest('SHA-256', encodedData)\n  const bytes = new Uint8Array(hash)\n\n  return Array.from(bytes)\n    .map((c) => String.fromCharCode(c))\n    .join('')\n}\n\nexport async function generatePKCEChallenge(verifier: string) {\n  const hasCryptoSupport =\n    typeof crypto !== 'undefined' &&\n    typeof crypto.subtle !== 'undefined' &&\n    typeof TextEncoder !== 'undefined'\n\n  if (!hasCryptoSupport) {\n    console.warn(\n      'WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.'\n    )\n    return verifier\n  }\n  const hashed = await sha256(verifier)\n  return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '')\n}\n\nexport async function getCodeChallengeAndMethod(\n  storage: SupportedStorage,\n  storageKey: string,\n  isPasswordRecovery = false\n) {\n  const codeVerifier = generatePKCEVerifier()\n  let storedCodeVerifier = codeVerifier\n  if (isPasswordRecovery) {\n    storedCodeVerifier += '/PASSWORD_RECOVERY'\n  }\n  await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier)\n  const codeChallenge = await generatePKCEChallenge(codeVerifier)\n  const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256'\n  return [codeChallenge, codeChallengeMethod]\n}\n\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i\n\nexport function parseResponseAPIVersion(response: Response) {\n  const apiVersion = response.headers.get(API_VERSION_HEADER_NAME)\n\n  if (!apiVersion) {\n    return null\n  }\n\n  if (!apiVersion.match(API_VERSION_REGEX)) {\n    return null\n  }\n\n  try {\n    const date = new Date(`${apiVersion}T00:00:00.0Z`)\n    return date\n  } catch (e: any) {\n    return null\n  }\n}\n\nexport function validateExp(exp: number) {\n  if (!exp) {\n    throw new Error('Missing exp claim')\n  }\n  const timeNow = Math.floor(Date.now() / 1000)\n  if (exp <= timeNow) {\n    throw new Error('JWT has expired')\n  }\n}\n\nexport function getAlgorithm(\n  alg: 'HS256' | 'RS256' | 'ES256'\n): RsaHashedImportParams | EcKeyImportParams {\n  switch (alg) {\n    case 'RS256':\n      return {\n        name: 'RSASSA-PKCS1-v1_5',\n        hash: { name: 'SHA-256' },\n      }\n    case 'ES256':\n      return {\n        name: 'ECDSA',\n        namedCurve: 'P-256',\n        hash: { name: 'SHA-256' },\n      }\n    default:\n      throw new Error('Invalid alg claim')\n  }\n}\n\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/\n\nexport function validateUUID(str: string) {\n  if (!UUID_REGEX.test(str)) {\n    throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not')\n  }\n}\n\nexport function userNotAvailableProxy(): User {\n  const proxyTarget = {} as User\n\n  return new Proxy(proxyTarget, {\n    get: (target: any, prop: string) => {\n      if (prop === '__isUserNotAvailableProxy') {\n        return true\n      }\n      // Preventative check for common problematic symbols during cloning/inspection\n      // These symbols might be accessed by structuredClone or other internal mechanisms.\n      if (typeof prop === 'symbol') {\n        const sProp = (prop as symbol).toString()\n        if (\n          sProp === 'Symbol(Symbol.toPrimitive)' ||\n          sProp === 'Symbol(Symbol.toStringTag)' ||\n          sProp === 'Symbol(util.inspect.custom)'\n        ) {\n          // Node.js util.inspect\n          return undefined\n        }\n      }\n      throw new Error(\n        `@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the \"${prop}\" property of the session object is not supported. Please use getUser() instead.`\n      )\n    },\n    set: (_target: any, prop: string) => {\n      throw new Error(\n        `@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`\n      )\n    },\n    deleteProperty: (_target: any, prop: string) => {\n      throw new Error(\n        `@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`\n      )\n    },\n  })\n}\n\n/**\n * Deep clones a JSON-serializable object using JSON.parse(JSON.stringify(obj)).\n * Note: Only works for JSON-safe data.\n */\nexport function deepClone<T>(obj: T): T {\n  return JSON.parse(JSON.stringify(obj))\n}\n", "import { WeakPasswordReasons } from './types'\nimport { ErrorCode } from './error-codes'\n\nexport class AuthError extends Error {\n  /**\n   * Error code associated with the error. Most errors coming from\n   * HTTP responses will have a code, though some errors that occur\n   * before a response is received will not have one present. In that\n   * case {@link #status} will also be undefined.\n   */\n  code: ErrorCode | (string & {}) | undefined\n\n  /** HTTP status code that caused the error. */\n  status: number | undefined\n\n  protected __isAuthError = true\n\n  constructor(message: string, status?: number, code?: string) {\n    super(message)\n    this.name = 'AuthError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport function isAuthError(error: unknown): error is AuthError {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error\n}\n\nexport class AuthApiError extends AuthError {\n  status: number\n\n  constructor(message: string, status: number, code: string | undefined) {\n    super(message, status, code)\n    this.name = 'AuthApiError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport function isAuthApiError(error: unknown): error is AuthApiError {\n  return isAuthError(error) && error.name === 'AuthApiError'\n}\n\nexport class AuthUnknownError extends AuthError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'AuthUnknownError'\n    this.originalError = originalError\n  }\n}\n\nexport class CustomAuthError extends AuthError {\n  name: string\n  status: number\n\n  constructor(message: string, name: string, status: number, code: string | undefined) {\n    super(message, status, code)\n    this.name = name\n    this.status = status\n  }\n}\n\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400, undefined)\n  }\n}\n\nexport function isAuthSessionMissingError(error: any): error is AuthSessionMissingError {\n  return isAuthError(error) && error.name === 'AuthSessionMissingError'\n}\n\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined)\n  }\n}\n\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message: string) {\n    super(message, 'AuthInvalidCredentialsError', 400, undefined)\n  }\n}\n\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  details: { error: string; code: string } | null = null\n  constructor(message: string, details: { error: string; code: string } | null = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500, undefined)\n    this.details = details\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details,\n    }\n  }\n}\n\nexport function isAuthImplicitGrantRedirectError(\n  error: any\n): error is AuthImplicitGrantRedirectError {\n  return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError'\n}\n\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  details: { error: string; code: string } | null = null\n\n  constructor(message: string, details: { error: string; code: string } | null = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined)\n    this.details = details\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details,\n    }\n  }\n}\n\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message: string, status: number) {\n    super(message, 'AuthRetryableFetchError', status, undefined)\n  }\n}\n\nexport function isAuthRetryableFetchError(error: unknown): error is AuthRetryableFetchError {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError'\n}\n\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  /**\n   * Reasons why the password is deemed weak.\n   */\n  reasons: WeakPasswordReasons[]\n\n  constructor(message: string, status: number, reasons: string[]) {\n    super(message, 'AuthWeakPasswordError', status, 'weak_password')\n\n    this.reasons = reasons\n  }\n}\n\nexport function isAuthWeakPasswordError(error: unknown): error is AuthWeakPasswordError {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError'\n}\n\nexport class AuthInvalidJwtError extends CustomAuthError {\n  constructor(message: string) {\n    super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt')\n  }\n}\n", "/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('')\n\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('')\n\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n  const charMap: number[] = new Array(128)\n\n  for (let i = 0; i < charMap.length; i += 1) {\n    charMap[i] = -1\n  }\n\n  for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n    charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2\n  }\n\n  for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n    charMap[TO_BASE64URL[i].charCodeAt(0)] = i\n  }\n\n  return charMap\n})()\n\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nexport function byteToBase64URL(\n  byte: number | null,\n  state: { queue: number; queuedBits: number },\n  emit: (char: string) => void\n) {\n  if (byte !== null) {\n    state.queue = (state.queue << 8) | byte\n    state.queuedBits += 8\n\n    while (state.queuedBits >= 6) {\n      const pos = (state.queue >> (state.queuedBits - 6)) & 63\n      emit(TO_BASE64URL[pos])\n      state.queuedBits -= 6\n    }\n  } else if (state.queuedBits > 0) {\n    state.queue = state.queue << (6 - state.queuedBits)\n    state.queuedBits = 6\n\n    while (state.queuedBits >= 6) {\n      const pos = (state.queue >> (state.queuedBits - 6)) & 63\n      emit(TO_BASE64URL[pos])\n      state.queuedBits -= 6\n    }\n  }\n}\n\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nexport function byteFromBase64URL(\n  charCode: number,\n  state: { queue: number; queuedBits: number },\n  emit: (byte: number) => void\n) {\n  const bits = FROM_BASE64URL[charCode]\n\n  if (bits > -1) {\n    // valid Base64-URL character\n    state.queue = (state.queue << 6) | bits\n    state.queuedBits += 6\n\n    while (state.queuedBits >= 8) {\n      emit((state.queue >> (state.queuedBits - 8)) & 0xff)\n      state.queuedBits -= 8\n    }\n  } else if (bits === -2) {\n    // ignore spaces, tabs, newlines, =\n    return\n  } else {\n    throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`)\n  }\n}\n\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nexport function stringToBase64URL(str: string) {\n  const base64: string[] = []\n\n  const emitter = (char: string) => {\n    base64.push(char)\n  }\n\n  const state = { queue: 0, queuedBits: 0 }\n\n  stringToUTF8(str, (byte: number) => {\n    byteToBase64URL(byte, state, emitter)\n  })\n\n  byteToBase64URL(null, state, emitter)\n\n  return base64.join('')\n}\n\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nexport function stringFromBase64URL(str: string) {\n  const conv: string[] = []\n\n  const utf8Emit = (codepoint: number) => {\n    conv.push(String.fromCodePoint(codepoint))\n  }\n\n  const utf8State = {\n    utf8seq: 0,\n    codepoint: 0,\n  }\n\n  const b64State = { queue: 0, queuedBits: 0 }\n\n  const byteEmit = (byte: number) => {\n    stringFromUTF8(byte, utf8State, utf8Emit)\n  }\n\n  for (let i = 0; i < str.length; i += 1) {\n    byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit)\n  }\n\n  return conv.join('')\n}\n\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nexport function codepointToUTF8(codepoint: number, emit: (byte: number) => void) {\n  if (codepoint <= 0x7f) {\n    emit(codepoint)\n    return\n  } else if (codepoint <= 0x7ff) {\n    emit(0xc0 | (codepoint >> 6))\n    emit(0x80 | (codepoint & 0x3f))\n    return\n  } else if (codepoint <= 0xffff) {\n    emit(0xe0 | (codepoint >> 12))\n    emit(0x80 | ((codepoint >> 6) & 0x3f))\n    emit(0x80 | (codepoint & 0x3f))\n    return\n  } else if (codepoint <= 0x10ffff) {\n    emit(0xf0 | (codepoint >> 18))\n    emit(0x80 | ((codepoint >> 12) & 0x3f))\n    emit(0x80 | ((codepoint >> 6) & 0x3f))\n    emit(0x80 | (codepoint & 0x3f))\n    return\n  }\n\n  throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`)\n}\n\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nexport function stringToUTF8(str: string, emit: (byte: number) => void) {\n  for (let i = 0; i < str.length; i += 1) {\n    let codepoint = str.charCodeAt(i)\n\n    if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n      // most UTF-16 codepoints are Unicode codepoints, except values in this\n      // range where the next UTF-16 codepoint needs to be combined with the\n      // current one to get the Unicode codepoint\n      const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff\n      const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff\n      codepoint = (lowSurrogate | highSurrogate) + 0x10000\n      i += 1\n    }\n\n    codepointToUTF8(codepoint, emit)\n  }\n}\n\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nexport function stringFromUTF8(\n  byte: number,\n  state: { utf8seq: number; codepoint: number },\n  emit: (codepoint: number) => void\n) {\n  if (state.utf8seq === 0) {\n    if (byte <= 0x7f) {\n      emit(byte)\n      return\n    }\n\n    // count the number of 1 leading bits until you reach 0\n    for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n      if (((byte >> (7 - leadingBit)) & 1) === 0) {\n        state.utf8seq = leadingBit\n        break\n      }\n    }\n\n    if (state.utf8seq === 2) {\n      state.codepoint = byte & 31\n    } else if (state.utf8seq === 3) {\n      state.codepoint = byte & 15\n    } else if (state.utf8seq === 4) {\n      state.codepoint = byte & 7\n    } else {\n      throw new Error('Invalid UTF-8 sequence')\n    }\n\n    state.utf8seq -= 1\n  } else if (state.utf8seq > 0) {\n    if (byte <= 0x7f) {\n      throw new Error('Invalid UTF-8 sequence')\n    }\n\n    state.codepoint = (state.codepoint << 6) | (byte & 63)\n    state.utf8seq -= 1\n\n    if (state.utf8seq === 0) {\n      emit(state.codepoint)\n    }\n  }\n}\n\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\n\nexport function base64UrlToUint8Array(str: string): Uint8Array {\n  const result: number[] = []\n  const state = { queue: 0, queuedBits: 0 }\n\n  const onByte = (byte: number) => {\n    result.push(byte)\n  }\n\n  for (let i = 0; i < str.length; i += 1) {\n    byteFromBase64URL(str.charCodeAt(i), state, onByte)\n  }\n\n  return new Uint8Array(result)\n}\n\nexport function stringToUint8Array(str: string): Uint8Array {\n  const result: number[] = []\n  stringToUTF8(str, (byte: number) => result.push(byte))\n  return new Uint8Array(result)\n}\n\nexport function bytesToBase64URL(bytes: Uint8Array) {\n  const result: string[] = []\n  const state = { queue: 0, queuedBits: 0 }\n\n  const onChar = (char: string) => {\n    result.push(char)\n  }\n\n  bytes.forEach((byte) => byteToBase64URL(byte, state, onChar))\n\n  // always call with `null` after processing all bytes\n  byteToBase64URL(null, state, onChar)\n\n  return result.join('')\n}\n", "import { API_VERSIONS, API_VERSION_HEADER_NAME } from './constants'\nimport { expiresAt, looksLikeFetchResponse, parseResponseAPIVersion } from './helpers'\nimport {\n  AuthResponse,\n  AuthResponsePassword,\n  SSOResponse,\n  GenerateLinkProperties,\n  GenerateLinkResponse,\n  User,\n  UserResponse,\n} from './types'\nimport {\n  AuthApiError,\n  AuthRetryableFetchError,\n  AuthWeakPasswordError,\n  AuthUnknownError,\n  AuthSessionMissingError,\n} from './errors'\n\nexport type Fetch = typeof fetch\n\nexport interface FetchOptions {\n  headers?: {\n    [key: string]: string\n  }\n  noResolveJson?: boolean\n}\n\nexport interface FetchParameters {\n  signal?: AbortSignal\n}\n\nexport type RequestMethodType = 'GET' | 'POST' | 'PUT' | 'DELETE'\n\nconst _getErrorMessage = (err: any): string =>\n  err.msg || err.message || err.error_description || err.error || JSON.stringify(err)\n\nconst NETWORK_ERROR_CODES = [502, 503, 504]\n\nexport async function handleError(error: unknown) {\n  if (!looksLikeFetchResponse(error)) {\n    throw new AuthRetryableFetchError(_getErrorMessage(error), 0)\n  }\n\n  if (NETWORK_ERROR_CODES.includes(error.status)) {\n    // status in 500...599 range - server had an error, request might be retryed.\n    throw new AuthRetryableFetchError(_getErrorMessage(error), error.status)\n  }\n\n  let data: any\n  try {\n    data = await error.json()\n  } catch (e: any) {\n    throw new AuthUnknownError(_getErrorMessage(e), e)\n  }\n\n  let errorCode: string | undefined = undefined\n\n  const responseAPIVersion = parseResponseAPIVersion(error)\n  if (\n    responseAPIVersion &&\n    responseAPIVersion.getTime() >= API_VERSIONS['2024-01-01'].timestamp &&\n    typeof data === 'object' &&\n    data &&\n    typeof data.code === 'string'\n  ) {\n    errorCode = data.code\n  } else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n    errorCode = data.error_code\n  }\n\n  if (!errorCode) {\n    // Legacy support for weak password errors, when there were no error codes\n    if (\n      typeof data === 'object' &&\n      data &&\n      typeof data.weak_password === 'object' &&\n      data.weak_password &&\n      Array.isArray(data.weak_password.reasons) &&\n      data.weak_password.reasons.length &&\n      data.weak_password.reasons.reduce((a: boolean, i: any) => a && typeof i === 'string', true)\n    ) {\n      throw new AuthWeakPasswordError(\n        _getErrorMessage(data),\n        error.status,\n        data.weak_password.reasons\n      )\n    }\n  } else if (errorCode === 'weak_password') {\n    throw new AuthWeakPasswordError(\n      _getErrorMessage(data),\n      error.status,\n      data.weak_password?.reasons || []\n    )\n  } else if (errorCode === 'session_not_found') {\n    // The `session_id` inside the JWT does not correspond to a row in the\n    // `sessions` table. This usually means the user has signed out, has been\n    // deleted, or their session has somehow been terminated.\n    throw new AuthSessionMissingError()\n  }\n\n  throw new AuthApiError(_getErrorMessage(data), error.status || 500, errorCode)\n}\n\nconst _getRequestParams = (\n  method: RequestMethodType,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n) => {\n  const params: { [k: string]: any } = { method, headers: options?.headers || {} }\n\n  if (method === 'GET') {\n    return params\n  }\n\n  params.headers = { 'Content-Type': 'application/json;charset=UTF-8', ...options?.headers }\n  params.body = JSON.stringify(body)\n  return { ...params, ...parameters }\n}\n\ninterface GotrueRequestOptions extends FetchOptions {\n  jwt?: string\n  redirectTo?: string\n  body?: object\n  query?: { [key: string]: string }\n  /**\n   * Function that transforms api response from gotrue into a desirable / standardised format\n   */\n  xform?: (data: any) => any\n}\n\nexport async function _request(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: GotrueRequestOptions\n) {\n  const headers = {\n    ...options?.headers,\n  }\n\n  if (!headers[API_VERSION_HEADER_NAME]) {\n    headers[API_VERSION_HEADER_NAME] = API_VERSIONS['2024-01-01'].name\n  }\n\n  if (options?.jwt) {\n    headers['Authorization'] = `Bearer ${options.jwt}`\n  }\n\n  const qs = options?.query ?? {}\n  if (options?.redirectTo) {\n    qs['redirect_to'] = options.redirectTo\n  }\n\n  const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : ''\n  const data = await _handleRequest(\n    fetcher,\n    method,\n    url + queryString,\n    {\n      headers,\n      noResolveJson: options?.noResolveJson,\n    },\n    {},\n    options?.body\n  )\n  return options?.xform ? options?.xform(data) : { data: { ...data }, error: null }\n}\n\nasync function _handleRequest(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n): Promise<any> {\n  const requestParams = _getRequestParams(method, options, parameters, body)\n\n  let result: any\n\n  try {\n    result = await fetcher(url, {\n      ...requestParams,\n    })\n  } catch (e) {\n    console.error(e)\n\n    // fetch failed, likely due to a network or CORS error\n    throw new AuthRetryableFetchError(_getErrorMessage(e), 0)\n  }\n\n  if (!result.ok) {\n    await handleError(result)\n  }\n\n  if (options?.noResolveJson) {\n    return result\n  }\n\n  try {\n    return await result.json()\n  } catch (e: any) {\n    await handleError(e)\n  }\n}\n\nexport function _sessionResponse(data: any): AuthResponse {\n  let session = null\n  if (hasSession(data)) {\n    session = { ...data }\n\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in)\n    }\n  }\n\n  const user: User = data.user ?? (data as User)\n  return { data: { session, user }, error: null }\n}\n\nexport function _sessionResponsePassword(data: any): AuthResponsePassword {\n  const response = _sessionResponse(data) as AuthResponsePassword\n\n  if (\n    !response.error &&\n    data.weak_password &&\n    typeof data.weak_password === 'object' &&\n    Array.isArray(data.weak_password.reasons) &&\n    data.weak_password.reasons.length &&\n    data.weak_password.message &&\n    typeof data.weak_password.message === 'string' &&\n    data.weak_password.reasons.reduce((a: boolean, i: any) => a && typeof i === 'string', true)\n  ) {\n    response.data.weak_password = data.weak_password\n  }\n\n  return response\n}\n\nexport function _userResponse(data: any): UserResponse {\n  const user: User = data.user ?? (data as User)\n  return { data: { user }, error: null }\n}\n\nexport function _ssoResponse(data: any): SSOResponse {\n  return { data, error: null }\n}\n\nexport function _generateLinkResponse(data: any): GenerateLinkResponse {\n  const { action_link, email_otp, hashed_token, redirect_to, verification_type, ...rest } = data\n\n  const properties: GenerateLinkProperties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type,\n  }\n\n  const user: User = { ...rest }\n  return {\n    data: {\n      properties,\n      user,\n    },\n    error: null,\n  }\n}\n\nexport function _noResolveJsonResponse(data: any): Response {\n  return data\n}\n\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data: any): boolean {\n  return data.access_token && data.refresh_token && data.expires_in\n}\n", "import { AuthError } from './errors'\nimport { Fetch } from './fetch'\nimport type { SolanaSignInInput, SolanaSignInOutput } from './solana'\n\n/** One of the providers supported by GoTrue. */\nexport type Provider =\n  | 'apple'\n  | 'azure'\n  | 'bitbucket'\n  | 'discord'\n  | 'facebook'\n  | 'figma'\n  | 'github'\n  | 'gitlab'\n  | 'google'\n  | 'kakao'\n  | 'keycloak'\n  | 'linkedin'\n  | 'linkedin_oidc'\n  | 'notion'\n  | 'slack'\n  | 'slack_oidc'\n  | 'spotify'\n  | 'twitch'\n  | 'twitter'\n  | 'workos'\n  | 'zoom'\n  | 'fly'\n\nexport type AuthChangeEventMFA = 'MFA_CHALLENGE_VERIFIED'\n\nexport type AuthChangeEvent =\n  | 'INITIAL_SESSION'\n  | 'PASSWORD_RECOVERY'\n  | 'SIGNED_IN'\n  | 'SIGNED_OUT'\n  | 'TOKEN_REFRESHED'\n  | 'USER_UPDATED'\n  | AuthChangeEventMFA\n\n/**\n * Provide your own global lock implementation instead of the default\n * implementation. The function should acquire a lock for the duration of the\n * `fn` async function, such that no other client instances will be able to\n * hold it at the same time.\n *\n * @experimental\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout should occur. If positive it\n *                       should throw an Error with an `isAcquireTimeout`\n *                       property set to true if the operation fails to be\n *                       acquired after this much time (ms).\n * @param fn The operation to execute when the lock is acquired.\n */\nexport type LockFunc = <R>(name: string, acquireTimeout: number, fn: () => Promise<R>) => Promise<R>\n\nexport type GoTrueClientOptions = {\n  /* The URL of the GoTrue server. */\n  url?: string\n  /* Any additional headers to send to the GoTrue server. */\n  headers?: { [key: string]: string }\n  /* Optional key name used for storing tokens in local storage. */\n  storageKey?: string\n  /* Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user. */\n  detectSessionInUrl?: boolean\n  /* Set to \"true\" if you want to automatically refresh the token before expiring. */\n  autoRefreshToken?: boolean\n  /* Set to \"true\" if you want to automatically save the user session into local storage. If set to false, session will just be saved in memory. */\n  persistSession?: boolean\n  /* Provide your own local storage implementation to use instead of the browser's local storage. */\n  storage?: SupportedStorage\n  /**\n   * Stores the user object in a separate storage location from the rest of the session data. When non-null, `storage` will only store a JSON object containing the access and refresh token and some adjacent metadata, while `userStorage` will only contain the user object under the key `storageKey + '-user'`.\n   *\n   * When this option is set and cookie storage is used, `getSession()` and other functions that load a session from the cookie store might not return back a user. It's very important to always use `getUser()` to fetch a user object in those scenarios.\n   *\n   * @experimental\n   */\n  userStorage?: SupportedStorage\n  /* A custom fetch implementation. */\n  fetch?: Fetch\n  /* If set to 'pkce' PKCE flow. Defaults to the 'implicit' flow otherwise */\n  flowType?: AuthFlowType\n  /* If debug messages are emitted. Can be used to inspect the behavior of the library. If set to a function, the provided function will be used instead of `console.log()` to perform the logging. */\n  debug?: boolean | ((message: string, ...args: any[]) => void)\n  /**\n   * Provide your own locking mechanism based on the environment. By default no locking is done at this time.\n   *\n   * @experimental\n   */\n  lock?: LockFunc\n  /**\n   * Set to \"true\" if there is a custom authorization header set globally.\n   * @experimental\n   */\n  hasCustomAuthorizationHeader?: boolean\n}\n\nexport type WeakPasswordReasons = 'length' | 'characters' | 'pwned' | (string & {})\nexport type WeakPassword = {\n  reasons: WeakPasswordReasons[]\n  message: string\n}\n\nexport type AuthResponse =\n  | {\n      data: {\n        user: User | null\n        session: Session | null\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n      }\n      error: AuthError\n    }\n\nexport type AuthResponsePassword =\n  | {\n      data: {\n        user: User | null\n        session: Session | null\n        weak_password?: WeakPassword | null\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n      }\n      error: AuthError\n    }\n\n/**\n * AuthOtpResponse is returned when OTP is used.\n *\n * {@see AuthResponse}\n */\nexport type AuthOtpResponse =\n  | {\n      data: { user: null; session: null; messageId?: string | null }\n      error: null\n    }\n  | {\n      data: { user: null; session: null; messageId?: string | null }\n      error: AuthError\n    }\n\nexport type AuthTokenResponse =\n  | {\n      data: {\n        user: User\n        session: Session\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n      }\n      error: AuthError\n    }\n\nexport type AuthTokenResponsePassword =\n  | {\n      data: {\n        user: User\n        session: Session\n        weakPassword?: WeakPassword\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n        weakPassword?: null\n      }\n      error: AuthError\n    }\n\nexport type OAuthResponse =\n  | {\n      data: {\n        provider: Provider\n        url: string\n      }\n      error: null\n    }\n  | {\n      data: {\n        provider: Provider\n        url: null\n      }\n      error: AuthError\n    }\n\nexport type SSOResponse =\n  | {\n      data: {\n        /**\n         * URL to open in a browser which will complete the sign-in flow by\n         * taking the user to the identity provider's authentication flow.\n         *\n         * On browsers you can set the URL to `window.location.href` to take\n         * the user to the authentication flow.\n         */\n        url: string\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type UserResponse =\n  | {\n      data: {\n        user: User\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n      }\n      error: AuthError\n    }\n\nexport interface Session {\n  /**\n   * The oauth provider token. If present, this can be used to make external API requests to the oauth provider used.\n   */\n  provider_token?: string | null\n  /**\n   * The oauth provider refresh token. If present, this can be used to refresh the provider_token via the oauth provider's API.\n   * Not all oauth providers return a provider refresh token. If the provider_refresh_token is missing, please refer to the oauth provider's documentation for information on how to obtain the provider refresh token.\n   */\n  provider_refresh_token?: string | null\n  /**\n   * The access token jwt. It is recommended to set the JWT_EXPIRY to a shorter expiry value.\n   */\n  access_token: string\n  /**\n   * A one-time used refresh token that never expires.\n   */\n  refresh_token: string\n  /**\n   * The number of seconds until the token expires (since it was issued). Returned when a login is confirmed.\n   */\n  expires_in: number\n  /**\n   * A timestamp of when the token will expire. Returned when a login is confirmed.\n   */\n  expires_at?: number\n  token_type: string\n\n  /**\n   * When using a separate user storage, accessing properties of this object will throw an error.\n   */\n  user: User\n}\n\n/**\n * An authentication methord reference (AMR) entry.\n *\n * An entry designates what method was used by the user to verify their\n * identity and at what time.\n *\n * @see {@link GoTrueMFAApi#getAuthenticatorAssuranceLevel}.\n */\nexport interface AMREntry {\n  /** Authentication method name. */\n  method: 'password' | 'otp' | 'oauth' | 'mfa/totp' | (string & {})\n\n  /**\n   * Timestamp when the method was successfully used. Represents number of\n   * seconds since 1st January 1970 (UNIX epoch) in UTC.\n   */\n  timestamp: number\n}\n\nexport interface UserIdentity {\n  id: string\n  user_id: string\n  identity_data?: {\n    [key: string]: any\n  }\n  identity_id: string\n  provider: string\n  created_at?: string\n  last_sign_in_at?: string\n  updated_at?: string\n}\n\n/**\n * A MFA factor.\n *\n * @see {@link GoTrueMFAApi#enroll}\n * @see {@link GoTrueMFAApi#listFactors}\n * @see {@link GoTrueMFAAdminApi#listFactors}\n */\nexport interface Factor {\n  /** ID of the factor. */\n  id: string\n\n  /** Friendly name of the factor, useful to disambiguate between multiple factors. */\n  friendly_name?: string\n\n  /**\n   * Type of factor. `totp` and `phone` supported with this version\n   */\n  factor_type: 'totp' | 'phone' | (string & {})\n\n  /** Factor's status. */\n  status: 'verified' | 'unverified'\n\n  created_at: string\n  updated_at: string\n}\n\nexport interface UserAppMetadata {\n  provider?: string\n  [key: string]: any\n}\n\nexport interface UserMetadata {\n  [key: string]: any\n}\n\nexport interface User {\n  id: string\n  app_metadata: UserAppMetadata\n  user_metadata: UserMetadata\n  aud: string\n  confirmation_sent_at?: string\n  recovery_sent_at?: string\n  email_change_sent_at?: string\n  new_email?: string\n  new_phone?: string\n  invited_at?: string\n  action_link?: string\n  email?: string\n  phone?: string\n  created_at: string\n  confirmed_at?: string\n  email_confirmed_at?: string\n  phone_confirmed_at?: string\n  last_sign_in_at?: string\n  role?: string\n  updated_at?: string\n  identities?: UserIdentity[]\n  is_anonymous?: boolean\n  is_sso_user?: boolean\n  factors?: Factor[]\n  deleted_at?: string\n}\n\nexport interface UserAttributes {\n  /**\n   * The user's email.\n   */\n  email?: string\n\n  /**\n   * The user's phone.\n   */\n  phone?: string\n\n  /**\n   * The user's password.\n   */\n  password?: string\n\n  /**\n   * The nonce sent for reauthentication if the user's password is to be updated.\n   *\n   * Call reauthenticate() to obtain the nonce first.\n   */\n  nonce?: string\n\n  /**\n   * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n   *\n   * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n   *\n   */\n  data?: object\n}\n\nexport interface AdminUserAttributes extends Omit<UserAttributes, 'data'> {\n  /**\n   * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n   *\n   *\n   * The `user_metadata` should be a JSON object that includes user-specific info, such as their first and last name.\n   *\n   * Note: When using the GoTrueAdminApi and wanting to modify a user's metadata,\n   * this attribute is used instead of UserAttributes data.\n   *\n   */\n  user_metadata?: object\n\n  /**\n   * A custom data object to store the user's application specific metadata. This maps to the `auth.users.app_metadata` column.\n   *\n   * Only a service role can modify.\n   *\n   * The `app_metadata` should be a JSON object that includes app-specific info, such as identity providers, roles, and other\n   * access control information.\n   */\n  app_metadata?: object\n\n  /**\n   * Confirms the user's email address if set to true.\n   *\n   * Only a service role can modify.\n   */\n  email_confirm?: boolean\n\n  /**\n   * Confirms the user's phone number if set to true.\n   *\n   * Only a service role can modify.\n   */\n  phone_confirm?: boolean\n\n  /**\n   * Determines how long a user is banned for.\n   *\n   * The format for the ban duration follows a strict sequence of decimal numbers with a unit suffix.\n   * Valid time units are \"ns\", \"us\" (or \"µs\"), \"ms\", \"s\", \"m\", \"h\".\n   *\n   * For example, some possible durations include: '300ms', '2h45m'.\n   *\n   * Setting the ban duration to 'none' lifts the ban on the user.\n   */\n  ban_duration?: string | 'none'\n\n  /**\n   * The `role` claim set in the user's access token JWT.\n   *\n   * When a user signs up, this role is set to `authenticated` by default. You should only modify the `role` if you need to provision several levels of admin access that have different permissions on individual columns in your database.\n   *\n   * Setting this role to `service_role` is not recommended as it grants the user admin privileges.\n   */\n  role?: string\n\n  /**\n   * The `password_hash` for the user's password.\n   *\n   * Allows you to specify a password hash for the user. This is useful for migrating a user's password hash from another service.\n   *\n   * Supports bcrypt, scrypt (firebase), and argon2 password hashes.\n   */\n  password_hash?: string\n\n  /**\n   * The `id` for the user.\n   *\n   * Allows you to overwrite the default `id` set for the user.\n   */\n  id?: string\n}\n\nexport interface Subscription {\n  /**\n   * The subscriber UUID. This will be set by the client.\n   */\n  id: string\n  /**\n   * The function to call every time there is an event. eg: (eventName) => {}\n   */\n  callback: (event: AuthChangeEvent, session: Session | null) => void\n  /**\n   * Call this to remove the listener.\n   */\n  unsubscribe: () => void\n}\n\nexport type SignInAnonymouslyCredentials = {\n  options?: {\n    /**\n     * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n     *\n     * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n     */\n    data?: object\n    /** Verification token received when the user completes the captcha on the site. */\n    captchaToken?: string\n  }\n}\n\nexport type SignUpWithPasswordCredentials =\n  | {\n      /** The user's email address. */\n      email: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /** The redirect url embedded in the email link */\n        emailRedirectTo?: string\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** The user's phone number. */\n      phone: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. Requires a configured WhatsApp sender on Twilio */\n        captchaToken?: string\n        /** Messaging channel to use (e.g. whatsapp or sms) */\n        channel?: 'sms' | 'whatsapp'\n      }\n    }\n\nexport type SignInWithPasswordCredentials =\n  | {\n      /** The user's email address. */\n      email: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** The user's phone number. */\n      phone: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type SignInWithPasswordlessCredentials =\n  | {\n      /** The user's email address. */\n      email: string\n      options?: {\n        /** The redirect url embedded in the email link */\n        emailRedirectTo?: string\n        /** If set to false, this method will not create a new user. Defaults to true. */\n        shouldCreateUser?: boolean\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** The user's phone number. */\n      phone: string\n      options?: {\n        /** If set to false, this method will not create a new user. Defaults to true. */\n        shouldCreateUser?: boolean\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n        /** Messaging channel to use (e.g. whatsapp or sms) */\n        channel?: 'sms' | 'whatsapp'\n      }\n    }\n\nexport type AuthFlowType = 'implicit' | 'pkce'\nexport type SignInWithOAuthCredentials = {\n  /** One of the providers supported by GoTrue. */\n  provider: Provider\n  options?: {\n    /** A URL to send the user to after they are confirmed. */\n    redirectTo?: string\n    /** A space-separated list of scopes granted to the OAuth application. */\n    scopes?: string\n    /** An object of query params */\n    queryParams?: { [key: string]: string }\n    /** If set to true does not immediately redirect the current browser context to visit the OAuth authorization page for the provider. */\n    skipBrowserRedirect?: boolean\n  }\n}\n\nexport type SignInWithIdTokenCredentials = {\n  /** Provider name or OIDC `iss` value identifying which provider should be used to verify the provided token. Supported names: `google`, `apple`, `azure`, `facebook`, `kakao`, `keycloak` (deprecated). */\n  provider: 'google' | 'apple' | 'azure' | 'facebook' | 'kakao' | (string & {})\n  /** OIDC ID token issued by the specified provider. The `iss` claim in the ID token must match the supplied provider. Some ID tokens contain an `at_hash` which require that you provide an `access_token` value to be accepted properly. If the token contains a `nonce` claim you must supply the nonce used to obtain the ID token. */\n  token: string\n  /** If the ID token contains an `at_hash` claim, then the hash of this value is compared to the value in the ID token. */\n  access_token?: string\n  /** If the ID token contains a `nonce` claim, then the hash of this value is compared to the value in the ID token. */\n  nonce?: string\n  options?: {\n    /** Verification token received when the user completes the captcha on the site. */\n    captchaToken?: string\n  }\n}\n\nexport type SolanaWallet = {\n  signIn?: (...inputs: SolanaSignInInput[]) => Promise<SolanaSignInOutput | SolanaSignInOutput[]>\n  publicKey?: {\n    toBase58: () => string\n  } | null\n\n  signMessage?: (message: Uint8Array, encoding?: 'utf8' | string) => Promise<Uint8Array> | undefined\n}\n\nexport type SolanaWeb3Credentials =\n  | {\n      chain: 'solana'\n\n      /** Wallet interface to use. If not specified will default to `window.solana`. */\n      wallet?: SolanaWallet\n\n      /** Optional statement to include in the Sign in with Solana message. Must not include new line characters. Most wallets like Phantom **require specifying a statement!** */\n      statement?: string\n\n      options?: {\n        /** URL to use with the wallet interface. Some wallets do not allow signing a message for URLs different from the current page. */\n        url?: string\n\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n\n        signInWithSolana?: Partial<\n          Omit<SolanaSignInInput, 'version' | 'chain' | 'domain' | 'uri' | 'statement'>\n        >\n      }\n    }\n  | {\n      chain: 'solana'\n\n      /** Sign in with Solana compatible message. Must include `Issued At`, `URI` and `Version`. */\n      message: string\n\n      /** Ed25519 signature of the message. */\n      signature: Uint8Array\n\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type Web3Credentials = SolanaWeb3Credentials\n\nexport type VerifyOtpParams = VerifyMobileOtpParams | VerifyEmailOtpParams | VerifyTokenHashParams\nexport interface VerifyMobileOtpParams {\n  /** The user's phone number. */\n  phone: string\n  /** The otp sent to the user's phone number. */\n  token: string\n  /** The user's verification type. */\n  type: MobileOtpType\n  options?: {\n    /** A URL to send the user to after they are confirmed. */\n    redirectTo?: string\n\n    /**\n     * Verification token received when the user completes the captcha on the site.\n     *\n     * @deprecated\n     */\n    captchaToken?: string\n  }\n}\nexport interface VerifyEmailOtpParams {\n  /** The user's email address. */\n  email: string\n  /** The otp sent to the user's email address. */\n  token: string\n  /** The user's verification type. */\n  type: EmailOtpType\n  options?: {\n    /** A URL to send the user to after they are confirmed. */\n    redirectTo?: string\n\n    /** Verification token received when the user completes the captcha on the site.\n     *\n     * @deprecated\n     */\n    captchaToken?: string\n  }\n}\n\nexport interface VerifyTokenHashParams {\n  /** The token hash used in an email link */\n  token_hash: string\n\n  /** The user's verification type. */\n  type: EmailOtpType\n}\n\nexport type MobileOtpType = 'sms' | 'phone_change'\nexport type EmailOtpType = 'signup' | 'invite' | 'magiclink' | 'recovery' | 'email_change' | 'email'\n\nexport type ResendParams =\n  | {\n      type: Extract<EmailOtpType, 'signup' | 'email_change'>\n      email: string\n      options?: {\n        /** A URL to send the user to after they have signed-in. */\n        emailRedirectTo?: string\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      type: Extract<MobileOtpType, 'sms' | 'phone_change'>\n      phone: string\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type SignInWithSSO =\n  | {\n      /** UUID of the SSO provider to invoke single-sign on to. */\n      providerId: string\n\n      options?: {\n        /** A URL to send the user to after they have signed-in. */\n        redirectTo?: string\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** Domain name of the organization for which to invoke single-sign on. */\n      domain: string\n\n      options?: {\n        /** A URL to send the user to after they have signed-in. */\n        redirectTo?: string\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type GenerateSignupLinkParams = {\n  type: 'signup'\n  email: string\n  password: string\n  options?: Pick<GenerateLinkOptions, 'data' | 'redirectTo'>\n}\n\nexport type GenerateInviteOrMagiclinkParams = {\n  type: 'invite' | 'magiclink'\n  /** The user's email */\n  email: string\n  options?: Pick<GenerateLinkOptions, 'data' | 'redirectTo'>\n}\n\nexport type GenerateRecoveryLinkParams = {\n  type: 'recovery'\n  /** The user's email */\n  email: string\n  options?: Pick<GenerateLinkOptions, 'redirectTo'>\n}\n\nexport type GenerateEmailChangeLinkParams = {\n  type: 'email_change_current' | 'email_change_new'\n  /** The user's email */\n  email: string\n  /**\n   * The user's new email. Only required if type is 'email_change_current' or 'email_change_new'.\n   */\n  newEmail: string\n  options?: Pick<GenerateLinkOptions, 'redirectTo'>\n}\n\nexport interface GenerateLinkOptions {\n  /**\n   * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n   *\n   * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n   */\n  data?: object\n  /** The URL which will be appended to the email link generated. */\n  redirectTo?: string\n}\n\nexport type GenerateLinkParams =\n  | GenerateSignupLinkParams\n  | GenerateInviteOrMagiclinkParams\n  | GenerateRecoveryLinkParams\n  | GenerateEmailChangeLinkParams\n\nexport type GenerateLinkResponse =\n  | {\n      data: {\n        properties: GenerateLinkProperties\n        user: User\n      }\n      error: null\n    }\n  | {\n      data: {\n        properties: null\n        user: null\n      }\n      error: AuthError\n    }\n\n/** The properties related to the email link generated  */\nexport type GenerateLinkProperties = {\n  /**\n   * The email link to send to the user.\n   * The action_link follows the following format: auth/v1/verify?type={verification_type}&token={hashed_token}&redirect_to={redirect_to}\n   * */\n  action_link: string\n  /**\n   * The raw email OTP.\n   * You should send this in the email if you want your users to verify using an OTP instead of the action link.\n   * */\n  email_otp: string\n  /**\n   * The hashed token appended to the action link.\n   * */\n  hashed_token: string\n  /** The URL appended to the action link. */\n  redirect_to: string\n  /** The verification type that the email link is associated to. */\n  verification_type: GenerateLinkType\n}\n\nexport type GenerateLinkType =\n  | 'signup'\n  | 'invite'\n  | 'magiclink'\n  | 'recovery'\n  | 'email_change_current'\n  | 'email_change_new'\n\nexport type MFAEnrollParams = MFAEnrollTOTPParams | MFAEnrollPhoneParams\n\nexport type MFAUnenrollParams = {\n  /** ID of the factor being unenrolled. */\n  factorId: string\n}\n\nexport type MFAVerifyParams = {\n  /** ID of the factor being verified. Returned in enroll(). */\n  factorId: string\n\n  /** ID of the challenge being verified. Returned in challenge(). */\n  challengeId: string\n\n  /** Verification code provided by the user. */\n  code: string\n}\n\nexport type MFAChallengeParams = {\n  /** ID of the factor to be challenged. Returned in enroll(). */\n  factorId: string\n  /** Messaging channel to use (e.g. whatsapp or sms). Only relevant for phone factors */\n  channel?: 'sms' | 'whatsapp'\n}\n\nexport type MFAChallengeAndVerifyParams = {\n  /** ID of the factor being verified. Returned in enroll(). */\n  factorId: string\n  /** Verification code provided by the user. */\n  code: string\n}\n\nexport type AuthMFAVerifyResponse =\n  | {\n      data: {\n        /** New access token (JWT) after successful verification. */\n        access_token: string\n\n        /** Type of token, typically `Bearer`. */\n        token_type: string\n\n        /** Number of seconds in which the access token will expire. */\n        expires_in: number\n\n        /** Refresh token you can use to obtain new access tokens when expired. */\n        refresh_token: string\n\n        /** Updated user profile. */\n        user: User\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type AuthMFAEnrollResponse = AuthMFAEnrollTOTPResponse | AuthMFAEnrollPhoneResponse\n\nexport type AuthMFAUnenrollResponse =\n  | {\n      data: {\n        /** ID of the factor that was successfully unenrolled. */\n        id: string\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\nexport type AuthMFAChallengeResponse =\n  | {\n      data: {\n        /** ID of the newly created challenge. */\n        id: string\n\n        /** Factor Type which generated the challenge */\n        type: 'totp' | 'phone'\n\n        /** Timestamp in UNIX seconds when this challenge will no longer be usable. */\n        expires_at: number\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\nexport type AuthMFAListFactorsResponse =\n  | {\n      data: {\n        /** All available factors (verified and unverified). */\n        all: Factor[]\n\n        /** Only verified TOTP factors. (A subset of `all`.) */\n        totp: Factor[]\n        /** Only verified Phone factors. (A subset of `all`.) */\n        phone: Factor[]\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\nexport type AuthenticatorAssuranceLevels = 'aal1' | 'aal2'\n\nexport type AuthMFAGetAuthenticatorAssuranceLevelResponse =\n  | {\n      data: {\n        /** Current AAL level of the session. */\n        currentLevel: AuthenticatorAssuranceLevels | null\n\n        /**\n         * Next possible AAL level for the session. If the next level is higher\n         * than the current one, the user should go through MFA.\n         *\n         * @see {@link GoTrueMFAApi#challenge}\n         */\n        nextLevel: AuthenticatorAssuranceLevels | null\n\n        /**\n         * A list of all authentication methods attached to this session. Use\n         * the information here to detect the last time a user verified a\n         * factor, for example if implementing a step-up scenario.\n         */\n        currentAuthenticationMethods: AMREntry[]\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\n/**\n * Contains the full multi-factor authentication API.\n *\n */\nexport interface GoTrueMFAApi {\n  /**\n   * Starts the enrollment process for a new Multi-Factor Authentication (MFA)\n   * factor. This method creates a new `unverified` factor.\n   * To verify a factor, present the QR code or secret to the user and ask them to add it to their\n   * authenticator app.\n   * The user has to enter the code from their authenticator app to verify it.\n   *\n   * Upon verifying a factor, all other sessions are logged out and the current session's authenticator level is promoted to `aal2`.\n   *\n   */\n  enroll(params: MFAEnrollTOTPParams): Promise<AuthMFAEnrollTOTPResponse>\n  enroll(params: MFAEnrollPhoneParams): Promise<AuthMFAEnrollPhoneResponse>\n  enroll(params: MFAEnrollParams): Promise<AuthMFAEnrollResponse>\n\n  /**\n   * Prepares a challenge used to verify that a user has access to a MFA\n   * factor.\n   */\n  challenge(params: MFAChallengeParams): Promise<AuthMFAChallengeResponse>\n\n  /**\n   * Verifies a code against a challenge. The verification code is\n   * provided by the user by entering a code seen in their authenticator app.\n   */\n  verify(params: MFAVerifyParams): Promise<AuthMFAVerifyResponse>\n\n  /**\n   * Unenroll removes a MFA factor.\n   * A user has to have an `aal2` authenticator level in order to unenroll a `verified` factor.\n   */\n  unenroll(params: MFAUnenrollParams): Promise<AuthMFAUnenrollResponse>\n\n  /**\n   * Helper method which creates a challenge and immediately uses the given code to verify against it thereafter. The verification code is\n   * provided by the user by entering a code seen in their authenticator app.\n   */\n  challengeAndVerify(params: MFAChallengeAndVerifyParams): Promise<AuthMFAVerifyResponse>\n\n  /**\n   * Returns the list of MFA factors enabled for this user.\n   *\n   * @see {@link GoTrueMFAApi#enroll}\n   * @see {@link GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   * @see {@link GoTrueClient#getUser}\n   *\n   */\n  listFactors(): Promise<AuthMFAListFactorsResponse>\n\n  /**\n   * Returns the Authenticator Assurance Level (AAL) for the active session.\n   *\n   * - `aal1` (or `null`) means that the user's identity has been verified only\n   * with a conventional login (email+password, OTP, magic link, social login,\n   * etc.).\n   * - `aal2` means that the user's identity has been verified both with a conventional login and at least one MFA factor.\n   *\n   * Although this method returns a promise, it's fairly quick (microseconds)\n   * and rarely uses the network. You can use this to check whether the current\n   * user needs to be shown a screen to verify their MFA factors.\n   *\n   */\n  getAuthenticatorAssuranceLevel(): Promise<AuthMFAGetAuthenticatorAssuranceLevelResponse>\n}\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminDeleteFactorResponse =\n  | {\n      data: {\n        /** ID of the factor that was successfully deleted. */\n        id: string\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminDeleteFactorParams = {\n  /** ID of the MFA factor to delete. */\n  id: string\n\n  /** ID of the user whose factor is being deleted. */\n  userId: string\n}\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminListFactorsResponse =\n  | {\n      data: {\n        /** All factors attached to the user. */\n        factors: Factor[]\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminListFactorsParams = {\n  /** ID of the user. */\n  userId: string\n}\n\n/**\n * Contains the full multi-factor authentication administration API.\n *\n * @expermental\n */\nexport interface GoTrueAdminMFAApi {\n  /**\n   * Lists all factors associated to a user.\n   *\n   */\n  listFactors(params: AuthMFAAdminListFactorsParams): Promise<AuthMFAAdminListFactorsResponse>\n\n  /**\n   * Deletes a factor on a user. This will log the user out of all active\n   * sessions if the deleted factor was verified.\n   *\n   * @see {@link GoTrueMFAApi#unenroll}\n   *\n   * @expermental\n   */\n  deleteFactor(params: AuthMFAAdminDeleteFactorParams): Promise<AuthMFAAdminDeleteFactorResponse>\n}\n\ntype AnyFunction = (...args: any[]) => any\ntype MaybePromisify<T> = T | Promise<T>\n\ntype PromisifyMethods<T> = {\n  [K in keyof T]: T[K] extends AnyFunction\n    ? (...args: Parameters<T[K]>) => MaybePromisify<ReturnType<T[K]>>\n    : T[K]\n}\n\nexport type SupportedStorage = PromisifyMethods<\n  Pick<Storage, 'getItem' | 'setItem' | 'removeItem'>\n> & {\n  /**\n   * If set to `true` signals to the library that the storage medium is used\n   * on a server and the values may not be authentic, such as reading from\n   * request cookies. Implementations should not set this to true if the client\n   * is used on a server that reads storage information from authenticated\n   * sources, such as a secure database or file.\n   */\n  isServer?: boolean\n}\n\nexport type InitializeResult = { error: AuthError | null }\n\nexport type CallRefreshTokenResult =\n  | {\n      session: Session\n      error: null\n    }\n  | {\n      session: null\n      error: AuthError\n    }\n\nexport type Pagination = {\n  [key: string]: any\n  nextPage: number | null\n  lastPage: number\n  total: number\n}\n\nexport type PageParams = {\n  /** The page number */\n  page?: number\n  /** Number of items returned per page */\n  perPage?: number\n}\n\nexport type SignOut = {\n  /**\n   * Determines which sessions should be\n   * logged out. Global means all\n   * sessions by this account. Local\n   * means only this session. Others\n   * means all other sessions except the\n   * current one. When using others,\n   * there is no sign-out event fired on\n   * the current session!\n   */\n  scope?: 'global' | 'local' | 'others'\n}\n\nexport type MFAEnrollTOTPParams = {\n  /** The type of factor being enrolled. */\n  factorType: 'totp'\n  /** Domain which the user is enrolled with. */\n  issuer?: string\n  /** Human readable name assigned to the factor. */\n  friendlyName?: string\n}\nexport type MFAEnrollPhoneParams = {\n  /** The type of factor being enrolled. */\n  factorType: 'phone'\n  /** Human readable name assigned to the factor. */\n  friendlyName?: string\n  /** Phone number associated with a factor. Number should conform to E.164 format */\n  phone: string\n}\n\nexport type AuthMFAEnrollTOTPResponse =\n  | {\n      data: {\n        /** ID of the factor that was just enrolled (in an unverified state). */\n        id: string\n\n        /** Type of MFA factor.*/\n        type: 'totp'\n\n        /** TOTP enrollment information. */\n        totp: {\n          /** Contains a QR code encoding the authenticator URI. You can\n           * convert it to a URL by prepending `data:image/svg+xml;utf-8,` to\n           * the value. Avoid logging this value to the console. */\n          qr_code: string\n\n          /** The TOTP secret (also encoded in the QR code). Show this secret\n           * in a password-style field to the user, in case they are unable to\n           * scan the QR code. Avoid logging this value to the console. */\n          secret: string\n\n          /** The authenticator URI encoded within the QR code, should you need\n           * to use it. Avoid loggin this value to the console. */\n          uri: string\n        }\n        /** Friendly name of the factor, useful for distinguishing between factors **/\n        friendly_name?: string\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type AuthMFAEnrollPhoneResponse =\n  | {\n      data: {\n        /** ID of the factor that was just enrolled (in an unverified state). */\n        id: string\n\n        /** Type of MFA factor. */\n        type: 'phone'\n\n        /** Friendly name of the factor, useful for distinguishing between factors **/\n        friendly_name?: string\n\n        /** Phone number of the MFA factor in E.164 format. Used to send messages  */\n        phone: string\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type JwtHeader = {\n  alg: 'RS256' | 'ES256' | 'HS256'\n  kid: string\n  typ: string\n}\n\nexport type RequiredClaims = {\n  iss: string\n  sub: string\n  aud: string | string[]\n  exp: number\n  iat: number\n  role: string\n  aal: AuthenticatorAssuranceLevels\n  session_id: string\n}\n\nexport type JwtPayload = RequiredClaims & {\n  [key: string]: any\n}\n\nexport interface JWK {\n  kty: 'RSA' | 'EC' | 'oct'\n  key_ops: string[]\n  alg?: string\n  kid?: string\n  [key: string]: any\n}\n\nexport const SIGN_OUT_SCOPES = ['global', 'local', 'others'] as const\nexport type SignOutScope = typeof SIGN_OUT_SCOPES[number]\n", "import GoTrueAdmin<PERSON>pi from './GoTrueAdminApi'\nimport {\n  DEFAULT_HEADERS,\n  EXPIRY_MARGIN_MS,\n  AUTO_REFRESH_TICK_DURATION_MS,\n  AUTO_REFRESH_TICK_THRESHOLD,\n  GOTRUE_URL,\n  STORAGE_KEY,\n  J<PERSON><PERSON>_TTL,\n} from './lib/constants'\nimport {\n  AuthError,\n  AuthImplicitGrantRedirectError,\n  AuthPKCEGrantCodeExchangeError,\n  AuthInvalidCredentialsError,\n  AuthSessionMissingError,\n  AuthInvalidTokenResponseError,\n  AuthUnknownError,\n  isAuthApiError,\n  isAuthError,\n  isAuthRetryableFetchError,\n  isAuthSessionMissingError,\n  isAuthImplicitGrantRedirectError,\n  AuthInvalidJwtError,\n} from './lib/errors'\nimport {\n  Fetch,\n  _request,\n  _sessionResponse,\n  _sessionResponsePassword,\n  _userResponse,\n  _ssoResponse,\n} from './lib/fetch'\nimport {\n  Deferred,\n  getItemAsync,\n  isBrowser,\n  removeItemAsync,\n  resolveFetch,\n  setItemAsync,\n  uuid,\n  retryable,\n  sleep,\n  parseParametersFromURL,\n  getCodeChallengeAndMethod,\n  getAlgorithm,\n  validateExp,\n  decodeJWT,\n  userNotAvailableProxy,\n  supportsLocalStorage,\n} from './lib/helpers'\nimport { memoryLocalStorageAdapter } from './lib/local-storage'\nimport { polyfillGlobalThis } from './lib/polyfills'\nimport { version } from './lib/version'\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks'\n\nimport type {\n  AuthChangeEvent,\n  AuthResponse,\n  AuthResponsePassword,\n  AuthTokenResponse,\n  AuthTokenResponsePassword,\n  AuthOtpResponse,\n  CallRefreshTokenResult,\n  GoTrueClientOptions,\n  InitializeResult,\n  OAuthResponse,\n  SSOResponse,\n  Provider,\n  Session,\n  SignInWithIdTokenCredentials,\n  SignInWithOAuthCredentials,\n  SignInWithPasswordCredentials,\n  SignInWithPasswordlessCredentials,\n  SignUpWithPasswordCredentials,\n  SignInWithSSO,\n  SignOut,\n  Subscription,\n  SupportedStorage,\n  User,\n  UserAttributes,\n  UserResponse,\n  VerifyOtpParams,\n  GoTrueMFAApi,\n  MFAEnrollParams,\n  AuthMFAEnrollResponse,\n  MFAChallengeParams,\n  AuthMFAChallengeResponse,\n  MFAUnenrollParams,\n  AuthMFAUnenrollResponse,\n  MFAVerifyParams,\n  AuthMFAVerifyResponse,\n  AuthMFAListFactorsResponse,\n  AuthMFAGetAuthenticatorAssuranceLevelResponse,\n  AuthenticatorAssuranceLevels,\n  Factor,\n  MFAChallengeAndVerifyParams,\n  ResendParams,\n  AuthFlowType,\n  LockFunc,\n  UserIdentity,\n  SignInAnonymouslyCredentials,\n  MFAEnrollTOTPParams,\n  MFAEnrollPhoneParams,\n  AuthMFAEnrollTOTPResponse,\n  AuthMFAEnrollPhoneResponse,\n  JWK,\n  JwtPayload,\n  JwtHeader,\n  SolanaWeb3Credentials,\n  SolanaWallet,\n  Web3Credentials,\n} from './lib/types'\nimport { stringToUint8Array, bytesToBase64URL } from './lib/base64url'\nimport { deepClone } from './lib/helpers'\n\npolyfillGlobalThis() // Make \"globalThis\" available\n\nconst DEFAULT_OPTIONS: Omit<\n  Required<GoTrueClientOptions>,\n  'fetch' | 'storage' | 'userStorage' | 'lock'\n> = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false,\n}\n\nasync function lockNoOp<R>(name: string, acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n  return await fn()\n}\n\n/**\n * Caches JWKS values for all clients created in the same environment. This is\n * especially useful for shared-memory execution environments such as Vercel's\n * Fluid Compute, AWS Lambda or Supabase's Edge Functions. Regardless of how\n * many clients are created, if they share the same storage key they will use\n * the same JWKS cache, significantly speeding up getClaims() with asymmetric\n * JWTs.\n */\nconst GLOBAL_JWKS: { [storageKey: string]: { cachedAt: number; jwks: { keys: JWK[] } } } = {}\n\nexport default class GoTrueClient {\n  private static nextInstanceID = 0\n\n  private instanceID: number\n\n  /**\n   * Namespace for the GoTrue admin methods.\n   * These methods should only be used in a trusted server-side environment.\n   */\n  admin: GoTrueAdminApi\n  /**\n   * Namespace for the MFA methods.\n   */\n  mfa: GoTrueMFAApi\n  /**\n   * The storage key used to identify the values saved in localStorage\n   */\n  protected storageKey: string\n\n  protected flowType: AuthFlowType\n\n  /**\n   * The JWKS used for verifying asymmetric JWTs\n   */\n  protected get jwks() {\n    return GLOBAL_JWKS[this.storageKey]?.jwks ?? { keys: [] }\n  }\n\n  protected set jwks(value: { keys: JWK[] }) {\n    GLOBAL_JWKS[this.storageKey] = { ...GLOBAL_JWKS[this.storageKey], jwks: value }\n  }\n\n  protected get jwks_cached_at() {\n    return GLOBAL_JWKS[this.storageKey]?.cachedAt ?? Number.MIN_SAFE_INTEGER\n  }\n\n  protected set jwks_cached_at(value: number) {\n    GLOBAL_JWKS[this.storageKey] = { ...GLOBAL_JWKS[this.storageKey], cachedAt: value }\n  }\n\n  protected autoRefreshToken: boolean\n  protected persistSession: boolean\n  protected storage: SupportedStorage\n  /**\n   * @experimental\n   */\n  protected userStorage: SupportedStorage | null = null\n  protected memoryStorage: { [key: string]: string } | null = null\n  protected stateChangeEmitters: Map<string, Subscription> = new Map()\n  protected autoRefreshTicker: ReturnType<typeof setInterval> | null = null\n  protected visibilityChangedCallback: (() => Promise<any>) | null = null\n  protected refreshingDeferred: Deferred<CallRefreshTokenResult> | null = null\n  /**\n   * Keeps track of the async client initialization.\n   * When null or not yet resolved the auth state is `unknown`\n   * Once resolved the the auth state is known and it's save to call any further client methods.\n   * Keep extra care to never reject or throw uncaught errors\n   */\n  protected initializePromise: Promise<InitializeResult> | null = null\n  protected detectSessionInUrl = true\n  protected url: string\n  protected headers: {\n    [key: string]: string\n  }\n  protected hasCustomAuthorizationHeader = false\n  protected suppressGetSessionWarning = false\n  protected fetch: Fetch\n  protected lock: LockFunc\n  protected lockAcquired = false\n  protected pendingInLock: Promise<any>[] = []\n\n  /**\n   * Used to broadcast state change events to other tabs listening.\n   */\n  protected broadcastChannel: BroadcastChannel | null = null\n\n  protected logDebugMessages: boolean\n  protected logger: (message: string, ...args: any[]) => void = console.log\n\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options: GoTrueClientOptions) {\n    this.instanceID = GoTrueClient.nextInstanceID\n    GoTrueClient.nextInstanceID += 1\n\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn(\n        'Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.'\n      )\n    }\n\n    const settings = { ...DEFAULT_OPTIONS, ...options }\n\n    this.logDebugMessages = !!settings.debug\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug\n    }\n\n    this.persistSession = settings.persistSession\n    this.storageKey = settings.storageKey\n    this.autoRefreshToken = settings.autoRefreshToken\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch,\n    })\n\n    this.url = settings.url\n    this.headers = settings.headers\n    this.fetch = resolveFetch(settings.fetch)\n    this.lock = settings.lock || lockNoOp\n    this.detectSessionInUrl = settings.detectSessionInUrl\n    this.flowType = settings.flowType\n    this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader\n\n    if (settings.lock) {\n      this.lock = settings.lock\n    } else if (isBrowser() && globalThis?.navigator?.locks) {\n      this.lock = navigatorLock\n    } else {\n      this.lock = lockNoOp\n    }\n\n    if (!this.jwks) {\n      this.jwks = { keys: [] }\n      this.jwks_cached_at = Number.MIN_SAFE_INTEGER\n    }\n\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n    }\n\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = globalThis.localStorage\n        } else {\n          this.memoryStorage = {}\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n        }\n      }\n\n      if (settings.userStorage) {\n        this.userStorage = settings.userStorage\n      }\n    } else {\n      this.memoryStorage = {}\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n    }\n\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey)\n      } catch (e: any) {\n        console.error(\n          'Failed to create a new BroadcastChannel, multi-tab state changes will not be available',\n          e\n        )\n      }\n\n      this.broadcastChannel?.addEventListener('message', async (event) => {\n        this._debug('received broadcast notification from other tab or client', event)\n\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false) // broadcast = false so we don't get an endless loop of messages\n      })\n    }\n\n    this.initialize()\n  }\n\n  private _debug(...args: any[]): GoTrueClient {\n    if (this.logDebugMessages) {\n      this.logger(\n        `GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`,\n        ...args\n      )\n    }\n\n    return this\n  }\n\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize(): Promise<InitializeResult> {\n    if (this.initializePromise) {\n      return await this.initializePromise\n    }\n\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize()\n      })\n    })()\n\n    return await this.initializePromise\n  }\n\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  private async _initialize(): Promise<InitializeResult> {\n    try {\n      const params = parseParametersFromURL(window.location.href)\n      let callbackUrlType = 'none'\n      if (this._isImplicitGrantCallback(params)) {\n        callbackUrlType = 'implicit'\n      } else if (await this._isPKCECallback(params)) {\n        callbackUrlType = 'pkce'\n      }\n\n      /**\n       * Attempt to get the session from the URL only if these conditions are fulfilled\n       *\n       * Note: If the URL isn't one of the callback url types (implicit or pkce),\n       * then there could be an existing session so we don't want to prematurely remove it\n       */\n      if (isBrowser() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n        const { data, error } = await this._getSessionFromURL(params, callbackUrlType)\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error)\n\n          if (isAuthImplicitGrantRedirectError(error)) {\n            const errorCode = error.details?.code\n            if (\n              errorCode === 'identity_already_exists' ||\n              errorCode === 'identity_not_found' ||\n              errorCode === 'single_identity_not_deletable'\n            ) {\n              return { error }\n            }\n          }\n\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession()\n\n          return { error }\n        }\n\n        const { session, redirectType } = data\n\n        this._debug(\n          '#_initialize()',\n          'detected session in URL',\n          session,\n          'redirect type',\n          redirectType\n        )\n\n        await this._saveSession(session)\n\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session)\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session)\n          }\n        }, 0)\n\n        return { error: null }\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh()\n      return { error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { error }\n      }\n\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error),\n      }\n    } finally {\n      await this._handleVisibilityChange()\n      this._debug('#_initialize()', 'end')\n    }\n  }\n\n  /**\n   * Creates a new anonymous user.\n   *\n   * @returns A session where the is_anonymous claim in the access token JWT set to true\n   */\n  async signInAnonymously(credentials?: SignInAnonymouslyCredentials): Promise<AuthResponse> {\n    try {\n      const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n        headers: this.headers,\n        body: {\n          data: credentials?.options?.data ?? {},\n          gotrue_meta_security: { captcha_token: credentials?.options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials: SignUpWithPasswordCredentials): Promise<AuthResponse> {\n    try {\n      let res: AuthResponse\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: options?.data ?? {},\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          xform: _sessionResponse,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: options?.data ?? {},\n            channel: options?.channel ?? 'sms',\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponse,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(\n    credentials: SignInWithPasswordCredentials\n  ): Promise<AuthTokenResponsePassword> {\n    try {\n      let res: AuthResponsePassword\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n      const { data, error } = res\n\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return { data: { user: null, session: null }, error: new AuthInvalidTokenResponseError() }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return {\n        data: {\n          user: data.user,\n          session: data.session,\n          ...(data.weak_password ? { weakPassword: data.weak_password } : null),\n        },\n        error,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: credentials.options?.redirectTo,\n      scopes: credentials.options?.scopes,\n      queryParams: credentials.options?.queryParams,\n      skipBrowserRedirect: credentials.options?.skipBrowserRedirect,\n    })\n  }\n\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode: string): Promise<AuthTokenResponse> {\n    await this.initializePromise\n\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode)\n    })\n  }\n\n  /**\n   * Signs in a user by verifying a message signed by the user's private key.\n   * Only Solana supported at this time, using the Sign in with Solana standard.\n   */\n  async signInWithWeb3(credentials: Web3Credentials): Promise<\n    | {\n        data: { session: Session; user: User }\n        error: null\n      }\n    | { data: { session: null; user: null }; error: AuthError }\n  > {\n    const { chain } = credentials\n\n    if (chain === 'solana') {\n      return await this.signInWithSolana(credentials)\n    }\n\n    throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`)\n  }\n\n  private async signInWithSolana(credentials: SolanaWeb3Credentials) {\n    let message: string\n    let signature: Uint8Array\n\n    if ('message' in credentials) {\n      message = credentials.message\n      signature = credentials.signature\n    } else {\n      const { chain, wallet, statement, options } = credentials\n\n      let resolvedWallet: SolanaWallet\n\n      if (!isBrowser()) {\n        if (typeof wallet !== 'object' || !options?.url) {\n          throw new Error(\n            '@supabase/auth-js: Both wallet and url must be specified in non-browser environments.'\n          )\n        }\n\n        resolvedWallet = wallet\n      } else if (typeof wallet === 'object') {\n        resolvedWallet = wallet\n      } else {\n        const windowAny = window as any\n\n        if (\n          'solana' in windowAny &&\n          typeof windowAny.solana === 'object' &&\n          (('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function') ||\n            ('signMessage' in windowAny.solana &&\n              typeof windowAny.solana.signMessage === 'function'))\n        ) {\n          resolvedWallet = windowAny.solana\n        } else {\n          throw new Error(\n            `@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`\n          )\n        }\n      }\n\n      const url = new URL(options?.url ?? window.location.href)\n\n      if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n        const output = await resolvedWallet.signIn({\n          issuedAt: new Date().toISOString(),\n\n          ...options?.signInWithSolana,\n\n          // non-overridable properties\n          version: '1',\n          domain: url.host,\n          uri: url.href,\n\n          ...(statement ? { statement } : null),\n        })\n\n        let outputToProcess: any\n\n        if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n          outputToProcess = output[0]\n        } else if (\n          output &&\n          typeof output === 'object' &&\n          'signedMessage' in output &&\n          'signature' in output\n        ) {\n          outputToProcess = output\n        } else {\n          throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value')\n        }\n\n        if (\n          'signedMessage' in outputToProcess &&\n          'signature' in outputToProcess &&\n          (typeof outputToProcess.signedMessage === 'string' ||\n            outputToProcess.signedMessage instanceof Uint8Array) &&\n          outputToProcess.signature instanceof Uint8Array\n        ) {\n          message =\n            typeof outputToProcess.signedMessage === 'string'\n              ? outputToProcess.signedMessage\n              : new TextDecoder().decode(outputToProcess.signedMessage)\n          signature = outputToProcess.signature\n        } else {\n          throw new Error(\n            '@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields'\n          )\n        }\n      } else {\n        if (\n          !('signMessage' in resolvedWallet) ||\n          typeof resolvedWallet.signMessage !== 'function' ||\n          !('publicKey' in resolvedWallet) ||\n          typeof resolvedWallet !== 'object' ||\n          !resolvedWallet.publicKey ||\n          !('toBase58' in resolvedWallet.publicKey) ||\n          typeof resolvedWallet.publicKey.toBase58 !== 'function'\n        ) {\n          throw new Error(\n            '@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API'\n          )\n        }\n\n        message = [\n          `${url.host} wants you to sign in with your Solana account:`,\n          resolvedWallet.publicKey.toBase58(),\n          ...(statement ? ['', statement, ''] : ['']),\n          'Version: 1',\n          `URI: ${url.href}`,\n          `Issued At: ${options?.signInWithSolana?.issuedAt ?? new Date().toISOString()}`,\n          ...(options?.signInWithSolana?.notBefore\n            ? [`Not Before: ${options.signInWithSolana.notBefore}`]\n            : []),\n          ...(options?.signInWithSolana?.expirationTime\n            ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`]\n            : []),\n          ...(options?.signInWithSolana?.chainId\n            ? [`Chain ID: ${options.signInWithSolana.chainId}`]\n            : []),\n          ...(options?.signInWithSolana?.nonce ? [`Nonce: ${options.signInWithSolana.nonce}`] : []),\n          ...(options?.signInWithSolana?.requestId\n            ? [`Request ID: ${options.signInWithSolana.requestId}`]\n            : []),\n          ...(options?.signInWithSolana?.resources?.length\n            ? [\n                'Resources',\n                ...options.signInWithSolana.resources.map((resource) => `- ${resource}`),\n              ]\n            : []),\n        ].join('\\n')\n\n        const maybeSignature = await resolvedWallet.signMessage(\n          new TextEncoder().encode(message),\n          'utf8'\n        )\n\n        if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n          throw new Error(\n            '@supabase/auth-js: Wallet signMessage() API returned an recognized value'\n          )\n        }\n\n        signature = maybeSignature\n      }\n    }\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'POST',\n        `${this.url}/token?grant_type=web3`,\n        {\n          headers: this.headers,\n          body: {\n            chain: 'solana',\n            message,\n            signature: bytesToBase64URL(signature),\n\n            ...(credentials.options?.captchaToken\n              ? { gotrue_meta_security: { captcha_token: credentials.options?.captchaToken } }\n              : null),\n          },\n          xform: _sessionResponse,\n        }\n      )\n      if (error) {\n        throw error\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data: { ...data }, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  private async _exchangeCodeForSession(authCode: string): Promise<\n    | {\n        data: { session: Session; user: User; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; user: null; redirectType: null }; error: AuthError }\n  > {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n    const [codeVerifier, redirectType] = ((storageItem ?? '') as string).split('/')\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'POST',\n        `${this.url}/token?grant_type=pkce`,\n        {\n          headers: this.headers,\n          body: {\n            auth_code: authCode,\n            code_verifier: codeVerifier,\n          },\n          xform: _sessionResponse,\n        }\n      )\n      await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      if (error) {\n        throw error\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null, redirectType: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data: { ...data, redirectType: redirectType ?? null }, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials: SignInWithIdTokenCredentials): Promise<AuthTokenResponse> {\n    try {\n      const { options, provider, token, access_token, nonce } = credentials\n\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: { captcha_token: options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n\n      const { data, error } = res\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials: SignInWithPasswordlessCredentials): Promise<AuthOtpResponse> {\n    try {\n      if ('email' in credentials) {\n        const { email, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        const { error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      }\n      if ('phone' in credentials) {\n        const { phone, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            channel: options?.channel ?? 'sms',\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.')\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params: VerifyOtpParams): Promise<AuthResponse> {\n    try {\n      let redirectTo: string | undefined = undefined\n      let captchaToken: string | undefined = undefined\n      if ('options' in params) {\n        redirectTo = params.options?.redirectTo\n        captchaToken = params.options?.captchaToken\n      }\n      const { data, error } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: {\n          ...params,\n          gotrue_meta_security: { captcha_token: captchaToken },\n        },\n        redirectTo,\n        xform: _sessionResponse,\n      })\n\n      if (error) {\n        throw error\n      }\n\n      if (!data) {\n        throw new Error('An error occurred on token verification.')\n      }\n\n      const session: Session | null = data.session\n      const user: User = data.user\n\n      if (session?.access_token) {\n        await this._saveSession(session as Session)\n        await this._notifyAllSubscribers(\n          params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN',\n          session\n        )\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params: SignInWithSSO): Promise<SSOResponse> {\n    try {\n      let codeChallenge: string | null = null\n      let codeChallengeMethod: string | null = null\n      if (this.flowType === 'pkce') {\n        ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n          this.storage,\n          this.storageKey\n        )\n      }\n\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: {\n          ...('providerId' in params ? { provider_id: params.providerId } : null),\n          ...('domain' in params ? { domain: params.domain } : null),\n          redirect_to: params.options?.redirectTo ?? undefined,\n          ...(params?.options?.captchaToken\n            ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n            : null),\n          skip_http_redirect: true, // fetch does not handle redirects\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n        },\n        headers: this.headers,\n        xform: _ssoResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate(): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate()\n    })\n  }\n\n  private async _reauthenticate(): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) throw sessionError\n        if (!session) throw new AuthSessionMissingError()\n\n        const { error } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token,\n        })\n        return { data: { user: null, session: null }, error }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials: ResendParams): Promise<AuthOtpResponse> {\n    try {\n      const endpoint = `${this.url}/resend`\n      if ('email' in credentials) {\n        const { email, type, options } = credentials\n        const { error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      } else if ('phone' in credentials) {\n        const { phone, type, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError(\n        'You must provide either an email or phone number and a type'\n      )\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Returns the session, refreshing it if necessary.\n   *\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   *\n   * **IMPORTANT:** This method loads values directly from the storage attached\n   * to the client. If that storage is based on request cookies for example,\n   * the values in it may not be authentic and therefore it's strongly advised\n   * against using this method and its results in such circumstances. A warning\n   * will be emitted if this is detected. Use {@link #getUser()} instead.\n   */\n  async getSession() {\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return this._useSession(async (result) => {\n        return result\n      })\n    })\n\n    return result\n  }\n\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  private async _acquireLock<R>(acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n    this._debug('#_acquireLock', 'begin', acquireTimeout)\n\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length\n          ? this.pendingInLock[this.pendingInLock.length - 1]\n          : Promise.resolve()\n\n        const result = (async () => {\n          await last\n          return await fn()\n        })()\n\n        this.pendingInLock.push(\n          (async () => {\n            try {\n              await result\n            } catch (e: any) {\n              // we just care if it finished\n            }\n          })()\n        )\n\n        return result\n      }\n\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey)\n\n        try {\n          this.lockAcquired = true\n\n          const result = fn()\n\n          this.pendingInLock.push(\n            (async () => {\n              try {\n                await result\n              } catch (e: any) {\n                // we just care if it finished\n              }\n            })()\n          )\n\n          await result\n\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock]\n\n            await Promise.all(waitOn)\n\n            this.pendingInLock.splice(0, waitOn.length)\n          }\n\n          return await result\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey)\n\n          this.lockAcquired = false\n        }\n      })\n    } finally {\n      this._debug('#_acquireLock', 'end')\n    }\n  }\n\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  private async _useSession<R>(\n    fn: (\n      result:\n        | {\n            data: {\n              session: Session\n            }\n            error: null\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: AuthError\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: null\n          }\n    ) => Promise<R>\n  ): Promise<R> {\n    this._debug('#_useSession', 'begin')\n\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession()\n\n      return await fn(result)\n    } finally {\n      this._debug('#_useSession', 'end')\n    }\n  }\n\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  private async __loadSession(): Promise<\n    | {\n        data: {\n          session: Session\n        }\n        error: null\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: AuthError\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: null\n      }\n  > {\n    this._debug('#__loadSession()', 'begin')\n\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack)\n    }\n\n    try {\n      let currentSession: Session | null = null\n\n      const maybeSession = await getItemAsync(this.storage, this.storageKey)\n\n      this._debug('#getSession()', 'session from storage', maybeSession)\n\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid')\n          await this._removeSession()\n        }\n      }\n\n      if (!currentSession) {\n        return { data: { session: null }, error: null }\n      }\n\n      // A session is considered expired before the access token _actually_\n      // expires. When the autoRefreshToken option is off (or when the tab is\n      // in the background), very eager users of getSession() -- like\n      // realtime-js -- might send a valid JWT which will expire by the time it\n      // reaches the server.\n      const hasExpired = currentSession.expires_at\n        ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS\n        : false\n\n      this._debug(\n        '#__loadSession()',\n        `session has${hasExpired ? '' : ' not'} expired`,\n        'expires_at',\n        currentSession.expires_at\n      )\n\n      if (!hasExpired) {\n        if (this.userStorage) {\n          const maybeUser: { user?: User | null } | null = (await getItemAsync(\n            this.userStorage,\n            this.storageKey + '-user'\n          )) as any\n\n          if (maybeUser?.user) {\n            currentSession.user = maybeUser.user\n          } else {\n            currentSession.user = userNotAvailableProxy()\n          }\n        }\n\n        if (this.storage.isServer && currentSession.user) {\n          let suppressWarning = this.suppressGetSessionWarning\n          const proxySession: Session = new Proxy(currentSession, {\n            get: (target: any, prop: string, receiver: any) => {\n              if (!suppressWarning && prop === 'user') {\n                // only show warning when the user object is being accessed from the server\n                console.warn(\n                  'Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.'\n                )\n                suppressWarning = true // keeps this proxy instance from logging additional warnings\n                this.suppressGetSessionWarning = true // keeps this client's future proxy instances from warning\n              }\n              return Reflect.get(target, prop, receiver)\n            },\n          })\n          currentSession = proxySession\n        }\n\n        return { data: { session: currentSession }, error: null }\n      }\n\n      const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n      if (error) {\n        return { data: { session: null }, error }\n      }\n\n      return { data: { session }, error: null }\n    } finally {\n      this._debug('#__loadSession()', 'end')\n    }\n  }\n\n  /**\n   * Gets the current user details if there is an existing session. This method\n   * performs a network request to the Supabase Auth server, so the returned\n   * value is authentic and can be used to base authorization rules on.\n   *\n   * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n   */\n  async getUser(jwt?: string): Promise<UserResponse> {\n    if (jwt) {\n      return await this._getUser(jwt)\n    }\n\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return await this._getUser()\n    })\n\n    return result\n  }\n\n  private async _getUser(jwt?: string): Promise<UserResponse> {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse,\n        })\n      }\n\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n\n        // returns an error if there is no access_token or custom authorization header\n        if (!data.session?.access_token && !this.hasCustomAuthorizationHeader) {\n          return { data: { user: null }, error: new AuthSessionMissingError() }\n        }\n\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n          xform: _userResponse,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        if (isAuthSessionMissingError(error)) {\n          // JWT contains a `session_id` which does not correspond to an active\n          // session in the database, indicating the user is signed out.\n\n          await this._removeSession()\n          await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n        }\n\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options)\n    })\n  }\n\n  protected async _updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          throw sessionError\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError()\n        }\n        const session: Session = sessionData.session\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n\n        const { data, error: userError } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            ...attributes,\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          jwt: session.access_token,\n          xform: _userResponse,\n        })\n        if (userError) throw userError\n        session.user = data.user as User\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('USER_UPDATED', session)\n        return { data: { user: session.user }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession)\n    })\n  }\n\n  protected async _setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError()\n      }\n\n      const timeNow = Date.now() / 1000\n      let expiresAt = timeNow\n      let hasExpired = true\n      let session: Session | null = null\n      const { payload } = decodeJWT(currentSession.access_token)\n      if (payload.exp) {\n        expiresAt = payload.exp\n        hasExpired = expiresAt <= timeNow\n      }\n\n      if (hasExpired) {\n        const { session: refreshedSession, error } = await this._callRefreshToken(\n          currentSession.refresh_token\n        )\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!refreshedSession) {\n          return { data: { user: null, session: null }, error: null }\n        }\n        session = refreshedSession\n      } else {\n        const { data, error } = await this._getUser(currentSession.access_token)\n        if (error) {\n          throw error\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt,\n        }\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user: session.user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession?: { refresh_token: string }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession)\n    })\n  }\n\n  protected async _refreshSession(currentSession?: {\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        if (!currentSession) {\n          const { data, error } = result\n          if (error) {\n            throw error\n          }\n\n          currentSession = data.session ?? undefined\n        }\n\n        if (!currentSession?.refresh_token) {\n          throw new AuthSessionMissingError()\n        }\n\n        const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!session) {\n          return { data: { user: null, session: null }, error: null }\n        }\n\n        return { data: { user: session.user, session }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets the session data from a URL string\n   */\n  private async _getSessionFromURL(\n    params: { [parameter: string]: string },\n    callbackUrlType: string\n  ): Promise<\n    | {\n        data: { session: Session; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; redirectType: null }; error: AuthError }\n  > {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.')\n\n      // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n      if (params.error || params.error_description || params.error_code) {\n        // The error class returned implies that the redirect is from an implicit grant flow\n        // but it could also be from a redirect error from a PKCE flow.\n        throw new AuthImplicitGrantRedirectError(\n          params.error_description || 'Error in URL with unspecified error_description',\n          {\n            error: params.error || 'unspecified_error',\n            code: params.error_code || 'unspecified_code',\n          }\n        )\n      }\n\n      // Checks for mismatches between the flowType initialised in the client and the URL parameters\n      switch (callbackUrlType) {\n        case 'implicit':\n          if (this.flowType === 'pkce') {\n            throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.')\n          }\n          break\n        case 'pkce':\n          if (this.flowType === 'implicit') {\n            throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.')\n          }\n          break\n        default:\n        // there's no mismatch so we continue\n      }\n\n      // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n      if (callbackUrlType === 'pkce') {\n        this._debug('#_initialize()', 'begin', 'is PKCE flow', true)\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.')\n        const { data, error } = await this._exchangeCodeForSession(params.code)\n        if (error) throw error\n\n        const url = new URL(window.location.href)\n        url.searchParams.delete('code')\n\n        window.history.replaceState(window.history.state, '', url.toString())\n\n        return { data: { session: data.session, redirectType: null }, error: null }\n      }\n\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type,\n      } = params\n\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL')\n      }\n\n      const timeNow = Math.round(Date.now() / 1000)\n      const expiresIn = parseInt(expires_in)\n      let expiresAt = timeNow + expiresIn\n\n      if (expires_at) {\n        expiresAt = parseInt(expires_at)\n      }\n\n      const actuallyExpiresIn = expiresAt - timeNow\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n        console.warn(\n          `@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`\n        )\n      }\n\n      const issuedAt = expiresAt - expiresIn\n      if (timeNow - issuedAt >= 120) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      } else if (timeNow - issuedAt < 0) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      }\n\n      const { data, error } = await this._getUser(access_token)\n      if (error) throw error\n\n      const session: Session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user,\n      }\n\n      // Remove tokens from URL\n      window.location.hash = ''\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash')\n\n      return { data: { session, redirectType: params.type }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  private _isImplicitGrantCallback(params: { [parameter: string]: string }): boolean {\n    return Boolean(params.access_token || params.error_description)\n  }\n\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  private async _isPKCECallback(params: { [parameter: string]: string }): Promise<boolean> {\n    const currentStorageContent = await getItemAsync(\n      this.storage,\n      `${this.storageKey}-code-verifier`\n    )\n\n    return !!(params.code && currentStorageContent)\n  }\n\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut(options: SignOut = { scope: 'global' }): Promise<{ error: AuthError | null }> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options)\n    })\n  }\n\n  protected async _signOut(\n    { scope }: SignOut = { scope: 'global' }\n  ): Promise<{ error: AuthError | null }> {\n    return await this._useSession(async (result) => {\n      const { data, error: sessionError } = result\n      if (sessionError) {\n        return { error: sessionError }\n      }\n      const accessToken = data.session?.access_token\n      if (accessToken) {\n        const { error } = await this.admin.signOut(accessToken, scope)\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (\n            !(\n              isAuthApiError(error) &&\n              (error.status === 404 || error.status === 401 || error.status === 403)\n            )\n          ) {\n            return { error }\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession()\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      }\n      return { error: null }\n    })\n  }\n\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(\n    callback: (event: AuthChangeEvent, session: Session | null) => void | Promise<void>\n  ): {\n    data: { subscription: Subscription }\n  } {\n    const id: string = uuid()\n    const subscription: Subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id)\n\n        this.stateChangeEmitters.delete(id)\n      },\n    }\n\n    this._debug('#onAuthStateChange()', 'registered callback with id', id)\n\n    this.stateChangeEmitters.set(id, subscription)\n    ;(async () => {\n      await this.initializePromise\n\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id)\n      })\n    })()\n\n    return { data: { subscription } }\n  }\n\n  private async _emitInitialSession(id: string): Promise<void> {\n    return await this._useSession(async (result) => {\n      try {\n        const {\n          data: { session },\n          error,\n        } = result\n        if (error) throw error\n\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', session)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session)\n      } catch (err) {\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', null)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err)\n        console.error(err)\n      }\n    })\n  }\n\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(\n    email: string,\n    options: {\n      redirectTo?: string\n      captchaToken?: string\n    } = {}\n  ): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    let codeChallenge: string | null = null\n    let codeChallengeMethod: string | null = null\n\n    if (this.flowType === 'pkce') {\n      ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey,\n        true // isPasswordRecovery\n      )\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: { captcha_token: options.captchaToken },\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities(): Promise<\n    | {\n        data: {\n          identities: UserIdentity[]\n        }\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      const { data, error } = await this.getUser()\n      if (error) throw error\n      return { data: { identities: data.user.identities ?? [] }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    try {\n      const { data, error } = await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) throw error\n        const url: string = await this._getUrlForProvider(\n          `${this.url}/user/identities/authorize`,\n          credentials.provider,\n          {\n            redirectTo: credentials.options?.redirectTo,\n            scopes: credentials.options?.scopes,\n            queryParams: credentials.options?.queryParams,\n            skipBrowserRedirect: true,\n          }\n        )\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n        })\n      })\n      if (error) throw error\n      if (isBrowser() && !credentials.options?.skipBrowserRedirect) {\n        window.location.assign(data?.url)\n      }\n      return { data: { provider: credentials.provider, url: data?.url }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { provider: credentials.provider, url: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity: UserIdentity): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n        return await _request(\n          this.fetch,\n          'DELETE',\n          `${this.url}/user/identities/${identity.identity_id}`,\n          {\n            headers: this.headers,\n            jwt: data.session?.access_token ?? undefined,\n          }\n        )\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  private async _refreshAccessToken(refreshToken: string): Promise<AuthResponse> {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`\n    this._debug(debugName, 'begin')\n\n    try {\n      const startedAt = Date.now()\n\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(\n        async (attempt) => {\n          if (attempt > 0) {\n            await sleep(200 * Math.pow(2, attempt - 1)) // 200, 400, 800, ...\n          }\n\n          this._debug(debugName, 'refreshing attempt', attempt)\n\n          return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n            body: { refresh_token: refreshToken },\n            headers: this.headers,\n            xform: _sessionResponse,\n          })\n        },\n        (attempt, error) => {\n          const nextBackOffInterval = 200 * Math.pow(2, attempt)\n          return (\n            error &&\n            isAuthRetryableFetchError(error) &&\n            // retryable only if the request can be sent before the backoff overflows the tick duration\n            Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS\n          )\n        }\n      )\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n      throw error\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private _isValidSession(maybeSession: unknown): maybeSession is Session {\n    const isValidSession =\n      typeof maybeSession === 'object' &&\n      maybeSession !== null &&\n      'access_token' in maybeSession &&\n      'refresh_token' in maybeSession &&\n      'expires_at' in maybeSession\n\n    return isValidSession\n  }\n\n  private async _handleProviderSignIn(\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const url: string = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams,\n    })\n\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url)\n\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url)\n    }\n\n    return { data: { provider, url }, error: null }\n  }\n\n  /**\n   * Recovers the session from LocalStorage and refreshes the token\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  private async _recoverAndRefresh() {\n    const debugName = '#_recoverAndRefresh()'\n    this._debug(debugName, 'begin')\n\n    try {\n      const currentSession = (await getItemAsync(this.storage, this.storageKey)) as Session | null\n\n      if (currentSession && this.userStorage) {\n        let maybeUser: { user: User | null } | null = (await getItemAsync(\n          this.userStorage,\n          this.storageKey + '-user'\n        )) as any\n\n        if (!this.storage.isServer && Object.is(this.storage, this.userStorage) && !maybeUser) {\n          // storage and userStorage are the same storage medium, for example\n          // window.localStorage if userStorage does not have the user from\n          // storage stored, store it first thereby migrating the user object\n          // from storage -> userStorage\n\n          maybeUser = { user: currentSession.user }\n          await setItemAsync(this.userStorage, this.storageKey + '-user', maybeUser)\n        }\n\n        currentSession.user = maybeUser?.user ?? userNotAvailableProxy()\n      } else if (currentSession && !currentSession.user) {\n        // user storage is not set, let's check if it was previously enabled so\n        // we bring back the storage as it should be\n\n        if (!currentSession.user) {\n          // test if userStorage was previously enabled and the storage medium was the same, to move the user back under the same key\n          const separateUser: { user: User | null } | null = (await getItemAsync(\n            this.storage,\n            this.storageKey + '-user'\n          )) as any\n\n          if (separateUser && separateUser?.user) {\n            currentSession.user = separateUser.user\n\n            await removeItemAsync(this.storage, this.storageKey + '-user')\n            await setItemAsync(this.storage, this.storageKey, currentSession)\n          } else {\n            currentSession.user = userNotAvailableProxy()\n          }\n        }\n      }\n\n      this._debug(debugName, 'session from storage', currentSession)\n\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid')\n        if (currentSession !== null) {\n          await this._removeSession()\n        }\n\n        return\n      }\n\n      const expiresWithMargin =\n        (currentSession.expires_at ?? Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS\n\n      this._debug(\n        debugName,\n        `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`\n      )\n\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const { error } = await this._callRefreshToken(currentSession.refresh_token)\n\n          if (error) {\n            console.error(error)\n\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(\n                debugName,\n                'refresh failed with a non-retryable error, removing the session',\n                error\n              )\n              await this._removeSession()\n            }\n          }\n        }\n      } else if (\n        currentSession.user &&\n        (currentSession.user as any).__isUserNotAvailableProxy === true\n      ) {\n        // If we have a proxy user, try to get the real user data\n        try {\n          const { data, error: userError } = await this._getUser(currentSession.access_token)\n\n          if (!userError && data?.user) {\n            currentSession.user = data.user\n            await this._saveSession(currentSession)\n            await this._notifyAllSubscribers('SIGNED_IN', currentSession)\n          } else {\n            this._debug(debugName, 'could not get user data, skipping SIGNED_IN notification')\n          }\n        } catch (getUserError) {\n          console.error('Error getting user data:', getUserError)\n          this._debug(\n            debugName,\n            'error getting user data, skipping SIGNED_IN notification',\n            getUserError\n          )\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession)\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err)\n\n      console.error(err)\n      return\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _callRefreshToken(refreshToken: string): Promise<CallRefreshTokenResult> {\n    if (!refreshToken) {\n      throw new AuthSessionMissingError()\n    }\n\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise\n    }\n\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`\n\n    this._debug(debugName, 'begin')\n\n    try {\n      this.refreshingDeferred = new Deferred<CallRefreshTokenResult>()\n\n      const { data, error } = await this._refreshAccessToken(refreshToken)\n      if (error) throw error\n      if (!data.session) throw new AuthSessionMissingError()\n\n      await this._saveSession(data.session)\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session)\n\n      const result = { session: data.session, error: null }\n\n      this.refreshingDeferred.resolve(result)\n\n      return result\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        const result = { session: null, error }\n\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession()\n        }\n\n        this.refreshingDeferred?.resolve(result)\n\n        return result\n      }\n\n      this.refreshingDeferred?.reject(error)\n      throw error\n    } finally {\n      this.refreshingDeferred = null\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _notifyAllSubscribers(\n    event: AuthChangeEvent,\n    session: Session | null,\n    broadcast = true\n  ) {\n    const debugName = `#_notifyAllSubscribers(${event})`\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`)\n\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({ event, session })\n      }\n\n      const errors: any[] = []\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n        try {\n          await x.callback(event, session)\n        } catch (e: any) {\n          errors.push(e)\n        }\n      })\n\n      await Promise.all(promises)\n\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i])\n        }\n\n        throw errors[0]\n      }\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  private async _saveSession(session: Session) {\n    this._debug('#_saveSession()', session)\n    // _saveSession is always called whenever a new session has been acquired\n    // so we can safely suppress the warning returned by future getSession calls\n    this.suppressGetSessionWarning = true\n\n    // Create a shallow copy to work with, to avoid mutating the original session object if it's used elsewhere\n    const sessionToProcess = { ...session }\n\n    const userIsProxy =\n      sessionToProcess.user && (sessionToProcess.user as any).__isUserNotAvailableProxy === true\n    if (this.userStorage) {\n      if (!userIsProxy && sessionToProcess.user) {\n        // If it's a real user object, save it to userStorage.\n        await setItemAsync(this.userStorage, this.storageKey + '-user', {\n          user: sessionToProcess.user,\n        })\n      } else if (userIsProxy) {\n        // If it's the proxy, it means user was not found in userStorage.\n        // We should ensure no stale user data for this key exists in userStorage if we were to save null,\n        // or simply not save the proxy. For now, we don't save the proxy here.\n        // If there's a need to clear userStorage if user becomes proxy, that logic would go here.\n      }\n\n      // Prepare the main session data for primary storage: remove the user property before cloning\n      // This is important because the original session.user might be the proxy\n      const mainSessionData: Omit<Session, 'user'> & { user?: User } = { ...sessionToProcess }\n      delete mainSessionData.user // Remove user (real or proxy) before cloning for main storage\n\n      const clonedMainSessionData = deepClone(mainSessionData)\n      await setItemAsync(this.storage, this.storageKey, clonedMainSessionData)\n    } else {\n      // No userStorage is configured.\n      // In this case, session.user should ideally not be a proxy.\n      // If it were, structuredClone would fail. This implies an issue elsewhere if user is a proxy here\n      const clonedSession = deepClone(sessionToProcess) // sessionToProcess still has its original user property\n      await setItemAsync(this.storage, this.storageKey, clonedSession)\n    }\n  }\n\n  private async _removeSession() {\n    this._debug('#_removeSession()')\n\n    await removeItemAsync(this.storage, this.storageKey)\n    await removeItemAsync(this.storage, this.storageKey + '-code-verifier')\n    await removeItemAsync(this.storage, this.storageKey + '-user')\n\n    if (this.userStorage) {\n      await removeItemAsync(this.userStorage, this.storageKey + '-user')\n    }\n\n    await this._notifyAllSubscribers('SIGNED_OUT', null)\n  }\n\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  private _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()')\n\n    const callback = this.visibilityChangedCallback\n    this.visibilityChangedCallback = null\n\n    try {\n      if (callback && isBrowser() && window?.removeEventListener) {\n        window.removeEventListener('visibilitychange', callback)\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e)\n    }\n  }\n\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _startAutoRefresh() {\n    await this._stopAutoRefresh()\n\n    this._debug('#_startAutoRefresh()')\n\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS)\n    this.autoRefreshTicker = ticker\n\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref()\n      // @ts-expect-error TS has no context of Deno\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-expect-error TS has no context of Deno\n      Deno.unrefTimer(ticker)\n    }\n\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise\n      await this._autoRefreshTokenTick()\n    }, 0)\n  }\n\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()')\n\n    const ticker = this.autoRefreshTicker\n    this.autoRefreshTicker = null\n\n    if (ticker) {\n      clearInterval(ticker)\n    }\n  }\n\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._startAutoRefresh()\n  }\n\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._stopAutoRefresh()\n  }\n\n  /**\n   * Runs the auto refresh token tick.\n   */\n  private async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin')\n\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now()\n\n          try {\n            return await this._useSession(async (result) => {\n              const {\n                data: { session },\n              } = result\n\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session')\n                return\n              }\n\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor(\n                (session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS\n              )\n\n              this._debug(\n                '#_autoRefreshTokenTick()',\n                `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`\n              )\n\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token)\n              }\n            })\n          } catch (e: any) {\n            console.error(\n              'Auto refresh tick failed with error. This is likely a transient error.',\n              e\n            )\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end')\n        }\n      })\n    } catch (e: any) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available')\n      } else {\n        throw e\n      }\n    }\n  }\n\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  private async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()')\n\n    if (!isBrowser() || !window?.addEventListener) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh()\n      }\n\n      return false\n    }\n\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false)\n\n      window?.addEventListener('visibilitychange', this.visibilityChangedCallback)\n\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true) // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error)\n    }\n  }\n\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  private async _onVisibilityChanged(calledFromInitialize: boolean) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`\n    this._debug(methodName, 'visibilityState', document.visibilityState)\n\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh()\n      }\n\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise\n\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(\n              methodName,\n              'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting'\n            )\n\n            // visibility has changed while waiting for the lock, abort\n            return\n          }\n\n          // recover the session\n          await this._recoverAndRefresh()\n        })\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh()\n      }\n    }\n  }\n\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  private async _getUrlForProvider(\n    url: string,\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const urlParams: string[] = [`provider=${encodeURIComponent(provider)}`]\n    if (options?.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`)\n    }\n    if (options?.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`)\n    }\n    if (this.flowType === 'pkce') {\n      const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey\n      )\n\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n      })\n      urlParams.push(flowParams.toString())\n    }\n    if (options?.queryParams) {\n      const query = new URLSearchParams(options.queryParams)\n      urlParams.push(query.toString())\n    }\n    if (options?.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`)\n    }\n\n    return `${url}?${urlParams.join('&')}`\n  }\n\n  private async _unenroll(params: MFAUnenrollParams): Promise<AuthMFAUnenrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#enroll}\n   */\n  private async _enroll(params: MFAEnrollTOTPParams): Promise<AuthMFAEnrollTOTPResponse>\n  private async _enroll(params: MFAEnrollPhoneParams): Promise<AuthMFAEnrollPhoneResponse>\n  private async _enroll(params: MFAEnrollParams): Promise<AuthMFAEnrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        const body = {\n          friendly_name: params.friendlyName,\n          factor_type: params.factorType,\n          ...(params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }),\n        }\n\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body,\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n\n        if (error) {\n          return { data: null, error }\n        }\n\n        if (params.factorType === 'totp' && data?.totp?.qr_code) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`\n        }\n\n        return { data, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  private async _verify(params: MFAVerifyParams): Promise<AuthMFAVerifyResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          const { data, error } = await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/verify`,\n            {\n              body: { code: params.code, challenge_id: params.challengeId },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n          if (error) {\n            return { data: null, error }\n          }\n\n          await this._saveSession({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in,\n            ...data,\n          })\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data)\n\n          return { data, error }\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  private async _challenge(params: MFAChallengeParams): Promise<AuthMFAChallengeResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          return await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/challenge`,\n            {\n              body: { channel: params.channel },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  private async _challengeAndVerify(\n    params: MFAChallengeAndVerifyParams\n  ): Promise<AuthMFAVerifyResponse> {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n\n    const { data: challengeData, error: challengeError } = await this._challenge({\n      factorId: params.factorId,\n    })\n    if (challengeError) {\n      return { data: null, error: challengeError }\n    }\n\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code,\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  private async _listFactors(): Promise<AuthMFAListFactorsResponse> {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: { user },\n      error: userError,\n    } = await this.getUser()\n    if (userError) {\n      return { data: null, error: userError }\n    }\n\n    const factors = user?.factors || []\n    const totp = factors.filter(\n      (factor) => factor.factor_type === 'totp' && factor.status === 'verified'\n    )\n    const phone = factors.filter(\n      (factor) => factor.factor_type === 'phone' && factor.status === 'verified'\n    )\n\n    return {\n      data: {\n        all: factors,\n        totp,\n        phone,\n      },\n      error: null,\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  private async _getAuthenticatorAssuranceLevel(): Promise<AuthMFAGetAuthenticatorAssuranceLevelResponse> {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n        if (!session) {\n          return {\n            data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n            error: null,\n          }\n        }\n\n        const { payload } = decodeJWT(session.access_token)\n\n        let currentLevel: AuthenticatorAssuranceLevels | null = null\n\n        if (payload.aal) {\n          currentLevel = payload.aal\n        }\n\n        let nextLevel: AuthenticatorAssuranceLevels | null = currentLevel\n\n        const verifiedFactors =\n          session.user.factors?.filter((factor: Factor) => factor.status === 'verified') ?? []\n\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2'\n        }\n\n        const currentAuthenticationMethods = payload.amr || []\n\n        return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null }\n      })\n    })\n  }\n\n  private async fetchJwk(kid: string, jwks: { keys: JWK[] } = { keys: [] }): Promise<JWK | null> {\n    // try fetching from the supplied jwks\n    let jwk = jwks.keys.find((key) => key.kid === kid)\n    if (jwk) {\n      return jwk\n    }\n\n    const now = Date.now()\n\n    // try fetching from cache\n    jwk = this.jwks.keys.find((key) => key.kid === kid)\n\n    // jwk exists and jwks isn't stale\n    if (jwk && this.jwks_cached_at + JWKS_TTL > now) {\n      return jwk\n    }\n    // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n    const { data, error } = await _request(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n      headers: this.headers,\n    })\n    if (error) {\n      throw error\n    }\n    if (!data.keys || data.keys.length === 0) {\n      return null\n    }\n\n    this.jwks = data\n    this.jwks_cached_at = now\n\n    // Find the signing key\n    jwk = data.keys.find((key: any) => key.kid === kid)\n    if (!jwk) {\n      return null\n    }\n    return jwk\n  }\n\n  /**\n   * Extracts the JWT claims present in the access token by first verifying the\n   * JWT against the server's JSON Web Key Set endpoint\n   * `/.well-known/jwks.json` which is often cached, resulting in significantly\n   * faster responses. Prefer this method over {@link #getUser} which always\n   * sends a request to the Auth server for each JWT.\n   *\n   * If the project is not using an asymmetric JWT signing key (like ECC or\n   * RSA) it always sends a request to the Auth server (similar to {@link\n   * #getUser}) to verify the JWT.\n   *\n   * @param jwt An optional specific JWT you wish to verify, not the one you\n   *            can obtain from {@link #getSession}.\n   * @param options Various additional options that allow you to customize the\n   *                behavior of this method.\n   */\n  async getClaims(\n    jwt?: string,\n    options: {\n      /**\n       * @deprecated Please use options.jwks instead.\n       */\n      keys?: JWK[]\n\n      /** If set to `true` the `exp` claim will not be validated against the current time. */\n      allowExpired?: boolean\n\n      /** If set, this JSON Web Key Set is going to have precedence over the cached value available on the server. */\n      jwks?: { keys: JWK[] }\n    } = {}\n  ): Promise<\n    | {\n        data: { claims: JwtPayload; header: JwtHeader; signature: Uint8Array }\n        error: null\n      }\n    | { data: null; error: AuthError }\n    | { data: null; error: null }\n  > {\n    try {\n      let token = jwt\n      if (!token) {\n        const { data, error } = await this.getSession()\n        if (error || !data.session) {\n          return { data: null, error }\n        }\n        token = data.session.access_token\n      }\n\n      const {\n        header,\n        payload,\n        signature,\n        raw: { header: rawHeader, payload: rawPayload },\n      } = decodeJWT(token)\n\n      if (!options?.allowExpired) {\n        // Reject expired JWTs should only happen if jwt argument was passed\n        validateExp(payload.exp)\n      }\n\n      const signingKey =\n        !header.alg ||\n        header.alg.startsWith('HS') ||\n        !header.kid ||\n        !('crypto' in globalThis && 'subtle' in globalThis.crypto)\n          ? null\n          : await this.fetchJwk(header.kid, options?.keys ? { keys: options.keys } : options?.jwks)\n\n      // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n      if (!signingKey) {\n        const { error } = await this.getUser(token)\n        if (error) {\n          throw error\n        }\n        // getUser succeeds so the claims in the JWT can be trusted\n        return {\n          data: {\n            claims: payload,\n            header,\n            signature,\n          },\n          error: null,\n        }\n      }\n\n      const algorithm = getAlgorithm(header.alg)\n\n      // Convert JWK to CryptoKey\n      const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n        'verify',\n      ])\n\n      // Verify the signature\n      const isValid = await crypto.subtle.verify(\n        algorithm,\n        publicKey,\n        signature,\n        stringToUint8Array(`${rawHeader}.${rawPayload}`)\n      )\n\n      if (!isValid) {\n        throw new AuthInvalidJwtError('Invalid JWT signature')\n      }\n\n      // If verification succeeds, decode and return claims\n      return {\n        data: {\n          claims: payload,\n          header,\n          signature,\n        },\n        error: null,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n}\n", "import { SupportedStorage } from './types'\n\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store: { [key: string]: string } = {}): SupportedStorage {\n  return {\n    getItem: (key) => {\n      return store[key] || null\n    },\n\n    setItem: (key, value) => {\n      store[key] = value\n    },\n\n    removeItem: (key) => {\n      delete store[key]\n    },\n  }\n}\n", "/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n  if (typeof globalThis === 'object') return\n  try {\n    Object.defineProperty(Object.prototype, '__magic__', {\n      get: function () {\n        return this\n      },\n      configurable: true,\n    })\n    // @ts-expect-error 'Allow access to magic'\n    __magic__.globalThis = __magic__\n    // @ts-expect-error 'Allow access to magic'\n    delete Object.prototype.__magic__\n  } catch (e) {\n    if (typeof self !== 'undefined') {\n      // @ts-expect-error 'Allow access to globals'\n      self.globalThis = self\n    }\n  }\n}\n", "import { supportsLocalStorage } from './helpers'\n\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(\n    globalThis &&\n    supportsLocalStorage() &&\n    globalThis.localStorage &&\n    globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'\n  ),\n}\n\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nexport abstract class LockAcquireTimeoutError extends Error {\n  public readonly isAcquireTimeout = true\n\n  constructor(message: string) {\n    super(message)\n  }\n}\n\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\nexport class ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock<R>(\n  name: string,\n  acquireTimeout: number,\n  fn: () => Promise<R>\n): Promise<R> {\n  if (internals.debug) {\n    console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout)\n  }\n\n  const abortController = new globalThis.AbortController()\n\n  if (acquireTimeout > 0) {\n    setTimeout(() => {\n      abortController.abort()\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name)\n      }\n    }, acquireTimeout)\n  }\n\n  // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n\n  // Wrapping navigator.locks.request() with a plain Promise is done as some\n  // libraries like zone.js patch the Promise object to track the execution\n  // context. However, it appears that most browsers use an internal promise\n  // implementation when using the navigator.locks.request() API causing them\n  // to lose context and emit confusing log messages or break certain features.\n  // This wrapping is believed to help zone.js track the execution context\n  // better.\n  return await Promise.resolve().then(() =>\n    globalThis.navigator.locks.request(\n      name,\n      acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n          }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n          },\n      async (lock) => {\n        if (lock) {\n          if (internals.debug) {\n            console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name)\n          }\n\n          try {\n            return await fn()\n          } finally {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name)\n            }\n          }\n        } else {\n          if (acquireTimeout === 0) {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name)\n            }\n\n            throw new NavigatorLockAcquireTimeoutError(\n              `Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`\n            )\n          } else {\n            if (internals.debug) {\n              try {\n                const result = await globalThis.navigator.locks.query()\n\n                console.log(\n                  '@supabase/gotrue-js: Navigator LockManager state',\n                  JSON.stringify(result, null, '  ')\n                )\n              } catch (e: any) {\n                console.warn(\n                  '@supabase/gotrue-js: Error when querying Navigator LockManager state',\n                  e\n                )\n              }\n            }\n\n            // Browser is not following the Navigator LockManager spec, it\n            // returned a null lock when we didn't use ifAvailable. So we can\n            // pretend the lock is acquired in the name of backward compatibility\n            // and user experience and just run the function.\n            console.warn(\n              '@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request'\n            )\n\n            return await fn()\n          }\n        }\n      }\n    )\n  )\n}\n\nconst PROCESS_LOCKS: { [name: string]: Promise<any> } = {}\n\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function processLock<R>(\n  name: string,\n  acquireTimeout: number,\n  fn: () => Promise<R>\n): Promise<R> {\n  const previousOperation = PROCESS_LOCKS[name] ?? Promise.resolve()\n\n  const currentOperation = Promise.race(\n    [\n      previousOperation.catch(() => {\n        // ignore error of previous operation that we're waiting to finish\n        return null\n      }),\n      acquireTimeout >= 0\n        ? new Promise((_, reject) => {\n            setTimeout(() => {\n              reject(\n                new ProcessLockAcquireTimeoutError(\n                  `Acquring process lock with name \"${name}\" timed out`\n                )\n              )\n            }, acquireTimeout)\n          })\n        : null,\n    ].filter((x) => x)\n  )\n    .catch((e: any) => {\n      if (e && e.isAcquireTimeout) {\n        throw e\n      }\n\n      return null\n    })\n    .then(async () => {\n      // previous operations finished and we didn't get a race on the acquire\n      // timeout, so the current operation can finally start\n      return await fn()\n    })\n\n  PROCESS_LOCKS[name] = currentOperation.catch(async (e: any) => {\n    if (e && e.isAcquireTimeout) {\n      // if the current operation timed out, it doesn't mean that the previous\n      // operation finished, so we need contnue waiting for it to finish\n      await previousOperation\n\n      return null\n    }\n\n    throw e\n  })\n\n  // finally wait for the current operation to finish successfully, with an\n  // error or with an acquire timeout error\n  return await currentOperation\n}\n", "import GoTrueAdminApi from './GoTrueAdminApi'\n\nconst AuthAdminApi = GoTrueAdminApi\n\nexport default AuthAdminApi\n", "import GoTrueClient from './GoTrueClient'\n\nconst AuthClient = GoTrueClient\n\nexport default AuthClient\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,UAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;AAAA;AAAA;;;ACA7F;AAAA;AAAA,mBAAAA;AAAA,IAAA;AAAA,oBAAAC;AAAA,IAAA;AAAA,iBAAAC;AAAA;AAAA,MAGI,WAUA,cAESA,QAEN,iBAEMF,UACA,SACAC;AArBb;AAAA;AAAA;AAAA;AAGA,MAAI,YAAY,kCAAW;AAIvB,YAAI,OAAO,SAAS,aAAa;AAAE,iBAAO;AAAA,QAAM;AAChD,YAAI,OAAO,WAAW,aAAa;AAAE,iBAAO;AAAA,QAAQ;AACpD,YAAI,OAAO,WAAW,aAAa;AAAE,iBAAO;AAAA,QAAQ;AACpD,cAAM,IAAI,MAAM,gCAAgC;AAAA,MACpD,GARgB;AAUhB,MAAI,eAAe,UAAU;AAEtB,MAAMC,SAAQ,aAAa;AAElC,MAAO,kBAAQ,aAAa,MAAM,KAAK,YAAY;AAE5C,MAAMF,WAAU,aAAa;AAC7B,MAAM,UAAU,aAAa;AAC7B,MAAMC,YAAW,aAAa;AAAA;AAAA;;;;;;;;AChBrC,UAAqBE,kBAArB,cAA4C,MAAK;eAAA;;;QAK/C,YAAY,SAAyE;AACnF,gBAAM,QAAQ,OAAO;AACrB,eAAK,OAAO;AACZ,eAAK,UAAU,QAAQ;AACvB,eAAK,OAAO,QAAQ;AACpB,eAAK,OAAO,QAAQ;QACtB;;AAXF,cAAA,UAAAA;;;;;;;;;;;;;ACJA,UAAA,eAAA,gBAAA,+CAAA;AAUA,UAAA,mBAAA,gBAAA,wBAAA;AAGA,UAA8BC,oBAA9B,MAA8C;eAAA;;;QAgB5C,YAAY,SAAiC;AALnC,eAAA,qBAAqB;AAM7B,eAAK,SAAS,QAAQ;AACtB,eAAK,MAAM,QAAQ;AACnB,eAAK,UAAU,QAAQ;AACvB,eAAK,SAAS,QAAQ;AACtB,eAAK,OAAO,QAAQ;AACpB,eAAK,qBAAqB,QAAQ;AAClC,eAAK,SAAS,QAAQ;AACtB,eAAK,gBAAgB,QAAQ;AAE7B,cAAI,QAAQ,OAAO;AACjB,iBAAK,QAAQ,QAAQ;qBACZ,OAAO,UAAU,aAAa;AACvC,iBAAK,QAAQ,aAAA;iBACR;AACL,iBAAK,QAAQ;;QAEjB;;;;;;;QAQA,eAAY;AACV,eAAK,qBAAqB;AAC1B,iBAAO;QACT;;;;QAKA,UAAU,MAAc,OAAa;AACnC,eAAK,UAAO,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO;AAChC,eAAK,QAAQ,IAAI,IAAI;AACrB,iBAAO;QACT;QAEA,KAME,aAQA,YAAmF;AAGnF,cAAI,KAAK,WAAW,QAAW;qBAEpB,CAAC,OAAO,MAAM,EAAE,SAAS,KAAK,MAAM,GAAG;AAChD,iBAAK,QAAQ,gBAAgB,IAAI,KAAK;iBACjC;AACL,iBAAK,QAAQ,iBAAiB,IAAI,KAAK;;AAEzC,cAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,iBAAK,QAAQ,cAAc,IAAI;;AAKjC,gBAAM,SAAS,KAAK;AACpB,cAAI,MAAM,OAAO,KAAK,IAAI,SAAQ,GAAI;YACpC,QAAQ,KAAK;YACb,SAAS,KAAK;YACd,MAAM,KAAK,UAAU,KAAK,IAAI;YAC9B,QAAQ,KAAK;WACd,EAAE,KAAK,OAAOC,SAAO;;AACpB,gBAAI,QAAQ;AACZ,gBAAI,OAAO;AACX,gBAAI,QAAuB;AAC3B,gBAAI,SAASA,KAAI;AACjB,gBAAI,aAAaA,KAAI;AAErB,gBAAIA,KAAI,IAAI;AACV,kBAAI,KAAK,WAAW,QAAQ;AAC1B,sBAAM,OAAO,MAAMA,KAAI,KAAI;AAC3B,oBAAI,SAAS,IAAI;2BAEN,KAAK,QAAQ,QAAQ,MAAM,YAAY;AAChD,yBAAO;2BAEP,KAAK,QAAQ,QAAQ,KACrB,KAAK,QAAQ,QAAQ,EAAE,SAAS,iCAAiC,GACjE;AACA,yBAAO;uBACF;AACL,yBAAO,KAAK,MAAM,IAAI;;;AAI1B,oBAAM,eAAc,KAAA,KAAK,QAAQ,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,iCAAiC;AACnF,oBAAM,gBAAe,KAAAA,KAAI,QAAQ,IAAI,eAAe,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,GAAG;AAChE,kBAAI,eAAe,gBAAgB,aAAa,SAAS,GAAG;AAC1D,wBAAQ,SAAS,aAAa,CAAC,CAAC;;AAKlC,kBAAI,KAAK,iBAAiB,KAAK,WAAW,SAAS,MAAM,QAAQ,IAAI,GAAG;AACtE,oBAAI,KAAK,SAAS,GAAG;AACnB,0BAAQ;;oBAEN,MAAM;oBACN,SAAS,mBAAmB,KAAK,MAAM;oBACvC,MAAM;oBACN,SAAS;;AAEX,yBAAO;AACP,0BAAQ;AACR,2BAAS;AACT,+BAAa;2BACJ,KAAK,WAAW,GAAG;AAC5B,yBAAO,KAAK,CAAC;uBACR;AACL,yBAAO;;;mBAGN;AACL,oBAAM,OAAO,MAAMA,KAAI,KAAI;AAE3B,kBAAI;AACF,wBAAQ,KAAK,MAAM,IAAI;AAGvB,oBAAI,MAAM,QAAQ,KAAK,KAAKA,KAAI,WAAW,KAAK;AAC9C,yBAAO,CAAA;AACP,0BAAQ;AACR,2BAAS;AACT,+BAAa;;uBAEf,IAAM;AAEN,oBAAIA,KAAI,WAAW,OAAO,SAAS,IAAI;AACrC,2BAAS;AACT,+BAAa;uBACR;AACL,0BAAQ;oBACN,SAAS;;;;AAKf,kBAAI,SAAS,KAAK,mBAAiB,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,QAAQ,IAAG;AACrE,wBAAQ;AACR,yBAAS;AACT,6BAAa;;AAGf,kBAAI,SAAS,KAAK,oBAAoB;AACpC,sBAAM,IAAI,iBAAA,QAAe,KAAK;;;AAIlC,kBAAM,oBAAoB;cACxB;cACA;cACA;cACA;cACA;;AAGF,mBAAO;UACT,CAAC;AACD,cAAI,CAAC,KAAK,oBAAoB;AAC5B,kBAAM,IAAI,MAAM,CAAC,eAAc;;AAAC,qBAAC;gBAC/B,OAAO;kBACL,SAAS,IAAG,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,UAAI,QAAA,OAAA,SAAA,KAAI,YAAY,KAAK,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,OAAO;kBACpE,SAAS,IAAG,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,WAAK,QAAA,OAAA,SAAA,KAAI,EAAE;kBACnC,MAAM;kBACN,MAAM,IAAG,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,UAAI,QAAA,OAAA,SAAA,KAAI,EAAE;;gBAEjC,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,YAAY;;aACZ;;AAGJ,iBAAO,IAAI,KAAK,aAAa,UAAU;QACzC;;;;;;;QAQA,UAAO;AAEL,iBAAO;QAIT;;;;;;;;;;;;;;;;;;;;;;;QAwBA,gBAAa;AAYX,iBAAO;QAST;;AAvQF,cAAA,UAAAD;;;;;;;;;;;;;ACdA,UAAA,qBAAA,gBAAA,0BAAA;AAIA,UAAqBE,6BAArB,cAMU,mBAAA,QAAwB;eAAA;;;;;;;;;;;;QAUhC,OAIE,SAAe;AAGf,cAAI,SAAS;AACb,gBAAM,kBAAkB,YAAO,QAAP,YAAO,SAAP,UAAW,KAChC,MAAM,EAAE,EACR,IAAI,CAAC,MAAK;AACT,gBAAI,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC3B,qBAAO;;AAET,gBAAI,MAAM,KAAK;AACb,uBAAS,CAAC;;AAEZ,mBAAO;UACT,CAAC,EACA,KAAK,EAAE;AACV,eAAK,IAAI,aAAa,IAAI,UAAU,cAAc;AAClD,cAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,iBAAK,QAAQ,QAAQ,KAAK;;AAE5B,eAAK,QAAQ,QAAQ,KAAK;AAC1B,iBAAO;QAOT;;;;;;;;;;;;;;;;;;;QA0CA,MACE,QACA,EACE,YAAY,MACZ,YACA,cACA,kBAAkB,aAAY,IAM5B,CAAA,GAAE;AAEN,gBAAM,MAAM,kBAAkB,GAAG,eAAe,WAAW;AAC3D,gBAAM,gBAAgB,KAAK,IAAI,aAAa,IAAI,GAAG;AAEnD,eAAK,IAAI,aAAa,IACpB,KACA,GAAG,gBAAgB,GAAG,aAAa,MAAM,EAAE,GAAG,MAAM,IAAI,YAAY,QAAQ,MAAM,GAChF,eAAe,SAAY,KAAK,aAAa,gBAAgB,YAC/D,EAAE;AAEJ,iBAAO;QACT;;;;;;;;;;;QAYA,MACE,OACA,EACE,cACA,kBAAkB,aAAY,IACyB,CAAA,GAAE;AAE3D,gBAAM,MAAM,OAAO,oBAAoB,cAAc,UAAU,GAAG,eAAe;AACjF,eAAK,IAAI,aAAa,IAAI,KAAK,GAAG,KAAK,EAAE;AACzC,iBAAO;QACT;;;;;;;;;;;;;;;;QAiBA,MACE,MACA,IACA,EACE,cACA,kBAAkB,aAAY,IACyB,CAAA,GAAE;AAE3D,gBAAM,YACJ,OAAO,oBAAoB,cAAc,WAAW,GAAG,eAAe;AACxE,gBAAM,WAAW,OAAO,oBAAoB,cAAc,UAAU,GAAG,eAAe;AACtF,eAAK,IAAI,aAAa,IAAI,WAAW,GAAG,IAAI,EAAE;AAE9C,eAAK,IAAI,aAAa,IAAI,UAAU,GAAG,KAAK,OAAO,CAAC,EAAE;AACtD,iBAAO;QACT;;;;;;QAOA,YAAY,QAAmB;AAC7B,eAAK,SAAS;AACd,iBAAO;QACT;;;;;;;QAQA,SAAM;AAGJ,eAAK,QAAQ,QAAQ,IAAI;AACzB,iBAAO;QACT;;;;;;;QAQA,cAAW;AAKT,cAAI,KAAK,WAAW,OAAO;AACzB,iBAAK,QAAQ,QAAQ,IAAI;iBACpB;AACL,iBAAK,QAAQ,QAAQ,IAAI;;AAE3B,eAAK,gBAAgB;AACrB,iBAAO;QACT;;;;QAKA,MAAG;AACD,eAAK,QAAQ,QAAQ,IAAI;AACzB,iBAAO;QACT;;;;QAKA,UAAO;AACL,eAAK,QAAQ,QAAQ,IAAI;AACzB,iBAAO;QACT;;;;;;;;;;;;;;;;;;;;;;;;;;QA2BA,QAAQ,EACN,UAAU,OACV,UAAU,OACV,WAAW,OACX,UAAU,OACV,MAAM,OACN,SAAS,OAAM,IAQb,CAAA,GAAE;;AACJ,gBAAM,UAAU;YACd,UAAU,YAAY;YACtB,UAAU,YAAY;YACtB,WAAW,aAAa;YACxB,UAAU,YAAY;YACtB,MAAM,QAAQ;YAEb,OAAO,OAAO,EACd,KAAK,GAAG;AAEX,gBAAM,gBAAe,KAAA,KAAK,QAAQ,QAAQ,OAAC,QAAA,OAAA,SAAA,KAAI;AAC/C,eAAK,QACH,QAAQ,IACN,8BAA8B,MAAM,UAAU,YAAY,cAAc,OAAO;AACnF,cAAI,WAAW;AAAQ,mBAAO;;AACzB,mBAAO;QACd;;;;;;QAOA,WAAQ;;AACN,gBAAK,KAAA,KAAK,QAAQ,QAAQ,OAAC,QAAA,OAAA,SAAA,KAAI,IAAI,KAAI,EAAG,SAAS,GAAG;AACpD,iBAAK,QAAQ,QAAQ,KAAK;iBACrB;AACL,iBAAK,QAAQ,QAAQ,IAAI;;AAE3B,iBAAO;QACT;;;;;;;QAQA,UAAO;AAOL,iBAAO;QAOT;;AAjUF,cAAA,UAAAA;;;;;;;;;;;;;ACJA,UAAA,8BAAA,gBAAA,mCAAA;AAuEA,UAAqBC,0BAArB,cAMU,4BAAA,QAA2E;eAAA;;;;;;;;;;;QASnF,GACE,QACA,OAOS;AAET,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,iBAAO;QACT;;;;;;;QAQA,IACE,QACA,OAIS;AAET,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,iBAAO;QACT;;;;;;;QAUA,GAAG,QAAgB,OAAc;AAC/B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,iBAAO;QACT;;;;;;;QAUA,IAAI,QAAgB,OAAc;AAChC,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,iBAAO;QACT;;;;;;;QAUA,GAAG,QAAgB,OAAc;AAC/B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,iBAAO;QACT;;;;;;;QAUA,IAAI,QAAgB,OAAc;AAChC,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,iBAAO;QACT;;;;;;;QAUA,KAAK,QAAgB,SAAe;AAClC,eAAK,IAAI,aAAa,OAAO,QAAQ,QAAQ,OAAO,EAAE;AACtD,iBAAO;QACT;;;;;;;QAaA,UAAU,QAAgB,UAA2B;AACnD,eAAK,IAAI,aAAa,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,CAAC,GAAG;AACxE,iBAAO;QACT;;;;;;;QAaA,UAAU,QAAgB,UAA2B;AACnD,eAAK,IAAI,aAAa,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,CAAC,GAAG;AACxE,iBAAO;QACT;;;;;;;QAUA,MAAM,QAAgB,SAAe;AACnC,eAAK,IAAI,aAAa,OAAO,QAAQ,SAAS,OAAO,EAAE;AACvD,iBAAO;QACT;;;;;;;QAaA,WAAW,QAAgB,UAA2B;AACpD,eAAK,IAAI,aAAa,OAAO,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC,GAAG;AACzE,iBAAO;QACT;;;;;;;QAaA,WAAW,QAAgB,UAA2B;AACpD,eAAK,IAAI,aAAa,OAAO,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC,GAAG;AACzE,iBAAO;QACT;;;;;;;;;;;;;QAmBA,GAAG,QAAgB,OAAqB;AACtC,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,iBAAO;QACT;;;;;;;QAQA,GACE,QACA,QASC;AAED,gBAAM,gBAAgB,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,EAC7C,IAAI,CAAC,MAAK;AAGT,gBAAI,OAAO,MAAM,YAAY,IAAI,OAAO,OAAO,EAAE,KAAK,CAAC;AAAG,qBAAO,IAAI,CAAC;;AACjE,qBAAO,GAAG,CAAC;UAClB,CAAC,EACA,KAAK,GAAG;AACX,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,aAAa,GAAG;AAC5D,iBAAO;QACT;;;;;;;;QAcA,SAAS,QAAgB,OAA4D;AACnF,cAAI,OAAO,UAAU,UAAU;AAG7B,iBAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;qBACzC,MAAM,QAAQ,KAAK,GAAG;AAE/B,iBAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;iBACzD;AAEL,iBAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;;AAEpE,iBAAO;QACT;;;;;;;;QAcA,YAAY,QAAgB,OAA4D;AACtF,cAAI,OAAO,UAAU,UAAU;AAE7B,iBAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;qBACzC,MAAM,QAAQ,KAAK,GAAG;AAE/B,iBAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;iBACzD;AAEL,iBAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;;AAEpE,iBAAO;QACT;;;;;;;;QAWA,QAAQ,QAAgB,OAAa;AACnC,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,iBAAO;QACT;;;;;;;;;QAYA,SAAS,QAAgB,OAAa;AACpC,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,iBAAO;QACT;;;;;;;;QAWA,QAAQ,QAAgB,OAAa;AACnC,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,iBAAO;QACT;;;;;;;;;QAYA,SAAS,QAAgB,OAAa;AACpC,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,iBAAO;QACT;;;;;;;;;QAYA,cAAc,QAAgB,OAAa;AACzC,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,iBAAO;QACT;;;;;;;;QAcA,SAAS,QAAgB,OAAkC;AACzD,cAAI,OAAO,UAAU,UAAU;AAE7B,iBAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;iBAC7C;AAEL,iBAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;;AAEhE,iBAAO;QACT;;;;;;;;;;;QAsBA,WACE,QACA,OACA,EAAE,QAAQ,KAAI,IAAmE,CAAA,GAAE;AAEnF,cAAI,WAAW;AACf,cAAI,SAAS,SAAS;AACpB,uBAAW;qBACF,SAAS,UAAU;AAC5B,uBAAW;qBACF,SAAS,aAAa;AAC/B,uBAAW;;AAEb,gBAAM,aAAa,WAAW,SAAY,KAAK,IAAI,MAAM;AACzD,eAAK,IAAI,aAAa,OAAO,QAAQ,GAAG,QAAQ,MAAM,UAAU,IAAI,KAAK,EAAE;AAC3E,iBAAO;QACT;;;;;;;;QAWA,MAAM,OAA8B;AAClC,iBAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,KAAK,MAAK;AAChD,iBAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;UACpD,CAAC;AACD,iBAAO;QACT;;;;;;;;;;;;;;QAqBA,IAAI,QAAgB,UAAkB,OAAc;AAClD,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,QAAQ,IAAI,KAAK,EAAE;AAC/D,iBAAO;QACT;;;;;;;;;;;;;;;;QAiBA,GACE,SACA,EACE,cACA,kBAAkB,aAAY,IACyB,CAAA,GAAE;AAE3D,gBAAM,MAAM,kBAAkB,GAAG,eAAe,QAAQ;AACxD,eAAK,IAAI,aAAa,OAAO,KAAK,IAAI,OAAO,GAAG;AAChD,iBAAO;QACT;;;;;;;;;;;;;;QAqBA,OAAO,QAAgB,UAAkB,OAAc;AACrD,eAAK,IAAI,aAAa,OAAO,QAAQ,GAAG,QAAQ,IAAI,KAAK,EAAE;AAC3D,iBAAO;QACT;;AAvgBF,cAAA,UAAAA;;;;;;;;;;;;;ACtEA,UAAA,2BAAA,gBAAA,gCAAA;AAIA,UAAqBC,yBAArB,MAA0C;eAAA;;;QAYxC,YACE,KACA,EACE,UAAU,CAAA,GACV,QACA,OAAAC,OAAK,GAKN;AAED,eAAK,MAAM;AACX,eAAK,UAAU;AACf,eAAK,SAAS;AACd,eAAK,QAAQA;QACf;;;;;;;;;;;;;;;;;;;;;;QAuBA,OAIE,SACA,EACE,MAAAC,QAAO,OACP,MAAK,IAIH,CAAA,GAAE;AAEN,gBAAM,SAASA,QAAO,SAAS;AAE/B,cAAI,SAAS;AACb,gBAAM,kBAAkB,YAAO,QAAP,YAAO,SAAP,UAAW,KAChC,MAAM,EAAE,EACR,IAAI,CAAC,MAAK;AACT,gBAAI,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC3B,qBAAO;;AAET,gBAAI,MAAM,KAAK;AACb,uBAAS,CAAC;;AAEZ,mBAAO;UACT,CAAC,EACA,KAAK,EAAE;AACV,eAAK,IAAI,aAAa,IAAI,UAAU,cAAc;AAClD,cAAI,OAAO;AACT,iBAAK,QAAQ,QAAQ,IAAI,SAAS,KAAK;;AAGzC,iBAAO,IAAI,yBAAA,QAAuB;YAChC;YACA,KAAK,KAAK;YACV,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,YAAY;WAC+B;QAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;QA0CA,OACE,QACA,EACE,OACA,gBAAgB,KAAI,IAIlB,CAAA,GAAE;AAEN,gBAAM,SAAS;AAEf,gBAAM,iBAAiB,CAAA;AACvB,cAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,2BAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;;AAE5C,cAAI,OAAO;AACT,2BAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,cAAI,CAAC,eAAe;AAClB,2BAAe,KAAK,iBAAiB;;AAEvC,eAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,kBAAM,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAA,CAAc;AACpF,gBAAI,QAAQ,SAAS,GAAG;AACtB,oBAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AACzE,mBAAK,IAAI,aAAa,IAAI,WAAW,cAAc,KAAK,GAAG,CAAC;;;AAIhE,iBAAO,IAAI,yBAAA,QAAuB;YAChC;YACA,KAAK,KAAK;YACV,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,MAAM;YACN,OAAO,KAAK;YACZ,YAAY;WACwB;QACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA0DA,OACE,QACA,EACE,YACA,mBAAmB,OACnB,OACA,gBAAgB,KAAI,IAMlB,CAAA,GAAE;AAEN,gBAAM,SAAS;AAEf,gBAAM,iBAAiB,CAAC,cAAc,mBAAmB,WAAW,OAAO,aAAa;AAExF,cAAI,eAAe;AAAW,iBAAK,IAAI,aAAa,IAAI,eAAe,UAAU;AACjF,cAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,2BAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;;AAE5C,cAAI,OAAO;AACT,2BAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,cAAI,CAAC,eAAe;AAClB,2BAAe,KAAK,iBAAiB;;AAEvC,eAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,kBAAM,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAA,CAAc;AACpF,gBAAI,QAAQ,SAAS,GAAG;AACtB,oBAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AACzE,mBAAK,IAAI,aAAa,IAAI,WAAW,cAAc,KAAK,GAAG,CAAC;;;AAIhE,iBAAO,IAAI,yBAAA,QAAuB;YAChC;YACA,KAAK,KAAK;YACV,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,MAAM;YACN,OAAO,KAAK;YACZ,YAAY;WACwB;QACxC;;;;;;;;;;;;;;;;;;;;;;QAuBA,OACE,QACA,EACE,MAAK,IAGH,CAAA,GAAE;AAEN,gBAAM,SAAS;AACf,gBAAM,iBAAiB,CAAA;AACvB,cAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,2BAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;;AAE5C,cAAI,OAAO;AACT,2BAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,eAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,iBAAO,IAAI,yBAAA,QAAuB;YAChC;YACA,KAAK,KAAK;YACV,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,MAAM;YACN,OAAO,KAAK;YACZ,YAAY;WACwB;QACxC;;;;;;;;;;;;;;;;;;;;QAqBA,OAAO,EACL,MAAK,IAGH,CAAA,GAAE;AACJ,gBAAM,SAAS;AACf,gBAAM,iBAAiB,CAAA;AACvB,cAAI,OAAO;AACT,2BAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,cAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,2BAAe,QAAQ,KAAK,QAAQ,QAAQ,CAAC;;AAE/C,eAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,iBAAO,IAAI,yBAAA,QAAuB;YAChC;YACA,KAAK,KAAK;YACV,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,YAAY;WACwB;QACxC;;AAtXF,cAAA,UAAAF;;;;;;;;;;;ACLa,cAAA,UAAU;;;;;;;;;;;ACAvB,UAAA,YAAA;AACa,cAAA,kBAAkB,EAAE,iBAAiB,gBAAgB,UAAA,OAAO,GAAE;;;;;;;;;;;;;ACD3E,UAAA,0BAAA,gBAAA,+BAAA;AACA,UAAA,2BAAA,gBAAA,gCAAA;AAEA,UAAA,cAAA;AAaA,UAAqBG,mBAArB,MAAqB,iBAAe;eAAA;;;;;;;;;;;;;QAwBlC,YACE,KACA,EACE,UAAU,CAAA,GACV,QACA,OAAAC,OAAK,IAKH,CAAA,GAAE;AAEN,eAAK,MAAM;AACX,eAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,YAAA,eAAe,GAAK,OAAO;AAC/C,eAAK,aAAa;AAClB,eAAK,QAAQA;QACf;;;;;;QAcA,KAAK,UAAgB;AACnB,gBAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,QAAQ,EAAE;AAC7C,iBAAO,IAAI,wBAAA,QAAsB,KAAK;YACpC,SAAO,OAAA,OAAA,CAAA,GAAO,KAAK,OAAO;YAC1B,QAAQ,KAAK;YACb,OAAO,KAAK;WACb;QACH;;;;;;;;QASA,OACE,QAAqB;AAMrB,iBAAO,IAAI,iBAAgB,KAAK,KAAK;YACnC,SAAS,KAAK;YACd;YACA,OAAO,KAAK;WACb;QACH;;;;;;;;;;;;;;;;;;;;;;;;QAyBA,IACE,IACA,OAAmB,CAAA,GACnB,EACE,MAAAC,QAAO,OACP,KAAAC,OAAM,OACN,MAAK,IAKH,CAAA,GAAE;AAYN,cAAI;AACJ,gBAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ,EAAE,EAAE;AAC3C,cAAI;AACJ,cAAID,SAAQC,MAAK;AACf,qBAASD,QAAO,SAAS;AACzB,mBAAO,QAAQ,IAAI,EAGhB,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,MAAS,EAE1C,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,MAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC,EACzF,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAK;AACzB,kBAAI,aAAa,OAAO,MAAM,KAAK;YACrC,CAAC;iBACE;AACL,qBAAS;AACT,mBAAO;;AAGT,gBAAM,UAAO,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO;AACjC,cAAI,OAAO;AACT,oBAAQ,QAAQ,IAAI,SAAS,KAAK;;AAGpC,iBAAO,IAAI,yBAAA,QAAuB;YAChC;YACA;YACA;YACA,QAAQ,KAAK;YACb;YACA,OAAO,KAAK;YACZ,YAAY;WACiC;QACjD;;AAnKF,cAAA,UAAAF;;;;;;;;;;;;;;ACfA,UAAA,oBAAA,gBAAA,yBAAA;AAQE,cAAA,kBARK,kBAAA;AACP,UAAA,0BAAA,gBAAA,+BAAA;AAQE,cAAA,wBARK,wBAAA;AACP,UAAA,2BAAA,gBAAA,gCAAA;AAQE,cAAA,yBARK,yBAAA;AACP,UAAA,8BAAA,gBAAA,mCAAA;AAQE,cAAA,4BARK,4BAAA;AACP,UAAA,qBAAA,gBAAA,0BAAA;AAQE,cAAA,mBARK,mBAAA;AACP,UAAA,mBAAA,gBAAA,wBAAA;AAQE,cAAA,iBARK,iBAAA;AAUP,cAAA,UAAe;QACb,iBAAA,kBAAA;QACA,uBAAA,wBAAA;QACA,wBAAA,yBAAA;QACA,2BAAA,4BAAA;QACA,kBAAA,mBAAA;QACA,gBAAA,iBAAA;;;;;;ACtBF;;;ACAA;AAMA,MAAM,oBAAoB;AAAA;AAAA,IAExB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA;AAAA,IAGhB,aAAa;AAAA,IACb,SAAS;AAAA;AAAA;AAAA,IAGT,SAAS;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,uBAAuB;AAAA;AAAA,IACzB;AAAA,EACF;AAKA,MAAM,iBAAN,MAAqB;AAAA,IAhCrB,OAgCqB;AAAA;AAAA;AAAA,IACnB,cAAc;AACZ,WAAK,cAAc;AACnB,WAAK,cAAc;AACnB,WAAK,cAAc;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,eAAe;AACnB,UAAI;AACF,cAAM,UAAU,GAAG,kBAAkB,YAAY;AAEjD,cAAM,SAAS,IAAI,OAAO,gBAAgB;AAAA,UACxC,YAAY;AAAA,UACZ,WAAW,kBAAkB;AAAA,UAC7B,eAAe,kBAAkB;AAAA,UACjC,UAAU,kBAAkB;AAAA,UAC5B,UAAU,kBAAkB,WAAW,kBAAkB;AAAA,QAC3D,CAAC;AAED,cAAM,WAAW,MAAM,OAAO,MAAM,SAAS;AAAA,UAC3C,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,UAClB;AAAA,UACA,MAAM,OAAO,SAAS;AAAA,QACxB,CAAC;AAED,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAM,IAAI,MAAM,2BAA2B,SAAS,MAAM,MAAM,SAAS,EAAE;AAAA,QAC7E;AAEA,cAAM,WAAW,MAAM,SAAS,KAAK;AAErC,aAAK,cAAc,SAAS;AAC5B,aAAK,cAAc,SAAS;AAC5B,aAAK,cAAc,KAAK,IAAI,IAAI,OAAO;AAEvC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,aAAa,KAAK;AAAA,UAClB,aAAa,KAAK;AAAA,UAClB,WAAW,SAAS;AAAA,UACpB,WAAW,SAAS;AAAA,UACpB,WAAW,SAAS;AAAA,QACtB;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,oCAAoC,KAAK;AACvD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,eAAe;AACb,aAAO,KAAK,eAAe,KAAK,eAAe,KAAK,IAAI,IAAI,KAAK;AAAA,IACnE;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,gBAAgB;AACpB,UAAI,KAAK,aAAa,GAAG;AACvB,eAAO,KAAK;AAAA,MACd;AAEA,YAAM,aAAa,MAAM,KAAK,aAAa;AAC3C,aAAO,WAAW,UAAU,KAAK,cAAc;AAAA,IACjD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,iBAAiB;AACrB,YAAM,QAAQ,MAAM,KAAK,cAAc;AACvC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AAEA,aAAO;AAAA,QACL,eAAe,UAAU,KAAK;AAAA,QAC9B,gBAAgB;AAAA,QAChB,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAKA,MAAM,gBAAN,MAAoB;AAAA,IArIpB,OAqIoB;AAAA;AAAA;AAAA,IAClB,cAAc;AACZ,WAAK,OAAO,IAAI,eAAe;AAC/B,WAAK,UAAU;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,OAAO;AACX,UAAI;AACF,cAAM,aAAa,MAAM,KAAK,KAAK,aAAa;AAChD,YAAI,WAAW,SAAS;AACtB,eAAK,UAAU,GAAG,KAAK,KAAK,WAAW,mBAAmB,kBAAkB,WAAW;AACvF,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,8BAA8B,KAAK;AACjD,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,MAAM,QAAQ,UAAU,UAAU,CAAC,GAAG;AACpC,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,KAAK,eAAe;AAE/C,cAAM,WAAW,MAAM,OAAO,MAAM,GAAG,KAAK,OAAO,GAAG,QAAQ,IAAI;AAAA,UAChE,GAAG;AAAA,UACH,SAAS;AAAA,YACP,GAAG;AAAA,YACH,GAAG,QAAQ;AAAA,UACb;AAAA,UACA,SAAS,kBAAkB;AAAA,QAC7B,CAAC;AAED,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAM,IAAI,MAAM,yBAAyB,SAAS,MAAM,MAAM,SAAS,EAAE;AAAA,QAC3E;AAEA,eAAO,MAAM,SAAS,KAAK;AAAA,MAC7B,SAAS,OAAO;AACd,gBAAQ,MAAM,iCAAiC,KAAK;AACpD,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,MAAM,aAAa,YAAY,MAAM;AACnC,aAAO,MAAM,KAAK,QAAQ,aAAa,UAAU,IAAI;AAAA,QACnD,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,MAAM,aAAa,YAAY,UAAU,MAAM;AAC7C,aAAO,MAAM,KAAK,QAAQ,aAAa,UAAU,IAAI,QAAQ,IAAI;AAAA,QAC/D,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,MAAM,OAAO;AACjB,YAAM,eAAe,mBAAmB,KAAK;AAC7C,aAAO,MAAM,KAAK,QAAQ,YAAY,YAAY,EAAE;AAAA,IACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,MAAM,UAAU,YAAY,UAAU,SAAS,CAAC,GAAG;AACjD,YAAM,cAAc,OAAO,SAAS,IAAI,WAAW,OAAO,KAAK,GAAG,CAAC,KAAK;AACxE,aAAO,MAAM,KAAK,QAAQ,aAAa,UAAU,IAAI,QAAQ,GAAG,WAAW,EAAE;AAAA,IAC/E;AAAA,EACF;AAMO,WAAS,2BAA2B;AACzC,UAAM,UAAU,CAAC;AAEjB,QAAI,CAAC,kBAAkB,gBAAgB,kBAAkB,aAAa,SAAS,UAAU,GAAG;AAC1F,cAAQ,KAAK,yBAAyB;AAAA,IACxC;AACA,QAAI,CAAC,kBAAkB,aAAa,kBAAkB,UAAU,SAAS,OAAO,GAAG;AACjF,cAAQ,KAAK,sBAAsB;AAAA,IACrC;AACA,QAAI,CAAC,kBAAkB,iBAAiB,kBAAkB,cAAc,SAAS,OAAO,GAAG;AACzF,cAAQ,KAAK,0BAA0B;AAAA,IACzC;AACA,QAAI,CAAC,kBAAkB,YAAY,kBAAkB,SAAS,SAAS,OAAO,GAAG;AAC/E,cAAQ,KAAK,qBAAqB;AAAA,IACpC;AACA,QAAI,CAAC,kBAAkB,YAAY,kBAAkB,SAAS,SAAS,OAAO,GAAG;AAC/E,cAAQ,KAAK,qBAAqB;AAAA,IACpC;AACA,QAAI,CAAC,kBAAkB,kBAAkB,kBAAkB,eAAe,SAAS,OAAO,GAAG;AAC3F,cAAQ,KAAK,2BAA2B;AAAA,IAC1C;AAEA,WAAO;AAAA,MACL,SAAS,QAAQ,WAAW;AAAA,MAC5B;AAAA,MACA,SACE,QAAQ,SAAS,IACb,yDAAgD,QAAQ,KAAK,IAAI,CAAC,KAClE;AAAA,IACR;AAAA,EACF;AA9BgB;AAiCT,MAAM,iBAAiB,IAAI,eAAe;AAC1C,MAAM,gBAAgB,IAAI,cAAc;;;ACpR/C;;;ACAA;A;;;;;ACAA;A;;;;;ACEA;AAAO,MAAM,eAAe,wBAAC,gBAA8B;AACzD,QAAI;AACJ,QAAI,aAAa;AACf,eAAS;eACA,OAAO,UAAU,aAAa;AACvC,eAAS,2BAAI,SACX,gEAAsC,KAAK,CAAC,EAAE,SAASI,OAAK,MAAOA,OAAM,GAAG,IAAI,CAAC,GAD1E;WAEJ;AACL,eAAS;;AAEX,WAAO,IAAI,SAAS,OAAO,GAAG,IAAI;EACpC,GAX4B;;;ACe5B;AAAM,MAAO,iBAAP,cAA8B,MAAK;IAAzC,OAAyC;;;IAEvC,YAAY,SAAiB,OAAO,kBAAkB,SAAa;AACjE,YAAM,OAAO;AACb,WAAK,OAAO;AACZ,WAAK,UAAU;IACjB;;AAGI,MAAO,sBAAP,cAAmC,eAAc;IATvD,OASuD;;;IACrD,YAAY,SAAY;AACtB,YAAM,iDAAiD,uBAAuB,OAAO;IACvF;;AAGI,MAAO,sBAAP,cAAmC,eAAc;IAfvD,OAeuD;;;IACrD,YAAY,SAAY;AACtB,YAAM,0CAA0C,uBAAuB,OAAO;IAChF;;AAGI,MAAO,qBAAP,cAAkC,eAAc;IArBtD,OAqBsD;;;IACpD,YAAY,SAAY;AACtB,YAAM,gDAAgD,sBAAsB,OAAO;IACrF;;AAGF,MAAY;AAAZ,GAAA,SAAYC,iBAAc;AACxB,IAAAA,gBAAA,KAAA,IAAA;AACA,IAAAA,gBAAA,cAAA,IAAA;AACA,IAAAA,gBAAA,cAAA,IAAA;AACA,IAAAA,gBAAA,UAAA,IAAA;AACA,IAAAA,gBAAA,cAAA,IAAA;AACA,IAAAA,gBAAA,cAAA,IAAA;AACA,IAAAA,gBAAA,YAAA,IAAA;AACA,IAAAA,gBAAA,YAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;EACF,GAhBY,mBAAA,iBAAc,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADjCpB,MAAO,kBAAP,MAAsB;WAAA;;;IAM1B,YACE,KACA,EACE,UAAU,CAAA,GACV,aACA,SAAS,eAAe,IAAG,IAKzB,CAAA,GAAE;AAEN,WAAK,MAAM;AACX,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,QAAQ,aAAa,WAAW;IACvC;;;;;IAMA,QAAQ,OAAa;AACnB,WAAK,QAAQ,gBAAgB,UAAU,KAAK;IAC9C;;;;;;IAOM,OACJ,cACA,UAAiC,CAAA,GAAE;;;AAEnC,YAAI;AACF,gBAAM,EAAE,SAAS,QAAQ,MAAM,aAAY,IAAK;AAChD,cAAI,WAAmC,CAAA;AACvC,cAAI,EAAE,OAAM,IAAK;AACjB,cAAI,CAAC,QAAQ;AACX,qBAAS,KAAK;;AAGhB,gBAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,YAAY,EAAE;AACjD,cAAI,UAAU,WAAW,OAAO;AAC9B,qBAAS,UAAU,IAAI;AACvB,gBAAI,aAAa,IAAI,uBAAuB,MAAM;;AAEpD,cAAI;AACJ,cACE,iBACE,WAAW,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,cAAc,KAAM,CAAC,UACjF;AACA,gBACG,OAAO,SAAS,eAAe,wBAAwB,QACxD,wBAAwB,aACxB;AAGA,uBAAS,cAAc,IAAI;AAC3B,qBAAO;uBACE,OAAO,iBAAiB,UAAU;AAE3C,uBAAS,cAAc,IAAI;AAC3B,qBAAO;uBACE,OAAO,aAAa,eAAe,wBAAwB,UAAU;AAG9E,qBAAO;mBACF;AAEL,uBAAS,cAAc,IAAI;AAC3B,qBAAO,KAAK,UAAU,YAAY;;;AAItC,gBAAM,WAAW,MAAM,KAAK,MAAM,IAAI,SAAQ,GAAI;YAChD,QAAQ,UAAU;;;;;YAKlB,SAAO,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,QAAQ,GAAK,KAAK,OAAO,GAAK,OAAO;YACnD;WACD,EAAE,MAAM,CAAC,eAAc;AACtB,kBAAM,IAAI,oBAAoB,UAAU;UAC1C,CAAC;AAED,gBAAM,eAAe,SAAS,QAAQ,IAAI,eAAe;AACzD,cAAI,gBAAgB,iBAAiB,QAAQ;AAC3C,kBAAM,IAAI,oBAAoB,QAAQ;;AAGxC,cAAI,CAAC,SAAS,IAAI;AAChB,kBAAM,IAAI,mBAAmB,QAAQ;;AAGvC,cAAI,iBAAgB,KAAA,SAAS,QAAQ,IAAI,cAAc,OAAC,QAAA,OAAA,SAAA,KAAI,cAAc,MAAM,GAAG,EAAE,CAAC,EAAE,KAAI;AAC5F,cAAI;AACJ,cAAI,iBAAiB,oBAAoB;AACvC,mBAAO,MAAM,SAAS,KAAI;qBACjB,iBAAiB,4BAA4B;AACtD,mBAAO,MAAM,SAAS,KAAI;qBACjB,iBAAiB,qBAAqB;AAC/C,mBAAO;qBACE,iBAAiB,uBAAuB;AACjD,mBAAO,MAAM,SAAS,SAAQ;iBACzB;AAEL,mBAAO,MAAM,SAAS,KAAI;;AAG5B,iBAAO,EAAE,MAAM,OAAO,MAAM,SAAQ;iBAC7B,OAAO;AACd,iBAAO;YACL,MAAM;YACN;YACA,UACE,iBAAiB,sBAAsB,iBAAiB,sBACpD,MAAM,UACN;;;;;;;;AEzId;AAAA,mBAAkB;AAClB,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,WAAAC;AAYJ,MAAO,kBAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;;;AC3BA;;;ACAA;;;ACAA;;;ACAA;AAAM,WAAU,qBAAkB;AAChC,QAAI,OAAO,cAAc;AAAa,aAAO;AAC7C,QAAI,OAAO,OAAO,cAAc;AAAa,aAAO,OAAO;AAC3D,QAAI,OAAO,OAAO,cAAc;AAAa,aAAO,OAAO;AAC3D,QAAI,OAAO,KAAK,cAAc;AAAa,aAAO,KAAK;AACvD,UAAM,IAAI,MAAM,kDAAkD;EACpE;AANgB;;;ADET,MAAMC,aAAY,mBAAkB;;;AEF3C;;;ACAA;AAAO,MAAM,UAAU;;;ADEhB,MAAM,kBAAkB,eAAe,OAAO;AAC9C,MAAM,MAAc;AAEpB,MAAM,UAAU;AAEhB,MAAM,kBAAkB;AAExB,MAAM,kBAAkB;AAE/B,MAAY;AAAZ,GAAA,SAAYC,gBAAa;AACvB,IAAAA,eAAAA,eAAA,YAAA,IAAA,CAAA,IAAA;AACA,IAAAA,eAAAA,eAAA,MAAA,IAAA,CAAA,IAAA;AACA,IAAAA,eAAAA,eAAA,SAAA,IAAA,CAAA,IAAA;AACA,IAAAA,eAAAA,eAAA,QAAA,IAAA,CAAA,IAAA;EACF,GALY,kBAAA,gBAAa,CAAA,EAAA;AAOzB,MAAY;AAAZ,GAAA,SAAYC,iBAAc;AACxB,IAAAA,gBAAA,QAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,QAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;AACA,IAAAA,gBAAA,SAAA,IAAA;EACF,GANY,mBAAA,iBAAc,CAAA,EAAA;AAQ1B,MAAY;AAAZ,GAAA,SAAYC,iBAAc;AACxB,IAAAA,gBAAA,OAAA,IAAA;AACA,IAAAA,gBAAA,OAAA,IAAA;AACA,IAAAA,gBAAA,MAAA,IAAA;AACA,IAAAA,gBAAA,OAAA,IAAA;AACA,IAAAA,gBAAA,OAAA,IAAA;AACA,IAAAA,gBAAA,cAAA,IAAA;EACF,GAPY,mBAAA,iBAAc,CAAA,EAAA;AAS1B,MAAY;AAAZ,GAAA,SAAYC,aAAU;AACpB,IAAAA,YAAA,WAAA,IAAA;EACF,GAFY,eAAA,aAAU,CAAA,EAAA;AAItB,MAAY;AAAZ,GAAA,SAAYC,mBAAgB;AAC1B,IAAAA,kBAAA,YAAA,IAAA;AACA,IAAAA,kBAAA,MAAA,IAAA;AACA,IAAAA,kBAAA,SAAA,IAAA;AACA,IAAAA,kBAAA,QAAA,IAAA;EACF,GALY,qBAAA,mBAAgB,CAAA,EAAA;;;AEvC5B;AAGA,MAAqB,aAArB,MAA+B;IAH/B,OAG+B;;;IAA/B,cAAA;AACE,WAAA,gBAAgB;IA4ClB;IA1CE,OAAO,YAAkC,UAAkB;AACzD,UAAI,WAAW,gBAAgB,aAAa;AAC1C,eAAO,SAAS,KAAK,cAAc,UAAU,CAAC;MAChD;AAEA,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO,SAAS,KAAK,MAAM,UAAU,CAAC;MACxC;AAEA,aAAO,SAAS,CAAA,CAAE;IACpB;IAEQ,cAAc,QAAmB;AACvC,YAAM,OAAO,IAAI,SAAS,MAAM;AAChC,YAAM,UAAU,IAAI,YAAW;AAE/B,aAAO,KAAK,iBAAiB,QAAQ,MAAM,OAAO;IACpD;IAEQ,iBACN,QACA,MACA,SAAoB;AAOpB,YAAM,YAAY,KAAK,SAAS,CAAC;AACjC,YAAM,YAAY,KAAK,SAAS,CAAC;AACjC,UAAI,SAAS,KAAK,gBAAgB;AAClC,YAAM,QAAQ,QAAQ,OAAO,OAAO,MAAM,QAAQ,SAAS,SAAS,CAAC;AACrE,eAAS,SAAS;AAClB,YAAM,QAAQ,QAAQ,OAAO,OAAO,MAAM,QAAQ,SAAS,SAAS,CAAC;AACrE,eAAS,SAAS;AAClB,YAAM,OAAO,KAAK,MAChB,QAAQ,OAAO,OAAO,MAAM,QAAQ,OAAO,UAAU,CAAC,CAAC;AAGzD,aAAO,EAAE,KAAK,MAAM,OAAc,OAAc,SAAS,KAAI;IAC/D;;;;AC/CF;AAYA,MAAqB,QAArB,MAA0B;IAZ1B,OAY0B;;;IAIxB,YAAmB,UAA2B,WAAmB;AAA9C,WAAA,WAAA;AAA2B,WAAA,YAAA;AAH9C,WAAA,QAA4B;AAC5B,WAAA,QAAgB;AAGd,WAAK,WAAW;AAChB,WAAK,YAAY;IACnB;IAEA,QAAK;AACH,WAAK,QAAQ;AACb,mBAAa,KAAK,KAAK;IACzB;;IAGA,kBAAe;AACb,mBAAa,KAAK,KAAK;AAEvB,WAAK,QAAa,WAAW,MAAK;AAChC,aAAK,QAAQ,KAAK,QAAQ;AAC1B,aAAK,SAAQ;MACf,GAAG,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;IACnC;;;;AClCF;AAOA,MAAY;AAAZ,GAAA,SAAYC,gBAAa;AACvB,IAAAA,eAAA,SAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,WAAA,IAAA;AACA,IAAAA,eAAA,QAAA,IAAA;AACA,IAAAA,eAAA,QAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,WAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,WAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,OAAA,IAAA;AACA,IAAAA,eAAA,OAAA,IAAA;AACA,IAAAA,eAAA,SAAA,IAAA;AACA,IAAAA,eAAA,KAAA,IAAA;AACA,IAAAA,eAAA,SAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,MAAA,IAAA;AACA,IAAAA,eAAA,WAAA,IAAA;AACA,IAAAA,eAAA,aAAA,IAAA;AACA,IAAAA,eAAA,QAAA,IAAA;AACA,IAAAA,eAAA,SAAA,IAAA;AACA,IAAAA,eAAA,WAAA,IAAA;EACF,GAzBY,kBAAA,gBAAa,CAAA,EAAA;AAqDlB,MAAM,oBAAoB,wBAC/B,SACA,QACA,UAAoC,CAAA,MAC1B;;AACV,UAAM,aAAY,KAAA,QAAQ,eAAS,QAAA,OAAA,SAAA,KAAI,CAAA;AAEvC,WAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,KAAK,YAAW;AACjD,UAAI,OAAO,IAAI,cAAc,SAAS,SAAS,QAAQ,SAAS;AAChE,aAAO;IACT,GAAG,CAAA,CAAY;EACjB,GAXiC;AA2B1B,MAAM,gBAAgB,wBAC3B,YACA,SACA,QACA,cACe;AACf,UAAM,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU;AACxD,UAAM,UAAU,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ;AACxB,UAAM,QAAQ,OAAO,UAAU;AAE/B,QAAI,WAAW,CAAC,UAAU,SAAS,OAAO,GAAG;AAC3C,aAAO,YAAY,SAAS,KAAK;IACnC;AAEA,WAAO,KAAK,KAAK;EACnB,GAf6B;AA8BtB,MAAM,cAAc,wBAAC,MAAc,UAAmC;AAE3E,QAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,YAAM,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AAC1C,aAAO,QAAQ,OAAO,QAAQ;IAChC;AAGA,YAAQ,MAAM;MACZ,KAAK,cAAc;AACjB,eAAO,UAAU,KAAK;MACxB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;AACjB,eAAO,SAAS,KAAK;MACvB,KAAK,cAAc;MACnB,KAAK,cAAc;AACjB,eAAO,OAAO,KAAK;MACrB,KAAK,cAAc;AACjB,eAAO,kBAAkB,KAAK;;MAChC,KAAK,cAAc;;MACnB,KAAK,cAAc;;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;;MACnB,KAAK,cAAc;;MACnB,KAAK,cAAc;;MACnB,KAAK,cAAc;MACnB,KAAK,cAAc;AACjB,eAAO,KAAK,KAAK;MACnB;AAEE,eAAO,KAAK,KAAK;IACrB;EACF,GA1C2B;AA4C3B,MAAM,OAAO,wBAAC,UAAmC;AAC/C,WAAO;EACT,GAFa;AAGN,MAAM,YAAY,wBAAC,UAAmC;AAC3D,YAAQ,OAAO;MACb,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF,GATyB;AAUlB,MAAM,WAAW,wBAAC,UAAmC;AAC1D,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,cAAc,WAAW,KAAK;AACpC,UAAI,CAAC,OAAO,MAAM,WAAW,GAAG;AAC9B,eAAO;MACT;IACF;AACA,WAAO;EACT,GARwB;AASjB,MAAM,SAAS,wBAAC,UAAmC;AACxD,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI;AACF,eAAO,KAAK,MAAM,KAAK;MACzB,SAAS,OAAO;AACd,gBAAQ,IAAI,qBAAqB,KAAK,EAAE;AACxC,eAAO;MACT;IACF;AACA,WAAO;EACT,GAVsB;AAsBf,MAAM,UAAU,wBAAC,OAAoB,SAA6B;AACvE,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;IACT;AAEA,UAAM,UAAU,MAAM,SAAS;AAC/B,UAAM,aAAa,MAAM,OAAO;AAChC,UAAM,YAAY,MAAM,CAAC;AAGzB,QAAI,cAAc,OAAO,eAAe,KAAK;AAC3C,UAAI;AACJ,YAAM,UAAU,MAAM,MAAM,GAAG,OAAO;AAGtC,UAAI;AACF,cAAM,KAAK,MAAM,MAAM,UAAU,GAAG;MACtC,SAAS,GAAG;AAEV,cAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,CAAA;MACvC;AAEA,aAAO,IAAI,IAAI,CAAC,QAAmB,YAAY,MAAM,GAAG,CAAC;IAC3D;AAEA,WAAO;EACT,GA1BuB;AAmChB,MAAM,oBAAoB,wBAAC,UAAmC;AACnE,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,MAAM,QAAQ,KAAK,GAAG;IAC/B;AAEA,WAAO;EACT,GANiC;AAQ1B,MAAM,kBAAkB,wBAAC,cAA6B;AAC3D,QAAI,MAAM;AACV,UAAM,IAAI,QAAQ,QAAQ,MAAM;AAChC,UAAM,IAAI,QAAQ,mDAAmD,EAAE;AACvE,WAAO,IAAI,QAAQ,QAAQ,EAAE;EAC/B,GAL+B;;;ACxP/B;;;ACAA;AAGA,MAAqB,OAArB,MAAyB;IAHzB,OAGyB;;;;;;;;;;;IAsBvB,YACS,SACA,OACA,UAAkC,CAAA,GAClC,UAAkB,iBAAe;AAHjC,WAAA,UAAA;AACA,WAAA,QAAA;AACA,WAAA,UAAA;AACA,WAAA,UAAA;AAzBT,WAAA,OAAgB;AAChB,WAAA,eAAmC;AACnC,WAAA,MAAc;AACd,WAAA,eAGW;AACX,WAAA,WAGM,CAAA;AACN,WAAA,WAA0B;IAevB;IAEH,OAAO,SAAe;AACpB,WAAK,UAAU;AACf,WAAK,gBAAe;AACpB,WAAK,MAAM;AACX,WAAK,WAAW;AAChB,WAAK,eAAe;AACpB,WAAK,OAAO;AACZ,WAAK,KAAI;IACX;IAEA,OAAI;AACF,UAAI,KAAK,aAAa,SAAS,GAAG;AAChC;MACF;AACA,WAAK,aAAY;AACjB,WAAK,OAAO;AACZ,WAAK,QAAQ,OAAO,KAAK;QACvB,OAAO,KAAK,QAAQ;QACpB,OAAO,KAAK;QACZ,SAAS,KAAK;QACd,KAAK,KAAK;QACV,UAAU,KAAK,QAAQ,SAAQ;OAChC;IACH;IAEA,cAAc,SAA+B;AAC3C,WAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO,GAAK,OAAO;IAC9C;IAEA,QAAQ,QAAgB,UAAkB;;AACxC,UAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,kBAAS,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ;MACtC;AAEA,WAAK,SAAS,KAAK,EAAE,QAAQ,SAAQ,CAAE;AACvC,aAAO;IACT;IAEA,eAAY;AACV,UAAI,KAAK,cAAc;AACrB;MACF;AACA,WAAK,MAAM,KAAK,QAAQ,OAAO,SAAQ;AACvC,WAAK,WAAW,KAAK,QAAQ,gBAAgB,KAAK,GAAG;AAErD,YAAM,WAAW,wBAAC,YAAgB;AAChC,aAAK,gBAAe;AACpB,aAAK,eAAc;AACnB,aAAK,eAAe;AACpB,aAAK,cAAc,OAAO;MAC5B,GALiB;AAOjB,WAAK,QAAQ,IAAI,KAAK,UAAU,CAAA,GAAI,QAAQ;AAE5C,WAAK,eAAoB,WAAW,MAAK;AACvC,aAAK,QAAQ,WAAW,CAAA,CAAE;MAC5B,GAAG,KAAK,OAAO;IACjB;IAEA,QAAQ,QAAgB,UAAa;AACnC,UAAI,KAAK;AACP,aAAK,QAAQ,SAAS,KAAK,UAAU,EAAE,QAAQ,SAAQ,CAAE;IAC7D;IAEA,UAAO;AACL,WAAK,gBAAe;AACpB,WAAK,eAAc;IACrB;IAEQ,kBAAe;AACrB,UAAI,CAAC,KAAK,UAAU;AAClB;MACF;AAEA,WAAK,QAAQ,KAAK,KAAK,UAAU,CAAA,CAAE;IACrC;IAEQ,iBAAc;AACpB,mBAAa,KAAK,YAAY;AAC9B,WAAK,eAAe;IACtB;IAEQ,cAAc,EACpB,QACA,SAAQ,GAIT;AACC,WAAK,SACF,OAAO,CAAC,MAAM,EAAE,WAAW,MAAM,EACjC,QAAQ,CAAC,MAAM,EAAE,SAAS,QAAQ,CAAC;IACxC;IAEQ,aAAa,QAAc;AACjC,aAAO,KAAK,gBAAgB,KAAK,aAAa,WAAW;IAC3D;;;;AChIF;AAkCA,MAAY;AAAZ,GAAA,SAAYC,kCAA+B;AACzC,IAAAA,iCAAA,MAAA,IAAA;AACA,IAAAA,iCAAA,MAAA,IAAA;AACA,IAAAA,iCAAA,OAAA,IAAA;EACF,GAJY,oCAAA,kCAA+B,CAAA,EAAA;AA4B3C,MAAqB,mBAArB,MAAqB,kBAAgB;IA9DrC,OA8DqC;;;;;;;;;;IAqBnC,YAAmB,SAA0B,MAAmB;AAA7C,WAAA,UAAA;AApBnB,WAAA,QAA+B,CAAA;AAC/B,WAAA,eAAkC,CAAA;AAClC,WAAA,UAAyB;AACzB,WAAA,SAII;QACF,QAAQ,6BAAK;QAAE,GAAP;QACR,SAAS,6BAAK;QAAE,GAAP;QACT,QAAQ,6BAAK;QAAE,GAAP;;AAWR,YAAM,UAAS,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU;QAC7B,OAAO;QACP,MAAM;;AAGR,WAAK,QAAQ,IAAI,OAAO,OAAO,CAAA,GAAI,CAAC,aAA8B;AAChE,cAAM,EAAE,QAAQ,SAAS,OAAM,IAAK,KAAK;AAEzC,aAAK,UAAU,KAAK,QAAQ,SAAQ;AAEpC,aAAK,QAAQ,kBAAiB,UAC5B,KAAK,OACL,UACA,QACA,OAAO;AAGT,aAAK,aAAa,QAAQ,CAAC,SAAQ;AACjC,eAAK,QAAQ,kBAAiB,SAC5B,KAAK,OACL,MACA,QACA,OAAO;QAEX,CAAC;AAED,aAAK,eAAe,CAAA;AAEpB,eAAM;MACR,CAAC;AAED,WAAK,QAAQ,IAAI,OAAO,MAAM,CAAA,GAAI,CAAC,SAAyB;AAC1D,cAAM,EAAE,QAAQ,SAAS,OAAM,IAAK,KAAK;AAEzC,YAAI,KAAK,mBAAkB,GAAI;AAC7B,eAAK,aAAa,KAAK,IAAI;QAC7B,OAAO;AACL,eAAK,QAAQ,kBAAiB,SAC5B,KAAK,OACL,MACA,QACA,OAAO;AAGT,iBAAM;QACR;MACF,CAAC;AAED,WAAK,OAAO,CAAC,KAAK,kBAAkB,iBAAgB;AAClD,aAAK,QAAQ,SAAS,YAAY;UAChC,OAAO;UACP;UACA;UACA;SACD;MACH,CAAC;AAED,WAAK,QAAQ,CAAC,KAAK,kBAAkB,kBAAiB;AACpD,aAAK,QAAQ,SAAS,YAAY;UAChC,OAAO;UACP;UACA;UACA;SACD;MACH,CAAC;AAED,WAAK,OAAO,MAAK;AACf,aAAK,QAAQ,SAAS,YAAY,EAAE,OAAO,OAAM,CAAE;MACrD,CAAC;IACH;;;;;;;;;;;IAYQ,OAAO,UACb,cACA,UACA,QACA,SAAgC;AAEhC,YAAM,QAAQ,KAAK,UAAU,YAAY;AACzC,YAAM,mBAAmB,KAAK,eAAe,QAAQ;AACrD,YAAM,QAA+B,CAAA;AACrC,YAAM,SAAgC,CAAA;AAEtC,WAAK,IAAI,OAAO,CAAC,KAAa,cAAyB;AACrD,YAAI,CAAC,iBAAiB,GAAG,GAAG;AAC1B,iBAAO,GAAG,IAAI;QAChB;MACF,CAAC;AAED,WAAK,IAAI,kBAAkB,CAAC,KAAK,iBAA4B;AAC3D,cAAM,mBAA+B,MAAM,GAAG;AAE9C,YAAI,kBAAkB;AACpB,gBAAM,kBAAkB,aAAa,IACnC,CAAC,MAAgB,EAAE,YAAY;AAEjC,gBAAM,kBAAkB,iBAAiB,IACvC,CAAC,MAAgB,EAAE,YAAY;AAEjC,gBAAM,kBAA8B,aAAa,OAC/C,CAAC,MAAgB,gBAAgB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAE9D,gBAAM,gBAA4B,iBAAiB,OACjD,CAAC,MAAgB,gBAAgB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAG9D,cAAI,gBAAgB,SAAS,GAAG;AAC9B,kBAAM,GAAG,IAAI;UACf;AAEA,cAAI,cAAc,SAAS,GAAG;AAC5B,mBAAO,GAAG,IAAI;UAChB;QACF,OAAO;AACL,gBAAM,GAAG,IAAI;QACf;MACF,CAAC;AAED,aAAO,KAAK,SAAS,OAAO,EAAE,OAAO,OAAM,GAAI,QAAQ,OAAO;IAChE;;;;;;;;;;;IAYQ,OAAO,SACb,OACA,MACA,QACA,SAAgC;AAEhC,YAAM,EAAE,OAAO,OAAM,IAAK;QACxB,OAAO,KAAK,eAAe,KAAK,KAAK;QACrC,QAAQ,KAAK,eAAe,KAAK,MAAM;;AAGzC,UAAI,CAAC,QAAQ;AACX,iBAAS,6BAAK;QAAE,GAAP;MACX;AAEA,UAAI,CAAC,SAAS;AACZ,kBAAU,6BAAK;QAAE,GAAP;MACZ;AAEA,WAAK,IAAI,OAAO,CAAC,KAAK,iBAA4B;;AAChD,cAAM,oBAA+B,KAAA,MAAM,GAAG,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AACnD,cAAM,GAAG,IAAI,KAAK,UAAU,YAAY;AAExC,YAAI,iBAAiB,SAAS,GAAG;AAC/B,gBAAM,qBAAqB,MAAM,GAAG,EAAE,IACpC,CAAC,MAAgB,EAAE,YAAY;AAEjC,gBAAM,eAA2B,iBAAiB,OAChD,CAAC,MAAgB,mBAAmB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAGjE,gBAAM,GAAG,EAAE,QAAQ,GAAG,YAAY;QACpC;AAEA,eAAO,KAAK,kBAAkB,YAAY;MAC5C,CAAC;AAED,WAAK,IAAI,QAAQ,CAAC,KAAK,kBAA6B;AAClD,YAAI,mBAA+B,MAAM,GAAG;AAE5C,YAAI,CAAC;AAAkB;AAEvB,cAAM,uBAAuB,cAAc,IACzC,CAAC,MAAgB,EAAE,YAAY;AAEjC,2BAAmB,iBAAiB,OAClC,CAAC,MAAgB,qBAAqB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAGnE,cAAM,GAAG,IAAI;AAEb,gBAAQ,KAAK,kBAAkB,aAAa;AAE5C,YAAI,iBAAiB,WAAW;AAAG,iBAAO,MAAM,GAAG;MACrD,CAAC;AAED,aAAO;IACT;;IAGQ,OAAO,IACb,KACA,MAAwB;AAExB,aAAO,OAAO,oBAAoB,GAAG,EAAE,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC;IACzE;;;;;;;;;;;;;;;;;;;;;;;;IAyBQ,OAAO,eACb,OAA+C;AAE/C,cAAQ,KAAK,UAAU,KAAK;AAE5B,aAAO,OAAO,oBAAoB,KAAK,EAAE,OAAO,CAAC,UAAU,QAAO;AAChE,cAAM,YAAY,MAAM,GAAG;AAE3B,YAAI,WAAW,WAAW;AACxB,mBAAS,GAAG,IAAI,UAAU,MAAM,IAAI,CAAC,aAAY;AAC/C,qBAAS,cAAc,IAAI,SAAS,SAAS;AAE7C,mBAAO,SAAS,SAAS;AACzB,mBAAO,SAAS,cAAc;AAE9B,mBAAO;UACT,CAAC;QACH,OAAO;AACL,mBAAS,GAAG,IAAI;QAClB;AAEA,eAAO;MACT,GAAG,CAAA,CAA2B;IAChC;;IAGQ,OAAO,UAAU,KAA2B;AAClD,aAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;IACvC;;IAGQ,OAAO,UAAgC;AAC7C,WAAK,OAAO,SAAS;IACvB;;IAGQ,QAAQ,UAAiC;AAC/C,WAAK,OAAO,UAAU;IACxB;;IAGQ,OAAO,UAAoB;AACjC,WAAK,OAAO,SAAS;IACvB;;IAGQ,qBAAkB;AACxB,aAAO,CAAC,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ,SAAQ;IAChE;;;;AFjRF,MAAY;AAAZ,GAAA,SAAYC,yCAAsC;AAChD,IAAAA,wCAAA,KAAA,IAAA;AACA,IAAAA,wCAAA,QAAA,IAAA;AACA,IAAAA,wCAAA,QAAA,IAAA;AACA,IAAAA,wCAAA,QAAA,IAAA;EACF,GALY,2CAAA,yCAAsC,CAAA,EAAA;AAOlD,MAAY;AAAZ,GAAA,SAAYC,wBAAqB;AAC/B,IAAAA,uBAAA,WAAA,IAAA;AACA,IAAAA,uBAAA,UAAA,IAAA;AACA,IAAAA,uBAAA,kBAAA,IAAA;AACA,IAAAA,uBAAA,QAAA,IAAA;EACF,GALY,0BAAA,wBAAqB,CAAA,EAAA;AAOjC,MAAY;AAAZ,GAAA,SAAYC,4BAAyB;AACnC,IAAAA,2BAAA,YAAA,IAAA;AACA,IAAAA,2BAAA,WAAA,IAAA;AACA,IAAAA,2BAAA,QAAA,IAAA;AACA,IAAAA,2BAAA,eAAA,IAAA;EACF,GALY,8BAAA,4BAAyB,CAAA,EAAA;AAO9B,MAAM,0BAA0B;AAgBvC,MAAqB,kBAArB,MAAqB,iBAAe;IA9HpC,OA8HoC;;;IAoBlC,YAES,OACA,SAAiC,EAAE,QAAQ,CAAA,EAAE,GAC7C,QAAsB;AAFtB,WAAA,QAAA;AACA,WAAA,SAAA;AACA,WAAA,SAAA;AAvBT,WAAA,WAOI,CAAA;AAEJ,WAAA,QAAwB,eAAe;AACvC,WAAA,aAAa;AAGb,WAAA,aAAqB,CAAA;AAYnB,WAAK,WAAW,MAAM,QAAQ,eAAe,EAAE;AAC/C,WAAK,OAAO,SAAM,OAAA,OACb;QACD,WAAW,EAAE,KAAK,OAAO,MAAM,MAAK;QACpC,UAAU,EAAE,KAAK,GAAE;QACnB,SAAS;SAER,OAAO,MAAM;AAElB,WAAK,UAAU,KAAK,OAAO;AAC3B,WAAK,WAAW,IAAI,KAClB,MACA,eAAe,MACf,KAAK,QACL,KAAK,OAAO;AAEd,WAAK,cAAc,IAAI,MACrB,MAAM,KAAK,sBAAqB,GAChC,KAAK,OAAO,gBAAgB;AAE9B,WAAK,SAAS,QAAQ,MAAM,MAAK;AAC/B,aAAK,QAAQ,eAAe;AAC5B,aAAK,YAAY,MAAK;AACtB,aAAK,WAAW,QAAQ,CAAC,cAAoB,UAAU,KAAI,CAAE;AAC7D,aAAK,aAAa,CAAA;MACpB,CAAC;AACD,WAAK,SAAS,MAAK;AACjB,aAAK,YAAY,MAAK;AACtB,aAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,IAAI,KAAK,SAAQ,CAAE,EAAE;AACnE,aAAK,QAAQ,eAAe;AAC5B,aAAK,OAAO,QAAQ,IAAI;MAC1B,CAAC;AACD,WAAK,SAAS,CAAC,WAAkB;AAC/B,YAAI,KAAK,WAAU,KAAM,KAAK,UAAS,GAAI;AACzC;QACF;AACA,aAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,IAAI,MAAM;AACxD,aAAK,QAAQ,eAAe;AAC5B,aAAK,YAAY,gBAAe;MAClC,CAAC;AACD,WAAK,SAAS,QAAQ,WAAW,MAAK;AACpC,YAAI,CAAC,KAAK,WAAU,GAAI;AACtB;QACF;AACA,aAAK,OAAO,IAAI,WAAW,WAAW,KAAK,KAAK,IAAI,KAAK,SAAS,OAAO;AACzE,aAAK,QAAQ,eAAe;AAC5B,aAAK,YAAY,gBAAe;MAClC,CAAC;AACD,WAAK,IAAI,eAAe,OAAO,CAAA,GAAI,CAAC,SAAc,QAAe;AAC/D,aAAK,SAAS,KAAK,gBAAgB,GAAG,GAAG,OAAO;MAClD,CAAC;AAED,WAAK,WAAW,IAAI,iBAAiB,IAAI;AAEzC,WAAK,uBACH,gBAAgB,KAAK,OAAO,QAAQ,IAAI;AAC1C,WAAK,UAAU,KAAK,OAAO,OAAO,WAAW;IAC/C;;IAGA,UACE,UACA,UAAU,KAAK,SAAO;;AAEtB,UAAI,CAAC,KAAK,OAAO,YAAW,GAAI;AAC9B,aAAK,OAAO,QAAO;MACrB;AACA,UAAI,KAAK,SAAS,eAAe,QAAQ;AACvC,cAAM,EACJ,QAAQ,EAAE,WAAW,UAAU,SAAS,UAAS,EAAE,IACjD,KAAK;AAET,aAAK,SAAS,CAAC,MACb,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAW,0BAA0B,eAAe,CAAC,CAAC;AAExD,aAAK,SAAS,MAAM,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAW,0BAA0B,MAAM,CAAC;AAEhE,cAAM,qBAAgD,CAAA;AACtD,cAAM,SAAS;UACb;UACA;UACA,mBACE,MAAA,KAAA,KAAK,SAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,CAAC,MAAM,EAAE,MAAM,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;UAC1D,SAAS;;AAGX,YAAI,KAAK,OAAO,kBAAkB;AAChC,6BAAmB,eAAe,KAAK,OAAO;QAChD;AAEA,aAAK,kBAAiB,OAAA,OAAM,EAAE,OAAM,GAAO,kBAAkB,CAAA;AAE7D,aAAK,aAAa;AAClB,aAAK,QAAQ,OAAO;AAEpB,aAAK,SACF,QAAQ,MAAM,OAAO,EAAE,iBAAgB,MAA8B;;AACpE,eAAK,OAAO,QAAO;AACnB,cAAI,qBAAqB,QAAW;AAClC,yBAAQ,QAAR,aAAQ,SAAA,SAAR,SAAW,0BAA0B,UAAU;AAC/C;UACF,OAAO;AACL,kBAAM,yBAAyB,KAAK,SAAS;AAC7C,kBAAM,eAAcC,MAAA,2BAAsB,QAAtB,2BAAsB,SAAA,SAAtB,uBAAwB,YAAM,QAAAA,QAAA,SAAAA,MAAI;AACtD,kBAAM,sBAAsB,CAAA;AAE5B,qBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,oBAAM,wBAAwB,uBAAuB,CAAC;AACtD,oBAAM,EACJ,QAAQ,EAAE,OAAO,QAAQ,OAAO,OAAM,EAAE,IACtC;AACJ,oBAAM,uBACJ,oBAAoB,iBAAiB,CAAC;AAExC,kBACE,wBACA,qBAAqB,UAAU,SAC/B,qBAAqB,WAAW,UAChC,qBAAqB,UAAU,SAC/B,qBAAqB,WAAW,QAChC;AACA,oCAAoB,KAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACnB,qBAAqB,GAAA,EACxB,IAAI,qBAAqB,GAAE,CAAA,CAAA;cAE/B,OAAO;AACL,qBAAK,YAAW;AAChB,qBAAK,QAAQ,eAAe;AAE5B,6BAAQ,QAAR,aAAQ,SAAA,SAAR,SACE,0BAA0B,eAC1B,IAAI,MACF,kEAAkE,CACnE;AAEH;cACF;YACF;AAEA,iBAAK,SAAS,mBAAmB;AAEjC,wBAAY,SAAS,0BAA0B,UAAU;AACzD;UACF;QACF,CAAC,EACA,QAAQ,SAAS,CAAC,UAAiC;AAClD,eAAK,QAAQ,eAAe;AAC5B,uBAAQ,QAAR,aAAQ,SAAA,SAAR,SACE,0BAA0B,eAC1B,IAAI,MACF,KAAK,UAAU,OAAO,OAAO,KAAK,EAAE,KAAK,IAAI,KAAK,OAAO,CAAC,CAC3D;AAEH;QACF,CAAC,EACA,QAAQ,WAAW,MAAK;AACvB,uBAAQ,QAAR,aAAQ,SAAA,SAAR,SAAW,0BAA0B,SAAS;AAC9C;QACF,CAAC;MACL;AACA,aAAO;IACT;IAEA,gBAAa;AAGX,aAAO,KAAK,SAAS;IACvB;IAEA,MAAM,MACJ,SACA,OAA+B,CAAA,GAAE;AAEjC,aAAO,MAAM,KAAK,KAChB;QACE,MAAM;QACN,OAAO;QACP;SAEF,KAAK,WAAW,KAAK,OAAO;IAEhC;IAEA,MAAM,QACJ,OAA+B,CAAA,GAAE;AAEjC,aAAO,MAAM,KAAK,KAChB;QACE,MAAM;QACN,OAAO;SAET,IAAI;IAER;IAqEA,GACE,MACA,QACA,UAAgC;AAEhC,aAAO,KAAK,IAAI,MAAM,QAAQ,QAAQ;IACxC;;;;;;;;;;IAUA,MAAM,KACJ,MAMA,OAA+B,CAAA,GAAE;;AAEjC,UAAI,CAAC,KAAK,SAAQ,KAAM,KAAK,SAAS,aAAa;AACjD,cAAM,EAAE,OAAO,SAAS,iBAAgB,IAAK;AAC7C,cAAM,gBAAgB,KAAK,OAAO,mBAC9B,UAAU,KAAK,OAAO,gBAAgB,KACtC;AACJ,cAAM,UAAU;UACd,QAAQ;UACR,SAAS;YACP,eAAe;YACf,QAAQ,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS;YAClD,gBAAgB;;UAElB,MAAM,KAAK,UAAU;YACnB,UAAU;cACR;gBACE,OAAO,KAAK;gBACZ;gBACA,SAAS;gBACT,SAAS,KAAK;;;WAGnB;;AAGH,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,kBAC1B,KAAK,sBACL,UACA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,KAAI,KAAK,OAAO;AAG9B,kBAAM,KAAA,SAAS,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM;AAC3B,iBAAO,SAAS,KAAK,OAAO;QAC9B,SAAS,OAAY;AACnB,cAAI,MAAM,SAAS,cAAc;AAC/B,mBAAO;UACT,OAAO;AACL,mBAAO;UACT;QACF;MACF,OAAO;AACL,eAAO,IAAI,QAAQ,CAAC,YAAW;;AAC7B,gBAAM,OAAO,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,WAAW,KAAK,OAAO;AAErE,cAAI,KAAK,SAAS,eAAe,GAAC,MAAAC,OAAAD,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,YAAM,QAAAC,QAAA,SAAA,SAAAA,IAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;AACrE,oBAAQ,IAAI;UACd;AAEA,eAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI,CAAC;AACtC,eAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,CAAC;AAC5C,eAAK,QAAQ,WAAW,MAAM,QAAQ,WAAW,CAAC;QACpD,CAAC;MACH;IACF;IAEA,kBAAkB,SAA+B;AAC/C,WAAK,SAAS,cAAc,OAAO;IACrC;;;;;;;;;;IAWA,YAAY,UAAU,KAAK,SAAO;AAChC,WAAK,QAAQ,eAAe;AAC5B,YAAM,UAAU,6BAAK;AACnB,aAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,EAAE;AAChD,aAAK,SAAS,eAAe,OAAO,SAAS,KAAK,SAAQ,CAAE;MAC9D,GAHgB;AAKhB,WAAK,SAAS,QAAO;AAErB,UAAI,YAAyB;AAE7B,aAAO,IAAI,QAAqC,CAAC,YAAW;AAC1D,oBAAY,IAAI,KAAK,MAAM,eAAe,OAAO,CAAA,GAAI,OAAO;AAC5D,kBACG,QAAQ,MAAM,MAAK;AAClB,kBAAO;AACP,kBAAQ,IAAI;QACd,CAAC,EACA,QAAQ,WAAW,MAAK;AACvB,kBAAO;AACP,kBAAQ,WAAW;QACrB,CAAC,EACA,QAAQ,SAAS,MAAK;AACrB,kBAAQ,OAAO;QACjB,CAAC;AAEH,kBAAU,KAAI;AACd,YAAI,CAAC,KAAK,SAAQ,GAAI;AACpB,oBAAU,QAAQ,MAAM,CAAA,CAAE;QAC5B;MACF,CAAC,EAAE,QAAQ,MAAK;AACd,sBAAS,QAAT,cAAS,SAAA,SAAT,UAAW,QAAO;MACpB,CAAC;IACH;;;;;;IAMA,WAAQ;AACN,WAAK,WAAW,QAAQ,CAAC,SAAe,KAAK,QAAO,CAAE;AACtD,WAAK,eAAe,aAAa,KAAK,YAAY,KAAK;AACvD,WAAK,SAAS,QAAO;IACvB;;IAIA,MAAM,kBACJ,KACA,SACA,SAAe;AAEf,YAAM,aAAa,IAAI,gBAAe;AACtC,YAAM,KAAK,WAAW,MAAM,WAAW,MAAK,GAAI,OAAO;AAEvD,YAAM,WAAW,MAAM,KAAK,OAAO,MAAM,KAAG,OAAA,OAAA,OAAA,OAAA,CAAA,GACvC,OAAO,GAAA,EACV,QAAQ,WAAW,OAAM,CAAA,CAAA;AAG3B,mBAAa,EAAE;AAEf,aAAO;IACT;;IAGA,MACE,OACA,SACA,UAAU,KAAK,SAAO;AAEtB,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,kBAAkB,KAAK,SAAS,KAAK,KAAK;MAClD;AACA,UAAI,YAAY,IAAI,KAAK,MAAM,OAAO,SAAS,OAAO;AACtD,UAAI,KAAK,SAAQ,GAAI;AACnB,kBAAU,KAAI;MAChB,OAAO;AACL,kBAAU,aAAY;AACtB,aAAK,WAAW,KAAK,SAAS;MAChC;AAEA,aAAO;IACT;;;;;;;;;IAUA,WAAW,QAAgB,SAAc,MAAa;AACpD,aAAO;IACT;;IAGA,UAAU,OAAa;AACrB,aAAO,KAAK,UAAU;IACxB;;IAGA,WAAQ;AACN,aAAO,KAAK,SAAS;IACvB;;IAGA,SAAS,MAAc,SAAe,KAAY;;AAChD,YAAM,YAAY,KAAK,kBAAiB;AACxC,YAAM,EAAE,OAAO,OAAO,OAAO,KAAI,IAAK;AACtC,YAAM,SAAmB,CAAC,OAAO,OAAO,OAAO,IAAI;AACnD,UAAI,OAAO,OAAO,QAAQ,SAAS,KAAK,KAAK,QAAQ,KAAK,SAAQ,GAAI;AACpE;MACF;AACA,UAAI,iBAAiB,KAAK,WAAW,WAAW,SAAS,GAAG;AAC5D,UAAI,WAAW,CAAC,gBAAgB;AAC9B,cAAM;MACR;AAEA,UAAI,CAAC,UAAU,UAAU,QAAQ,EAAE,SAAS,SAAS,GAAG;AACtD,SAAA,KAAA,KAAK,SAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAC1B,OAAO,CAAC,SAAQ;;AAChB,mBACED,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,WAAU,SACvB,MAAAC,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB,OAAO;QAEhD,CAAC,EACA,IAAI,CAAC,SAAS,KAAK,SAAS,gBAAgB,GAAG,CAAC;MACrD,OAAO;AACL,SAAA,KAAA,KAAK,SAAS,SAAS,OAAC,QAAA,OAAA,SAAA,SAAA,GACpB,OAAO,CAAC,SAAQ;;AAChB,cACE,CAAC,aAAa,YAAY,kBAAkB,EAAE,SAAS,SAAS,GAChE;AACA,gBAAI,QAAQ,MAAM;AAChB,oBAAM,SAAS,KAAK;AACpB,oBAAM,aAAYD,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE;AAC/B,qBACE,YACAC,MAAA,QAAQ,SAAG,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAAS,MAAM,OAC3B,cAAc,QACb,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,kBAAiB,SAC1B,KAAA,QAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,kBAAiB;YAE5C,OAAO;AACL,oBAAM,aAAY,MAAA,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB;AACxD,qBACE,cAAc,OACd,gBAAc,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB;YAEnD;UACF,OAAO;AACL,mBAAO,KAAK,KAAK,kBAAiB,MAAO;UAC3C;QACF,CAAC,EACA,IAAI,CAAC,SAAQ;AACZ,cAAI,OAAO,mBAAmB,YAAY,SAAS,gBAAgB;AACjE,kBAAM,kBAAkB,eAAe;AACvC,kBAAM,EAAE,QAAQ,OAAO,kBAAkB,MAAAC,OAAM,OAAM,IACnD;AACF,kBAAM,kBAAkB;cACtB;cACA;cACA;cACA,WAAWA;cACX,KAAK,CAAA;cACL,KAAK,CAAA;cACL;;AAEF,6BAAc,OAAA,OAAA,OAAA,OAAA,CAAA,GACT,eAAe,GACf,KAAK,mBAAmB,eAAe,CAAC;UAE/C;AACA,eAAK,SAAS,gBAAgB,GAAG;QACnC,CAAC;MACL;IACF;;IAGA,YAAS;AACP,aAAO,KAAK,UAAU,eAAe;IACvC;;IAGA,YAAS;AACP,aAAO,KAAK,UAAU,eAAe;IACvC;;IAGA,aAAU;AACR,aAAO,KAAK,UAAU,eAAe;IACvC;;IAGA,aAAU;AACR,aAAO,KAAK,UAAU,eAAe;IACvC;;IAGA,gBAAgB,KAAW;AACzB,aAAO,cAAc,GAAG;IAC1B;;IAGA,IAAI,MAAc,QAAgC,UAAkB;AAClE,YAAM,YAAY,KAAK,kBAAiB;AAExC,YAAM,UAAU;QACd,MAAM;QACN;QACA;;AAGF,UAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,aAAK,SAAS,SAAS,EAAE,KAAK,OAAO;MACvC,OAAO;AACL,aAAK,SAAS,SAAS,IAAI,CAAC,OAAO;MACrC;AAEA,aAAO;IACT;;IAGA,KAAK,MAAc,QAA8B;AAC/C,YAAM,YAAY,KAAK,kBAAiB;AAExC,WAAK,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,OAAO,CAAC,SAAQ;;AAClE,eAAO,IACL,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB,OAAO,aACnC,iBAAgB,QAAQ,KAAK,QAAQ,MAAM;MAE/C,CAAC;AACD,aAAO;IACT;;IAGQ,OAAO,QACb,MACA,MAA+B;AAE/B,UAAI,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACzD,eAAO;MACT;AAEA,iBAAW,KAAK,MAAM;AACpB,YAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,iBAAO;QACT;MACF;AAEA,aAAO;IACT;;IAGQ,wBAAqB;AAC3B,WAAK,YAAY,gBAAe;AAChC,UAAI,KAAK,OAAO,YAAW,GAAI;AAC7B,aAAK,QAAO;MACd;IACF;;;;;;IAOQ,SAAS,UAAkB;AACjC,WAAK,IAAI,eAAe,OAAO,CAAA,GAAI,QAAQ;IAC7C;;;;;;IAOQ,SAAS,UAAkB;AACjC,WAAK,IAAI,eAAe,OAAO,CAAA,GAAI,CAAC,WAAmB,SAAS,MAAM,CAAC;IACzE;;;;;;IAOQ,WAAQ;AACd,aAAO,KAAK,OAAO,YAAW,KAAM,KAAK,UAAS;IACpD;;IAGQ,QAAQ,UAAU,KAAK,SAAO;AACpC,UAAI,KAAK,WAAU,GAAI;AACrB;MACF;AACA,WAAK,OAAO,gBAAgB,KAAK,KAAK;AACtC,WAAK,QAAQ,eAAe;AAC5B,WAAK,SAAS,OAAO,OAAO;IAC9B;;IAGQ,mBAAmB,SAAY;AACrC,YAAM,UAAU;QACd,KAAK,CAAA;QACL,KAAK,CAAA;;AAGP,UAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAC1D,gBAAQ,MAAmB,kBACzB,QAAQ,SACR,QAAQ,MAAM;MAElB;AAEA,UAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAC1D,gBAAQ,MAAmB,kBACzB,QAAQ,SACR,QAAQ,UAAU;MAEtB;AAEA,aAAO;IACT;;;;AR/wBF,MAAMC,QAAO,6BAAK;EAAE,GAAP;AAoCb,MAAM,gBAAgB;;;;;;AAOtB,MAAqB,iBAArB,MAAmC;IAzFnC,OAyFmC;;;;;;;;;;;;;;;;;;;;;IA4DjC,YAAY,UAAkB,SAA+B;;AA3D7D,WAAA,mBAAkC;AAClC,WAAA,SAAwB;AACxB,WAAA,WAA8B,IAAI,MAAK;AACvC,WAAA,WAAmB;AACnB,WAAA,eAAuB;AAEvB,WAAA,UAAsC,CAAA;AACtC,WAAA,SAAqC,CAAA;AACrC,WAAA,UAAkB;AAElB,WAAA,sBAA8B;AAC9B,WAAA,iBAA6D;AAC7D,WAAA,sBAAqC;AACrC,WAAA,oBAAuDA;AACvD,WAAA,MAAc;AAEd,WAAA,SAAmBA;AAKnB,WAAA,OAA6B;AAC7B,WAAA,aAAyB,CAAA;AACzB,WAAA,aAAyB,IAAI,WAAU;AACvC,WAAA,uBAKI;QACF,MAAM,CAAA;QACN,OAAO,CAAA;QACP,OAAO,CAAA;QACP,SAAS,CAAA;;AAGX,WAAA,cAAqD;AA0TrD,WAAA,gBAAgB,CAAC,gBAA8B;AAC7C,YAAI;AACJ,YAAI,aAAa;AACf,mBAAS;QACX,WAAW,OAAO,UAAU,aAAa;AACvC,mBAAS,2BAAI,SACX,gEAAsC,KAAK,CAAC,EAAE,SAASC,OAAK,MAC1DA,OAAM,GAAG,IAAI,CAAC,GAFT;QAIX,OAAO;AACL,mBAAS;QACX;AACA,eAAO,IAAI,SAAS,OAAO,GAAG,IAAI;MACpC;AA/SE,WAAK,WAAW,GAAG,QAAQ,IAAI,WAAW,SAAS;AACnD,WAAK,eAAe,gBAAgB,QAAQ;AAC5C,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;AACtB,aAAK,YAAY,QAAQ;MAC3B,OAAO;AACL,aAAK,YAAY;MACnB;AACA,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAQ,aAAK,SAAS,QAAQ;AAC3C,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAS,aAAK,UAAU,QAAQ;AAC7C,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAQ,aAAK,SAAS,QAAQ;AAC3C,WAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAW;AAC3C,aAAK,WAAW,QAAQ,YAAY,QAAQ;AAC5C,aAAK,SAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,KAAK,MAAM,GAAA,EAAE,WAAW,KAAK,SAAkB,CAAA;MACpE;AAEA,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AACX,aAAK,sBAAsB,QAAQ;AAErC,YAAM,oBAAmB,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE;AAC1C,UAAI,kBAAkB;AACpB,aAAK,mBAAmB;AACxB,aAAK,SAAS;MAChB;AAEA,WAAK,oBAAmB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,oBAC7B,QAAQ,mBACR,CAAC,UAAiB;AAChB,eAAO,CAAC,KAAM,KAAM,KAAM,GAAK,EAAE,QAAQ,CAAC,KAAK;MACjD;AACJ,WAAK,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UACnB,QAAQ,SACR,CAAC,SAAe,aAAsB;AACpC,eAAO,SAAS,KAAK,UAAU,OAAO,CAAC;MACzC;AACJ,WAAK,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UACnB,QAAQ,SACR,KAAK,WAAW,OAAO,KAAK,KAAK,UAAU;AAC/C,WAAK,iBAAiB,IAAI,MAAM,YAAW;AACzC,aAAK,WAAU;AACf,aAAK,QAAO;MACd,GAAG,KAAK,gBAAgB;AAExB,WAAK,QAAQ,KAAK,cAAc,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,KAAK;AAC9C,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACnB,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,QAAQ;AACnD,gBAAM,IAAI,MAAM,6BAA6B;QAC/C;AACA,aAAK,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAU;AACjC,aAAK,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;MAC5B;AACA,WAAK,eAAc,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAe;IAC7C;;;;IAKA,UAAO;AACL,UAAI,KAAK,MAAM;AACb;MACF;AACA,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,YAAYC;MACnB;AACA,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,IAAI,MAAM,uBAAuB;MACzC;AACA,WAAK,OAAO,IAAI,KAAK,UAAU,KAAK,YAAW,CAAE;AACjD,WAAK,gBAAe;IACtB;;;;;IAMA,cAAW;AACT,aAAO,KAAK,cACV,KAAK,UACL,OAAO,OAAO,CAAA,GAAI,KAAK,QAAQ,EAAE,KAAK,IAAG,CAAE,CAAC;IAEhD;;;;;;;IAQA,WAAW,MAAe,QAAe;AACvC,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,UAAU,WAAA;QAAa;AACjC,YAAI,MAAM;AACR,eAAK,KAAK,MAAM,MAAM,WAAM,QAAN,WAAM,SAAN,SAAU,EAAE;QACpC,OAAO;AACL,eAAK,KAAK,MAAK;QACjB;AACA,aAAK,OAAO;AAGZ,aAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,aAAK,eAAe,MAAK;AACzB,aAAK,SAAS,QAAQ,CAAC,YAAY,QAAQ,SAAQ,CAAE;MACvD;IACF;;;;IAKA,cAAW;AACT,aAAO,KAAK;IACd;;;;;IAMA,MAAM,cACJ,SAAwB;AAExB,YAAM,SAAS,MAAM,QAAQ,YAAW;AAExC,UAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,aAAK,WAAU;MACjB;AAEA,aAAO;IACT;;;;IAKA,MAAM,oBAAiB;AACrB,YAAM,WAAW,MAAM,QAAQ,IAC7B,KAAK,SAAS,IAAI,CAAC,YAAY,QAAQ,YAAW,CAAE,CAAC;AAEvD,WAAK,WAAW,CAAA;AAChB,WAAK,WAAU;AACf,aAAO;IACT;;;;;;IAOA,IAAI,MAAc,KAAa,MAAU;AACvC,WAAK,OAAO,MAAM,KAAK,IAAI;IAC7B;;;;IAKA,kBAAe;AACb,cAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY;QACzC,KAAK,cAAc;AACjB,iBAAO,iBAAiB;QAC1B,KAAK,cAAc;AACjB,iBAAO,iBAAiB;QAC1B,KAAK,cAAc;AACjB,iBAAO,iBAAiB;QAC1B;AACE,iBAAO,iBAAiB;MAC5B;IACF;;;;IAKA,cAAW;AACT,aAAO,KAAK,gBAAe,MAAO,iBAAiB;IACrD;IAEA,QACE,OACA,SAAiC,EAAE,QAAQ,CAAA,EAAE,GAAE;AAE/C,YAAM,gBAAgB,YAAY,KAAK;AACvC,YAAM,SAAS,KAAK,YAAW,EAAG,KAChC,CAAC,MAAuB,EAAE,UAAU,aAAa;AAGnD,UAAI,CAAC,QAAQ;AACX,cAAM,OAAO,IAAI,gBAAgB,YAAY,KAAK,IAAI,QAAQ,IAAI;AAClE,aAAK,SAAS,KAAK,IAAI;AAEvB,eAAO;MACT,OAAO;AACL,eAAO;MACT;IACF;;;;;;IAOA,KAAK,MAAqB;AACxB,YAAM,EAAE,OAAO,OAAO,SAAS,IAAG,IAAK;AACvC,YAAM,WAAW,6BAAK;AACpB,aAAK,OAAO,MAAM,CAAC,WAAe;;AAChC,WAAA,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,MAAM;QACxB,CAAC;MACH,GAJiB;AAKjB,WAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO;AACtD,UAAI,KAAK,YAAW,GAAI;AACtB,iBAAQ;MACV,OAAO;AACL,aAAK,WAAW,KAAK,QAAQ;MAC/B;IACF;;;;;;;;;;IAWA,MAAM,QAAQ,QAAuB,MAAI;AACvC,UAAI,cACF,SACC,KAAK,eAAgB,MAAM,KAAK,YAAW,KAC5C,KAAK;AAEP,UAAI,KAAK,oBAAoB,aAAa;AACxC,aAAK,mBAAmB;AACxB,aAAK,SAAS,QAAQ,CAAC,YAAW;AAChC,gBAAM,UAAU;YACd,cAAc;YACd,SAAS;;AAGX,yBAAe,QAAQ,kBAAkB,OAAO;AAEhD,cAAI,QAAQ,cAAc,QAAQ,UAAS,GAAI;AAC7C,oBAAQ,MAAM,eAAe,cAAc;cACzC,cAAc;aACf;UACH;QACF,CAAC;MACH;IACF;;;;IAIA,MAAM,gBAAa;;AACjB,UAAI,CAAC,KAAK,YAAW,GAAI;AACvB,aAAK,kBAAkB,cAAc;AACrC;MACF;AACA,UAAI,KAAK,qBAAqB;AAC5B,aAAK,sBAAsB;AAC3B,aAAK,IACH,aACA,0DAA0D;AAE5D,aAAK,kBAAkB,SAAS;AAChC,SAAA,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,iBAAiB,kBAAkB;AACpD;MACF;AACA,WAAK,sBAAsB,KAAK,SAAQ;AACxC,WAAK,KAAK;QACR,OAAO;QACP,OAAO;QACP,SAAS,CAAA;QACT,KAAK,KAAK;OACX;AACD,WAAK,kBAAkB,MAAM;AAC7B,YAAM,KAAK,QAAO;IACpB;IAEA,YAAY,UAA2C;AACrD,WAAK,oBAAoB;IAC3B;;;;IAIA,kBAAe;AACb,UAAI,KAAK,YAAW,KAAM,KAAK,WAAW,SAAS,GAAG;AACpD,aAAK,WAAW,QAAQ,CAAC,aAAa,SAAQ,CAAE;AAChD,aAAK,aAAa,CAAA;MACpB;IACF;;;;;;IA2BA,WAAQ;AACN,UAAI,SAAS,KAAK,MAAM;AACxB,UAAI,WAAW,KAAK,KAAK;AACvB,aAAK,MAAM;MACb,OAAO;AACL,aAAK,MAAM;MACb;AAEA,aAAO,KAAK,IAAI,SAAQ;IAC1B;;;;;;IAOA,gBAAgB,OAAa;AAC3B,UAAI,aAAa,KAAK,SAAS,KAC7B,CAAC,MAAM,EAAE,UAAU,UAAU,EAAE,UAAS,KAAM,EAAE,WAAU,EAAG;AAE/D,UAAI,YAAY;AACd,aAAK,IAAI,aAAa,4BAA4B,KAAK,GAAG;AAC1D,mBAAW,YAAW;MACxB;IACF;;;;;;;;IASA,QAAQ,SAAwB;AAC9B,WAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,UAAU,QAAQ,KAAK;IACvE;;;;;;IAOQ,kBAAe;AACrB,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,aAAa;AACvB,aAAK,KAAK,SAAS,MAAM,KAAK,YAAW;AACzC,aAAK,KAAK,UAAU,CAAC,UAAiB,KAAK,aAAa,KAAK;AAC7D,aAAK,KAAK,YAAY,CAAC,UAAe,KAAK,eAAe,KAAK;AAC/D,aAAK,KAAK,UAAU,CAAC,UAAe,KAAK,aAAa,KAAK;MAC7D;IACF;;IAGQ,eAAe,YAAyB;AAC9C,WAAK,OAAO,WAAW,MAAM,CAAC,QAAwB;AACpD,YAAI,EAAE,OAAO,OAAO,SAAS,IAAG,IAAK;AAErC,YAAI,UAAU,aAAa,UAAU,aAAa;AAChD,eAAK,kBAAkB,IAAI,QAAQ,UAAU,OAAO,OAAO,OAAO;QACpE;AAEA,YAAI,OAAO,QAAQ,KAAK,qBAAqB;AAC3C,eAAK,sBAAsB;QAC7B;AAEA,aAAK,IACH,WACA,GAAG,QAAQ,UAAU,EAAE,IAAI,KAAK,IAAI,KAAK,IACtC,OAAO,MAAM,MAAM,OAAQ,EAC9B,IACA,OAAO;AAGT,cAAM,KAAK,KAAK,QAAQ,EACrB,OAAO,CAAC,YAA6B,QAAQ,UAAU,KAAK,CAAC,EAC7D,QAAQ,CAAC,YACR,QAAQ,SAAS,OAAO,SAAS,GAAG,CAAC;AAGzC,aAAK,qBAAqB,QAAQ,QAAQ,CAAC,aAAa,SAAS,GAAG,CAAC;MACvE,CAAC;IACH;;IAGQ,cAAW;AACjB,WAAK,IAAI,aAAa,gBAAgB,KAAK,YAAW,CAAE,EAAE;AAC1D,WAAK,gBAAe;AACpB,WAAK,eAAe,MAAK;AACzB,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,gBAAe;MACtB,OAAO;AACL,YAAI,CAAC,KAAK,WAAW;AACnB,eAAK,sBAAqB;QAC5B;MACF;AAEA,WAAK,qBAAqB,KAAK,QAAQ,CAAC,aAAa,SAAQ,CAAE;IACjE;;IAEQ,kBAAe;AACrB,WAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,WAAK,iBAAiB,YACpB,MAAM,KAAK,cAAa,GACxB,KAAK,mBAAmB;IAE5B;;IAGQ,wBAAqB;AAC3B,UAAI,KAAK,WAAW;AAClB,aAAK,IAAI,UAAU,4BAA4B,KAAK,SAAS,EAAE;MACjE,OAAO;AACL,aAAK,IAAI,UAAU,yBAAyB;MAC9C;AACA,YAAM,YAAY,KAAK,iBAAiB,KAAK,SAAU;AACvD,WAAK,YAAY,IAAI,OAAO,SAAS;AACrC,WAAK,UAAU,UAAU,CAAC,UAAS;AACjC,aAAK,IAAI,UAAU,gBAAiB,MAAqB,OAAO;AAChE,aAAK,UAAW,UAAS;MAC3B;AACA,WAAK,UAAU,YAAY,CAAC,UAAS;AACnC,YAAI,MAAM,KAAK,UAAU,aAAa;AACpC,eAAK,cAAa;QACpB;MACF;AACA,WAAK,UAAU,YAAY;QACzB,OAAO;QACP,UAAU,KAAK;OAChB;IACH;;IAEQ,aAAa,OAAU;AAC7B,WAAK,IAAI,aAAa,SAAS,KAAK;AACpC,WAAK,kBAAiB;AACtB,WAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,WAAK,eAAe,gBAAe;AACnC,WAAK,qBAAqB,MAAM,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;IACvE;;IAGQ,aAAa,OAAY;AAC/B,WAAK,IAAI,aAAa,GAAG,KAAK,EAAE;AAChC,WAAK,kBAAiB;AACtB,WAAK,qBAAqB,MAAM,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;IACvE;;IAGQ,oBAAiB;AACvB,WAAK,SAAS,QAAQ,CAAC,YACrB,QAAQ,SAAS,eAAe,KAAK,CAAC;IAE1C;;IAGQ,cACN,KACA,QAAiC;AAEjC,UAAI,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AACpC,eAAO;MACT;AACA,YAAM,SAAS,IAAI,MAAM,IAAI,IAAI,MAAM;AACvC,YAAM,QAAQ,IAAI,gBAAgB,MAAM;AACxC,aAAO,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK;IAChC;IAEQ,iBAAiB,KAAuB;AAC9C,UAAI;AACJ,UAAI,KAAK;AACP,qBAAa;MACf,OAAO;AACL,cAAM,OAAO,IAAI,KAAK,CAAC,aAAa,GAAG,EAAE,MAAM,yBAAwB,CAAE;AACzE,qBAAa,IAAI,gBAAgB,IAAI;MACvC;AACA,aAAO;IACT;;;;AW3nBF;;;ACAA;A;;;;;ACAA;AAAM,MAAO,eAAP,cAA4B,MAAK;IAAvC,OAAuC;;;IAGrC,YAAY,SAAe;AACzB,YAAM,OAAO;AAHL,WAAA,mBAAmB;AAI3B,WAAK,OAAO;IACd;;AAGI,WAAU,eAAe,OAAc;AAC3C,WAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB;EAC9E;AAFgB;AAIV,MAAO,kBAAP,cAA+B,aAAY;IAbjD,OAaiD;;;IAI/C,YAAY,SAAiB,QAAgB,YAAkB;AAC7D,YAAM,OAAO;AACb,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,aAAa;IACpB;IAEA,SAAM;AACJ,aAAO;QACL,MAAM,KAAK;QACX,SAAS,KAAK;QACd,QAAQ,KAAK;QACb,YAAY,KAAK;;IAErB;;AAGI,MAAO,sBAAP,cAAmC,aAAY;IAlCrD,OAkCqD;;;IAGnD,YAAY,SAAiB,eAAsB;AACjD,YAAM,OAAO;AACb,WAAK,OAAO;AACZ,WAAK,gBAAgB;IACvB;;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvCK,MAAMC,gBAAe,wBAAC,gBAA8B;AACzD,QAAI;AACJ,QAAI,aAAa;AACf,eAAS;eACA,OAAO,UAAU,aAAa;AACvC,eAAS,2BAAI,SACX,gEAAsC,KAAK,CAAC,EAAE,SAASC,OAAK,MAAOA,OAAM,GAAG,IAAI,CAAC,GAD1E;WAEJ;AACL,eAAS;;AAEX,WAAO,IAAI,SAAS,OAAO,GAAG,IAAI;EACpC,GAX4B;AAarB,MAAM,kBAAkB,6BAAqCC,WAAA,QAAA,QAAA,QAAA,aAAA;AAClE,QAAI,OAAO,aAAa,aAAa;AAEnC,cAAQ,MAAM,iEAAuC;;AAGvD,WAAO;EACT,CAAC,GAP8B;AASxB,MAAM,mBAAmB,wBAAC,SAAsC;AACrE,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAO,KAAK,IAAI,CAAC,OAAO,iBAAiB,EAAE,CAAC;eACnC,OAAO,SAAS,cAAc,SAAS,OAAO,IAAI,GAAG;AAC9D,aAAO;;AAGT,UAAM,SAA8B,CAAA;AACpC,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AAC5C,YAAM,SAAS,IAAI,QAAQ,iBAAiB,CAAC,MAAM,EAAE,YAAW,EAAG,QAAQ,SAAS,EAAE,CAAC;AACvF,aAAO,MAAM,IAAI,iBAAiB,KAAK;IACzC,CAAC;AAED,WAAO;EACT,GAdgC;AAqBzB,MAAM,gBAAgB,wBAAC,UAA0B;AACtD,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,aAAO;;AAGT,UAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,YACG,cAAc,QACb,cAAc,OAAO,aACrB,OAAO,eAAe,SAAS,MAAM,SACvC,EAAE,OAAO,eAAe,UACxB,EAAE,OAAO,YAAY;EAEzB,GAb6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9B7B,MAAM,mBAAmB,wBAAC,QACxB,IAAI,OAAO,IAAI,WAAW,IAAI,qBAAqB,IAAI,SAAS,KAAK,UAAU,GAAG,GAD3D;AAGzB,MAAM,cAAc,wBAClB,OACA,QACA,YACEC,WAAA,QAAA,QAAA,QAAA,aAAA;AACF,UAAM,MAAM,MAAM,gBAAe;AAEjC,QAAI,iBAAiB,OAAO,EAAC,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAe;AACnD,YACG,KAAI,EACJ,KAAK,CAAC,QAAO;AACZ,cAAM,SAAS,MAAM,UAAU;AAC/B,cAAM,cAAa,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,eAAc,SAAS;AAC/C,eAAO,IAAI,gBAAgB,iBAAiB,GAAG,GAAG,QAAQ,UAAU,CAAC;MACvE,CAAC,EACA,MAAM,CAAC,QAAO;AACb,eAAO,IAAI,oBAAoB,iBAAiB,GAAG,GAAG,GAAG,CAAC;MAC5D,CAAC;WACE;AACL,aAAO,IAAI,oBAAoB,iBAAiB,KAAK,GAAG,KAAK,CAAC;;EAElE,CAAC,GArBmB;AAuBpB,MAAM,oBAAoB,wBACxB,QACA,SACA,YACA,SACE;AACF,UAAM,SAA+B,EAAE,QAAQ,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAW,CAAA,EAAE;AAE9E,QAAI,WAAW,SAAS,CAAC,MAAM;AAC7B,aAAO;;AAGT,QAAI,cAAc,IAAI,GAAG;AACvB,aAAO,UAAO,OAAA,OAAA,EAAK,gBAAgB,mBAAkB,GAAK,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AAC1E,aAAO,OAAO,KAAK,UAAU,IAAI;WAC5B;AACL,aAAO,OAAO;;AAGhB,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,MAAM,GAAK,UAAU;EACnC,GApB0B;AAsB1B,WAAe,eACb,SACA,QACA,KACA,SACA,YACA,MAAa;;AAEb,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAQ,KAAK,kBAAkB,QAAQ,SAAS,YAAY,IAAI,CAAC,EAC9D,KAAK,CAAC,WAAU;AACf,cAAI,CAAC,OAAO;AAAI,kBAAM;AACtB,cAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAe,mBAAO;AACnC,iBAAO,OAAO,KAAI;QACpB,CAAC,EACA,KAAK,CAAC,SAAS,QAAQ,IAAI,CAAC,EAC5B,MAAM,CAAC,UAAU,YAAY,OAAO,QAAQ,OAAO,CAAC;MACzD,CAAC;IACH,CAAC;;AAlBc;AAoBT,WAAgB,IACpB,SACA,KACA,SACA,YAA4B;;AAE5B,aAAO,eAAe,SAAS,OAAO,KAAK,SAAS,UAAU;IAChE,CAAC;;AAPqB;AAShB,WAAgB,KACpB,SACA,KACA,MACA,SACA,YAA4B;;AAE5B,aAAO,eAAe,SAAS,QAAQ,KAAK,SAAS,YAAY,IAAI;IACvE,CAAC;;AARqB;AAUhB,WAAgB,IACpB,SACA,KACA,MACA,SACA,YAA4B;;AAE5B,aAAO,eAAe,SAAS,OAAO,KAAK,SAAS,YAAY,IAAI;IACtE,CAAC;;AARqB;AAUhB,WAAgB,KACpB,SACA,KACA,SACA,YAA4B;;AAE5B,aAAO,eACL,SACA,QACA,KAAG,OAAA,OAAA,OAAA,OAAA,CAAA,GAEE,OAAO,GAAA,EACV,eAAe,KAAI,CAAA,GAErB,UAAU;IAEd,CAAC;;AAhBqB;AAkBhB,WAAgB,OACpB,SACA,KACA,MACA,SACA,YAA4B;;AAE5B,aAAO,eAAe,SAAS,UAAU,KAAK,SAAS,YAAY,IAAI;IACzE,CAAC;;AARqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADpHtB,MAAM,yBAAyB;IAC7B,OAAO;IACP,QAAQ;IACR,QAAQ;MACN,QAAQ;MACR,OAAO;;;AAIX,MAAM,uBAAoC;IACxC,cAAc;IACd,aAAa;IACb,QAAQ;;AAeV,MAAqB,iBAArB,MAAmC;WAAA;;;IAMjC,YACE,KACA,UAAqC,CAAA,GACrC,UACAC,QAAa;AAEb,WAAK,MAAM;AACX,WAAK,UAAU;AACf,WAAK,WAAW;AAChB,WAAK,QAAQC,cAAaD,MAAK;IACjC;;;;;;;;IASc,eACZ,QACA,MACA,UACA,aAAyB;;AAWzB,YAAI;AACF,cAAI;AACJ,gBAAM,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,oBAAoB,GAAK,WAAW;AACzD,cAAI,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACN,KAAK,OAAO,GACX,WAAW,UAAU,EAAE,YAAY,OAAO,QAAQ,MAAiB,EAAC,CAAG;AAG7E,gBAAM,WAAW,QAAQ;AAEzB,cAAI,OAAO,SAAS,eAAe,oBAAoB,MAAM;AAC3D,mBAAO,IAAI,SAAQ;AACnB,iBAAK,OAAO,gBAAgB,QAAQ,YAAsB;AAC1D,gBAAI,UAAU;AACZ,mBAAK,OAAO,YAAY,KAAK,eAAe,QAAQ,CAAC;;AAEvD,iBAAK,OAAO,IAAI,QAAQ;qBACf,OAAO,aAAa,eAAe,oBAAoB,UAAU;AAC1E,mBAAO;AACP,iBAAK,OAAO,gBAAgB,QAAQ,YAAsB;AAC1D,gBAAI,UAAU;AACZ,mBAAK,OAAO,YAAY,KAAK,eAAe,QAAQ,CAAC;;iBAElD;AACL,mBAAO;AACP,oBAAQ,eAAe,IAAI,WAAW,QAAQ,YAAY;AAC1D,oBAAQ,cAAc,IAAI,QAAQ;AAElC,gBAAI,UAAU;AACZ,sBAAQ,YAAY,IAAI,KAAK,SAAS,KAAK,eAAe,QAAQ,CAAC;;;AAIvE,cAAI,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,SAAS;AACxB,sBAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,OAAO,GAAK,YAAY,OAAO;;AAGhD,gBAAM,YAAY,KAAK,oBAAoB,IAAI;AAC/C,gBAAM,QAAQ,KAAK,cAAc,SAAS;AAC1C,gBAAM,OAAO,OAAO,UAAU,QAAQ,MAAM,MAC1C,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,KAAK,IAC3B,MAAc,OAAA,OAAA,EACZ,QAAO,IAAM,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAS,EAAE,QAAQ,QAAQ,OAAM,IAAK,CAAA,CAAG,CAAA;AAGnE,iBAAO;YACL,MAAM,EAAE,MAAM,WAAW,IAAI,KAAK,IAAI,UAAU,KAAK,IAAG;YACxD,OAAO;;iBAEF,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;IAQK,OACJ,MACA,UACA,aAAyB;;AAWzB,eAAO,KAAK,eAAe,QAAQ,MAAM,UAAU,WAAW;MAChE,CAAC;;;;;;;;IAQK,kBACJ,MACA,OACA,UACA,aAAyB;;AAEzB,cAAM,YAAY,KAAK,oBAAoB,IAAI;AAC/C,cAAM,QAAQ,KAAK,cAAc,SAAS;AAE1C,cAAM,MAAM,IAAI,IAAI,KAAK,MAAM,uBAAuB,KAAK,EAAE;AAC7D,YAAI,aAAa,IAAI,SAAS,KAAK;AAEnC,YAAI;AACF,cAAI;AACJ,gBAAM,UAAO,OAAA,OAAA,EAAK,QAAQ,qBAAqB,OAAM,GAAK,WAAW;AACrE,gBAAM,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACR,KAAK,OAAO,GACZ,EAAE,YAAY,OAAO,QAAQ,MAAiB,EAAC,CAAE;AAGtD,cAAI,OAAO,SAAS,eAAe,oBAAoB,MAAM;AAC3D,mBAAO,IAAI,SAAQ;AACnB,iBAAK,OAAO,gBAAgB,QAAQ,YAAsB;AAC1D,iBAAK,OAAO,IAAI,QAAQ;qBACf,OAAO,aAAa,eAAe,oBAAoB,UAAU;AAC1E,mBAAO;AACP,iBAAK,OAAO,gBAAgB,QAAQ,YAAsB;iBACrD;AACL,mBAAO;AACP,oBAAQ,eAAe,IAAI,WAAW,QAAQ,YAAY;AAC1D,oBAAQ,cAAc,IAAI,QAAQ;;AAGpC,gBAAM,OAAO,MAAM,IAAI,KAAK,OAAO,IAAI,SAAQ,GAAI,MAAgB,EAAE,QAAO,CAAE;AAE9E,iBAAO;YACL,MAAM,EAAE,MAAM,WAAW,UAAU,KAAK,IAAG;YAC3C,OAAO;;iBAEF,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;IASK,sBACJ,MACA,SAA6B;;AAW7B,YAAI;AACF,cAAI,QAAQ,KAAK,cAAc,IAAI;AAEnC,gBAAM,UAAO,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO;AAEjC,cAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACnB,oBAAQ,UAAU,IAAI;;AAGxB,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,uBAAuB,KAAK,IACvC,CAAA,GACA,EAAE,QAAO,CAAE;AAGb,gBAAM,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,GAAG;AAEvC,gBAAM,QAAQ,IAAI,aAAa,IAAI,OAAO;AAE1C,cAAI,CAAC,OAAO;AACV,kBAAM,IAAI,aAAa,0BAA0B;;AAGnD,iBAAO,EAAE,MAAM,EAAE,WAAW,IAAI,SAAQ,GAAI,MAAM,MAAK,GAAI,OAAO,KAAI;iBAC/D,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;IAQK,OACJ,MACA,UAWA,aAAyB;;AAWzB,eAAO,KAAK,eAAe,OAAO,MAAM,UAAU,WAAW;MAC/D,CAAC;;;;;;;;;IASK,KACJ,UACA,QACA,SAA4B;;AAW5B,YAAI;AACF,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBACX;YACE,UAAU,KAAK;YACf,WAAW;YACX,gBAAgB;YAChB,mBAAmB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;aAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;IASK,KACJ,UACA,QACA,SAA4B;;AAW5B,YAAI;AACF,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBACX;YACE,UAAU,KAAK;YACf,WAAW;YACX,gBAAgB;YAChB,mBAAmB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;aAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAK,IAAG,GAAI,OAAO,KAAI;iBACvC,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;;IAUK,gBACJ,MACA,WACA,SAAuE;;AAWvE,YAAI;AACF,cAAI,QAAQ,KAAK,cAAc,IAAI;AAEnC,cAAI,OAAO,MAAM,KACf,KAAK,OACL,GAAG,KAAK,GAAG,gBAAgB,KAAK,IAAE,OAAA,OAAA,EAChC,UAAS,IAAM,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY,EAAE,WAAW,QAAQ,UAAS,IAAK,CAAA,CAAG,GAC5E,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,gBAAM,sBAAqB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAChC,aAAa,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAC9D;AACJ,gBAAM,YAAY,UAAU,GAAG,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG,kBAAkB,EAAE;AAC/E,iBAAO,EAAE,UAAS;AAClB,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;IASK,iBACJ,OACA,WACA,SAAwC;;AAWxC,YAAI;AACF,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBAAgB,KAAK,QAAQ,IACxC,EAAE,WAAW,MAAK,GAClB,EAAE,SAAS,KAAK,QAAO,CAAE;AAG3B,gBAAM,sBAAqB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAChC,aAAa,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAC9D;AACJ,iBAAO;YACL,MAAM,KAAK,IAAI,CAAC,UAAiC,OAAA,OAAA,OAAA,OAAA,CAAA,GAC5C,KAAK,GAAA,EACR,WAAW,MAAM,YACb,UAAU,GAAG,KAAK,GAAG,GAAG,MAAM,SAAS,GAAG,kBAAkB,EAAE,IAC9D,KAAI,CAAA,CACR;YACF,OAAO;;iBAEF,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;IAQK,SACJ,MACA,SAA0C;;AAW1C,cAAM,sBAAsB,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAc;AAC1D,cAAM,aAAa,sBAAsB,+BAA+B;AACxE,cAAM,sBAAsB,KAAK,4BAA2B,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAa,CAAA,CAAE;AACpF,cAAM,cAAc,sBAAsB,IAAI,mBAAmB,KAAK;AAEtE,YAAI;AACF,gBAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,gBAAM,MAAM,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,IAAI,UAAU,IAAI,KAAK,GAAG,WAAW,IAAI;YACpF,SAAS,KAAK;YACd,eAAe;WAChB;AACD,gBAAM,OAAO,MAAM,IAAI,KAAI;AAC3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;IAMK,KACJ,MAAY;;AAWZ,cAAM,QAAQ,KAAK,cAAc,IAAI;AAErC,YAAI;AACF,gBAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB,KAAK,IAAI;YACrE,SAAS,KAAK;WACf;AAED,iBAAO,EAAE,MAAM,iBAAiB,IAAI,GAA6B,OAAO,KAAI;iBACrE,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;IAMK,OACJ,MAAY;;AAWZ,cAAM,QAAQ,KAAK,cAAc,IAAI;AAErC,YAAI;AACF,gBAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,KAAK,IAAI;YACpD,SAAS,KAAK;WACf;AAED,iBAAO,EAAE,MAAM,MAAM,OAAO,KAAI;iBACzB,OAAO;AACd,cAAI,eAAe,KAAK,KAAK,iBAAiB,qBAAqB;AACjE,kBAAM,gBAAiB,MAAM;AAE7B,gBAAI,CAAC,KAAK,GAAG,EAAE,SAAS,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,MAAM,GAAG;AAC9C,qBAAO,EAAE,MAAM,OAAO,MAAK;;;AAI/B,gBAAM;;MAEV,CAAC;;;;;;;;;;IAUD,aACE,MACA,SAAuE;AAEvE,YAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,YAAM,eAAe,CAAA;AAErB,YAAM,sBAAqB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAChC,YAAY,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAC7D;AAEJ,UAAI,uBAAuB,IAAI;AAC7B,qBAAa,KAAK,kBAAkB;;AAGtC,YAAM,sBAAsB,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAc;AAC1D,YAAM,aAAa,sBAAsB,iBAAiB;AAC1D,YAAM,sBAAsB,KAAK,4BAA2B,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAa,CAAA,CAAE;AAEpF,UAAI,wBAAwB,IAAI;AAC9B,qBAAa,KAAK,mBAAmB;;AAGvC,UAAI,cAAc,aAAa,KAAK,GAAG;AACvC,UAAI,gBAAgB,IAAI;AACtB,sBAAc,IAAI,WAAW;;AAG/B,aAAO;QACL,MAAM,EAAE,WAAW,UAAU,GAAG,KAAK,GAAG,IAAI,UAAU,WAAW,KAAK,GAAG,WAAW,EAAE,EAAC;;IAE3F;;;;;;IAOM,OACJ,OAAe;;AAWf,YAAI;AACF,gBAAM,OAAO,MAAM,OACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,KAAK,QAAQ,IACnC,EAAE,UAAU,MAAK,GACjB,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsEK,KACJ,MACA,SACA,YAA4B;;AAW5B,YAAI;AACF,gBAAM,OAAI,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,sBAAsB,GAAK,OAAO,GAAA,EAAE,QAAQ,QAAQ,GAAE,CAAA;AACxE,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBAAgB,KAAK,QAAQ,IACxC,MACA,EAAE,SAAS,KAAK,QAAO,GACvB,UAAU;AAEZ,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;IAES,eAAe,UAA6B;AACpD,aAAO,KAAK,UAAU,QAAQ;IAChC;IAEA,SAAS,MAAY;AACnB,UAAI,OAAO,WAAW,aAAa;AACjC,eAAO,OAAO,KAAK,IAAI,EAAE,SAAS,QAAQ;;AAE5C,aAAO,KAAK,IAAI;IAClB;IAEQ,cAAc,MAAY;AAChC,aAAO,GAAG,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,EAAE,CAAC;IACrD;IAEQ,oBAAoB,MAAY;AACtC,aAAO,KAAK,QAAQ,YAAY,EAAE,EAAE,QAAQ,QAAQ,GAAG;IACzD;IAEQ,2BAA2B,WAA2B;AAC5D,YAAM,SAAS,CAAA;AACf,UAAI,UAAU,OAAO;AACnB,eAAO,KAAK,SAAS,UAAU,KAAK,EAAE;;AAGxC,UAAI,UAAU,QAAQ;AACpB,eAAO,KAAK,UAAU,UAAU,MAAM,EAAE;;AAG1C,UAAI,UAAU,QAAQ;AACpB,eAAO,KAAK,UAAU,UAAU,MAAM,EAAE;;AAG1C,UAAI,UAAU,QAAQ;AACpB,eAAO,KAAK,UAAU,UAAU,MAAM,EAAE;;AAG1C,UAAI,UAAU,SAAS;AACrB,eAAO,KAAK,WAAW,UAAU,OAAO,EAAE;;AAG5C,aAAO,OAAO,KAAK,GAAG;IACxB;;A;;;;;AE/yBF;;;ACAA;AACO,MAAME,WAAU;;;ADAhB,MAAM,kBAAkB,EAAE,iBAAiB,cAAcC,QAAO,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzE,MAAqB,mBAArB,MAAqC;WAAA;;;IAKnC,YACE,KACA,UAAqC,CAAA,GACrCC,QACA,MAA2B;AAE3B,YAAM,UAAU,IAAI,IAAI,GAAG;AAI3B,UAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,gBAAgB;AACxB,cAAM,iBAAiB,yBAAyB,KAAK,QAAQ,QAAQ;AACrE,YAAI,kBAAkB,CAAC,QAAQ,SAAS,SAAS,mBAAmB,GAAG;AACrE,kBAAQ,WAAW,QAAQ,SAAS,QAAQ,aAAa,mBAAmB;;;AAIhF,WAAK,MAAM,QAAQ;AACnB,WAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,eAAe,GAAK,OAAO;AAC/C,WAAK,QAAQC,cAAaD,MAAK;IACjC;;;;IAKM,cAAW;;AAUf,YAAI;AACF,gBAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,SAAS,KAAK,QAAO,CAAE;AAClF,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;IAOK,UACJ,IAAU;;AAWV,YAAI;AACF,gBAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,IAAI,EAAE,SAAS,KAAK,QAAO,CAAE;AACxF,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;;;;;;;;;IAiBK,aACJ,IACA,UAKI;MACF,QAAQ;OACT;;AAWD,YAAI;AACF,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,WACX;YACE;YACA,MAAM;YACN,MAAM,QAAQ;YACd,QAAQ,QAAQ;YAChB,iBAAiB,QAAQ;YACzB,oBAAoB,QAAQ;aAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;;;;;;;IAcK,aACJ,IACA,SAIC;;AAWD,YAAI;AACF,gBAAM,OAAO,MAAM,IACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,EAAE,IACxB;YACE;YACA,MAAM;YACN,QAAQ,QAAQ;YAChB,iBAAiB,QAAQ;YACzB,oBAAoB,QAAQ;aAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;IAOK,YACJ,IAAU;;AAWV,YAAI;AACF,gBAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,EAAE,UACxB,CAAA,GACA,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;;;;IAQK,aACJ,IAAU;;AAWV,YAAI;AACF,gBAAM,OAAO,MAAM,OACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,EAAE,IACxB,CAAA,GACA,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,iBAAO,EAAE,MAAM,OAAO,KAAI;iBACnB,OAAO;AACd,cAAI,eAAe,KAAK,GAAG;AACzB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM;;MAEV,CAAC;;;;;AHlQG,MAAO,gBAAP,cAA6B,iBAAgB;IARnD,OAQmD;;;IACjD,YACE,KACA,UAAqC,CAAA,GACrCE,QACA,MAA2B;AAE3B,YAAM,KAAK,SAASA,QAAO,IAAI;IACjC;;;;;;IAOA,KAAK,IAAU;AACb,aAAO,IAAI,eAAe,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK;IAClE;;;;AKtBF;;;ACHA;AAAO,MAAMC,WAAU;;;ADKvB,MAAI,SAAS;AAEb,MAAI,OAAO,SAAS,aAAa;AAC/B,aAAS;aACA,OAAO,aAAa,aAAa;AAC1C,aAAS;aACA,OAAO,cAAc,eAAe,UAAU,YAAY,eAAe;AAClF,aAAS;SACJ;AACL,aAAS;;AAGJ,MAAMC,mBAAkB,EAAE,iBAAiB,eAAe,MAAM,IAAIC,QAAO,GAAE;AAE7E,MAAM,yBAAyB;IACpC,SAASD;;AAGJ,MAAM,qBAAqB;IAChC,QAAQ;;AAGH,MAAM,uBAAkD;IAC7D,kBAAkB;IAClB,gBAAgB;IAChB,oBAAoB;IACpB,UAAU;;AAGL,MAAM,2BAAkD,CAAA;A;;;AEjC/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAME,gBAAe,wBAAC,gBAA8B;AACzD,QAAI;AACJ,QAAI,aAAa;AACf,eAAS;eACA,OAAO,UAAU,aAAa;AACvC,eAAS;WACJ;AACL,eAAS;;AAEX,WAAO,IAAI,SAA4B,OAAO,GAAG,IAAI;EACvD,GAV4B;AAYrB,MAAM,4BAA4B,6BAAK;AAC5C,QAAI,OAAO,YAAY,aAAa;AAClC,aAAOC;;AAGT,WAAO;EACT,GANyC;AAQlC,MAAM,gBAAgB,wBAC3B,aACA,gBACA,gBACS;AACT,UAAMC,SAAQF,cAAa,WAAW;AACtC,UAAM,qBAAqB,0BAAyB;AAEpD,WAAO,CAAO,OAAO,SAAQG,WAAA,QAAA,QAAA,QAAA,aAAA;;AAC3B,YAAM,eAAc,KAAC,MAAM,eAAc,OAAG,QAAA,OAAA,SAAA,KAAI;AAChD,UAAI,UAAU,IAAI,mBAAmB,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,OAAO;AAElD,UAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC1B,gBAAQ,IAAI,UAAU,WAAW;;AAGnC,UAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,gBAAQ,IAAI,iBAAiB,UAAU,WAAW,EAAE;;AAGtD,aAAOD,OAAM,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,IAAI,GAAA,EAAE,QAAO,CAAA,CAAA;IACxC,CAAC;EACH,GAtB6B;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtBvB,WAAU,OAAI;AAClB,WAAO,uCAAuC,QAAQ,SAAS,SAAU,GAAC;AACxE,UAAI,IAAK,KAAK,OAAM,IAAK,KAAM,GAC7B,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACjC,aAAO,EAAE,SAAS,EAAE;IACtB,CAAC;EACH;AANgB;AAQV,WAAU,oBAAoB,KAAW;AAC7C,WAAO,IAAI,SAAS,GAAG,IAAI,MAAM,MAAM;EACzC;AAFgB;AAIT,MAAM,YAAY,6BAAM,OAAO,WAAW,aAAxB;AAEnB,WAAU,qBAMd,SACA,UAAoC;;AAEpC,UAAM,EACJ,IAAI,WACJ,MAAM,aACN,UAAU,iBACV,QAAQ,cAAa,IACnB;AACJ,UAAM,EACJ,IAAIE,qBACJ,MAAMC,uBACN,UAAUC,2BACV,QAAQC,wBAAsB,IAC5B;AAEJ,UAAM,SAAsD;MAC1D,IAAE,OAAA,OAAA,OAAA,OAAA,CAAA,GACGH,mBAAkB,GAClB,SAAS;MAEd,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACCC,qBAAoB,GACpB,WAAW;MAEhB,UAAQ,OAAA,OAAA,OAAA,OAAA,CAAA,GACHC,yBAAwB,GACxB,eAAe;MAEpB,SAAS,CAAA;MACT,QAAM,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACDC,uBAAsB,GACtB,aAAa,GAAA,EAChB,SAAO,OAAA,OAAA,OAAA,OAAA,CAAA,IACD,KAAAA,4BAAsB,QAAtBA,4BAAsB,SAAA,SAAtBA,wBAAwB,aAAO,QAAA,OAAA,SAAA,KAAI,CAAA,CAAG,IACtC,KAAA,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,aAAO,QAAA,OAAA,SAAA,KAAI,CAAA,CAAG,EAAA,CAAA;MAGrC,aAAa,6BAAWC,WAAA,MAAA,QAAA,QAAA,aAAA;AAAC,eAAA;MAAE,CAAA,GAAd;;AAGf,QAAI,QAAQ,aAAa;AACvB,aAAO,cAAc,QAAQ;WACxB;AAEL,aAAQ,OAAe;;AAGzB,WAAO;EACT;AAvDgB;;;AAjBhB;;;ACAA;A;;;;;;;;ACAA;;;ACAA;AAAO,MAAMC,WAAU;;;ADGhB,MAAM,gCAAgC,KAAK;AAI3C,MAAM,8BAA8B;AAKpC,MAAM,mBAAmB,8BAA8B;AAEvD,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,WAAW;AACjB,MAAMC,mBAAkB,EAAE,iBAAiB,aAAaC,QAAO,GAAE;AACjE,MAAM,kBAAkB;IAC7B,aAAa;IACb,gBAAgB;;;AAGX,MAAM,0BAA0B;AAChC,MAAM,eAAe;IAC1B,cAAc;MACZ,WAAW,KAAK,MAAM,wBAAwB;MAC9C,MAAM;;;AAIH,MAAM,kBAAkB;AAExB,MAAM,WAAW,KAAK,KAAK;;;AEjClC;;;ACGA;AAAM,MAAO,YAAP,cAAyB,MAAK;IAApC,OAAoC;;;IAclC,YAAY,SAAiB,QAAiB,MAAa;AACzD,YAAM,OAAO;AAHL,WAAA,gBAAgB;AAIxB,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,OAAO;IACd;;AAGI,WAAU,YAAY,OAAc;AACxC,WAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,mBAAmB;EAC3E;AAFgB;AAIV,MAAO,eAAP,cAA4B,UAAS;IA1B3C,OA0B2C;;;IAGzC,YAAY,SAAiB,QAAgB,MAAwB;AACnE,YAAM,SAAS,QAAQ,IAAI;AAC3B,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,OAAO;IACd;;AAGI,WAAU,eAAe,OAAc;AAC3C,WAAO,YAAY,KAAK,KAAK,MAAM,SAAS;EAC9C;AAFgB;AAIV,MAAO,mBAAP,cAAgC,UAAS;IAzC/C,OAyC+C;;;IAG7C,YAAY,SAAiB,eAAsB;AACjD,YAAM,OAAO;AACb,WAAK,OAAO;AACZ,WAAK,gBAAgB;IACvB;;AAGI,MAAO,kBAAP,cAA+B,UAAS;IAnD9C,OAmD8C;;;IAI5C,YAAY,SAAiB,MAAc,QAAgB,MAAwB;AACjF,YAAM,SAAS,QAAQ,IAAI;AAC3B,WAAK,OAAO;AACZ,WAAK,SAAS;IAChB;;AAGI,MAAO,0BAAP,cAAuC,gBAAe;IA9D5D,OA8D4D;;;IAC1D,cAAA;AACE,YAAM,yBAAyB,2BAA2B,KAAK,MAAS;IAC1E;;AAGI,WAAU,0BAA0B,OAAU;AAClD,WAAO,YAAY,KAAK,KAAK,MAAM,SAAS;EAC9C;AAFgB;AAIV,MAAO,gCAAP,cAA6C,gBAAe;IAxElE,OAwEkE;;;IAChE,cAAA;AACE,YAAM,gCAAgC,iCAAiC,KAAK,MAAS;IACvF;;AAGI,MAAO,8BAAP,cAA2C,gBAAe;IA9EhE,OA8EgE;;;IAC9D,YAAY,SAAe;AACzB,YAAM,SAAS,+BAA+B,KAAK,MAAS;IAC9D;;AAGI,MAAO,iCAAP,cAA8C,gBAAe;IApFnE,OAoFmE;;;IAEjE,YAAY,SAAiB,UAAkD,MAAI;AACjF,YAAM,SAAS,kCAAkC,KAAK,MAAS;AAFjE,WAAA,UAAkD;AAGhD,WAAK,UAAU;IACjB;IAEA,SAAM;AACJ,aAAO;QACL,MAAM,KAAK;QACX,SAAS,KAAK;QACd,QAAQ,KAAK;QACb,SAAS,KAAK;;IAElB;;AAGI,WAAU,iCACd,OAAU;AAEV,WAAO,YAAY,KAAK,KAAK,MAAM,SAAS;EAC9C;AAJgB;AAMV,MAAO,iCAAP,cAA8C,gBAAe;IA3GnE,OA2GmE;;;IAGjE,YAAY,SAAiB,UAAkD,MAAI;AACjF,YAAM,SAAS,kCAAkC,KAAK,MAAS;AAHjE,WAAA,UAAkD;AAIhD,WAAK,UAAU;IACjB;IAEA,SAAM;AACJ,aAAO;QACL,MAAM,KAAK;QACX,SAAS,KAAK;QACd,QAAQ,KAAK;QACb,SAAS,KAAK;;IAElB;;AAGI,MAAO,0BAAP,cAAuC,gBAAe;IA7H5D,OA6H4D;;;IAC1D,YAAY,SAAiB,QAAc;AACzC,YAAM,SAAS,2BAA2B,QAAQ,MAAS;IAC7D;;AAGI,WAAU,0BAA0B,OAAc;AACtD,WAAO,YAAY,KAAK,KAAK,MAAM,SAAS;EAC9C;AAFgB;AASV,MAAO,wBAAP,cAAqC,gBAAe;IA5I1D,OA4I0D;;;IAMxD,YAAY,SAAiB,QAAgB,SAAiB;AAC5D,YAAM,SAAS,yBAAyB,QAAQ,eAAe;AAE/D,WAAK,UAAU;IACjB;;AAGI,WAAU,wBAAwB,OAAc;AACpD,WAAO,YAAY,KAAK,KAAK,MAAM,SAAS;EAC9C;AAFgB;AAIV,MAAO,sBAAP,cAAmC,gBAAe;IA7JxD,OA6JwD;;;IACtD,YAAY,SAAe;AACzB,YAAM,SAAS,uBAAuB,KAAK,aAAa;IAC1D;;;;ACnKF;AAUA,MAAM,eAAe,mEAAmE,MAAM,EAAE;AAMhG,MAAM,mBAAmB,UAAW,MAAM,EAAE;AAM5C,MAAM,kBAAkB,MAAK;AAC3B,UAAM,UAAoB,IAAI,MAAM,GAAG;AAEvC,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1C,cAAQ,CAAC,IAAI;;AAGf,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACnD,cAAQ,iBAAiB,CAAC,EAAE,WAAW,CAAC,CAAC,IAAI;;AAG/C,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC/C,cAAQ,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC,IAAI;;AAG3C,WAAO;EACT,GAAE;AASI,WAAU,gBACd,MACA,OACA,MAA4B;AAE5B,QAAI,SAAS,MAAM;AACjB,YAAM,QAAS,MAAM,SAAS,IAAK;AACnC,YAAM,cAAc;AAEpB,aAAO,MAAM,cAAc,GAAG;AAC5B,cAAM,MAAO,MAAM,SAAU,MAAM,aAAa,IAAM;AACtD,aAAK,aAAa,GAAG,CAAC;AACtB,cAAM,cAAc;;eAEb,MAAM,aAAa,GAAG;AAC/B,YAAM,QAAQ,MAAM,SAAU,IAAI,MAAM;AACxC,YAAM,aAAa;AAEnB,aAAO,MAAM,cAAc,GAAG;AAC5B,cAAM,MAAO,MAAM,SAAU,MAAM,aAAa,IAAM;AACtD,aAAK,aAAa,GAAG,CAAC;AACtB,cAAM,cAAc;;;EAG1B;AAxBgB;AAiCV,WAAU,kBACd,UACA,OACA,MAA4B;AAE5B,UAAM,OAAO,eAAe,QAAQ;AAEpC,QAAI,OAAO,IAAI;AAEb,YAAM,QAAS,MAAM,SAAS,IAAK;AACnC,YAAM,cAAc;AAEpB,aAAO,MAAM,cAAc,GAAG;AAC5B,aAAM,MAAM,SAAU,MAAM,aAAa,IAAM,GAAI;AACnD,cAAM,cAAc;;eAEb,SAAS,IAAI;AAEtB;WACK;AACL,YAAM,IAAI,MAAM,iCAAiC,OAAO,aAAa,QAAQ,CAAC,GAAG;;EAErF;AAtBgB;AA+BV,WAAU,kBAAkB,KAAW;AAC3C,UAAM,SAAmB,CAAA;AAEzB,UAAM,UAAU,wBAAC,SAAgB;AAC/B,aAAO,KAAK,IAAI;IAClB,GAFgB;AAIhB,UAAM,QAAQ,EAAE,OAAO,GAAG,YAAY,EAAC;AAEvC,iBAAa,KAAK,CAAC,SAAgB;AACjC,sBAAgB,MAAM,OAAO,OAAO;IACtC,CAAC;AAED,oBAAgB,MAAM,OAAO,OAAO;AAEpC,WAAO,OAAO,KAAK,EAAE;EACvB;AAhBgB;AAwBV,WAAU,oBAAoB,KAAW;AAC7C,UAAM,OAAiB,CAAA;AAEvB,UAAM,WAAW,wBAAC,cAAqB;AACrC,WAAK,KAAK,OAAO,cAAc,SAAS,CAAC;IAC3C,GAFiB;AAIjB,UAAM,YAAY;MAChB,SAAS;MACT,WAAW;;AAGb,UAAM,WAAW,EAAE,OAAO,GAAG,YAAY,EAAC;AAE1C,UAAM,WAAW,wBAAC,SAAgB;AAChC,qBAAe,MAAM,WAAW,QAAQ;IAC1C,GAFiB;AAIjB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,wBAAkB,IAAI,WAAW,CAAC,GAAG,UAAU,QAAQ;;AAGzD,WAAO,KAAK,KAAK,EAAE;EACrB;AAvBgB;AA+BV,WAAU,gBAAgB,WAAmB,MAA4B;AAC7E,QAAI,aAAa,KAAM;AACrB,WAAK,SAAS;AACd;eACS,aAAa,MAAO;AAC7B,WAAK,MAAQ,aAAa,CAAE;AAC5B,WAAK,MAAQ,YAAY,EAAK;AAC9B;eACS,aAAa,OAAQ;AAC9B,WAAK,MAAQ,aAAa,EAAG;AAC7B,WAAK,MAAS,aAAa,IAAK,EAAK;AACrC,WAAK,MAAQ,YAAY,EAAK;AAC9B;eACS,aAAa,SAAU;AAChC,WAAK,MAAQ,aAAa,EAAG;AAC7B,WAAK,MAAS,aAAa,KAAM,EAAK;AACtC,WAAK,MAAS,aAAa,IAAK,EAAK;AACrC,WAAK,MAAQ,YAAY,EAAK;AAC9B;;AAGF,UAAM,IAAI,MAAM,mCAAmC,UAAU,SAAS,EAAE,CAAC,EAAE;EAC7E;AAtBgB;AA8BV,WAAU,aAAa,KAAa,MAA4B;AACpE,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,UAAI,YAAY,IAAI,WAAW,CAAC;AAEhC,UAAI,YAAY,SAAU,aAAa,OAAQ;AAI7C,cAAM,iBAAkB,YAAY,SAAU,OAAS;AACvD,cAAM,eAAgB,IAAI,WAAW,IAAI,CAAC,IAAI,QAAU;AACxD,qBAAa,eAAe,iBAAiB;AAC7C,aAAK;;AAGP,sBAAgB,WAAW,IAAI;;EAEnC;AAhBgB;AA0BV,WAAU,eACd,MACA,OACA,MAAiC;AAEjC,QAAI,MAAM,YAAY,GAAG;AACvB,UAAI,QAAQ,KAAM;AAChB,aAAK,IAAI;AACT;;AAIF,eAAS,aAAa,GAAG,aAAa,GAAG,cAAc,GAAG;AACxD,aAAM,QAAS,IAAI,aAAe,OAAO,GAAG;AAC1C,gBAAM,UAAU;AAChB;;;AAIJ,UAAI,MAAM,YAAY,GAAG;AACvB,cAAM,YAAY,OAAO;iBAChB,MAAM,YAAY,GAAG;AAC9B,cAAM,YAAY,OAAO;iBAChB,MAAM,YAAY,GAAG;AAC9B,cAAM,YAAY,OAAO;aACpB;AACL,cAAM,IAAI,MAAM,wBAAwB;;AAG1C,YAAM,WAAW;eACR,MAAM,UAAU,GAAG;AAC5B,UAAI,QAAQ,KAAM;AAChB,cAAM,IAAI,MAAM,wBAAwB;;AAG1C,YAAM,YAAa,MAAM,aAAa,IAAM,OAAO;AACnD,YAAM,WAAW;AAEjB,UAAI,MAAM,YAAY,GAAG;AACvB,aAAK,MAAM,SAAS;;;EAG1B;AA1CgB;AAgDV,WAAU,sBAAsB,KAAW;AAC/C,UAAM,SAAmB,CAAA;AACzB,UAAM,QAAQ,EAAE,OAAO,GAAG,YAAY,EAAC;AAEvC,UAAM,SAAS,wBAAC,SAAgB;AAC9B,aAAO,KAAK,IAAI;IAClB,GAFe;AAIf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,wBAAkB,IAAI,WAAW,CAAC,GAAG,OAAO,MAAM;;AAGpD,WAAO,IAAI,WAAW,MAAM;EAC9B;AAbgB;AAeV,WAAU,mBAAmB,KAAW;AAC5C,UAAM,SAAmB,CAAA;AACzB,iBAAa,KAAK,CAAC,SAAiB,OAAO,KAAK,IAAI,CAAC;AACrD,WAAO,IAAI,WAAW,MAAM;EAC9B;AAJgB;AAMV,WAAU,iBAAiB,OAAiB;AAChD,UAAM,SAAmB,CAAA;AACzB,UAAM,QAAQ,EAAE,OAAO,GAAG,YAAY,EAAC;AAEvC,UAAM,SAAS,wBAAC,SAAgB;AAC9B,aAAO,KAAK,IAAI;IAClB,GAFe;AAIf,UAAM,QAAQ,CAAC,SAAS,gBAAgB,MAAM,OAAO,MAAM,CAAC;AAG5D,oBAAgB,MAAM,OAAO,MAAM;AAEnC,WAAO,OAAO,KAAK,EAAE;EACvB;AAdgB;;;AF9RV,WAAU,UAAU,WAAiB;AACzC,UAAM,UAAU,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAC5C,WAAO,UAAU;EACnB;AAHgB;AAKV,WAAUC,QAAI;AAClB,WAAO,uCAAuC,QAAQ,SAAS,SAAU,GAAC;AACxE,YAAM,IAAK,KAAK,OAAM,IAAK,KAAM,GAC/B,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACjC,aAAO,EAAE,SAAS,EAAE;IACtB,CAAC;EACH;AANgB,SAAAA,OAAA;AAQT,MAAMC,aAAY,6BAAM,OAAO,WAAW,eAAe,OAAO,aAAa,aAA3D;AAEzB,MAAM,yBAAyB;IAC7B,QAAQ;IACR,UAAU;;AAML,MAAM,uBAAuB,6BAAK;AACvC,QAAI,CAACA,WAAS,GAAI;AAChB,aAAO;;AAGT,QAAI;AACF,UAAI,OAAO,WAAW,iBAAiB,UAAU;AAC/C,eAAO;;aAEF,GAAG;AAEV,aAAO;;AAGT,QAAI,uBAAuB,QAAQ;AACjC,aAAO,uBAAuB;;AAGhC,UAAM,YAAY,QAAQ,KAAK,OAAM,CAAE,GAAG,KAAK,OAAM,CAAE;AAEvD,QAAI;AACF,iBAAW,aAAa,QAAQ,WAAW,SAAS;AACpD,iBAAW,aAAa,WAAW,SAAS;AAE5C,6BAAuB,SAAS;AAChC,6BAAuB,WAAW;aAC3B,GAAG;AAIV,6BAAuB,SAAS;AAChC,6BAAuB,WAAW;;AAGpC,WAAO,uBAAuB;EAChC,GAnCoC;AAwC9B,WAAU,uBAAuB,MAAY;AACjD,UAAM,SAA0C,CAAA;AAEhD,UAAM,MAAM,IAAI,IAAI,IAAI;AAExB,QAAI,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK;AACnC,UAAI;AACF,cAAM,mBAAmB,IAAI,gBAAgB,IAAI,KAAK,UAAU,CAAC,CAAC;AAClE,yBAAiB,QAAQ,CAAC,OAAO,QAAO;AACtC,iBAAO,GAAG,IAAI;QAChB,CAAC;eACM,GAAQ;;;AAMnB,QAAI,aAAa,QAAQ,CAAC,OAAO,QAAO;AACtC,aAAO,GAAG,IAAI;IAChB,CAAC;AAED,WAAO;EACT;AAtBgB;AA0BT,MAAMC,gBAAe,wBAAC,gBAA8B;AACzD,QAAI;AACJ,QAAI,aAAa;AACf,eAAS;eACA,OAAO,UAAU,aAAa;AACvC,eAAS,2BAAI,SACX,gEAAsC,KAAK,CAAC,EAAE,SAASC,OAAK,MAAOA,OAAM,GAAG,IAAI,CAAC,GAD1E;WAEJ;AACL,eAAS;;AAEX,WAAO,IAAI,SAAS,OAAO,GAAG,IAAI;EACpC,GAX4B;AAarB,MAAM,yBAAyB,wBAAC,kBAAqD;AAC1F,WACE,OAAO,kBAAkB,YACzB,kBAAkB,QAClB,YAAY,iBACZ,QAAQ,iBACR,UAAU,iBACV,OAAQ,cAAsB,SAAS;EAE3C,GATsC;AAY/B,MAAM,eAAe,8BAC1B,SACA,KACA,SACiB;AACjB,UAAM,QAAQ,QAAQ,KAAK,KAAK,UAAU,IAAI,CAAC;EACjD,GAN4B;AAQrB,MAAM,eAAe,8BAAO,SAA2B,QAAiC;AAC7F,UAAM,QAAQ,MAAM,QAAQ,QAAQ,GAAG;AAEvC,QAAI,CAAC,OAAO;AACV,aAAO;;AAGT,QAAI;AACF,aAAO,KAAK,MAAM,KAAK;aACvB,IAAM;AACN,aAAO;;EAEX,GAZ4B;AAcrB,MAAM,kBAAkB,8BAAO,SAA2B,QAA8B;AAC7F,UAAM,QAAQ,WAAW,GAAG;EAC9B,GAF+B;AASzB,MAAO,WAAP,MAAO,UAAQ;IAtJrB,OAsJqB;;;IASnB,cAAA;AAEE;AAAE,WAAa,UAAU,IAAI,UAAS,mBAAmB,CAAC,KAAK,QAAO;AAEpE;AAAE,aAAa,UAAU;AAEvB,aAAa,SAAS;MAC1B,CAAC;IACH;;AAhBc,WAAA,qBAAyC;AAmBnD,WAAU,UAAU,OAAa;AASrC,UAAM,QAAQ,MAAM,MAAM,GAAG;AAE7B,QAAI,MAAM,WAAW,GAAG;AACtB,YAAM,IAAI,oBAAoB,uBAAuB;;AAIvD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,CAAC,gBAAgB,KAAK,MAAM,CAAC,CAAW,GAAG;AAC7C,cAAM,IAAI,oBAAoB,6BAA6B;;;AAG/D,UAAM,OAAO;;MAEX,QAAQ,KAAK,MAAM,oBAAoB,MAAM,CAAC,CAAC,CAAC;MAChD,SAAS,KAAK,MAAM,oBAAoB,MAAM,CAAC,CAAC,CAAC;MACjD,WAAW,sBAAsB,MAAM,CAAC,CAAC;MACzC,KAAK;QACH,QAAQ,MAAM,CAAC;QACf,SAAS,MAAM,CAAC;;;AAGpB,WAAO;EACT;AAhCgB;AAqChB,iBAAsB,MAAM,MAAY;AACtC,WAAO,MAAM,IAAI,QAAQ,CAAC,WAAU;AAClC,iBAAW,MAAM,OAAO,IAAI,GAAG,IAAI;IACrC,CAAC;EACH;AAJsB;AAWhB,WAAU,UACd,IACA,aAAwE;AAExE,UAAM,UAAU,IAAI,QAAW,CAAC,QAAQ,WAAU;AAEhD;AAAC,OAAC,YAAW;AACX,iBAAS,UAAU,GAAG,UAAU,UAAU,WAAW;AACnD,cAAI;AACF,kBAAM,SAAS,MAAM,GAAG,OAAO;AAE/B,gBAAI,CAAC,YAAY,SAAS,MAAM,MAAM,GAAG;AACvC,qBAAO,MAAM;AACb;;mBAEK,GAAQ;AACf,gBAAI,CAAC,YAAY,SAAS,CAAC,GAAG;AAC5B,qBAAO,CAAC;AACR;;;;MAIR,GAAE;IACJ,CAAC;AAED,WAAO;EACT;AA1BgB;AA4BhB,WAAS,QAAQ,KAAW;AAC1B,YAAQ,MAAM,IAAI,SAAS,EAAE,GAAG,OAAO,EAAE;EAC3C;AAFS;AAKH,WAAU,uBAAoB;AAClC,UAAM,iBAAiB;AACvB,UAAM,QAAQ,IAAI,YAAY,cAAc;AAC5C,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM,UAAU;AAChB,YAAM,aAAa,QAAQ;AAC3B,UAAI,WAAW;AACf,eAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,oBAAY,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAU,CAAC;;AAEnE,aAAO;;AAET,WAAO,gBAAgB,KAAK;AAC5B,WAAO,MAAM,KAAK,OAAO,OAAO,EAAE,KAAK,EAAE;EAC3C;AAdgB;AAgBhB,iBAAe,OAAO,cAAoB;AACxC,UAAM,UAAU,IAAI,YAAW;AAC/B,UAAM,cAAc,QAAQ,OAAO,YAAY;AAC/C,UAAM,OAAO,MAAM,OAAO,OAAO,OAAO,WAAW,WAAW;AAC9D,UAAM,QAAQ,IAAI,WAAW,IAAI;AAEjC,WAAO,MAAM,KAAK,KAAK,EACpB,IAAI,CAAC,MAAM,OAAO,aAAa,CAAC,CAAC,EACjC,KAAK,EAAE;EACZ;AATe;AAWf,iBAAsB,sBAAsB,UAAgB;AAC1D,UAAM,mBACJ,OAAO,WAAW,eAClB,OAAO,OAAO,WAAW,eACzB,OAAO,gBAAgB;AAEzB,QAAI,CAAC,kBAAkB;AACrB,cAAQ,KACN,oGAAoG;AAEtG,aAAO;;AAET,UAAM,SAAS,MAAM,OAAO,QAAQ;AACpC,WAAO,KAAK,MAAM,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;EAC/E;AAdsB;AAgBtB,iBAAsB,0BACpB,SACA,YACA,qBAAqB,OAAK;AAE1B,UAAM,eAAe,qBAAoB;AACzC,QAAI,qBAAqB;AACzB,QAAI,oBAAoB;AACtB,4BAAsB;;AAExB,UAAM,aAAa,SAAS,GAAG,UAAU,kBAAkB,kBAAkB;AAC7E,UAAM,gBAAgB,MAAM,sBAAsB,YAAY;AAC9D,UAAM,sBAAsB,iBAAiB,gBAAgB,UAAU;AACvE,WAAO,CAAC,eAAe,mBAAmB;EAC5C;AAdsB;AAiBtB,MAAM,oBAAoB;AAEpB,WAAU,wBAAwB,UAAkB;AACxD,UAAM,aAAa,SAAS,QAAQ,IAAI,uBAAuB;AAE/D,QAAI,CAAC,YAAY;AACf,aAAO;;AAGT,QAAI,CAAC,WAAW,MAAM,iBAAiB,GAAG;AACxC,aAAO;;AAGT,QAAI;AACF,YAAM,OAAO,oBAAI,KAAK,GAAG,UAAU,cAAc;AACjD,aAAO;aACA,GAAQ;AACf,aAAO;;EAEX;AAjBgB;AAmBV,WAAU,YAAY,KAAW;AACrC,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,MAAM,mBAAmB;;AAErC,UAAM,UAAU,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAC5C,QAAI,OAAO,SAAS;AAClB,YAAM,IAAI,MAAM,iBAAiB;;EAErC;AARgB;AAUV,WAAU,aACd,KAAgC;AAEhC,YAAQ,KAAK;MACX,KAAK;AACH,eAAO;UACL,MAAM;UACN,MAAM,EAAE,MAAM,UAAS;;MAE3B,KAAK;AACH,eAAO;UACL,MAAM;UACN,YAAY;UACZ,MAAM,EAAE,MAAM,UAAS;;MAE3B;AACE,cAAM,IAAI,MAAM,mBAAmB;;EAEzC;AAlBgB;AAoBhB,MAAM,aAAa;AAEb,WAAU,aAAa,KAAW;AACtC,QAAI,CAAC,WAAW,KAAK,GAAG,GAAG;AACzB,YAAM,IAAI,MAAM,6DAA6D;;EAEjF;AAJgB;AAMV,WAAU,wBAAqB;AACnC,UAAM,cAAc,CAAA;AAEpB,WAAO,IAAI,MAAM,aAAa;MAC5B,KAAK,wBAAC,QAAa,SAAgB;AACjC,YAAI,SAAS,6BAA6B;AACxC,iBAAO;;AAIT,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,QAAS,KAAgB,SAAQ;AACvC,cACE,UAAU,gCACV,UAAU,gCACV,UAAU,+BACV;AAEA,mBAAO;;;AAGX,cAAM,IAAI,MACR,kIAAkI,IAAI,kFAAkF;MAE5N,GApBK;MAqBL,KAAK,wBAAC,SAAc,SAAgB;AAClC,cAAM,IAAI,MACR,gIAAgI,IAAI,oHAAoH;MAE5P,GAJK;MAKL,gBAAgB,wBAAC,SAAc,SAAgB;AAC7C,cAAM,IAAI,MACR,iIAAiI,IAAI,oHAAoH;MAE7P,GAJgB;KAKjB;EACH;AApCgB;AA0CV,WAAU,UAAa,KAAM;AACjC,WAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;EACvC;AAFgB;;;;;;;;;;;;;;AG1XhB,MAAMC,oBAAmB,wBAAC,QACxB,IAAI,OAAO,IAAI,WAAW,IAAI,qBAAqB,IAAI,SAAS,KAAK,UAAU,GAAG,GAD3D;AAGzB,MAAM,sBAAsB,CAAC,KAAK,KAAK,GAAG;AAE1C,iBAAsBC,aAAY,OAAc;;AAC9C,QAAI,CAAC,uBAAuB,KAAK,GAAG;AAClC,YAAM,IAAI,wBAAwBD,kBAAiB,KAAK,GAAG,CAAC;;AAG9D,QAAI,oBAAoB,SAAS,MAAM,MAAM,GAAG;AAE9C,YAAM,IAAI,wBAAwBA,kBAAiB,KAAK,GAAG,MAAM,MAAM;;AAGzE,QAAI;AACJ,QAAI;AACF,aAAO,MAAM,MAAM,KAAI;aAChB,GAAQ;AACf,YAAM,IAAI,iBAAiBA,kBAAiB,CAAC,GAAG,CAAC;;AAGnD,QAAI,YAAgC;AAEpC,UAAM,qBAAqB,wBAAwB,KAAK;AACxD,QACE,sBACA,mBAAmB,QAAO,KAAM,aAAa,YAAY,EAAE,aAC3D,OAAO,SAAS,YAChB,QACA,OAAO,KAAK,SAAS,UACrB;AACA,kBAAY,KAAK;eACR,OAAO,SAAS,YAAY,QAAQ,OAAO,KAAK,eAAe,UAAU;AAClF,kBAAY,KAAK;;AAGnB,QAAI,CAAC,WAAW;AAEd,UACE,OAAO,SAAS,YAChB,QACA,OAAO,KAAK,kBAAkB,YAC9B,KAAK,iBACL,MAAM,QAAQ,KAAK,cAAc,OAAO,KACxC,KAAK,cAAc,QAAQ,UAC3B,KAAK,cAAc,QAAQ,OAAO,CAAC,GAAY,MAAW,KAAK,OAAO,MAAM,UAAU,IAAI,GAC1F;AACA,cAAM,IAAI,sBACRA,kBAAiB,IAAI,GACrB,MAAM,QACN,KAAK,cAAc,OAAO;;eAGrB,cAAc,iBAAiB;AACxC,YAAM,IAAI,sBACRA,kBAAiB,IAAI,GACrB,MAAM,UACN,KAAA,KAAK,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW,CAAA,CAAE;eAE1B,cAAc,qBAAqB;AAI5C,YAAM,IAAI,wBAAuB;;AAGnC,UAAM,IAAI,aAAaA,kBAAiB,IAAI,GAAG,MAAM,UAAU,KAAK,SAAS;EAC/E;AA/DsB,SAAAC,cAAA;AAiEtB,MAAMC,qBAAoB,wBACxB,QACA,SACA,YACA,SACE;AACF,UAAM,SAA+B,EAAE,QAAQ,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAW,CAAA,EAAE;AAE9E,QAAI,WAAW,OAAO;AACpB,aAAO;;AAGT,WAAO,UAAO,OAAA,OAAA,EAAK,gBAAgB,iCAAgC,GAAK,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AACxF,WAAO,OAAO,KAAK,UAAU,IAAI;AACjC,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,MAAM,GAAK,UAAU;EACnC,GAf0B;AA4B1B,iBAAsB,SACpB,SACA,QACA,KACA,SAA8B;;AAE9B,UAAM,UAAO,OAAA,OAAA,CAAA,GACR,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AAGrB,QAAI,CAAC,QAAQ,uBAAuB,GAAG;AACrC,cAAQ,uBAAuB,IAAI,aAAa,YAAY,EAAE;;AAGhE,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,KAAK;AAChB,cAAQ,eAAe,IAAI,UAAU,QAAQ,GAAG;;AAGlD,UAAM,MAAK,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAK,QAAA,OAAA,SAAA,KAAI,CAAA;AAC7B,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;AACvB,SAAG,aAAa,IAAI,QAAQ;;AAG9B,UAAM,cAAc,OAAO,KAAK,EAAE,EAAE,SAAS,MAAM,IAAI,gBAAgB,EAAE,EAAE,SAAQ,IAAK;AACxF,UAAM,OAAO,MAAMC,gBACjB,SACA,QACA,MAAM,aACN;MACE;MACA,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;OAE1B,CAAA,GACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,YAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,SAAQ,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,MAAM,IAAI,IAAI,EAAE,MAAI,OAAA,OAAA,CAAA,GAAO,IAAI,GAAI,OAAO,KAAI;EACjF;AApCsB;AAsCtB,iBAAeA,gBACb,SACA,QACA,KACA,SACA,YACA,MAAa;AAEb,UAAM,gBAAgBD,mBAAkB,QAAQ,SAAS,YAAY,IAAI;AAEzE,QAAI;AAEJ,QAAI;AACF,eAAS,MAAM,QAAQ,KAAG,OAAA,OAAA,CAAA,GACrB,aAAa,CAAA;aAEX,GAAG;AACV,cAAQ,MAAM,CAAC;AAGf,YAAM,IAAI,wBAAwBF,kBAAiB,CAAC,GAAG,CAAC;;AAG1D,QAAI,CAAC,OAAO,IAAI;AACd,YAAMC,aAAY,MAAM;;AAG1B,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAe;AAC1B,aAAO;;AAGT,QAAI;AACF,aAAO,MAAM,OAAO,KAAI;aACjB,GAAQ;AACf,YAAMA,aAAY,CAAC;;EAEvB;AApCe,SAAAE,iBAAA;AAsCT,WAAU,iBAAiB,MAAS;;AACxC,QAAI,UAAU;AACd,QAAI,WAAW,IAAI,GAAG;AACpB,gBAAO,OAAA,OAAA,CAAA,GAAQ,IAAI;AAEnB,UAAI,CAAC,KAAK,YAAY;AACpB,gBAAQ,aAAa,UAAU,KAAK,UAAU;;;AAIlD,UAAM,QAAa,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,KAAK;AACjC,WAAO,EAAE,MAAM,EAAE,SAAS,KAAI,GAAI,OAAO,KAAI;EAC/C;AAZgB;AAcV,WAAU,yBAAyB,MAAS;AAChD,UAAM,WAAW,iBAAiB,IAAI;AAEtC,QACE,CAAC,SAAS,SACV,KAAK,iBACL,OAAO,KAAK,kBAAkB,YAC9B,MAAM,QAAQ,KAAK,cAAc,OAAO,KACxC,KAAK,cAAc,QAAQ,UAC3B,KAAK,cAAc,WACnB,OAAO,KAAK,cAAc,YAAY,YACtC,KAAK,cAAc,QAAQ,OAAO,CAAC,GAAY,MAAW,KAAK,OAAO,MAAM,UAAU,IAAI,GAC1F;AACA,eAAS,KAAK,gBAAgB,KAAK;;AAGrC,WAAO;EACT;AAjBgB;AAmBV,WAAU,cAAc,MAAS;;AACrC,UAAM,QAAa,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,KAAK;AACjC,WAAO,EAAE,MAAM,EAAE,KAAI,GAAI,OAAO,KAAI;EACtC;AAHgB;AAKV,WAAU,aAAa,MAAS;AACpC,WAAO,EAAE,MAAM,OAAO,KAAI;EAC5B;AAFgB;AAIV,WAAU,sBAAsB,MAAS;AAC7C,UAAM,EAAE,aAAa,WAAW,cAAc,aAAa,kBAAiB,IAAc,MAAT,OAAI,OAAK,MAApF,CAAA,eAAA,aAAA,gBAAA,eAAA,mBAAA,CAAiF;AAEvF,UAAM,aAAqC;MACzC;MACA;MACA;MACA;MACA;;AAGF,UAAM,OAAI,OAAA,OAAA,CAAA,GAAc,IAAI;AAC5B,WAAO;MACL,MAAM;QACJ;QACA;;MAEF,OAAO;;EAEX;AAnBgB;AAqBV,WAAU,uBAAuB,MAAS;AAC9C,WAAO;EACT;AAFgB;AAShB,WAAS,WAAW,MAAS;AAC3B,WAAO,KAAK,gBAAgB,KAAK,iBAAiB,KAAK;EACzD;AAFS;;;ACu/BT;AAAO,MAAM,kBAAkB,CAAC,UAAU,SAAS,QAAQ;;;;;;;;;;;;;;ANrvC3D,MAAqB,iBAArB,MAAmC;WAAA;;;IAUjC,YAAY,EACV,MAAM,IACN,UAAU,CAAA,GACV,OAAAC,OAAK,GAON;AACC,WAAK,MAAM;AACX,WAAK,UAAU;AACf,WAAK,QAAQC,cAAaD,MAAK;AAC/B,WAAK,MAAM;QACT,aAAa,KAAK,aAAa,KAAK,IAAI;QACxC,cAAc,KAAK,cAAc,KAAK,IAAI;;IAE9C;;;;;;IAOA,MAAM,QACJ,KACA,QAAsB,gBAAgB,CAAC,GAAC;AAExC,UAAI,gBAAgB,QAAQ,KAAK,IAAI,GAAG;AACtC,cAAM,IAAI,MACR,qDAAqD,gBAAgB,KAAK,IAAI,CAAC,EAAE;;AAIrF,UAAI;AACF,cAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,iBAAiB,KAAK,IAAI;UACtE,SAAS,KAAK;UACd;UACA,eAAe;SAChB;AACD,eAAO,EAAE,MAAM,MAAM,OAAO,KAAI;eACzB,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV;;;;;;IAOA,MAAM,kBACJ,OACA,UAMI,CAAA,GAAE;AAEN,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;UAC9D,MAAM,EAAE,OAAO,MAAM,QAAQ,KAAI;UACjC,SAAS,KAAK;UACd,YAAY,QAAQ;UACpB,OAAO;SACR;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;;;;;;;;IASA,MAAM,aAAa,QAA0B;AAC3C,UAAI;AACF,cAAM,EAAE,QAAO,IAAc,QAAT,OAAIE,QAAK,QAAvB,CAAA,SAAA,CAAoB;AAC1B,cAAM,OAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAa,IAAI,GAAK,OAAO;AACvC,YAAI,cAAc,MAAM;AAEtB,eAAK,YAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;AACvB,iBAAO,KAAK,UAAU;;AAExB,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,wBAAwB;UAC3E;UACA,SAAS,KAAK;UACd,OAAO;UACP,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;SACtB;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;YACL,MAAM;cACJ,YAAY;cACZ,MAAM;;YAER;;;AAGJ,cAAM;;IAEV;;;;;;IAOA,MAAM,WAAW,YAA+B;AAC9C,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,gBAAgB;UACnE,MAAM;UACN,SAAS,KAAK;UACd,OAAO;SACR;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;;;;;;;IAQA,MAAM,UACJ,QAAmB;;AAKnB,UAAI;AACF,cAAM,aAAyB,EAAE,UAAU,MAAM,UAAU,GAAG,OAAO,EAAC;AACtE,cAAM,WAAW,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB;UAC5E,SAAS,KAAK;UACd,eAAe;UACf,OAAO;YACL,OAAM,MAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,OAAE,QAAA,OAAA,SAAA,KAAI;YAClC,WAAU,MAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,OAAE,QAAA,OAAA,SAAA,KAAI;;UAE3C,OAAO;SACR;AACD,YAAI,SAAS;AAAO,gBAAM,SAAS;AAEnC,cAAM,QAAQ,MAAM,SAAS,KAAI;AACjC,cAAM,SAAQ,KAAA,SAAS,QAAQ,IAAI,eAAe,OAAC,QAAA,OAAA,SAAA,KAAI;AACvD,cAAM,SAAQ,MAAA,KAAA,SAAS,QAAQ,IAAI,MAAM,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,GAAG,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AAC1D,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,QAAQ,CAAC,SAAgB;AAC7B,kBAAM,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC;AACtE,kBAAM,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AACvD,uBAAW,GAAG,GAAG,MAAM,IAAI;UAC7B,CAAC;AAED,qBAAW,QAAQ,SAAS,KAAK;;AAEnC,eAAO,EAAE,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,KAAK,GAAK,UAAU,GAAI,OAAO,KAAI;eAChD,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,OAAO,CAAA,EAAE,GAAI,MAAK;;AAErC,cAAM;;IAEV;;;;;;;;IASA,MAAM,YAAY,KAAW;AAC3B,mBAAa,GAAG;AAEhB,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,GAAG,IAAI;UACzE,SAAS,KAAK;UACd,OAAO;SACR;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;;;;;;;;IASA,MAAM,eAAe,KAAa,YAA+B;AAC/D,mBAAa,GAAG;AAEhB,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,GAAG,IAAI;UACzE,MAAM;UACN,SAAS,KAAK;UACd,OAAO;SACR;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;;;;;;;;;;IAWA,MAAM,WAAW,IAAY,mBAAmB,OAAK;AACnD,mBAAa,EAAE;AAEf,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,gBAAgB,EAAE,IAAI;UAC3E,SAAS,KAAK;UACd,MAAM;YACJ,oBAAoB;;UAEtB,OAAO;SACR;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;IAEQ,MAAM,aACZ,QAAqC;AAErC,mBAAa,OAAO,MAAM;AAE1B,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,OACA,GAAG,KAAK,GAAG,gBAAgB,OAAO,MAAM,YACxC;UACE,SAAS,KAAK;UACd,OAAO,wBAAC,YAAgB;AACtB,mBAAO,EAAE,MAAM,EAAE,QAAO,GAAI,OAAO,KAAI;UACzC,GAFO;SAGR;AAEH,eAAO,EAAE,MAAM,MAAK;eACb,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV;IAEQ,MAAM,cACZ,QAAsC;AAEtC,mBAAa,OAAO,MAAM;AAC1B,mBAAa,OAAO,EAAE;AAEtB,UAAI;AACF,cAAM,OAAO,MAAM,SACjB,KAAK,OACL,UACA,GAAG,KAAK,GAAG,gBAAgB,OAAO,MAAM,YAAY,OAAO,EAAE,IAC7D;UACE,SAAS,KAAK;SACf;AAGH,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV;;;;AO9VF;;;ACEA;AAIM,WAAU,0BAA0B,QAAmC,CAAA,GAAE;AAC7E,WAAO;MACL,SAAS,wBAAC,QAAO;AACf,eAAO,MAAM,GAAG,KAAK;MACvB,GAFS;MAIT,SAAS,wBAAC,KAAK,UAAS;AACtB,cAAM,GAAG,IAAI;MACf,GAFS;MAIT,YAAY,wBAAC,QAAO;AAClB,eAAO,MAAM,GAAG;MAClB,GAFY;;EAIhB;AAdgB;;;ACNhB;AAGM,WAAU,qBAAkB;AAChC,QAAI,OAAO,eAAe;AAAU;AACpC,QAAI;AACF,aAAO,eAAe,OAAO,WAAW,aAAa;QACnD,KAAK,kCAAA;AACH,iBAAO;QACT,GAFK;QAGL,cAAc;OACf;AAED,gBAAU,aAAa;AAEvB,aAAO,OAAO,UAAU;aACjB,GAAG;AACV,UAAI,OAAO,SAAS,aAAa;AAE/B,aAAK,aAAa;;;EAGxB;AAnBgB;;;ACHhB;AAKO,MAAM,YAAY;;;;IAIvB,OAAO,CAAC,EACN,cACA,qBAAoB,KACpB,WAAW,gBACX,WAAW,aAAa,QAAQ,gCAAgC,MAAM;;AASpE,MAAgB,0BAAhB,cAAgD,MAAK;IAtB3D,OAsB2D;;;IAGzD,YAAY,SAAe;AACzB,YAAM,OAAO;AAHC,WAAA,mBAAmB;IAInC;;AAGI,MAAO,mCAAP,cAAgD,wBAAuB;IA9B7E,OA8B6E;;;;AACvE,MAAO,iCAAP,cAA8C,wBAAuB;IA/B3E,OA+B2E;;;;AA2B3E,iBAAsB,cACpB,MACA,gBACA,IAAoB;AAEpB,QAAI,UAAU,OAAO;AACnB,cAAQ,IAAI,oDAAoD,MAAM,cAAc;;AAGtF,UAAM,kBAAkB,IAAI,WAAW,gBAAe;AAEtD,QAAI,iBAAiB,GAAG;AACtB,iBAAW,MAAK;AACd,wBAAgB,MAAK;AACrB,YAAI,UAAU,OAAO;AACnB,kBAAQ,IAAI,wDAAwD,IAAI;;MAE5E,GAAG,cAAc;;AAYnB,WAAO,MAAM,QAAQ,QAAO,EAAG,KAAK,MAClC,WAAW,UAAU,MAAM,QACzB,MACA,mBAAmB,IACf;MACE,MAAM;MACN,aAAa;QAEf;MACE,MAAM;MACN,QAAQ,gBAAgB;OAE9B,OAAO,SAAQ;AACb,UAAI,MAAM;AACR,YAAI,UAAU,OAAO;AACnB,kBAAQ,IAAI,gDAAgD,MAAM,KAAK,IAAI;;AAG7E,YAAI;AACF,iBAAO,MAAM,GAAE;;AAEf,cAAI,UAAU,OAAO;AACnB,oBAAQ,IAAI,gDAAgD,MAAM,KAAK,IAAI;;;aAG1E;AACL,YAAI,mBAAmB,GAAG;AACxB,cAAI,UAAU,OAAO;AACnB,oBAAQ,IAAI,iEAAiE,IAAI;;AAGnF,gBAAM,IAAI,iCACR,sDAAsD,IAAI,sBAAsB;eAE7E;AACL,cAAI,UAAU,OAAO;AACnB,gBAAI;AACF,oBAAM,SAAS,MAAM,WAAW,UAAU,MAAM,MAAK;AAErD,sBAAQ,IACN,oDACA,KAAK,UAAU,QAAQ,MAAM,IAAI,CAAC;qBAE7B,GAAQ;AACf,sBAAQ,KACN,wEACA,CAAC;;;AASP,kBAAQ,KACN,yPAAyP;AAG3P,iBAAO,MAAM,GAAE;;;IAGrB,CAAC,CACF;EAEL;AA9FsB;AAgGtB,MAAM,gBAAkD,CAAA;AAgBxD,iBAAsB,YACpB,MACA,gBACA,IAAoB;;AAEpB,UAAM,qBAAoB,KAAA,cAAc,IAAI,OAAC,QAAA,OAAA,SAAA,KAAI,QAAQ,QAAO;AAEhE,UAAM,mBAAmB,QAAQ,KAC/B;MACE,kBAAkB,MAAM,MAAK;AAE3B,eAAO;MACT,CAAC;MACD,kBAAkB,IACd,IAAI,QAAQ,CAAC,GAAG,WAAU;AACxB,mBAAW,MAAK;AACd,iBACE,IAAI,+BACF,oCAAoC,IAAI,aAAa,CACtD;QAEL,GAAG,cAAc;MACnB,CAAC,IACD;MACJ,OAAO,CAAC,MAAM,CAAC,CAAC,EAEjB,MAAM,CAAC,MAAU;AAChB,UAAI,KAAK,EAAE,kBAAkB;AAC3B,cAAM;;AAGR,aAAO;IACT,CAAC,EACA,KAAK,YAAW;AAGf,aAAO,MAAM,GAAE;IACjB,CAAC;AAEH,kBAAc,IAAI,IAAI,iBAAiB,MAAM,OAAO,MAAU;AAC5D,UAAI,KAAK,EAAE,kBAAkB;AAG3B,cAAM;AAEN,eAAO;;AAGT,YAAM;IACR,CAAC;AAID,WAAO,MAAM;EACf;AAtDsB;;;AHtDtB,qBAAkB;AAElB,MAAM,kBAGF;IACF,KAAK;IACL,YAAY;IACZ,kBAAkB;IAClB,gBAAgB;IAChB,oBAAoB;IACpB,SAASC;IACT,UAAU;IACV,OAAO;IACP,8BAA8B;;AAGhC,iBAAe,SAAY,MAAc,gBAAwB,IAAoB;AACnF,WAAO,MAAM,GAAE;EACjB;AAFe;AAYf,MAAM,cAAqF,CAAA;AAE3F,MAAqB,eAArB,MAAqB,cAAY;IAnJjC,OAmJiC;;;;;;IAkF/B,YAAY,SAA4B;;AApC9B,WAAA,cAAuC;AACvC,WAAA,gBAAkD;AAClD,WAAA,sBAAiD,oBAAI,IAAG;AACxD,WAAA,oBAA2D;AAC3D,WAAA,4BAAyD;AACzD,WAAA,qBAA8D;AAO9D,WAAA,oBAAsD;AACtD,WAAA,qBAAqB;AAKrB,WAAA,+BAA+B;AAC/B,WAAA,4BAA4B;AAG5B,WAAA,eAAe;AACf,WAAA,gBAAgC,CAAA;AAKhC,WAAA,mBAA4C;AAG5C,WAAA,SAAoD,QAAQ;AAMpE,WAAK,aAAa,cAAa;AAC/B,oBAAa,kBAAkB;AAE/B,UAAI,KAAK,aAAa,KAAKC,WAAS,GAAI;AACtC,gBAAQ,KACN,8MAA8M;;AAIlN,YAAM,WAAQ,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,eAAe,GAAK,OAAO;AAEjD,WAAK,mBAAmB,CAAC,CAAC,SAAS;AACnC,UAAI,OAAO,SAAS,UAAU,YAAY;AACxC,aAAK,SAAS,SAAS;;AAGzB,WAAK,iBAAiB,SAAS;AAC/B,WAAK,aAAa,SAAS;AAC3B,WAAK,mBAAmB,SAAS;AACjC,WAAK,QAAQ,IAAI,eAAe;QAC9B,KAAK,SAAS;QACd,SAAS,SAAS;QAClB,OAAO,SAAS;OACjB;AAED,WAAK,MAAM,SAAS;AACpB,WAAK,UAAU,SAAS;AACxB,WAAK,QAAQC,cAAa,SAAS,KAAK;AACxC,WAAK,OAAO,SAAS,QAAQ;AAC7B,WAAK,qBAAqB,SAAS;AACnC,WAAK,WAAW,SAAS;AACzB,WAAK,+BAA+B,SAAS;AAE7C,UAAI,SAAS,MAAM;AACjB,aAAK,OAAO,SAAS;iBACZD,WAAS,OAAM,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,QAAO;AACtD,aAAK,OAAO;aACP;AACL,aAAK,OAAO;;AAGd,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,OAAO,EAAE,MAAM,CAAA,EAAE;AACtB,aAAK,iBAAiB,OAAO;;AAG/B,WAAK,MAAM;QACT,QAAQ,KAAK,QAAQ,KAAK,IAAI;QAC9B,QAAQ,KAAK,QAAQ,KAAK,IAAI;QAC9B,UAAU,KAAK,UAAU,KAAK,IAAI;QAClC,WAAW,KAAK,WAAW,KAAK,IAAI;QACpC,aAAa,KAAK,aAAa,KAAK,IAAI;QACxC,oBAAoB,KAAK,oBAAoB,KAAK,IAAI;QACtD,gCAAgC,KAAK,gCAAgC,KAAK,IAAI;;AAGhF,UAAI,KAAK,gBAAgB;AACvB,YAAI,SAAS,SAAS;AACpB,eAAK,UAAU,SAAS;eACnB;AACL,cAAI,qBAAoB,GAAI;AAC1B,iBAAK,UAAU,WAAW;iBACrB;AACL,iBAAK,gBAAgB,CAAA;AACrB,iBAAK,UAAU,0BAA0B,KAAK,aAAa;;;AAI/D,YAAI,SAAS,aAAa;AACxB,eAAK,cAAc,SAAS;;aAEzB;AACL,aAAK,gBAAgB,CAAA;AACrB,aAAK,UAAU,0BAA0B,KAAK,aAAa;;AAG7D,UAAIA,WAAS,KAAM,WAAW,oBAAoB,KAAK,kBAAkB,KAAK,YAAY;AACxF,YAAI;AACF,eAAK,mBAAmB,IAAI,WAAW,iBAAiB,KAAK,UAAU;iBAChE,GAAQ;AACf,kBAAQ,MACN,0FACA,CAAC;;AAIL,SAAA,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAiB,WAAW,OAAO,UAAS;AACjE,eAAK,OAAO,4DAA4D,KAAK;AAE7E,gBAAM,KAAK,sBAAsB,MAAM,KAAK,OAAO,MAAM,KAAK,SAAS,KAAK;QAC9E,CAAC;;AAGH,WAAK,WAAU;IACjB;;;;IAzJA,IAAc,OAAI;;AAChB,cAAO,MAAA,KAAA,YAAY,KAAK,UAAU,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,UAAI,QAAA,OAAA,SAAA,KAAI,EAAE,MAAM,CAAA,EAAE;IACzD;IAEA,IAAc,KAAK,OAAsB;AACvC,kBAAY,KAAK,UAAU,IAAC,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,YAAY,KAAK,UAAU,CAAC,GAAA,EAAE,MAAM,MAAK,CAAA;IAC/E;IAEA,IAAc,iBAAc;;AAC1B,cAAO,MAAA,KAAA,YAAY,KAAK,UAAU,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,cAAQ,QAAA,OAAA,SAAA,KAAI,OAAO;IAC1D;IAEA,IAAc,eAAe,OAAa;AACxC,kBAAY,KAAK,UAAU,IAAC,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,YAAY,KAAK,UAAU,CAAC,GAAA,EAAE,UAAU,MAAK,CAAA;IACnF;IA6IQ,UAAU,MAAW;AAC3B,UAAI,KAAK,kBAAkB;AACzB,aAAK,OACH,gBAAgB,KAAK,UAAU,KAAKE,QAAO,MAAK,oBAAI,KAAI,GAAG,YAAW,CAAE,IACxE,GAAG,IAAI;;AAIX,aAAO;IACT;;;;;;IAOA,MAAM,aAAU;AACd,UAAI,KAAK,mBAAmB;AAC1B,eAAO,MAAM,KAAK;;AAGpB,WAAK,qBAAqB,YAAW;AACnC,eAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,iBAAO,MAAM,KAAK,YAAW;QAC/B,CAAC;MACH,GAAE;AAEF,aAAO,MAAM,KAAK;IACpB;;;;;;;IAQQ,MAAM,cAAW;;AACvB,UAAI;AACF,cAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAC1D,YAAI,kBAAkB;AACtB,YAAI,KAAK,yBAAyB,MAAM,GAAG;AACzC,4BAAkB;mBACT,MAAM,KAAK,gBAAgB,MAAM,GAAG;AAC7C,4BAAkB;;AASpB,YAAIF,WAAS,KAAM,KAAK,sBAAsB,oBAAoB,QAAQ;AACxE,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,mBAAmB,QAAQ,eAAe;AAC7E,cAAI,OAAO;AACT,iBAAK,OAAO,kBAAkB,oCAAoC,KAAK;AAEvE,gBAAI,iCAAiC,KAAK,GAAG;AAC3C,oBAAM,aAAY,KAAA,MAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;AACjC,kBACE,cAAc,6BACd,cAAc,wBACd,cAAc,iCACd;AACA,uBAAO,EAAE,MAAK;;;AAMlB,kBAAM,KAAK,eAAc;AAEzB,mBAAO,EAAE,MAAK;;AAGhB,gBAAM,EAAE,SAAS,aAAY,IAAK;AAElC,eAAK,OACH,kBACA,2BACA,SACA,iBACA,YAAY;AAGd,gBAAM,KAAK,aAAa,OAAO;AAE/B,qBAAW,YAAW;AACpB,gBAAI,iBAAiB,YAAY;AAC/B,oBAAM,KAAK,sBAAsB,qBAAqB,OAAO;mBACxD;AACL,oBAAM,KAAK,sBAAsB,aAAa,OAAO;;UAEzD,GAAG,CAAC;AAEJ,iBAAO,EAAE,OAAO,KAAI;;AAGtB,cAAM,KAAK,mBAAkB;AAC7B,eAAO,EAAE,OAAO,KAAI;eACb,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAK;;AAGhB,eAAO;UACL,OAAO,IAAI,iBAAiB,0CAA0C,KAAK;;;AAG7E,cAAM,KAAK,wBAAuB;AAClC,aAAK,OAAO,kBAAkB,KAAK;;IAEvC;;;;;;IAOA,MAAM,kBAAkB,aAA0C;;AAChE,UAAI;AACF,cAAM,MAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;UACnE,SAAS,KAAK;UACd,MAAM;YACJ,OAAM,MAAA,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;YACpC,sBAAsB,EAAE,gBAAe,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY;;UAE3E,OAAO;SACR;AACD,cAAM,EAAE,MAAM,MAAK,IAAK;AAExB,YAAI,SAAS,CAAC,MAAM;AAClB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAE5D,cAAM,UAA0B,KAAK;AACrC,cAAM,OAAoB,KAAK;AAE/B,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,OAAO;;AAGvD,eAAO,EAAE,MAAM,EAAE,MAAM,QAAO,GAAI,OAAO,KAAI;eACtC,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;;;;;;;;;;;IAYA,MAAM,OAAO,aAA0C;;AACrD,UAAI;AACF,YAAI;AACJ,YAAI,WAAW,aAAa;AAC1B,gBAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,cAAI,gBAA+B;AACnC,cAAI,sBAAqC;AACzC,cAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,aAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAGnB,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;YAC7D,SAAS,KAAK;YACd,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;YACrB,MAAM;cACJ;cACA;cACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;cACvB,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;cAC5D,gBAAgB;cAChB,uBAAuB;;YAEzB,OAAO;WACR;mBACQ,WAAW,aAAa;AACjC,gBAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;YAC7D,SAAS,KAAK;YACd,MAAM;cACJ;cACA;cACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;cACvB,UAAS,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAO,QAAA,OAAA,SAAA,KAAI;cAC7B,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;YAE9D,OAAO;WACR;eACI;AACL,gBAAM,IAAI,4BACR,iEAAiE;;AAIrE,cAAM,EAAE,MAAM,MAAK,IAAK;AAExB,YAAI,SAAS,CAAC,MAAM;AAClB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAG5D,cAAM,UAA0B,KAAK;AACrC,cAAM,OAAoB,KAAK;AAE/B,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,OAAO;;AAGvD,eAAO,EAAE,MAAM,EAAE,MAAM,QAAO,GAAI,OAAO,KAAI;eACtC,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;;;;;;;;;IAUA,MAAM,mBACJ,aAA0C;AAE1C,UAAI;AACF,YAAI;AACJ,YAAI,WAAW,aAAa;AAC1B,gBAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;YAChF,SAAS,KAAK;YACd,MAAM;cACJ;cACA;cACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;YAE9D,OAAO;WACR;mBACQ,WAAW,aAAa;AACjC,gBAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;YAChF,SAAS,KAAK;YACd,MAAM;cACJ;cACA;cACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;YAE9D,OAAO;WACR;eACI;AACL,gBAAM,IAAI,4BACR,iEAAiE;;AAGrE,cAAM,EAAE,MAAM,MAAK,IAAK;AAExB,YAAI,OAAO;AACT,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;mBAC1C,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,OAAO,IAAI,8BAA6B,EAAE;;AAE1F,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,eAAO;UACL,MAAI,OAAA,OAAA,EACF,MAAM,KAAK,MACX,SAAS,KAAK,QAAO,GACjB,KAAK,gBAAgB,EAAE,cAAc,KAAK,cAAa,IAAK,IAAK;UAEvE;;eAEK,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,cAAM;;IAEV;;;;;IAMA,MAAM,gBAAgB,aAAuC;;AAC3D,aAAO,MAAM,KAAK,sBAAsB,YAAY,UAAU;QAC5D,aAAY,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;QACjC,SAAQ,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;QAC7B,cAAa,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;QAClC,sBAAqB,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;OAC3C;IACH;;;;IAKA,MAAM,uBAAuB,UAAgB;AAC3C,YAAM,KAAK;AAEX,aAAO,KAAK,aAAa,IAAI,YAAW;AACtC,eAAO,KAAK,wBAAwB,QAAQ;MAC9C,CAAC;IACH;;;;;IAMA,MAAM,eAAe,aAA4B;AAO/C,YAAM,EAAE,MAAK,IAAK;AAElB,UAAI,UAAU,UAAU;AACtB,eAAO,MAAM,KAAK,iBAAiB,WAAW;;AAGhD,YAAM,IAAI,MAAM,yCAAyC,KAAK,GAAG;IACnE;IAEQ,MAAM,iBAAiB,aAAkC;;AAC/D,UAAI;AACJ,UAAI;AAEJ,UAAI,aAAa,aAAa;AAC5B,kBAAU,YAAY;AACtB,oBAAY,YAAY;aACnB;AACL,cAAM,EAAE,OAAO,QAAQ,WAAW,QAAO,IAAK;AAE9C,YAAI;AAEJ,YAAI,CAACA,WAAS,GAAI;AAChB,cAAI,OAAO,WAAW,YAAY,EAAC,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,MAAK;AAC/C,kBAAM,IAAI,MACR,uFAAuF;;AAI3F,2BAAiB;mBACR,OAAO,WAAW,UAAU;AACrC,2BAAiB;eACZ;AACL,gBAAM,YAAY;AAElB,cACE,YAAY,aACZ,OAAO,UAAU,WAAW,aAC1B,YAAY,UAAU,UAAU,OAAO,UAAU,OAAO,WAAW,cAClE,iBAAiB,UAAU,UAC1B,OAAO,UAAU,OAAO,gBAAgB,aAC5C;AACA,6BAAiB,UAAU;iBACtB;AACL,kBAAM,IAAI,MACR,uTAAuT;;;AAK7T,cAAM,MAAM,IAAI,KAAI,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,SAAG,QAAA,OAAA,SAAA,KAAI,OAAO,SAAS,IAAI;AAExD,YAAI,YAAY,kBAAkB,eAAe,QAAQ;AACvD,gBAAM,SAAS,MAAM,eAAe,OAAM,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,EACxC,WAAU,oBAAI,KAAI,GAAG,YAAW,EAAE,GAE/B,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAgB,GAAA;;YAG5B,SAAS;YACT,QAAQ,IAAI;YACZ,KAAK,IAAI;UAAI,CAAA,GAET,YAAY,EAAE,UAAS,IAAK,IAAK,CAAA;AAGvC,cAAI;AAEJ,cAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,CAAC,KAAK,OAAO,OAAO,CAAC,MAAM,UAAU;AACvE,8BAAkB,OAAO,CAAC;qBAE1B,UACA,OAAO,WAAW,YAClB,mBAAmB,UACnB,eAAe,QACf;AACA,8BAAkB;iBACb;AACL,kBAAM,IAAI,MAAM,uEAAuE;;AAGzF,cACE,mBAAmB,mBACnB,eAAe,oBACd,OAAO,gBAAgB,kBAAkB,YACxC,gBAAgB,yBAAyB,eAC3C,gBAAgB,qBAAqB,YACrC;AACA,sBACE,OAAO,gBAAgB,kBAAkB,WACrC,gBAAgB,gBAChB,IAAI,YAAW,EAAG,OAAO,gBAAgB,aAAa;AAC5D,wBAAY,gBAAgB;iBACvB;AACL,kBAAM,IAAI,MACR,0GAA0G;;eAGzG;AACL,cACE,EAAE,iBAAiB,mBACnB,OAAO,eAAe,gBAAgB,cACtC,EAAE,eAAe,mBACjB,OAAO,mBAAmB,YAC1B,CAAC,eAAe,aAChB,EAAE,cAAc,eAAe,cAC/B,OAAO,eAAe,UAAU,aAAa,YAC7C;AACA,kBAAM,IAAI,MACR,iGAAiG;;AAIrG,oBAAU;YACR,GAAG,IAAI,IAAI;YACX,eAAe,UAAU,SAAQ;YACjC,GAAI,YAAY,CAAC,IAAI,WAAW,EAAE,IAAI,CAAC,EAAE;YACzC;YACA,QAAQ,IAAI,IAAI;YAChB,eAAc,MAAA,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,cAAQ,QAAA,OAAA,SAAA,MAAI,oBAAI,KAAI,GAAG,YAAW,CAAE;YAC7E,KAAI,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,aAC3B,CAAC,eAAe,QAAQ,iBAAiB,SAAS,EAAE,IACpD,CAAA;YACJ,KAAI,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,kBAC3B,CAAC,oBAAoB,QAAQ,iBAAiB,cAAc,EAAE,IAC9D,CAAA;YACJ,KAAI,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,WAC3B,CAAC,aAAa,QAAQ,iBAAiB,OAAO,EAAE,IAChD,CAAA;YACJ,KAAI,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,CAAC,UAAU,QAAQ,iBAAiB,KAAK,EAAE,IAAI,CAAA;YACtF,KAAI,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,aAC3B,CAAC,eAAe,QAAQ,iBAAiB,SAAS,EAAE,IACpD,CAAA;YACJ,KAAI,MAAA,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UACtC;cACE;cACA,GAAG,QAAQ,iBAAiB,UAAU,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;gBAEzE,CAAA;YACJ,KAAK,IAAI;AAEX,gBAAM,iBAAiB,MAAM,eAAe,YAC1C,IAAI,YAAW,EAAG,OAAO,OAAO,GAChC,MAAM;AAGR,cAAI,CAAC,kBAAkB,EAAE,0BAA0B,aAAa;AAC9D,kBAAM,IAAI,MACR,0EAA0E;;AAI9E,sBAAY;;;AAIhB,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,QACA,GAAG,KAAK,GAAG,0BACX;UACE,SAAS,KAAK;UACd,MAAI,OAAA,OAAA,EACF,OAAO,UACP,SACA,WAAW,iBAAiB,SAAS,EAAC,KAElC,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,gBACrB,EAAE,sBAAsB,EAAE,gBAAe,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY,EAAE,IAC5E,IAAK;UAEX,OAAO;SACR;AAEH,YAAI,OAAO;AACT,gBAAM;;AAER,YAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AACxC,iBAAO;YACL,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI;YACjC,OAAO,IAAI,8BAA6B;;;AAG5C,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,eAAO,EAAE,MAAI,OAAA,OAAA,CAAA,GAAO,IAAI,GAAI,MAAK;eAC1B,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;IAEQ,MAAM,wBAAwB,UAAgB;AAOpD,YAAM,cAAc,MAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACvF,YAAM,CAAC,cAAc,YAAY,KAAM,gBAAW,QAAX,gBAAW,SAAX,cAAe,IAAe,MAAM,GAAG;AAE9E,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,QACA,GAAG,KAAK,GAAG,0BACX;UACE,SAAS,KAAK;UACd,MAAM;YACJ,WAAW;YACX,eAAe;;UAEjB,OAAO;SACR;AAEH,cAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACtE,YAAI,OAAO;AACT,gBAAM;;AAER,YAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AACxC,iBAAO;YACL,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,cAAc,KAAI;YACrD,OAAO,IAAI,8BAA6B;;;AAG5C,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,eAAO,EAAE,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,IAAI,GAAA,EAAE,cAAc,iBAAY,QAAZ,iBAAY,SAAZ,eAAgB,KAAI,CAAA,GAAI,MAAK;eAC9D,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,cAAc,KAAI,GAAI,MAAK;;AAGzE,cAAM;;IAEV;;;;;IAMA,MAAM,kBAAkB,aAAyC;AAC/D,UAAI;AACF,cAAM,EAAE,SAAS,UAAU,OAAO,cAAc,MAAK,IAAK;AAE1D,cAAM,MAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;UACtF,SAAS,KAAK;UACd,MAAM;YACJ;YACA,UAAU;YACV;YACA;YACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;UAE9D,OAAO;SACR;AAED,cAAM,EAAE,MAAM,MAAK,IAAK;AACxB,YAAI,OAAO;AACT,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;mBAC1C,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,iBAAO;YACL,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI;YACjC,OAAO,IAAI,8BAA6B;;;AAG5C,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,eAAO,EAAE,MAAM,MAAK;eACb,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,cAAM;;IAEV;;;;;;;;;;;;;;;;;;IAmBA,MAAM,cAAc,aAA8C;;AAChE,UAAI;AACF,YAAI,WAAW,aAAa;AAC1B,gBAAM,EAAE,OAAO,QAAO,IAAK;AAC3B,cAAI,gBAA+B;AACnC,cAAI,sBAAqC;AACzC,cAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,aAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAGnB,gBAAM,EAAE,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;YACtE,SAAS,KAAK;YACd,MAAM;cACJ;cACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;cACvB,cAAa,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,KAAI;cAC1C,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;cAC5D,gBAAgB;cAChB,uBAAuB;;YAEzB,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;WACtB;AACD,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,YAAI,WAAW,aAAa;AAC1B,gBAAM,EAAE,OAAO,QAAO,IAAK;AAC3B,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;YAC5E,SAAS,KAAK;YACd,MAAM;cACJ;cACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;cACvB,cAAa,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,KAAI;cAC1C,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;cAC5D,UAAS,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAO,QAAA,OAAA,SAAA,KAAI;;WAEhC;AACD,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,WAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU,GAAI,MAAK;;AAElF,cAAM,IAAI,4BAA4B,mDAAmD;eAClF,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;;;;IAKA,MAAM,UAAU,QAAuB;;AACrC,UAAI;AACF,YAAI,aAAiC;AACrC,YAAI,eAAmC;AACvC,YAAI,aAAa,QAAQ;AACvB,wBAAa,KAAA,OAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;AAC7B,0BAAe,KAAA,OAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;;AAEjC,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;UAC/E,SAAS,KAAK;UACd,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACC,MAAM,GAAA,EACT,sBAAsB,EAAE,eAAe,aAAY,EAAE,CAAA;UAEvD;UACA,OAAO;SACR;AAED,YAAI,OAAO;AACT,gBAAM;;AAGR,YAAI,CAAC,MAAM;AACT,gBAAM,IAAI,MAAM,0CAA0C;;AAG5D,cAAM,UAA0B,KAAK;AACrC,cAAM,OAAa,KAAK;AAExB,YAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAc;AACzB,gBAAM,KAAK,aAAa,OAAkB;AAC1C,gBAAM,KAAK,sBACT,OAAO,QAAQ,aAAa,sBAAsB,aAClD,OAAO;;AAIX,eAAO,EAAE,MAAM,EAAE,MAAM,QAAO,GAAI,OAAO,KAAI;eACtC,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;;;;;;;;;;;;;;;IAgBA,MAAM,cAAc,QAAqB;;AACvC,UAAI;AACF,YAAI,gBAA+B;AACnC,YAAI,sBAAqC;AACzC,YAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,WAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAInB,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;UAC3D,MAAI,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACE,gBAAgB,SAAS,EAAE,aAAa,OAAO,WAAU,IAAK,IAAK,GACnE,YAAY,SAAS,EAAE,QAAQ,OAAO,OAAM,IAAK,IAAK,GAAA,EAC1D,cAAa,MAAA,KAAA,OAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAU,QAAA,OAAA,SAAA,KAAI,OAAS,CAAA,KAChD,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,gBACjB,EAAE,sBAAsB,EAAE,eAAe,OAAO,QAAQ,aAAY,EAAE,IACtE,IAAK,GAAA,EACT,oBAAoB,MACpB,gBAAgB,eAChB,uBAAuB,oBAAmB,CAAA;UAE5C,SAAS,KAAK;UACd,OAAO;SACR;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV;;;;;IAMA,MAAM,iBAAc;AAClB,YAAM,KAAK;AAEX,aAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,eAAO,MAAM,KAAK,gBAAe;MACnC,CAAC;IACH;IAEQ,MAAM,kBAAe;AAC3B,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;AAC7C,gBAAM,EACJ,MAAM,EAAE,QAAO,GACf,OAAO,aAAY,IACjB;AACJ,cAAI;AAAc,kBAAM;AACxB,cAAI,CAAC;AAAS,kBAAM,IAAI,wBAAuB;AAE/C,gBAAM,EAAE,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,mBAAmB;YAChF,SAAS,KAAK;YACd,KAAK,QAAQ;WACd;AACD,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;QACrD,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,cAAM;;IAEV;;;;IAKA,MAAM,OAAO,aAAyB;AACpC,UAAI;AACF,cAAM,WAAW,GAAG,KAAK,GAAG;AAC5B,YAAI,WAAW,aAAa;AAC1B,gBAAM,EAAE,OAAO,MAAM,QAAO,IAAK;AACjC,gBAAM,EAAE,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,UAAU;YAC7D,SAAS,KAAK;YACd,MAAM;cACJ;cACA;cACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;YAE9D,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;WACtB;AACD,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;mBAC1C,WAAW,aAAa;AACjC,gBAAM,EAAE,OAAO,MAAM,QAAO,IAAK;AACjC,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,UAAU;YACnE,SAAS,KAAK;YACd,MAAM;cACJ;cACA;cACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;WAE/D;AACD,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,WAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU,GAAI,MAAK;;AAElF,cAAM,IAAI,4BACR,6DAA6D;eAExD,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,cAAM;;IAEV;;;;;;;;;;;;IAaA,MAAM,aAAU;AACd,YAAM,KAAK;AAEX,YAAM,SAAS,MAAM,KAAK,aAAa,IAAI,YAAW;AACpD,eAAO,KAAK,YAAY,OAAOG,YAAU;AACvC,iBAAOA;QACT,CAAC;MACH,CAAC;AAED,aAAO;IACT;;;;IAKQ,MAAM,aAAgB,gBAAwB,IAAoB;AACxE,WAAK,OAAO,iBAAiB,SAAS,cAAc;AAEpD,UAAI;AACF,YAAI,KAAK,cAAc;AACrB,gBAAM,OAAO,KAAK,cAAc,SAC5B,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,IAChD,QAAQ,QAAO;AAEnB,gBAAM,UAAU,YAAW;AACzB,kBAAM;AACN,mBAAO,MAAM,GAAE;UACjB,GAAE;AAEF,eAAK,cAAc,MAChB,YAAW;AACV,gBAAI;AACF,oBAAM;qBACC,GAAQ;;UAGnB,GAAE,CAAE;AAGN,iBAAO;;AAGT,eAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,UAAU,IAAI,gBAAgB,YAAW;AAC3E,eAAK,OAAO,iBAAiB,iCAAiC,KAAK,UAAU;AAE7E,cAAI;AACF,iBAAK,eAAe;AAEpB,kBAAM,SAAS,GAAE;AAEjB,iBAAK,cAAc,MAChB,YAAW;AACV,kBAAI;AACF,sBAAM;uBACC,GAAQ;;YAGnB,GAAE,CAAE;AAGN,kBAAM;AAGN,mBAAO,KAAK,cAAc,QAAQ;AAChC,oBAAM,SAAS,CAAC,GAAG,KAAK,aAAa;AAErC,oBAAM,QAAQ,IAAI,MAAM;AAExB,mBAAK,cAAc,OAAO,GAAG,OAAO,MAAM;;AAG5C,mBAAO,MAAM;;AAEb,iBAAK,OAAO,iBAAiB,iCAAiC,KAAK,UAAU;AAE7E,iBAAK,eAAe;;QAExB,CAAC;;AAED,aAAK,OAAO,iBAAiB,KAAK;;IAEtC;;;;;;;IAQQ,MAAM,YACZ,IAoBe;AAEf,WAAK,OAAO,gBAAgB,OAAO;AAEnC,UAAI;AAEF,cAAM,SAAS,MAAM,KAAK,cAAa;AAEvC,eAAO,MAAM,GAAG,MAAM;;AAEtB,aAAK,OAAO,gBAAgB,KAAK;;IAErC;;;;;;IAOQ,MAAM,gBAAa;AAoBzB,WAAK,OAAO,oBAAoB,OAAO;AAEvC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,OAAO,oBAAoB,qCAAqC,IAAI,MAAK,EAAG,KAAK;;AAGxF,UAAI;AACF,YAAI,iBAAiC;AAErC,cAAM,eAAe,MAAM,aAAa,KAAK,SAAS,KAAK,UAAU;AAErE,aAAK,OAAO,iBAAiB,wBAAwB,YAAY;AAEjE,YAAI,iBAAiB,MAAM;AACzB,cAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC,6BAAiB;iBACZ;AACL,iBAAK,OAAO,iBAAiB,mCAAmC;AAChE,kBAAM,KAAK,eAAc;;;AAI7B,YAAI,CAAC,gBAAgB;AACnB,iBAAO,EAAE,MAAM,EAAE,SAAS,KAAI,GAAI,OAAO,KAAI;;AAQ/C,cAAM,aAAa,eAAe,aAC9B,eAAe,aAAa,MAAO,KAAK,IAAG,IAAK,mBAChD;AAEJ,aAAK,OACH,oBACA,cAAc,aAAa,KAAK,MAAM,YACtC,cACA,eAAe,UAAU;AAG3B,YAAI,CAAC,YAAY;AACf,cAAI,KAAK,aAAa;AACpB,kBAAM,YAA4C,MAAM,aACtD,KAAK,aACL,KAAK,aAAa,OAAO;AAG3B,gBAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,MAAM;AACnB,6BAAe,OAAO,UAAU;mBAC3B;AACL,6BAAe,OAAO,sBAAqB;;;AAI/C,cAAI,KAAK,QAAQ,YAAY,eAAe,MAAM;AAChD,gBAAI,kBAAkB,KAAK;AAC3B,kBAAM,eAAwB,IAAI,MAAM,gBAAgB;cACtD,KAAK,wBAAC,QAAa,MAAc,aAAiB;AAChD,oBAAI,CAAC,mBAAmB,SAAS,QAAQ;AAEvC,0BAAQ,KACN,iWAAiW;AAEnW,oCAAkB;AAClB,uBAAK,4BAA4B;;AAEnC,uBAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;cAC3C,GAVK;aAWN;AACD,6BAAiB;;AAGnB,iBAAO,EAAE,MAAM,EAAE,SAAS,eAAc,GAAI,OAAO,KAAI;;AAGzD,cAAM,EAAE,SAAS,MAAK,IAAK,MAAM,KAAK,kBAAkB,eAAe,aAAa;AACpF,YAAI,OAAO;AACT,iBAAO,EAAE,MAAM,EAAE,SAAS,KAAI,GAAI,MAAK;;AAGzC,eAAO,EAAE,MAAM,EAAE,QAAO,GAAI,OAAO,KAAI;;AAEvC,aAAK,OAAO,oBAAoB,KAAK;;IAEzC;;;;;;;;IASA,MAAM,QAAQ,KAAY;AACxB,UAAI,KAAK;AACP,eAAO,MAAM,KAAK,SAAS,GAAG;;AAGhC,YAAM,KAAK;AAEX,YAAM,SAAS,MAAM,KAAK,aAAa,IAAI,YAAW;AACpD,eAAO,MAAM,KAAK,SAAQ;MAC5B,CAAC;AAED,aAAO;IACT;IAEQ,MAAM,SAAS,KAAY;AACjC,UAAI;AACF,YAAI,KAAK;AACP,iBAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;YAC3D,SAAS,KAAK;YACd;YACA,OAAO;WACR;;AAGH,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EAAE,MAAM,MAAK,IAAK;AACxB,cAAI,OAAO;AACT,kBAAM;;AAIR,cAAI,GAAC,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAgB,CAAC,KAAK,8BAA8B;AACrE,mBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,OAAO,IAAI,wBAAuB,EAAE;;AAGrE,iBAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;YAC3D,SAAS,KAAK;YACd,MAAK,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;YACnC,OAAO;WACR;QACH,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,cAAI,0BAA0B,KAAK,GAAG;AAIpC,kBAAM,KAAK,eAAc;AACzB,kBAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;;AAGxE,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;;;;IAKA,MAAM,WACJ,YACA,UAEI,CAAA,GAAE;AAEN,YAAM,KAAK;AAEX,aAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,eAAO,MAAM,KAAK,YAAY,YAAY,OAAO;MACnD,CAAC;IACH;IAEU,MAAM,YACd,YACA,UAEI,CAAA,GAAE;AAEN,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;AAC7C,gBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,cAAI,cAAc;AAChB,kBAAM;;AAER,cAAI,CAAC,YAAY,SAAS;AACxB,kBAAM,IAAI,wBAAuB;;AAEnC,gBAAM,UAAmB,YAAY;AACrC,cAAI,gBAA+B;AACnC,cAAI,sBAAqC;AACzC,cAAI,KAAK,aAAa,UAAU,WAAW,SAAS,MAAM;AACxD;AAAC,aAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAInB,gBAAM,EAAE,MAAM,OAAO,UAAS,IAAK,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;YACvF,SAAS,KAAK;YACd,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;YACrB,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACC,UAAU,GAAA,EACb,gBAAgB,eAChB,uBAAuB,oBAAmB,CAAA;YAE5C,KAAK,QAAQ;YACb,OAAO;WACR;AACD,cAAI;AAAW,kBAAM;AACrB,kBAAQ,OAAO,KAAK;AACpB,gBAAM,KAAK,aAAa,OAAO;AAC/B,gBAAM,KAAK,sBAAsB,gBAAgB,OAAO;AACxD,iBAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,KAAI,GAAI,OAAO,KAAI;QACpD,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,cAAM;;IAEV;;;;;;IAOA,MAAM,WAAW,gBAGhB;AACC,YAAM,KAAK;AAEX,aAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,eAAO,MAAM,KAAK,YAAY,cAAc;MAC9C,CAAC;IACH;IAEU,MAAM,YAAY,gBAG3B;AACC,UAAI;AACF,YAAI,CAAC,eAAe,gBAAgB,CAAC,eAAe,eAAe;AACjE,gBAAM,IAAI,wBAAuB;;AAGnC,cAAM,UAAU,KAAK,IAAG,IAAK;AAC7B,YAAIC,aAAY;AAChB,YAAI,aAAa;AACjB,YAAI,UAA0B;AAC9B,cAAM,EAAE,QAAO,IAAK,UAAU,eAAe,YAAY;AACzD,YAAI,QAAQ,KAAK;AACf,UAAAA,aAAY,QAAQ;AACpB,uBAAaA,cAAa;;AAG5B,YAAI,YAAY;AACd,gBAAM,EAAE,SAAS,kBAAkB,MAAK,IAAK,MAAM,KAAK,kBACtD,eAAe,aAAa;AAE9B,cAAI,OAAO;AACT,mBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAG5D,cAAI,CAAC,kBAAkB;AACrB,mBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,OAAO,KAAI;;AAE3D,oBAAU;eACL;AACL,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAAS,eAAe,YAAY;AACvE,cAAI,OAAO;AACT,kBAAM;;AAER,oBAAU;YACR,cAAc,eAAe;YAC7B,eAAe,eAAe;YAC9B,MAAM,KAAK;YACX,YAAY;YACZ,YAAYA,aAAY;YACxB,YAAYA;;AAEd,gBAAM,KAAK,aAAa,OAAO;AAC/B,gBAAM,KAAK,sBAAsB,aAAa,OAAO;;AAGvD,eAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,MAAM,QAAO,GAAI,OAAO,KAAI;eACpD,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,SAAS,MAAM,MAAM,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;;;;;;;IAQA,MAAM,eAAe,gBAA0C;AAC7D,YAAM,KAAK;AAEX,aAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,eAAO,MAAM,KAAK,gBAAgB,cAAc;MAClD,CAAC;IACH;IAEU,MAAM,gBAAgB,gBAE/B;AACC,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAI,CAAC,gBAAgB;AACnB,kBAAM,EAAE,MAAM,OAAAC,OAAK,IAAK;AACxB,gBAAIA,QAAO;AACT,oBAAMA;;AAGR,8BAAiB,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,KAAI;;AAGnC,cAAI,EAAC,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,gBAAe;AAClC,kBAAM,IAAI,wBAAuB;;AAGnC,gBAAM,EAAE,SAAS,MAAK,IAAK,MAAM,KAAK,kBAAkB,eAAe,aAAa;AACpF,cAAI,OAAO;AACT,mBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAG5D,cAAI,CAAC,SAAS;AACZ,mBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,OAAO,KAAI;;AAG3D,iBAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,MAAM,QAAO,GAAI,OAAO,KAAI;QAC7D,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,cAAM;;IAEV;;;;IAKQ,MAAM,mBACZ,QACA,iBAAuB;AAQvB,UAAI;AACF,YAAI,CAACL,WAAS;AAAI,gBAAM,IAAI,+BAA+B,sBAAsB;AAGjF,YAAI,OAAO,SAAS,OAAO,qBAAqB,OAAO,YAAY;AAGjE,gBAAM,IAAI,+BACR,OAAO,qBAAqB,mDAC5B;YACE,OAAO,OAAO,SAAS;YACvB,MAAM,OAAO,cAAc;WAC5B;;AAKL,gBAAQ,iBAAiB;UACvB,KAAK;AACH,gBAAI,KAAK,aAAa,QAAQ;AAC5B,oBAAM,IAAI,+BAA+B,4BAA4B;;AAEvE;UACF,KAAK;AACH,gBAAI,KAAK,aAAa,YAAY;AAChC,oBAAM,IAAI,+BAA+B,sCAAsC;;AAEjF;UACF;;AAKF,YAAI,oBAAoB,QAAQ;AAC9B,eAAK,OAAO,kBAAkB,SAAS,gBAAgB,IAAI;AAC3D,cAAI,CAAC,OAAO;AAAM,kBAAM,IAAI,+BAA+B,mBAAmB;AAC9E,gBAAM,EAAE,MAAAM,OAAM,OAAAD,OAAK,IAAK,MAAM,KAAK,wBAAwB,OAAO,IAAI;AACtE,cAAIA;AAAO,kBAAMA;AAEjB,gBAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,cAAI,aAAa,OAAO,MAAM;AAE9B,iBAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,IAAI,IAAI,SAAQ,CAAE;AAEpE,iBAAO,EAAE,MAAM,EAAE,SAASC,MAAK,SAAS,cAAc,KAAI,GAAI,OAAO,KAAI;;AAG3E,cAAM,EACJ,gBACA,wBACA,cACA,eACA,YACA,YACA,WAAU,IACR;AAEJ,YAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY;AACjE,gBAAM,IAAI,+BAA+B,2BAA2B;;AAGtE,cAAM,UAAU,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAC5C,cAAM,YAAY,SAAS,UAAU;AACrC,YAAIF,aAAY,UAAU;AAE1B,YAAI,YAAY;AACd,UAAAA,aAAY,SAAS,UAAU;;AAGjC,cAAM,oBAAoBA,aAAY;AACtC,YAAI,oBAAoB,OAAQ,+BAA+B;AAC7D,kBAAQ,KACN,iEAAiE,iBAAiB,iCAAiC,SAAS,GAAG;;AAInI,cAAM,WAAWA,aAAY;AAC7B,YAAI,UAAU,YAAY,KAAK;AAC7B,kBAAQ,KACN,mGACA,UACAA,YACA,OAAO;mBAEA,UAAU,WAAW,GAAG;AACjC,kBAAQ,KACN,gHACA,UACAA,YACA,OAAO;;AAIX,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAAS,YAAY;AACxD,YAAI;AAAO,gBAAM;AAEjB,cAAM,UAAmB;UACvB;UACA;UACA;UACA,YAAY;UACZ,YAAYA;UACZ;UACA;UACA,MAAM,KAAK;;AAIb,eAAO,SAAS,OAAO;AACvB,aAAK,OAAO,yBAAyB,+BAA+B;AAEpE,eAAO,EAAE,MAAM,EAAE,SAAS,cAAc,OAAO,KAAI,GAAI,OAAO,KAAI;eAC3D,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,SAAS,MAAM,cAAc,KAAI,GAAI,MAAK;;AAG7D,cAAM;;IAEV;;;;IAKQ,yBAAyB,QAAuC;AACtE,aAAO,QAAQ,OAAO,gBAAgB,OAAO,iBAAiB;IAChE;;;;IAKQ,MAAM,gBAAgB,QAAuC;AACnE,YAAM,wBAAwB,MAAM,aAClC,KAAK,SACL,GAAG,KAAK,UAAU,gBAAgB;AAGpC,aAAO,CAAC,EAAE,OAAO,QAAQ;IAC3B;;;;;;;;;IAUA,MAAM,QAAQ,UAAmB,EAAE,OAAO,SAAQ,GAAE;AAClD,YAAM,KAAK;AAEX,aAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,eAAO,MAAM,KAAK,SAAS,OAAO;MACpC,CAAC;IACH;IAEU,MAAM,SACd,EAAE,MAAK,IAAc,EAAE,OAAO,SAAQ,GAAE;AAExC,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAM,EAAE,MAAM,OAAO,aAAY,IAAK;AACtC,YAAI,cAAc;AAChB,iBAAO,EAAE,OAAO,aAAY;;AAE9B,cAAM,eAAc,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;AAClC,YAAI,aAAa;AACf,gBAAM,EAAE,MAAK,IAAK,MAAM,KAAK,MAAM,QAAQ,aAAa,KAAK;AAC7D,cAAI,OAAO;AAGT,gBACE,EACE,eAAe,KAAK,MACnB,MAAM,WAAW,OAAO,MAAM,WAAW,OAAO,MAAM,WAAW,OAEpE;AACA,qBAAO,EAAE,MAAK;;;;AAIpB,YAAI,UAAU,UAAU;AACtB,gBAAM,KAAK,eAAc;AACzB,gBAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;;AAExE,eAAO,EAAE,OAAO,KAAI;MACtB,CAAC;IACH;;;;;IAMA,kBACE,UAAmF;AAInF,YAAM,KAAaG,MAAI;AACvB,YAAM,eAA6B;QACjC;QACA;QACA,aAAa,6BAAK;AAChB,eAAK,OAAO,kBAAkB,yCAAyC,EAAE;AAEzE,eAAK,oBAAoB,OAAO,EAAE;QACpC,GAJa;;AAOf,WAAK,OAAO,wBAAwB,+BAA+B,EAAE;AAErE,WAAK,oBAAoB,IAAI,IAAI,YAAY;AAC5C,OAAC,YAAW;AACX,cAAM,KAAK;AAEX,cAAM,KAAK,aAAa,IAAI,YAAW;AACrC,eAAK,oBAAoB,EAAE;QAC7B,CAAC;MACH,GAAE;AAEF,aAAO,EAAE,MAAM,EAAE,aAAY,EAAE;IACjC;IAEQ,MAAM,oBAAoB,IAAU;AAC1C,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,YAAI;AACF,gBAAM,EACJ,MAAM,EAAE,QAAO,GACf,MAAK,IACH;AACJ,cAAI;AAAO,kBAAM;AAEjB,kBAAM,KAAA,KAAK,oBAAoB,IAAI,EAAE,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,mBAAmB,OAAO;AAC3E,eAAK,OAAO,mBAAmB,eAAe,IAAI,WAAW,OAAO;iBAC7D,KAAK;AACZ,kBAAM,KAAA,KAAK,oBAAoB,IAAI,EAAE,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,mBAAmB,IAAI;AACxE,eAAK,OAAO,mBAAmB,eAAe,IAAI,SAAS,GAAG;AAC9D,kBAAQ,MAAM,GAAG;;MAErB,CAAC;IACH;;;;;;;;IASA,MAAM,sBACJ,OACA,UAGI,CAAA,GAAE;AAQN,UAAI,gBAA+B;AACnC,UAAI,sBAAqC;AAEzC,UAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,SAAC,eAAe,mBAAmB,IAAI,MAAM;UAC5C,KAAK;UACL,KAAK;UACL;;;;AAGJ,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY;UAC/D,MAAM;YACJ;YACA,gBAAgB;YAChB,uBAAuB;YACvB,sBAAsB,EAAE,eAAe,QAAQ,aAAY;;UAE7D,SAAS,KAAK;UACd,YAAY,QAAQ;SACrB;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV;;;;IAKA,MAAM,oBAAiB;;AASrB,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,QAAO;AAC1C,YAAI;AAAO,gBAAM;AACjB,eAAO,EAAE,MAAM,EAAE,aAAY,KAAA,KAAK,KAAK,gBAAU,QAAA,OAAA,SAAA,KAAI,CAAA,EAAE,GAAI,OAAO,KAAI;eAC/D,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV;;;;;IAKA,MAAM,aAAa,aAAuC;;AACxD,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC9D,gBAAM,EAAE,MAAAD,OAAM,OAAAD,OAAK,IAAK;AACxB,cAAIA;AAAO,kBAAMA;AACjB,gBAAM,MAAc,MAAM,KAAK,mBAC7B,GAAG,KAAK,GAAG,8BACX,YAAY,UACZ;YACE,aAAYG,MAAA,YAAY,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE;YACjC,SAAQ,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;YAC7B,cAAa,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;YAClC,qBAAqB;WACtB;AAEH,iBAAO,MAAM,SAAS,KAAK,OAAO,OAAO,KAAK;YAC5C,SAAS,KAAK;YACd,MAAK,MAAA,KAAAF,MAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;WACpC;QACH,CAAC;AACD,YAAI;AAAO,gBAAM;AACjB,YAAIN,WAAS,KAAM,GAAC,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,sBAAqB;AAC5D,iBAAO,SAAS,OAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,GAAG;;AAElC,eAAO,EAAE,MAAM,EAAE,UAAU,YAAY,UAAU,KAAK,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,IAAG,GAAI,OAAO,KAAI;eACvE,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,UAAU,YAAY,UAAU,KAAK,KAAI,GAAI,MAAK;;AAErE,cAAM;;IAEV;;;;IAKA,MAAM,eAAe,UAAsB;AAOzC,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EAAE,MAAM,MAAK,IAAK;AACxB,cAAI,OAAO;AACT,kBAAM;;AAER,iBAAO,MAAM,SACX,KAAK,OACL,UACA,GAAG,KAAK,GAAG,oBAAoB,SAAS,WAAW,IACnD;YACE,SAAS,KAAK;YACd,MAAK,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;WACpC;QAEL,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV;;;;;IAMQ,MAAM,oBAAoB,cAAoB;AACpD,YAAM,YAAY,wBAAwB,aAAa,UAAU,GAAG,CAAC,CAAC;AACtE,WAAK,OAAO,WAAW,OAAO;AAE9B,UAAI;AACF,cAAM,YAAY,KAAK,IAAG;AAG1B,eAAO,MAAM,UACX,OAAO,YAAW;AAChB,cAAI,UAAU,GAAG;AACf,kBAAM,MAAM,MAAM,KAAK,IAAI,GAAG,UAAU,CAAC,CAAC;;AAG5C,eAAK,OAAO,WAAW,sBAAsB,OAAO;AAEpD,iBAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,mCAAmC;YACtF,MAAM,EAAE,eAAe,aAAY;YACnC,SAAS,KAAK;YACd,OAAO;WACR;QACH,GACA,CAAC,SAAS,UAAS;AACjB,gBAAM,sBAAsB,MAAM,KAAK,IAAI,GAAG,OAAO;AACrD,iBACE,SACA,0BAA0B,KAAK;UAE/B,KAAK,IAAG,IAAK,sBAAsB,YAAY;QAEnD,CAAC;eAEI,OAAO;AACd,aAAK,OAAO,WAAW,SAAS,KAAK;AAErC,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,EAAE,SAAS,MAAM,MAAM,KAAI,GAAI,MAAK;;AAErD,cAAM;;AAEN,aAAK,OAAO,WAAW,KAAK;;IAEhC;IAEQ,gBAAgB,cAAqB;AAC3C,YAAM,iBACJ,OAAO,iBAAiB,YACxB,iBAAiB,QACjB,kBAAkB,gBAClB,mBAAmB,gBACnB,gBAAgB;AAElB,aAAO;IACT;IAEQ,MAAM,sBACZ,UACA,SAKC;AAED,YAAM,MAAc,MAAM,KAAK,mBAAmB,GAAG,KAAK,GAAG,cAAc,UAAU;QACnF,YAAY,QAAQ;QACpB,QAAQ,QAAQ;QAChB,aAAa,QAAQ;OACtB;AAED,WAAK,OAAO,4BAA4B,YAAY,UAAU,WAAW,SAAS,OAAO,GAAG;AAG5F,UAAIA,WAAS,KAAM,CAAC,QAAQ,qBAAqB;AAC/C,eAAO,SAAS,OAAO,GAAG;;AAG5B,aAAO,EAAE,MAAM,EAAE,UAAU,IAAG,GAAI,OAAO,KAAI;IAC/C;;;;;IAMQ,MAAM,qBAAkB;;AAC9B,YAAM,YAAY;AAClB,WAAK,OAAO,WAAW,OAAO;AAE9B,UAAI;AACF,cAAM,iBAAkB,MAAM,aAAa,KAAK,SAAS,KAAK,UAAU;AAExE,YAAI,kBAAkB,KAAK,aAAa;AACtC,cAAI,YAA2C,MAAM,aACnD,KAAK,aACL,KAAK,aAAa,OAAO;AAG3B,cAAI,CAAC,KAAK,QAAQ,YAAY,OAAO,GAAG,KAAK,SAAS,KAAK,WAAW,KAAK,CAAC,WAAW;AAMrF,wBAAY,EAAE,MAAM,eAAe,KAAI;AACvC,kBAAM,aAAa,KAAK,aAAa,KAAK,aAAa,SAAS,SAAS;;AAG3E,yBAAe,QAAO,KAAA,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAI,QAAA,OAAA,SAAA,KAAI,sBAAqB;mBACrD,kBAAkB,CAAC,eAAe,MAAM;AAIjD,cAAI,CAAC,eAAe,MAAM;AAExB,kBAAM,eAA8C,MAAM,aACxD,KAAK,SACL,KAAK,aAAa,OAAO;AAG3B,gBAAI,iBAAgB,iBAAY,QAAZ,iBAAY,SAAA,SAAZ,aAAc,OAAM;AACtC,6BAAe,OAAO,aAAa;AAEnC,oBAAM,gBAAgB,KAAK,SAAS,KAAK,aAAa,OAAO;AAC7D,oBAAM,aAAa,KAAK,SAAS,KAAK,YAAY,cAAc;mBAC3D;AACL,6BAAe,OAAO,sBAAqB;;;;AAKjD,aAAK,OAAO,WAAW,wBAAwB,cAAc;AAE7D,YAAI,CAAC,KAAK,gBAAgB,cAAc,GAAG;AACzC,eAAK,OAAO,WAAW,sBAAsB;AAC7C,cAAI,mBAAmB,MAAM;AAC3B,kBAAM,KAAK,eAAc;;AAG3B;;AAGF,cAAM,sBACH,KAAA,eAAe,gBAAU,QAAA,OAAA,SAAA,KAAI,YAAY,MAAO,KAAK,IAAG,IAAK;AAEhE,aAAK,OACH,WACA,cAAc,oBAAoB,KAAK,MAAM,2BAA2B,gBAAgB,GAAG;AAG7F,YAAI,mBAAmB;AACrB,cAAI,KAAK,oBAAoB,eAAe,eAAe;AACzD,kBAAM,EAAE,MAAK,IAAK,MAAM,KAAK,kBAAkB,eAAe,aAAa;AAE3E,gBAAI,OAAO;AACT,sBAAQ,MAAM,KAAK;AAEnB,kBAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,qBAAK,OACH,WACA,mEACA,KAAK;AAEP,sBAAM,KAAK,eAAc;;;;mBAK/B,eAAe,QACd,eAAe,KAAa,8BAA8B,MAC3D;AAEA,cAAI;AACF,kBAAM,EAAE,MAAM,OAAO,UAAS,IAAK,MAAM,KAAK,SAAS,eAAe,YAAY;AAElF,gBAAI,CAAC,cAAa,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,OAAM;AAC5B,6BAAe,OAAO,KAAK;AAC3B,oBAAM,KAAK,aAAa,cAAc;AACtC,oBAAM,KAAK,sBAAsB,aAAa,cAAc;mBACvD;AACL,mBAAK,OAAO,WAAW,0DAA0D;;mBAE5E,cAAc;AACrB,oBAAQ,MAAM,4BAA4B,YAAY;AACtD,iBAAK,OACH,WACA,4DACA,YAAY;;eAGX;AAIL,gBAAM,KAAK,sBAAsB,aAAa,cAAc;;eAEvD,KAAK;AACZ,aAAK,OAAO,WAAW,SAAS,GAAG;AAEnC,gBAAQ,MAAM,GAAG;AACjB;;AAEA,aAAK,OAAO,WAAW,KAAK;;IAEhC;IAEQ,MAAM,kBAAkB,cAAoB;;AAClD,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,wBAAuB;;AAInC,UAAI,KAAK,oBAAoB;AAC3B,eAAO,KAAK,mBAAmB;;AAGjC,YAAM,YAAY,sBAAsB,aAAa,UAAU,GAAG,CAAC,CAAC;AAEpE,WAAK,OAAO,WAAW,OAAO;AAE9B,UAAI;AACF,aAAK,qBAAqB,IAAI,SAAQ;AAEtC,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,oBAAoB,YAAY;AACnE,YAAI;AAAO,gBAAM;AACjB,YAAI,CAAC,KAAK;AAAS,gBAAM,IAAI,wBAAuB;AAEpD,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,mBAAmB,KAAK,OAAO;AAEhE,cAAM,SAAS,EAAE,SAAS,KAAK,SAAS,OAAO,KAAI;AAEnD,aAAK,mBAAmB,QAAQ,MAAM;AAEtC,eAAO;eACA,OAAO;AACd,aAAK,OAAO,WAAW,SAAS,KAAK;AAErC,YAAI,YAAY,KAAK,GAAG;AACtB,gBAAM,SAAS,EAAE,SAAS,MAAM,MAAK;AAErC,cAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,kBAAM,KAAK,eAAc;;AAG3B,WAAA,KAAA,KAAK,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,MAAM;AAEvC,iBAAO;;AAGT,SAAA,KAAA,KAAK,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,KAAK;AACrC,cAAM;;AAEN,aAAK,qBAAqB;AAC1B,aAAK,OAAO,WAAW,KAAK;;IAEhC;IAEQ,MAAM,sBACZ,OACA,SACA,YAAY,MAAI;AAEhB,YAAM,YAAY,0BAA0B,KAAK;AACjD,WAAK,OAAO,WAAW,SAAS,SAAS,eAAe,SAAS,EAAE;AAEnE,UAAI;AACF,YAAI,KAAK,oBAAoB,WAAW;AACtC,eAAK,iBAAiB,YAAY,EAAE,OAAO,QAAO,CAAE;;AAGtD,cAAM,SAAgB,CAAA;AACtB,cAAM,WAAW,MAAM,KAAK,KAAK,oBAAoB,OAAM,CAAE,EAAE,IAAI,OAAO,MAAK;AAC7E,cAAI;AACF,kBAAM,EAAE,SAAS,OAAO,OAAO;mBACxB,GAAQ;AACf,mBAAO,KAAK,CAAC;;QAEjB,CAAC;AAED,cAAM,QAAQ,IAAI,QAAQ;AAE1B,YAAI,OAAO,SAAS,GAAG;AACrB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,oBAAQ,MAAM,OAAO,CAAC,CAAC;;AAGzB,gBAAM,OAAO,CAAC;;;AAGhB,aAAK,OAAO,WAAW,KAAK;;IAEhC;;;;;IAMQ,MAAM,aAAa,SAAgB;AACzC,WAAK,OAAO,mBAAmB,OAAO;AAGtC,WAAK,4BAA4B;AAGjC,YAAM,mBAAgB,OAAA,OAAA,CAAA,GAAQ,OAAO;AAErC,YAAM,cACJ,iBAAiB,QAAS,iBAAiB,KAAa,8BAA8B;AACxF,UAAI,KAAK,aAAa;AACpB,YAAI,CAAC,eAAe,iBAAiB,MAAM;AAEzC,gBAAM,aAAa,KAAK,aAAa,KAAK,aAAa,SAAS;YAC9D,MAAM,iBAAiB;WACxB;mBACQ,aAAa;;AASxB,cAAM,kBAAe,OAAA,OAAA,CAAA,GAAiD,gBAAgB;AACtF,eAAO,gBAAgB;AAEvB,cAAM,wBAAwB,UAAU,eAAe;AACvD,cAAM,aAAa,KAAK,SAAS,KAAK,YAAY,qBAAqB;aAClE;AAIL,cAAM,gBAAgB,UAAU,gBAAgB;AAChD,cAAM,aAAa,KAAK,SAAS,KAAK,YAAY,aAAa;;IAEnE;IAEQ,MAAM,iBAAc;AAC1B,WAAK,OAAO,mBAAmB;AAE/B,YAAM,gBAAgB,KAAK,SAAS,KAAK,UAAU;AACnD,YAAM,gBAAgB,KAAK,SAAS,KAAK,aAAa,gBAAgB;AACtE,YAAM,gBAAgB,KAAK,SAAS,KAAK,aAAa,OAAO;AAE7D,UAAI,KAAK,aAAa;AACpB,cAAM,gBAAgB,KAAK,aAAa,KAAK,aAAa,OAAO;;AAGnE,YAAM,KAAK,sBAAsB,cAAc,IAAI;IACrD;;;;;;;IAQQ,mCAAgC;AACtC,WAAK,OAAO,qCAAqC;AAEjD,YAAM,WAAW,KAAK;AACtB,WAAK,4BAA4B;AAEjC,UAAI;AACF,YAAI,YAAYA,WAAS,MAAM,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,sBAAqB;AAC1D,iBAAO,oBAAoB,oBAAoB,QAAQ;;eAElD,GAAG;AACV,gBAAQ,MAAM,6CAA6C,CAAC;;IAEhE;;;;;IAMQ,MAAM,oBAAiB;AAC7B,YAAM,KAAK,iBAAgB;AAE3B,WAAK,OAAO,sBAAsB;AAElC,YAAM,SAAS,YAAY,MAAM,KAAK,sBAAqB,GAAI,6BAA6B;AAC5F,WAAK,oBAAoB;AAEzB,UAAI,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,UAAU,YAAY;AAO9E,eAAO,MAAK;iBAEH,OAAO,SAAS,eAAe,OAAO,KAAK,eAAe,YAAY;AAI/E,aAAK,WAAW,MAAM;;AAMxB,iBAAW,YAAW;AACpB,cAAM,KAAK;AACX,cAAM,KAAK,sBAAqB;MAClC,GAAG,CAAC;IACN;;;;;IAMQ,MAAM,mBAAgB;AAC5B,WAAK,OAAO,qBAAqB;AAEjC,YAAM,SAAS,KAAK;AACpB,WAAK,oBAAoB;AAEzB,UAAI,QAAQ;AACV,sBAAc,MAAM;;IAExB;;;;;;;;;;;;;;;;;;;;;;;IAwBA,MAAM,mBAAgB;AACpB,WAAK,iCAAgC;AACrC,YAAM,KAAK,kBAAiB;IAC9B;;;;;;;;;IAUA,MAAM,kBAAe;AACnB,WAAK,iCAAgC;AACrC,YAAM,KAAK,iBAAgB;IAC7B;;;;IAKQ,MAAM,wBAAqB;AACjC,WAAK,OAAO,4BAA4B,OAAO;AAE/C,UAAI;AACF,cAAM,KAAK,aAAa,GAAG,YAAW;AACpC,cAAI;AACF,kBAAM,MAAM,KAAK,IAAG;AAEpB,gBAAI;AACF,qBAAO,MAAM,KAAK,YAAY,OAAO,WAAU;AAC7C,sBAAM,EACJ,MAAM,EAAE,QAAO,EAAE,IACf;AAEJ,oBAAI,CAAC,WAAW,CAAC,QAAQ,iBAAiB,CAAC,QAAQ,YAAY;AAC7D,uBAAK,OAAO,4BAA4B,YAAY;AACpD;;AAIF,sBAAM,iBAAiB,KAAK,OACzB,QAAQ,aAAa,MAAO,OAAO,6BAA6B;AAGnE,qBAAK,OACH,4BACA,2BAA2B,cAAc,wBAAwB,6BAA6B,4BAA4B,2BAA2B,QAAQ;AAG/J,oBAAI,kBAAkB,6BAA6B;AACjD,wBAAM,KAAK,kBAAkB,QAAQ,aAAa;;cAEtD,CAAC;qBACM,GAAQ;AACf,sBAAQ,MACN,0EACA,CAAC;;;AAIL,iBAAK,OAAO,4BAA4B,KAAK;;QAEjD,CAAC;eACM,GAAQ;AACf,YAAI,EAAE,oBAAoB,aAAa,yBAAyB;AAC9D,eAAK,OAAO,4CAA4C;eACnD;AACL,gBAAM;;;IAGZ;;;;;;IAOQ,MAAM,0BAAuB;AACnC,WAAK,OAAO,4BAA4B;AAExC,UAAI,CAACA,WAAS,KAAM,EAAC,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,mBAAkB;AAC7C,YAAI,KAAK,kBAAkB;AAEzB,eAAK,iBAAgB;;AAGvB,eAAO;;AAGT,UAAI;AACF,aAAK,4BAA4B,YAAY,MAAM,KAAK,qBAAqB,KAAK;AAElF,mBAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,iBAAiB,oBAAoB,KAAK,yBAAyB;AAI3E,cAAM,KAAK,qBAAqB,IAAI;eAC7B,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;;IAElD;;;;IAKQ,MAAM,qBAAqB,sBAA6B;AAC9D,YAAM,aAAa,yBAAyB,oBAAoB;AAChE,WAAK,OAAO,YAAY,mBAAmB,SAAS,eAAe;AAEnE,UAAI,SAAS,oBAAoB,WAAW;AAC1C,YAAI,KAAK,kBAAkB;AAGzB,eAAK,kBAAiB;;AAGxB,YAAI,CAAC,sBAAsB;AAKzB,gBAAM,KAAK;AAEX,gBAAM,KAAK,aAAa,IAAI,YAAW;AACrC,gBAAI,SAAS,oBAAoB,WAAW;AAC1C,mBAAK,OACH,YACA,0GAA0G;AAI5G;;AAIF,kBAAM,KAAK,mBAAkB;UAC/B,CAAC;;iBAEM,SAAS,oBAAoB,UAAU;AAChD,YAAI,KAAK,kBAAkB;AACzB,eAAK,iBAAgB;;;IAG3B;;;;;;;IAQQ,MAAM,mBACZ,KACA,UACA,SAKC;AAED,YAAM,YAAsB,CAAC,YAAY,mBAAmB,QAAQ,CAAC,EAAE;AACvE,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;AACvB,kBAAU,KAAK,eAAe,mBAAmB,QAAQ,UAAU,CAAC,EAAE;;AAExE,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACnB,kBAAU,KAAK,UAAU,mBAAmB,QAAQ,MAAM,CAAC,EAAE;;AAE/D,UAAI,KAAK,aAAa,QAAQ;AAC5B,cAAM,CAAC,eAAe,mBAAmB,IAAI,MAAM,0BACjD,KAAK,SACL,KAAK,UAAU;AAGjB,cAAM,aAAa,IAAI,gBAAgB;UACrC,gBAAgB,GAAG,mBAAmB,aAAa,CAAC;UACpD,uBAAuB,GAAG,mBAAmB,mBAAmB,CAAC;SAClE;AACD,kBAAU,KAAK,WAAW,SAAQ,CAAE;;AAEtC,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAa;AACxB,cAAM,QAAQ,IAAI,gBAAgB,QAAQ,WAAW;AACrD,kBAAU,KAAK,MAAM,SAAQ,CAAE;;AAEjC,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,qBAAqB;AAChC,kBAAU,KAAK,sBAAsB,QAAQ,mBAAmB,EAAE;;AAGpE,aAAO,GAAG,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;IACtC;IAEQ,MAAM,UAAU,QAAyB;AAC/C,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,cAAI,cAAc;AAChB,mBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,iBAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,IAAI;YACpF,SAAS,KAAK;YACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;WAC5B;QACH,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV;IAOQ,MAAM,QAAQ,QAAuB;AAC3C,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,cAAI,cAAc;AAChB,mBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,gBAAM,OAAI,OAAA,OAAA,EACR,eAAe,OAAO,cACtB,aAAa,OAAO,WAAU,GAC1B,OAAO,eAAe,UAAU,EAAE,OAAO,OAAO,MAAK,IAAK,EAAE,QAAQ,OAAO,OAAM,CAAG;AAG1F,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY;YAChF;YACA,SAAS,KAAK;YACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;WAC5B;AAED,cAAI,OAAO;AACT,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAI,OAAO,eAAe,YAAU,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS;AACvD,iBAAK,KAAK,UAAU,4BAA4B,KAAK,KAAK,OAAO;;AAGnE,iBAAO,EAAE,MAAM,OAAO,KAAI;QAC5B,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV;;;;IAKQ,MAAM,QAAQ,QAAuB;AAC3C,aAAO,KAAK,aAAa,IAAI,YAAW;AACtC,YAAI;AACF,iBAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,kBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,gBAAI,cAAc;AAChB,qBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,kBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,QACA,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,WACtC;cACE,MAAM,EAAE,MAAM,OAAO,MAAM,cAAc,OAAO,YAAW;cAC3D,SAAS,KAAK;cACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;aAC5B;AAEH,gBAAI,OAAO;AACT,qBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,kBAAM,KAAK,aAAY,OAAA,OAAA,EACrB,YAAY,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI,IAAI,KAAK,WAAU,GACxD,IAAI,CAAA;AAET,kBAAM,KAAK,sBAAsB,0BAA0B,IAAI;AAE/D,mBAAO,EAAE,MAAM,MAAK;UACtB,CAAC;iBACM,OAAO;AACd,cAAI,YAAY,KAAK,GAAG;AACtB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,gBAAM;;MAEV,CAAC;IACH;;;;IAKQ,MAAM,WAAW,QAA0B;AACjD,aAAO,KAAK,aAAa,IAAI,YAAW;AACtC,YAAI;AACF,iBAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,kBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,gBAAI,cAAc;AAChB,qBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,mBAAO,MAAM,SACX,KAAK,OACL,QACA,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,cACtC;cACE,MAAM,EAAE,SAAS,OAAO,QAAO;cAC/B,SAAS,KAAK;cACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;aAC5B;UAEL,CAAC;iBACM,OAAO;AACd,cAAI,YAAY,KAAK,GAAG;AACtB,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,gBAAM;;MAEV,CAAC;IACH;;;;IAKQ,MAAM,oBACZ,QAAmC;AAKnC,YAAM,EAAE,MAAM,eAAe,OAAO,eAAc,IAAK,MAAM,KAAK,WAAW;QAC3E,UAAU,OAAO;OAClB;AACD,UAAI,gBAAgB;AAClB,eAAO,EAAE,MAAM,MAAM,OAAO,eAAc;;AAG5C,aAAO,MAAM,KAAK,QAAQ;QACxB,UAAU,OAAO;QACjB,aAAa,cAAc;QAC3B,MAAM,OAAO;OACd;IACH;;;;IAKQ,MAAM,eAAY;AAExB,YAAM,EACJ,MAAM,EAAE,KAAI,GACZ,OAAO,UAAS,IACd,MAAM,KAAK,QAAO;AACtB,UAAI,WAAW;AACb,eAAO,EAAE,MAAM,MAAM,OAAO,UAAS;;AAGvC,YAAM,WAAU,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,CAAA;AACjC,YAAM,OAAO,QAAQ,OACnB,CAAC,WAAW,OAAO,gBAAgB,UAAU,OAAO,WAAW,UAAU;AAE3E,YAAM,QAAQ,QAAQ,OACpB,CAAC,WAAW,OAAO,gBAAgB,WAAW,OAAO,WAAW,UAAU;AAG5E,aAAO;QACL,MAAM;UACJ,KAAK;UACL;UACA;;QAEF,OAAO;;IAEX;;;;IAKQ,MAAM,kCAA+B;AAC3C,aAAO,KAAK,aAAa,IAAI,YAAW;AACtC,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EACJ,MAAM,EAAE,QAAO,GACf,OAAO,aAAY,IACjB;AACJ,cAAI,cAAc;AAChB,mBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAE1C,cAAI,CAAC,SAAS;AACZ,mBAAO;cACL,MAAM,EAAE,cAAc,MAAM,WAAW,MAAM,8BAA8B,CAAA,EAAE;cAC7E,OAAO;;;AAIX,gBAAM,EAAE,QAAO,IAAK,UAAU,QAAQ,YAAY;AAElD,cAAI,eAAoD;AAExD,cAAI,QAAQ,KAAK;AACf,2BAAe,QAAQ;;AAGzB,cAAI,YAAiD;AAErD,gBAAM,mBACJ,MAAA,KAAA,QAAQ,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,CAAC,WAAmB,OAAO,WAAW,UAAU,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AAEpF,cAAI,gBAAgB,SAAS,GAAG;AAC9B,wBAAY;;AAGd,gBAAM,+BAA+B,QAAQ,OAAO,CAAA;AAEpD,iBAAO,EAAE,MAAM,EAAE,cAAc,WAAW,6BAA4B,GAAI,OAAO,KAAI;QACvF,CAAC;MACH,CAAC;IACH;IAEQ,MAAM,SAAS,KAAa,OAAwB,EAAE,MAAM,CAAA,EAAE,GAAE;AAEtE,UAAI,MAAM,KAAK,KAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,GAAG;AACjD,UAAI,KAAK;AACP,eAAO;;AAGT,YAAM,MAAM,KAAK,IAAG;AAGpB,YAAM,KAAK,KAAK,KAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAGlD,UAAI,OAAO,KAAK,iBAAiB,WAAW,KAAK;AAC/C,eAAO;;AAGT,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,0BAA0B;QAC7F,SAAS,KAAK;OACf;AACD,UAAI,OAAO;AACT,cAAM;;AAER,UAAI,CAAC,KAAK,QAAQ,KAAK,KAAK,WAAW,GAAG;AACxC,eAAO;;AAGT,WAAK,OAAO;AACZ,WAAK,iBAAiB;AAGtB,YAAM,KAAK,KAAK,KAAK,CAAC,QAAa,IAAI,QAAQ,GAAG;AAClD,UAAI,CAAC,KAAK;AACR,eAAO;;AAET,aAAO;IACT;;;;;;;;;;;;;;;;;IAkBA,MAAM,UACJ,KACA,UAWI,CAAA,GAAE;AASN,UAAI;AACF,YAAI,QAAQ;AACZ,YAAI,CAAC,OAAO;AACV,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,WAAU;AAC7C,cAAI,SAAS,CAAC,KAAK,SAAS;AAC1B,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,kBAAQ,KAAK,QAAQ;;AAGvB,cAAM,EACJ,QACA,SACA,WACA,KAAK,EAAE,QAAQ,WAAW,SAAS,WAAU,EAAE,IAC7C,UAAU,KAAK;AAEnB,YAAI,EAAC,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAc;AAE1B,sBAAY,QAAQ,GAAG;;AAGzB,cAAM,aACJ,CAAC,OAAO,OACR,OAAO,IAAI,WAAW,IAAI,KAC1B,CAAC,OAAO,OACR,EAAE,YAAY,cAAc,YAAY,WAAW,UAC/C,OACA,MAAM,KAAK,SAAS,OAAO,MAAK,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAO,EAAE,MAAM,QAAQ,KAAI,IAAK,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAG5F,YAAI,CAAC,YAAY;AACf,gBAAM,EAAE,MAAK,IAAK,MAAM,KAAK,QAAQ,KAAK;AAC1C,cAAI,OAAO;AACT,kBAAM;;AAGR,iBAAO;YACL,MAAM;cACJ,QAAQ;cACR;cACA;;YAEF,OAAO;;;AAIX,cAAM,YAAY,aAAa,OAAO,GAAG;AAGzC,cAAM,YAAY,MAAM,OAAO,OAAO,UAAU,OAAO,YAAY,WAAW,MAAM;UAClF;SACD;AAGD,cAAM,UAAU,MAAM,OAAO,OAAO,OAClC,WACA,WACA,WACA,mBAAmB,GAAG,SAAS,IAAI,UAAU,EAAE,CAAC;AAGlD,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,oBAAoB,uBAAuB;;AAIvD,eAAO;UACL,MAAM;YACJ,QAAQ;YACR;YACA;;UAEF,OAAO;;eAEF,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV;;AAp6Fe,eAAA,iBAAiB;;;AIpJlC;AAEA,MAAM,eAAe;AAErB,MAAA,uBAAe;;;ACJf;AAEA,MAAM,aAAa;AAEnB,MAAA,qBAAe;;;AdDT,MAAO,qBAAP,cAAkC,mBAAU;IAHlD,OAGkD;;;IAChD,YAAY,SAAkC;AAC5C,YAAM,OAAO;IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AxBwBF,MAAqB,iBAArB,MAAmC;WAAA;;;;;;;;;;;;;;;;IA4CjC,YACY,aACA,aACV,SAA2C;;AAFjC,WAAA,cAAA;AACA,WAAA,cAAA;AAGV,UAAI,CAAC;AAAa,cAAM,IAAI,MAAM,0BAA0B;AAC5D,UAAI,CAAC;AAAa,cAAM,IAAI,MAAM,0BAA0B;AAE5D,YAAM,eAAe,oBAAoB,WAAW;AACpD,YAAM,UAAU,IAAI,IAAI,YAAY;AAEpC,WAAK,cAAc,IAAI,IAAI,eAAe,OAAO;AACjD,WAAK,YAAY,WAAW,KAAK,YAAY,SAAS,QAAQ,QAAQ,IAAI;AAC1E,WAAK,UAAU,IAAI,IAAI,WAAW,OAAO;AACzC,WAAK,aAAa,IAAI,IAAI,cAAc,OAAO;AAC/C,WAAK,eAAe,IAAI,IAAI,gBAAgB,OAAO;AAGnD,YAAM,oBAAoB,MAAM,QAAQ,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AAC9D,YAAM,WAAW;QACf,IAAI;QACJ,UAAU;QACV,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,oBAAoB,GAAA,EAAE,YAAY,kBAAiB,CAAA;QAC9D,QAAQ;;AAGV,YAAM,WAAW,qBAAqB,YAAO,QAAP,YAAO,SAAP,UAAW,CAAA,GAAI,QAAQ;AAE7D,WAAK,cAAa,KAAA,SAAS,KAAK,gBAAU,QAAA,OAAA,SAAA,KAAI;AAC9C,WAAK,WAAU,KAAA,SAAS,OAAO,aAAO,QAAA,OAAA,SAAA,KAAI,CAAA;AAE1C,UAAI,CAAC,SAAS,aAAa;AACzB,aAAK,OAAO,KAAK,yBACf,KAAA,SAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA,GACjB,KAAK,SACL,SAAS,OAAO,KAAK;aAElB;AACL,aAAK,cAAc,SAAS;AAE5B,aAAK,OAAO,IAAI,MAA0B,CAAA,GAAW;UACnD,KAAK,wBAAC,GAAG,SAAQ;AACf,kBAAM,IAAI,MACR,6GAA6G,OAC3G,IAAI,CACL,kBAAkB;UAEvB,GANK;SAON;;AAGH,WAAK,QAAQ,cAAc,aAAa,KAAK,gBAAgB,KAAK,IAAI,GAAG,SAAS,OAAO,KAAK;AAC9F,WAAK,WAAW,KAAK,oBAAmB,OAAA,OAAA,EACtC,SAAS,KAAK,SACd,aAAa,KAAK,gBAAgB,KAAK,IAAI,EAAC,GACzC,SAAS,QAAQ,CAAA;AAEtB,WAAK,OAAO,IAAI,gBAAgB,IAAI,IAAI,WAAW,OAAO,EAAE,MAAM;QAChE,SAAS,KAAK;QACd,QAAQ,SAAS,GAAG;QACpB,OAAO,KAAK;OACb;AAED,WAAK,UAAU,IAAI,cACjB,KAAK,WAAW,MAChB,KAAK,SACL,KAAK,OACL,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AAGlB,UAAI,CAAC,SAAS,aAAa;AACzB,aAAK,qBAAoB;;IAE7B;;;;IAKA,IAAI,YAAS;AACX,aAAO,IAAI,gBAAgB,KAAK,aAAa,MAAM;QACjD,SAAS,KAAK;QACd,aAAa,KAAK;OACnB;IACH;;;;;;IAeA,KAAK,UAAgB;AACnB,aAAO,KAAK,KAAK,KAAK,QAAQ;IAChC;;;;;;;;;IAUA,OACE,QAAqB;AAMrB,aAAO,KAAK,KAAK,OAAsB,MAAM;IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;IA0BA,IACE,IACA,OAAmB,CAAA,GACnB,UAII,CAAA,GAAE;AAYN,aAAO,KAAK,KAAK,IAAI,IAAI,MAAM,OAAO;IACxC;;;;;;;;IASA,QAAQ,MAAc,OAA+B,EAAE,QAAQ,CAAA,EAAE,GAAE;AACjE,aAAO,KAAK,SAAS,QAAQ,MAAM,IAAI;IACzC;;;;IAKA,cAAW;AACT,aAAO,KAAK,SAAS,YAAW;IAClC;;;;;;;IAQA,cAAc,SAAwB;AACpC,aAAO,KAAK,SAAS,cAAc,OAAO;IAC5C;;;;IAKA,oBAAiB;AACf,aAAO,KAAK,SAAS,kBAAiB;IACxC;IAEc,kBAAe;;;AAC3B,YAAI,KAAK,aAAa;AACpB,iBAAO,MAAM,KAAK,YAAW;;AAG/B,cAAM,EAAE,KAAI,IAAK,MAAM,KAAK,KAAK,WAAU;AAE3C,gBAAO,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;;;IAG/B,wBACN,EACE,kBACA,gBACA,oBACA,SACA,YACA,UACA,MACA,MAAK,GAEP,SACAS,QAAa;AAEb,YAAM,cAAc;QAClB,eAAe,UAAU,KAAK,WAAW;QACzC,QAAQ,GAAG,KAAK,WAAW;;AAE7B,aAAO,IAAI,mBAAmB;QAC5B,KAAK,KAAK,QAAQ;QAClB,SAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,WAAW,GAAK,OAAO;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,OAAAA;;;QAGA,8BAA8B,mBAAmB,KAAK;OACvD;IACH;IAEQ,oBAAoB,SAA8B;AACxD,aAAO,IAAI,eAAe,KAAK,YAAY,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAC1C,OAAO,GAAA,EACV,QAAM,OAAA,OAAO,EAAE,QAAQ,KAAK,YAAW,GAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,MAAM,EAAA,CAAA,CAAA;IAEjE;IAEQ,uBAAoB;AAC1B,UAAI,OAAO,KAAK,KAAK,kBAAkB,CAAC,OAAO,YAAW;AACxD,aAAK,oBAAoB,OAAO,UAAU,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;MACjE,CAAC;AACD,aAAO;IACT;IAEQ,oBACN,OACA,QACA,OAAc;AAEd,WACG,UAAU,qBAAqB,UAAU,gBAC1C,KAAK,uBAAuB,OAC5B;AACA,aAAK,qBAAqB;iBACjB,UAAU,cAAc;AACjC,aAAK,SAAS,QAAO;AACrB,YAAI,UAAU;AAAW,eAAK,KAAK,QAAO;AAC1C,aAAK,qBAAqB;;IAE9B;;;;ADpUK,MAAM,eAAe,wBAS1B,aACA,aACA,YACgD;AAChD,WAAO,IAAI,eAA6C,aAAa,aAAa,OAAO;EAC3F,GAd4B;AAiB5B,WAAS,+BAA4B;AACnC,QACE,OAAO,WAAW,eAClB,OAAO,YAAY,eACnB,QAAQ,YAAY,UACpB,QAAQ,YAAY,MACpB;AACA,aAAO;;AAGT,UAAM,eAAe,QAAQ,QAAQ,MAAM,WAAW;AACtD,QAAI,CAAC,cAAc;AACjB,aAAO;;AAGT,UAAM,eAAe,SAAS,aAAa,CAAC,GAAG,EAAE;AACjD,WAAO,gBAAgB;EACzB;AAjBS;AAmBT,MAAI,6BAA4B,GAAI;AAClC,YAAQ,KACN,uPAEmF;;;;AD1DvF,MAAM,eAAe;AACrB,MAAM,oBACJ;AAGK,MAAM,WAAW,aAAa,cAAc,iBAAiB;AAG7D,MAAM,aAAa;AAGnB,MAAM,cAAc;AAAA;AAAA,IAEzB,IAAI;AAAA;AAAA,IAGJ,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,UAAU;AAAA;AAAA,IAGV,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,IAGZ,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,EACvB;AAGO,WAAS,yBAAyB;AACvC,QAAI,iBAAiB,uBAAuB,sBAAsB,0BAA0B;AAC1F,cAAQ;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AARgB;AAWhB,MAAO,mBAAQ;;;AFxCR,MAAM,uBAAN,MAA2B;AAAA,IAXlC,OAWkC;AAAA;AAAA;AAAA,IAChC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,YAAY;AACjB,WAAK,YAAY,CAAC;AAClB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA,IACpB;AAAA,IAEA,MAAM,OAAO;AACX,UAAI,KAAK,cAAe;AAExB,UAAI;AAEF,aAAK,YACH,OAAO,SAAS,aAAa,eAC7B,OAAO,SAAS,aAAa,eAC7B,OAAO,SAAS,OAAO,SAAS,YAAY;AAG9C,cAAM,mBAAmB,yBAAyB;AAClD,YAAI,CAAC,iBAAiB,SAAS;AAC7B,kBAAQ,KAAK,iDAAuC,iBAAiB,OAAO;AAC5E;AAAA,QACF;AAGA,cAAM,cAAc,MAAM,cAAc,KAAK;AAC7C,YAAI,CAAC,aAAa;AAChB,kBAAQ,MAAM,4CAAuC;AACrD;AAAA,QACF;AAGA,aAAK,sBAAsB;AAG3B,aAAK,aAAa;AAElB,aAAK,gBAAgB;AAErB,YAAI,KAAK,WAAW;AAElB,kBAAQ,IAAI,2CAAsC;AAAA,QACpD;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,8CAAyC,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,wBAAwB;AAEtB,YAAM,eAAe,SAClB,QAAQ,6BAA6B,EACrC;AAAA,QACC;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,CAAC,YAAY;AACX,cAAI,KAAK,WAAW;AAElB,oBAAQ,IAAI,sCAA+B,QAAQ,GAAG;AAAA,UACxD;AACA,eAAK,aAAa,QAAQ,GAAG;AAAA,QAC/B;AAAA,MACF,EACC,UAAU;AAEb,UAAI,KAAK,WAAW;AAElB,gBAAQ,IAAI,0DAAmD,UAAU;AAAA,MAC3E;AAGA,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa,YAAY;AACvB,YAAM,WAAW;AAAA,QACf,IAAI,WAAW;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,QAAQ;AAAA,MACV;AAEA,WAAK,UAAU,KAAK,QAAQ;AAE5B,UAAI,KAAK,WAAW;AAElB,gBAAQ,IAAI,yCAAkC,SAAS,EAAE;AAAA,MAC3D;AAGA,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,eAAe;AACnB,UAAI,KAAK,gBAAgB,KAAK,UAAU,WAAW,EAAG;AAEtD,WAAK,eAAe;AAEpB,UAAI;AACF,eAAO,KAAK,UAAU,SAAS,GAAG;AAChC,gBAAM,OAAO,KAAK,UAAU,CAAC;AAC7B,gBAAM,UAAU,MAAM,KAAK,iBAAiB,IAAI;AAEhD,cAAI,SAAS;AAEX,iBAAK,UAAU,MAAM;AACrB,gBAAI,KAAK,WAAW;AAElB,sBAAQ,IAAI,6CAAwC,KAAK,EAAE;AAAA,YAC7D;AAAA,UACF,OAAO;AAEL,iBAAK,YAAY;AACjB,gBAAI,KAAK,YAAY,KAAK,eAAe;AAEvC,mBAAK,UAAU,MAAM;AACrB,sBAAQ,MAAM,wCAAmC,KAAK,EAAE;AAGxD,mBAAK,cAAc,IAAI;AAAA,YACzB,OAAO;AAEL,kBAAI,KAAK,WAAW;AAElB,wBAAQ;AAAA,kBACN,+BAAwB,KAAK,EAAE,aAAa,KAAK,QAAQ,IAAI,KAAK,aAAa;AAAA,gBACjF;AAAA,cACF;AACA,oBAAM,KAAK,MAAM,KAAK,UAAU;AAAA,YAClC;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAkC,KAAK;AAAA,MACvD,UAAE;AACA,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,iBAAiB,UAAU;AAC/B,UAAI;AACF,cAAM,EAAE,MAAM,WAAW,IAAI;AAG7B,cAAM,iBAAiB,KAAK,2BAA2B,UAAU;AAGjE,cAAM,SAAS,MAAM,cAAc;AAAA,UACjC,kBAAkB,QAAQ;AAAA,UAC1B;AAAA,QACF;AAEA,YAAI,OAAO,YAAY,OAAO;AAE5B,gBAAM,KAAK,+BAA+B,WAAW,IAAI,OAAO,EAAE;AAClE,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAkC,KAAK;AACrD,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,2BAA2B,YAAY;AAErC,YAAM,iBAAiB;AAAA,QACrB,MAAM,iBAAiB,WAAW,EAAE;AAAA;AAAA,QACpC,gBAAgB,WAAW;AAAA,QAC3B,eAAe,WAAW;AAAA,QAC1B,sBAAsB,KAAK,UAAU,WAAW,iBAAiB;AAAA,QACjE,aAAa,KAAK,UAAU,WAAW,QAAQ;AAAA,QAC/C,kBAAkB,WAAW;AAAA,QAC7B,uBAAuB,WAAW;AAAA,QAClC,wBAAwB,WAAW;AAAA,QACnC,mBAAmB,WAAW;AAAA,QAC9B,eAAe,WAAW;AAAA,QAC1B,eAAe,WAAW;AAAA,QAC1B,iBAAiB,WAAW;AAAA,MAC9B;AAGA,qBAAe,YAAY;AAC3B,qBAAe,YAAY;AAG3B,UAAI,WAAW,qBAAqB,MAAM,QAAQ,WAAW,iBAAiB,GAAG;AAC/E,cAAM,aAAa,WAAW,kBAAkB,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,OAAO,OAAO;AAC3F,cAAM,mBAAmB,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC;AAChD,uBAAe,6BAA6B,iBAAiB,KAAK,IAAI;AACtE,uBAAe,+BAA+B,WAAW,kBAAkB;AAAA,MAC7E;AAGA,UAAI,WAAW,YAAY,OAAO,WAAW,aAAa,UAAU;AAClE,cAAM,cAAc,OAAO,OAAO,WAAW,QAAQ;AACrD,cAAM,aAAa,YAAY;AAC/B,cAAM,iBAAiB,YAAY,OAAO,CAAC,SAAS,KAAK,QAAQ,CAAC,EAAE;AAEpE,uBAAe,0BAA0B;AACzC,uBAAe,qBAAqB;AACpC,uBAAe,wBACb,aAAa,IAAK,iBAAiB,aAAc,MAAM;AAAA,MAC3D;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,+BAA+B,YAAY,cAAc;AAC7D,UAAI;AACF,cAAM,EAAE,MAAM,IAAI,MAAM,SACrB,KAAK,UAAU,EACf,OAAO;AAAA,UACN,eAAe;AAAA,UACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UAClC,aAAa;AAAA,QACf,CAAC,EACA,GAAG,MAAM,UAAU;AAEtB,YAAI,OAAO;AACT,kBAAQ,MAAM,sDAAiD,KAAK;AAAA,QACtE,WAAW,KAAK,WAAW;AAEzB,kBAAQ,IAAI,sDAAiD,YAAY;AAAA,QAC3E;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,0CAAqC,KAAK;AAAA,MAC1D;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,cAAc,UAAU;AAC5B,UAAI;AAEF,cAAM,SACH,KAAK,UAAU,EACf,OAAO;AAAA,UACN,aAAa;AAAA,UACb,YAAY,gBAAgB,SAAS,QAAQ;AAAA,UAC7C,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,QAC5C,CAAC,EACA,GAAG,MAAM,SAAS,KAAK,EAAE;AAE5B,gBAAQ,MAAM,gDAA2C,SAAS,EAAE;AAAA,MACtE,SAAS,OAAO;AACd,gBAAQ,MAAM,qCAAgC,KAAK;AAAA,MACrD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,WAAW,cAAc;AAC7B,UAAI;AAEF,cAAM,EAAE,MAAM,YAAY,MAAM,IAAI,MAAM,SACvC,KAAK,UAAU,EACf,OAAO,GAAG,EACV,GAAG,MAAM,YAAY,EACrB,OAAO;AAEV,YAAI,SAAS,CAAC,YAAY;AACxB,kBAAQ,MAAM,gCAA2B,YAAY;AACrD,iBAAO;AAAA,QACT;AAGA,cAAM,WAAW;AAAA,UACf,IAAI,WAAW;AAAA,UACf,MAAM;AAAA,UACN,UAAU;AAAA,UACV,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UAClC,QAAQ;AAAA,QACV;AAEA,cAAM,UAAU,MAAM,KAAK,iBAAiB,QAAQ;AAEpD,YAAI,SAAS;AAEX,kBAAQ,IAAI,kCAA6B,YAAY;AAAA,QACvD,OAAO;AACL,kBAAQ,MAAM,8BAAyB,YAAY;AAAA,QACrD;AAEA,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAA2B,KAAK;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,gBAAgB;AACpB,UAAI;AACF,cAAM,EAAE,MAAM,aAAa,MAAM,IAAI,MAAM,SACxC,KAAK,UAAU,EACf,OAAO,uDAAuD,EAC9D,MAAM,cAAc,EAAE,WAAW,MAAM,CAAC,EACxC,MAAM,GAAG;AAEZ,YAAI,OAAO;AACT,kBAAQ,MAAM,qCAAgC,KAAK;AACnD,iBAAO;AAAA,QACT;AAEA,cAAM,UAAU;AAAA,UACd,OAAO,YAAY;AAAA,UACnB,QAAQ,YAAY,OAAO,CAAC,MAAM,EAAE,gBAAgB,QAAQ,EAAE;AAAA,UAC9D,SAAS,YAAY,OAAO,CAAC,MAAM,CAAC,EAAE,eAAe,EAAE,gBAAgB,SAAS,EAAE;AAAA,UAClF,QAAQ,YAAY,OAAO,CAAC,MAAM,EAAE,gBAAgB,QAAQ,EAAE;AAAA,UAC9D,aAAa,KAAK,UAAU;AAAA,UAC5B,cAAc,KAAK;AAAA,UACnB;AAAA,QACF;AAEA,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,qCAAgC,KAAK;AACnD,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,mBAAmB;AACvB,UAAI;AACF,cAAM,EAAE,MAAM,mBAAmB,MAAM,IAAI,MAAM,SAC9C,KAAK,UAAU,EACf,OAAO,GAAG,EACV,GAAG,eAAe,QAAQ;AAE7B,YAAI,OAAO;AACT,kBAAQ,MAAM,4CAAuC,KAAK;AAC1D,iBAAO;AAAA,QACT;AAEA,YAAI,aAAa;AACjB,mBAAW,cAAc,mBAAmB;AAE1C,gBAAM,SAAS,KAAK,UAAU,EAAE,OAAO,EAAE,aAAa,UAAU,CAAC,EAAE,GAAG,MAAM,WAAW,EAAE;AAGzF,eAAK,aAAa,UAAU;AAC5B,wBAAc;AAAA,QAChB;AAGA,gBAAQ,IAAI,oBAAa,UAAU,+BAA+B;AAClE,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAkC,KAAK;AACrD,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,IAAI;AACR,aAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,IACzD;AAAA;AAAA;AAAA;AAAA,IAKA,UAAU;AACR,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa,YAAY;AAAA,MAChC;AACA,WAAK,YAAY,CAAC;AAClB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,YAAY;AACV,aAAO;AAAA,QACL,eAAe,KAAK;AAAA,QACpB,WAAW,KAAK;AAAA,QAChB,aAAa,KAAK,UAAU;AAAA,QAC5B,cAAc,KAAK;AAAA,QACnB,eAAe,KAAK;AAAA,QACpB,YAAY,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF;", "names": ["Headers", "Response", "fetch", "PostgrestError", "PostgrestBuilder", "res", "PostgrestTransformBuilder", "PostgrestFilterBuilder", "PostgrestQueryBuilder", "fetch", "head", "PostgrestClient", "fetch", "head", "get", "fetch", "FunctionRegion", "index", "WebSocket", "SOCKET_STATES", "CHANNEL_STATES", "CHANNEL_EVENTS", "TRANSPORTS", "CONNECTION_STATE", "PostgresTypes", "REALTIME_PRESENCE_LISTEN_EVENTS", "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "REALTIME_LISTEN_TYPES", "REALTIME_SUBSCRIBE_STATES", "_a", "_b", "type", "noop", "fetch", "WebSocket", "resolveFetch", "fetch", "__awaiter", "__awaiter", "fetch", "resolveFetch", "version", "version", "fetch", "resolveFetch", "fetch", "version", "DEFAULT_HEADERS", "version", "resolveFetch", "Headers", "fetch", "__awaiter", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "DEFAULT_GLOBAL_OPTIONS", "__awaiter", "version", "DEFAULT_HEADERS", "version", "uuid", "<PERSON><PERSON><PERSON><PERSON>", "resolveFetch", "fetch", "_getErrorMessage", "handleError", "_getRequestParams", "_handleRequest", "fetch", "resolveFetch", "__rest", "DEFAULT_HEADERS", "<PERSON><PERSON><PERSON><PERSON>", "resolveFetch", "version", "result", "expiresAt", "error", "data", "uuid", "_a", "fetch"]}