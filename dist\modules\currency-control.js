"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/event-coordinator.js
  var EventCoordinator = class {
    static {
      __name(this, "EventCoordinator");
    }
    constructor() {
      this.input = null;
      this.listeners = /* @__PURE__ */ new Map();
      this.isProcessing = false;
      this.eventQueue = [];
      this.boundHandlers = /* @__PURE__ */ new Map();
      this.isDestroyed = false;
      this.init();
    }
    init() {
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.findAndSetupInput());
      } else {
        this.findAndSetupInput();
      }
    }
    findAndSetupInput() {
      this.input = document.querySelector('[is-main="true"]');
      if (this.input && !this.isDestroyed) {
        this.setupMainListeners();
      }
    }
    setupMainListeners() {
      if (!this.input || this.boundHandlers.has("main")) return;
      const inputHandler = /* @__PURE__ */ __name((e) => this.handleInputEvent(e), "inputHandler");
      const focusHandler = /* @__PURE__ */ __name((e) => this.processFocusEvent(e), "focusHandler");
      const blurHandler = /* @__PURE__ */ __name((e) => this.processBlurEvent(e), "blurHandler");
      const changeHandler = /* @__PURE__ */ __name((e) => this.processChangeEvent(e), "changeHandler");
      this.boundHandlers.set("main", {
        input: inputHandler,
        focus: focusHandler,
        blur: blurHandler,
        change: changeHandler
      });
      this.input.addEventListener("input", inputHandler, { passive: true });
      this.input.addEventListener("focus", focusHandler, { passive: true });
      this.input.addEventListener("blur", blurHandler, { passive: true });
      this.input.addEventListener("change", changeHandler, { passive: true });
    }
    handleInputEvent(e) {
      if (this.isProcessing || this.isDestroyed) {
        return;
      }
      this.isProcessing = true;
      requestAnimationFrame(() => {
        this.processInputEvent(e);
        this.isProcessing = false;
        if (this.eventQueue.length > 0) {
          const nextEvent = this.eventQueue.shift();
          requestAnimationFrame(() => this.handleInputEvent(nextEvent));
        }
      });
    }
    // Registra um listener para um módulo específico
    registerListener(moduleId, eventType, callback) {
      if (this.isDestroyed) return;
      const key = `${moduleId}_${eventType}`;
      this.unregisterListener(moduleId, eventType);
      if (!this.listeners.has(key)) {
        this.listeners.set(key, []);
      }
      this.listeners.get(key).push(callback);
    }
    // Remove listener de um módulo
    unregisterListener(moduleId, eventType, specificCallback = null) {
      const key = `${moduleId}_${eventType}`;
      if (this.listeners.has(key)) {
        if (specificCallback) {
          const callbacks = this.listeners.get(key);
          const index = callbacks.indexOf(specificCallback);
          if (index > -1) {
            callbacks.splice(index, 1);
          }
        } else {
          this.listeners.delete(key);
        }
      }
    }
    // Remove todos os listeners de um módulo
    unregisterModule(moduleId) {
      const keysToRemove = [];
      for (const key of this.listeners.keys()) {
        if (key.startsWith(`${moduleId}_`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => this.listeners.delete(key));
    }
    processInputEvent(e) {
      if (this.isDestroyed) return;
      const inputCallbacks = this.getCallbacksForEvent("input");
      const priorityOrder = ["currency-formatting", "motion-animation", "patrimony-sync"];
      for (const moduleId of priorityOrder) {
        const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);
        for (const callbackInfo of moduleCallbacks) {
          try {
            callbackInfo.callback(e);
          } catch (error) {
            console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);
          }
        }
      }
    }
    processFocusEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("focus", e);
    }
    processBlurEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("blur", e);
    }
    processChangeEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("change", e);
    }
    executeCallbacksForEvent(eventType, e) {
      const callbacks = this.getCallbacksForEvent(eventType);
      callbacks.forEach(({ callback, moduleId }) => {
        try {
          callback(e);
        } catch (error) {
          console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);
        }
      });
    }
    getCallbacksForEvent(eventType) {
      const callbacks = [];
      for (const [key, callbackList] of this.listeners.entries()) {
        if (key.endsWith(`_${eventType}`)) {
          const moduleId = key.replace(`_${eventType}`, "");
          callbackList.forEach((callback) => {
            callbacks.push({ moduleId, callback });
          });
        }
      }
      return callbacks;
    }
    // Método para disparar eventos programaticamente
    dispatchInputEvent(sourceModule = "unknown") {
      if (this.isProcessing || this.isDestroyed || !this.input) {
        return;
      }
      const event = new Event("input", { bubbles: true });
      event.sourceModule = sourceModule;
      this.input.dispatchEvent(event);
    }
    // Método para atualizar valor sem disparar eventos
    setSilentValue(value) {
      if (this.isDestroyed || !this.input) return;
      this.isProcessing = true;
      this.input.value = value;
      requestAnimationFrame(() => {
        this.isProcessing = false;
      });
    }
    // Getter para o valor atual
    getValue() {
      return this.input ? this.input.value : "";
    }
    // Setter que dispara eventos controlados
    setValue(value, sourceModule = "unknown") {
      if (this.isDestroyed || !this.input) return;
      this.input.value = value;
      this.dispatchInputEvent(sourceModule);
    }
    // Método de cleanup para prevenir memory leaks
    destroy() {
      this.isDestroyed = true;
      if (this.input && this.boundHandlers.has("main")) {
        const handlers = this.boundHandlers.get("main");
        this.input.removeEventListener("input", handlers.input);
        this.input.removeEventListener("focus", handlers.focus);
        this.input.removeEventListener("blur", handlers.blur);
        this.input.removeEventListener("change", handlers.change);
      }
      this.listeners.clear();
      this.boundHandlers.clear();
      this.eventQueue.length = 0;
      this.input = null;
      this.isProcessing = false;
    }
    // Método para reinicializar se necessário
    reinitialize() {
      this.destroy();
      this.isDestroyed = false;
      this.init();
    }
  };
  var eventCoordinator = new EventCoordinator();
  window.addEventListener("beforeunload", () => {
    eventCoordinator.destroy();
  });

  // src/modules/currency-control.js
  var CurrencyControlSystem = class {
    static {
      __name(this, "CurrencyControlSystem");
    }
    constructor() {
      this.isInitialized = false;
    }
    init() {
      if (this.isInitialized) {
        return;
      }
      document.addEventListener("DOMContentLoaded", () => {
        this.initializeCurrencyControls();
      });
      this.isInitialized = true;
    }
    initializeCurrencyControls() {
      const input = document.querySelector('[is-main="true"]');
      if (!input) return;
      const getIncrement = /* @__PURE__ */ __name((value) => {
        if (value < 1e3) return 100;
        if (value < 1e4) return 1e3;
        if (value < 1e5) return 1e4;
        if (value < 1e6) return 5e4;
        return 1e5;
      }, "getIncrement");
      const updateValue = /* @__PURE__ */ __name((newValue) => {
        const formattedValue = new Intl.NumberFormat("pt-BR", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(newValue);
        eventCoordinator.setValue(formattedValue, "currency-control");
      }, "updateValue");
      document.querySelectorAll('[currency-control="decrease"]').forEach((btn) => {
        btn.addEventListener("click", (e) => {
          e.preventDefault();
          const current = parseFloat(input.value.replace(/[^\d,]/g, "").replace(",", ".")) || 0;
          updateValue(Math.max(0, current - getIncrement(current)));
        });
      });
      document.querySelectorAll('[currency-control="increase"]').forEach((btn) => {
        btn.addEventListener("click", (e) => {
          e.preventDefault();
          const current = parseFloat(input.value.replace(/[^\d,]/g, "").replace(",", ".")) || 0;
          updateValue(current + getIncrement(current));
        });
      });
    }
  };
})();
//# sourceMappingURL=currency-control.js.map
