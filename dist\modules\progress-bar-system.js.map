{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/progress-bar-system.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Step Navigation and Progress Bar System\r\n * Manages step navigation, validation, and progress bar interactions\r\n */\r\n\r\nexport class StepNavigationProgressSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.progressBar = null;\r\n    this.sectionIndicators = [];\r\n    this.currentStep = 0;\r\n    this.sectionCache = new Map();\r\n    this.startTime = Date.now();\r\n\r\n    // Step definitions\r\n    this.steps = [\r\n      {\r\n        id: '_0-home-section-calc-intro',\r\n        name: 'intro',\r\n        title: 'Introdução',\r\n        validator: () => this.validateIntroStep(),\r\n      },\r\n      {\r\n        id: '_1-section-calc-money',\r\n        name: 'money',\r\n        title: 'Renda',\r\n        validator: () => this.validateMoneyStep(),\r\n      },\r\n      {\r\n        id: '_2-section-calc-ativos',\r\n        name: 'assets',\r\n        title: 'Ativos',\r\n        validator: () => this.validateAssetsStep(),\r\n      },\r\n      {\r\n        id: '_3-section-patrimonio-alocation',\r\n        name: 'allocation',\r\n        title: 'Alocação',\r\n        validator: () => this.validateAllocationStep(),\r\n      },\r\n      {\r\n        id: '_4-section-resultado',\r\n        name: 'results',\r\n        title: 'Resultados',\r\n        validator: () => true, // Results section is always valid\r\n      },\r\n    ];\r\n\r\n    // Device capabilities detection\r\n    this.supportsAnimations = !window.matchMedia('(prefers-reduced-motion: reduce)').matches;\r\n    this.isTouch = 'ontouchstart' in window;\r\n    this.isMobile = window.innerWidth < 768;\r\n\r\n    this.config = {\r\n      enableLogging: false,\r\n      animationDuration: 300,\r\n    };\r\n\r\n    // Validation function with debounce\r\n    this.debouncedValidation = this.debounce(() => {\r\n      this.updateNavigationState();\r\n    }, 300);\r\n  }\r\n\r\n  async init(config = {}) {\r\n    if (this.isInitialized) {\r\n      console.warn('StepNavigationProgressSystem já foi inicializado');\r\n      return;\r\n    }\r\n\r\n    this.config = { ...this.config, ...config };\r\n\r\n    try {\r\n      await this.waitForDOM();\r\n      await this.cacheSections();\r\n      this.cacheElements();\r\n      this.setupSections();\r\n      this.setupEventListeners();\r\n      this.setupSectionIndicatorNavigation();\r\n      this.setupValidation();\r\n      this.setupInitialState();\r\n\r\n      // Mostra primeira seção\r\n      this.showStep(0);\r\n\r\n      this.isInitialized = true;\r\n\r\n      // Escuta mudanças de orientação/resize\r\n      window.addEventListener(\r\n        'resize',\r\n        this.debounce(() => {\r\n          this.isMobile = window.innerWidth < 768;\r\n          this.reinitializeSections();\r\n        }, 250)\r\n      );\r\n\r\n      // Notifica estado inicial após inicialização completa\r\n      setTimeout(() => {\r\n        this.notifyStateChange();\r\n      }, 50);\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn('✅ StepNavigationProgressSystem inicializado');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Erro ao inicializar StepNavigationProgressSystem:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  waitForDOM() {\r\n    return new Promise((resolve) => {\r\n      if (document.readyState === 'loading') {\r\n        document.addEventListener('DOMContentLoaded', resolve);\r\n      } else {\r\n        resolve();\r\n      }\r\n    });\r\n  }\r\n\r\n  async cacheSections() {\r\n    for (const step of this.steps) {\r\n      const section = document.querySelector(`.${step.id}`);\r\n      if (section) {\r\n        this.sectionCache.set(step.id, section);\r\n      }\r\n    }\r\n  }\r\n\r\n  setupSections() {\r\n    this.steps.forEach((step, index) => {\r\n      const section = this.sectionCache.get(step.id);\r\n      if (section) {\r\n        this.setupSimpleSection(section, index);\r\n      }\r\n    });\r\n  }\r\n\r\n  setupSimpleSection(section, index) {\r\n    section.style.display = index === 0 ? 'block' : 'none';\r\n  }\r\n\r\n  setupEventListeners() {\r\n    // Setup listeners for navigation buttons\r\n    document.addEventListener('click', (event) => {\r\n      if (event.target.matches('[element-function=\"next\"]')) {\r\n        event.preventDefault();\r\n        this.nextStep();\r\n      }\r\n\r\n      if (event.target.matches('.step-btn.prev-btn, [element-function=\"prev\"]')) {\r\n        event.preventDefault();\r\n        this.previousStep();\r\n      }\r\n    });\r\n  }\r\n\r\n  setupValidation() {\r\n    this.setupRealtimeValidation();\r\n  }\r\n\r\n  setupSectionIndicatorNavigation() {\r\n    // Adiciona event listeners para todos os section-indicator\r\n    const sectionIndicators = document.querySelectorAll('.section-indicator');\r\n\r\n    sectionIndicators.forEach((indicator) => {\r\n      indicator.addEventListener('click', (event) => {\r\n        event.preventDefault();\r\n\r\n        // Usa o número dentro do number-indicator para identificar o step\r\n        const numberIndicator = indicator.querySelector('.number-indicator div');\r\n        const stepNumber = numberIndicator ? parseInt(numberIndicator.textContent) : null;\r\n        // Mapeamento correto: número 1 = step 1 (seção _1-section-calc-money)\r\n        // número 2 = step 2, etc. Mas número 1 não é step 0!\r\n        const targetStep = stepNumber ? stepNumber : null;\r\n        const hasPointer = indicator.classList.contains('pointer');\r\n\r\n        if (this.config.enableLogging) {\r\n          console.warn(\r\n            `🖱️ Clique no section-indicator: número ${stepNumber}, target ${targetStep}, hasPointer: ${hasPointer}, currentStep: ${this.currentStep}`\r\n          );\r\n        }\r\n\r\n        // Verifica se o indicador tem a classe pointer (só navega se tiver)\r\n        if (hasPointer && targetStep !== null) {\r\n          if (targetStep >= 1 && targetStep <= this.steps.length) {\r\n            this.goToStepFromIndicator(targetStep);\r\n          }\r\n        } else {\r\n          if (this.config.enableLogging) {\r\n            console.warn('❌ Navegação bloqueada - indicador sem classe pointer ou step inválido');\r\n          }\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  setupRealtimeValidation() {\r\n    // Currency input validation\r\n    document.addEventListener('input', (event) => {\r\n      if (event.target.matches('#currency, .currency-input')) {\r\n        this.debouncedValidation();\r\n      }\r\n    });\r\n\r\n    // Asset allocation validation\r\n    document.addEventListener('input', (event) => {\r\n      if (event.target.matches('[data-allocation]')) {\r\n        this.debouncedValidation();\r\n      }\r\n    });\r\n  }\r\n\r\n  cacheElements() {\r\n    // Cache da progress bar principal\r\n    this.progressBar = document.querySelector('.progress-bar');\r\n    if (!this.progressBar) {\r\n      throw new Error('Progress bar não encontrada (.progress-bar)');\r\n    }\r\n\r\n    // Cache dos indicadores de seção\r\n    this.sectionIndicators = Array.from(document.querySelectorAll('.item-section-indicator'));\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn('Progress bar encontrada:', this.progressBar);\r\n      console.warn('Indicadores encontrados:', this.sectionIndicators.length);\r\n    }\r\n  }\r\n\r\n  setupInitialState() {\r\n    // Define o estado inicial\r\n    this.currentStep = 0;\r\n\r\n    // Garante que a progress bar tenha o estado correto inicialmente\r\n    if (this.progressBar) {\r\n      // Remove todas as classes especiais e adiciona first-section\r\n      this.progressBar.classList.remove('first-section', 'aloca-section');\r\n      this.progressBar.classList.add('first-section');\r\n    }\r\n\r\n    // Atualiza indicadores para o estado inicial\r\n    this.updateSectionIndicators(0);\r\n  }\r\n\r\n  /**\r\n   * Atualiza o estado da progress bar baseado no step atual\r\n   * @param {number} stepIndex - Índice do step atual (0-based)\r\n   */\r\n  updateProgressBarState(stepIndex) {\r\n    if (!this.progressBar) return;\r\n\r\n    const previousStep = this.currentStep;\r\n    this.currentStep = stepIndex;\r\n\r\n    // Remove todas as classes especiais primeiro\r\n    this.progressBar.classList.remove('first-section', 'aloca-section');\r\n\r\n    // Aplica a classe apropriada baseada no step atual\r\n    if (stepIndex === 0) {\r\n      // Seção 0: adiciona first-section\r\n      this.progressBar.classList.add('first-section');\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn('🔄 Adicionada classe \"first-section\" à progress bar');\r\n      }\r\n    } else if (stepIndex === 3 || stepIndex === 4) {\r\n      // Seções 3 e 4: adiciona aloca-section\r\n      this.progressBar.classList.add('aloca-section');\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn('🎯 Adicionada classe \"aloca-section\" à progress bar');\r\n      }\r\n    } else {\r\n      // Seções 1 e 2: sem classe especial\r\n      if (this.config.enableLogging) {\r\n        console.warn('🎯 Progress bar sem classe especial (seções 1-2)');\r\n      }\r\n    }\r\n\r\n    // Atualiza indicadores de seção\r\n    this.updateSectionIndicators(stepIndex);\r\n\r\n    // Atualiza classes pointer nos indicadores\r\n    this.updateSectionIndicatorPointers(stepIndex);\r\n\r\n    // Aplica disabled-item nas interactive-main-item após sair do step 0\r\n    this.updateInteractiveMainItems(stepIndex);\r\n\r\n    // Notifica mudança de estado\r\n    this.notifyStateChange(previousStep, stepIndex);\r\n  }\r\n\r\n  /**\r\n   * Atualiza os indicadores visuais das seções\r\n   * @param {number} activeStepIndex - Índice do step ativo\r\n   */\r\n  updateSectionIndicators(activeStepIndex) {\r\n    this.sectionIndicators.forEach((indicator, index) => {\r\n      const sectionMain = indicator.querySelector('[section-main]');\r\n      const sectionIndicator = indicator.querySelector('.section-indicator');\r\n      const numberIndicator = indicator.querySelector('.number-indicator');\r\n\r\n      if (!sectionMain) return;\r\n\r\n      const sectionNumber = parseInt(sectionMain.getAttribute('section-main')) || index + 1;\r\n\r\n      // Comportamento especial para step 0: todos os indicadores ficam ativos\r\n      if (activeStepIndex === 0) {\r\n        // Na seção inicial, todos os indicadores devem ter classe active\r\n        if (sectionIndicator) {\r\n          sectionIndicator.classList.add('active');\r\n          sectionIndicator.classList.remove('completed');\r\n        }\r\n\r\n        if (numberIndicator) {\r\n          numberIndicator.classList.add('active');\r\n          numberIndicator.classList.remove('completed');\r\n        }\r\n\r\n        // Adiciona active no item principal também\r\n        const interactiveItem = indicator.querySelector('.interactive-cards-item');\r\n        if (interactiveItem) {\r\n          interactiveItem.classList.add('active');\r\n          interactiveItem.classList.remove('completed');\r\n        }\r\n      } else {\r\n        // Comportamento normal para steps > 0\r\n        const isActive = activeStepIndex === sectionNumber;\r\n        const isCompleted = activeStepIndex > sectionNumber;\r\n\r\n        // Atualiza classes do indicador\r\n        if (sectionIndicator) {\r\n          sectionIndicator.classList.toggle('active', isActive);\r\n          sectionIndicator.classList.toggle('completed', isCompleted);\r\n        }\r\n\r\n        if (numberIndicator) {\r\n          numberIndicator.classList.toggle('active', isActive);\r\n          numberIndicator.classList.toggle('completed', isCompleted);\r\n        }\r\n\r\n        // Adiciona/remove classes no item principal\r\n        const interactiveItem = indicator.querySelector('.interactive-cards-item');\r\n        if (interactiveItem) {\r\n          interactiveItem.classList.toggle('active', isActive);\r\n          interactiveItem.classList.toggle('completed', isCompleted);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Atualiza as classes pointer nos section-indicator baseado no step atual\r\n   * @param {number} activeStepIndex - Índice do step ativo\r\n   */\r\n  updateSectionIndicatorPointers(activeStepIndex) {\r\n    const sectionIndicators = document.querySelectorAll('.section-indicator');\r\n\r\n    sectionIndicators.forEach((indicator) => {\r\n      if (activeStepIndex > 0) {\r\n        // Quando sair da seção 0, todos os indicadores ganham a classe pointer\r\n        indicator.classList.add('pointer');\r\n      } else {\r\n        // Na seção 0, remove a classe pointer de todos\r\n        indicator.classList.remove('pointer');\r\n      }\r\n    });\r\n\r\n    if (this.config.enableLogging) {\r\n      if (activeStepIndex > 0) {\r\n        console.warn('🔗 Adicionada classe \"pointer\" a todos os section-indicator');\r\n      } else {\r\n        console.warn('🔗 Removida classe \"pointer\" de todos os section-indicator');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Atualiza o estado das divs interactive-main-item\r\n   * @param {number} activeStepIndex - Índice do step ativo\r\n   */\r\n  updateInteractiveMainItems(activeStepIndex) {\r\n    const interactiveMainItems = document.querySelectorAll('.interactive-main-item');\r\n\r\n    if (activeStepIndex > 0) {\r\n      // Após sair da seção 0, todas as interactive-main-item ficam disabled\r\n      interactiveMainItems.forEach((item) => {\r\n        item.classList.add('disabled-item');\r\n      });\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn('🔒 Aplicada classe \"disabled-item\" em todas as interactive-main-item');\r\n      }\r\n    } else {\r\n      // Na seção 0, remove disabled-item de todas (exceto as que já tinham originalmente)\r\n      interactiveMainItems.forEach((item) => {\r\n        // Verifica se não é uma das que devem permanecer disabled originalmente\r\n        const shouldStayDisabled = this.shouldKeepOriginalDisabled(item);\r\n\r\n        if (!shouldStayDisabled) {\r\n          item.classList.remove('disabled-item');\r\n        }\r\n      });\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn(\r\n          '🔓 Removida classe \"disabled-item\" das interactive-main-item (exceto originais)'\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verifica se um item deve manter a classe disabled-item originalmente\r\n   * @param {Element} item - Elemento interactive-main-item\r\n   * @returns {boolean}\r\n   */\r\n  shouldKeepOriginalDisabled(item) {\r\n    // Verifica se tem animation-source-wrapper disabled-item dentro\r\n    const animationWrapper = item.querySelector('.animation-source-wrapper.disabled-item');\r\n    return !!animationWrapper;\r\n  }\r\n\r\n  /**\r\n   * Adiciona classe personalizada à progress bar\r\n   * @param {string} className - Nome da classe a ser adicionada\r\n   */\r\n  addProgressBarClass(className) {\r\n    if (this.progressBar) {\r\n      this.progressBar.classList.add(className);\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn(`➕ Adicionada classe \"${className}\" à progress bar`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove classe personalizada da progress bar\r\n   * @param {string} className - Nome da classe a ser removida\r\n   */\r\n  removeProgressBarClass(className) {\r\n    if (this.progressBar) {\r\n      this.progressBar.classList.remove(className);\r\n\r\n      if (this.config.enableLogging) {\r\n        console.warn(`➖ Removida classe \"${className}\" da progress bar`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verifica se a progress bar tem uma classe específica\r\n   * @param {string} className - Nome da classe a verificar\r\n   * @returns {boolean}\r\n   */\r\n  hasProgressBarClass(className) {\r\n    return this.progressBar ? this.progressBar.classList.contains(className) : false;\r\n  }\r\n\r\n  /**\r\n   * Define uma classe condicional baseada no step\r\n   * @param {string} className - Nome da classe\r\n   * @param {number|number[]} steps - Step(s) onde a classe deve estar presente\r\n   */\r\n  setConditionalClass(className, steps) {\r\n    if (!this.progressBar) return;\r\n\r\n    const stepsArray = Array.isArray(steps) ? steps : [steps];\r\n    const shouldHaveClass = stepsArray.includes(this.currentStep);\r\n\r\n    if (shouldHaveClass) {\r\n      this.addProgressBarClass(className);\r\n    } else {\r\n      this.removeProgressBarClass(className);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Obtém informações sobre o estado atual da progress bar\r\n   * @returns {Object}\r\n   */\r\n  getProgressBarState() {\r\n    if (!this.progressBar) return null;\r\n\r\n    return {\r\n      currentStep: this.currentStep,\r\n      classes: Array.from(this.progressBar.classList),\r\n      hasFirstSection: this.hasProgressBarClass('first-section'),\r\n      totalSections: this.sectionIndicators.length,\r\n      element: this.progressBar,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Força uma atualização completa do estado da progress bar\r\n   */\r\n  forceUpdate() {\r\n    if (!this.isInitialized) return;\r\n\r\n    this.updateProgressBarState(this.currentStep);\r\n  }\r\n\r\n  /**\r\n   * Notifica outros sistemas sobre mudanças de estado\r\n   * @param {number} previousStep - Step anterior\r\n   * @param {number} currentStep - Step atual\r\n   */\r\n  notifyStateChange(previousStep, currentStep) {\r\n    // Dispara evento customizado para outros sistemas\r\n    const event = new CustomEvent('progressBarStateChange', {\r\n      detail: {\r\n        previousStep,\r\n        currentStep,\r\n        progressBarState: this.getProgressBarState(),\r\n      },\r\n    });\r\n\r\n    document.dispatchEvent(event);\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn('📡 Progress bar state change notificado:', {\r\n        previousStep,\r\n        currentStep,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Atualiza configuração do sistema\r\n   * @param {Object} newConfig - Nova configuração\r\n   */\r\n  updateConfig(newConfig) {\r\n    this.config = { ...this.config, ...newConfig };\r\n  }\r\n\r\n  /**\r\n   * Método para ser chamado quando o step muda (interface para outros sistemas)\r\n   * @param {number} stepIndex - Novo step index\r\n   */\r\n  onStepChange(stepIndex) {\r\n    this.updateProgressBarState(stepIndex);\r\n  }\r\n\r\n  /**\r\n   * Destrói o sistema e limpa recursos\r\n   */\r\n  destroy() {\r\n    this.progressBar = null;\r\n    this.sectionIndicators = [];\r\n    this.currentStep = 0;\r\n    this.isInitialized = false;\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn('🔄 StepNavigationProgressSystem destruído');\r\n    }\r\n  }\r\n\r\n  // Navigation methods\r\n  async showStep(stepIndex) {\r\n    if (stepIndex < 0 || stepIndex >= this.steps.length) return;\r\n\r\n    const previousStep = this.currentStep;\r\n    this.currentStep = stepIndex;\r\n\r\n    // Salva dados do step anterior\r\n    if (previousStep !== stepIndex) {\r\n      this.saveStepData(previousStep);\r\n    }\r\n\r\n    this.showStepSimple(stepIndex);\r\n    this.updateProgressBarState(stepIndex);\r\n    this.updateAccessibility(stepIndex);\r\n    this.focusManagement(stepIndex);\r\n\r\n    // Notifica o webflow-button system sobre mudança de step\r\n    this.notifyWebflowButtonSystem();\r\n\r\n    // Scroll para o topo\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n\r\n  showStepSimple(stepIndex) {\r\n    this.steps.forEach((step, index) => {\r\n      const section = this.sectionCache.get(step.id);\r\n      if (!section) return;\r\n\r\n      if (index === stepIndex) {\r\n        section.style.display = 'block';\r\n        section.style.opacity = '1';\r\n        section.style.transform = 'translateY(0)';\r\n        section.style.pointerEvents = 'auto';\r\n      } else {\r\n        section.style.display = 'none';\r\n        section.style.opacity = '0';\r\n        section.style.transform = 'translateY(20px)';\r\n        section.style.pointerEvents = 'none';\r\n      }\r\n    });\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.currentStep >= this.steps.length - 1) {\r\n      this.submitForm();\r\n      return;\r\n    }\r\n\r\n    if (this.canProceedToNext()) {\r\n      this.showStep(this.currentStep + 1);\r\n    } else {\r\n      this.showValidationError();\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 0) {\r\n      this.showStep(this.currentStep - 1);\r\n    }\r\n  }\r\n\r\n  goToStep(stepIndex) {\r\n    // Sempre permite voltar para steps anteriores\r\n    if (stepIndex <= this.currentStep) {\r\n      this.showStep(stepIndex);\r\n      // Notifica webflow-button após navegação\r\n      this.notifyWebflowButtonSystem();\r\n      return;\r\n    }\r\n\r\n    // Para avançar, usa a mesma validação do webflow-button system\r\n    if (this.canProceedToNext()) {\r\n      this.showStep(stepIndex);\r\n      // Notifica webflow-button após navegação bem-sucedida\r\n      this.notifyWebflowButtonSystem();\r\n    } else {\r\n      this.showValidationError();\r\n    }\r\n  }\r\n\r\n  goToStepFromIndicator(sectionNumber) {\r\n    // Mapeamento de seção para step:\r\n    // Seção 1 = Step 1 (_1-section-calc-money)\r\n    // Seção 2 = Step 2 (_2-section-calc-ativos)\r\n    // Seção 3 = Step 3 (_3-section-patrimonio-alocation)\r\n    // Seção 4 = Step 4 (não existe ainda, seria resultado)\r\n\r\n    const targetStep = sectionNumber;\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn(`🔄 goToStepFromIndicator: seção ${sectionNumber} -> step ${targetStep}`);\r\n    }\r\n\r\n    // Verifica se o step é válido\r\n    if (targetStep < 0 || targetStep >= this.steps.length) {\r\n      if (this.config.enableLogging) {\r\n        console.warn(`❌ Step ${targetStep} inválido (max: ${this.steps.length - 1})`);\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Sempre permite voltar para steps anteriores ou atual\r\n    if (targetStep <= this.currentStep) {\r\n      this.showStep(targetStep);\r\n      return;\r\n    }\r\n\r\n    // Para avançar, verifica se pode ir para o próximo step\r\n    if (targetStep === this.currentStep + 1) {\r\n      // Usar validação local\r\n      if (this.canProceedToNext()) {\r\n        this.showStep(targetStep);\r\n        // Notifica webflow-button após navegação bem-sucedida\r\n        this.notifyWebflowButtonSystem();\r\n      } else {\r\n        this.showValidationError();\r\n      }\r\n    } else {\r\n      // Não permite pular múltiplos steps de uma vez\r\n      if (this.config.enableLogging) {\r\n        console.warn(`❌ Não é possível pular do step ${this.currentStep} para ${targetStep}`);\r\n      }\r\n      this.showValidationError();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Notifica o webflow-button system sobre mudanças de navegação\r\n   */\r\n  notifyWebflowButtonSystem() {\r\n    const webflowButton = window.ReinoCalculator?.systems?.webflowButton;\r\n\r\n    if (webflowButton && typeof webflowButton.updateNextButtonsState === 'function') {\r\n      // Força atualização do estado dos botões\r\n      setTimeout(() => {\r\n        webflowButton.updateNextButtonsState();\r\n        if (typeof webflowButton.forceUpdateButtons === 'function') {\r\n          webflowButton.forceUpdateButtons();\r\n        }\r\n      }, 50);\r\n    }\r\n  }\r\n\r\n  // Validation methods\r\n  canProceedToNext() {\r\n    // Usar sempre validação local para evitar loop infinito\r\n    return this.steps[this.currentStep]?.validator() || false;\r\n  }\r\n\r\n  validateIntroStep() {\r\n    if (this.config.enableLogging) {\r\n      console.warn('✅ validateIntroStep: sempre true');\r\n    }\r\n    return true;\r\n  }\r\n\r\n  validateMoneyStep() {\r\n    const currencyInput = document.querySelector('#currency, .currency-input[is-main=\"true\"]');\r\n    if (!currencyInput) {\r\n      if (this.config.enableLogging) {\r\n        console.warn('❌ validateMoneyStep: input não encontrado');\r\n      }\r\n      return false;\r\n    }\r\n\r\n    const value = this.parseInputValue(currencyInput.value);\r\n    const isValid = value > 0;\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn(`${isValid ? '✅' : '❌'} validateMoneyStep: valor=${value}, válido=${isValid}`);\r\n    }\r\n\r\n    return isValid;\r\n  }\r\n\r\n  validateAssetsStep() {\r\n    const selectedAssets = document.querySelectorAll('.selected-asset');\r\n    const isValid = selectedAssets.length > 0;\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn(\r\n        `${isValid ? '✅' : '❌'} validateAssetsStep: ${selectedAssets.length} assets selecionados`\r\n      );\r\n    }\r\n\r\n    return isValid;\r\n  }\r\n\r\n  validateAllocationStep() {\r\n    const totalAllocation = this.calculateTotalAllocation();\r\n    const isValid = Math.abs(totalAllocation - 100) < 0.01;\r\n\r\n    if (this.config.enableLogging) {\r\n      console.warn(\r\n        `${isValid ? '✅' : '❌'} validateAllocationStep: total=${totalAllocation}%, válido=${isValid}`\r\n      );\r\n    }\r\n\r\n    return isValid;\r\n  }\r\n\r\n  updateAccessibility(stepIndex) {\r\n    const currentStepData = this.steps[stepIndex];\r\n    if (currentStepData) {\r\n      document.title = `Reino Calculator - ${currentStepData.title}`;\r\n    }\r\n  }\r\n\r\n  focusManagement(stepIndex) {\r\n    setTimeout(() => {\r\n      const section = this.sectionCache.get(this.steps[stepIndex]?.id);\r\n      if (section) {\r\n        const firstInput = section.querySelector('input, button, select, textarea');\r\n        if (firstInput && typeof firstInput.focus === 'function') {\r\n          try {\r\n            firstInput.focus();\r\n          } catch {\r\n            // Ignore focus errors\r\n          }\r\n        }\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  showValidationError() {\r\n    const currentStepData = this.steps[this.currentStep];\r\n    let message = 'Por favor, complete os campos obrigatórios.';\r\n\r\n    if (currentStepData?.name === 'money') {\r\n      message = 'Por favor, insira um valor válido para seu patrimônio.';\r\n    } else if (currentStepData?.name === 'assets') {\r\n      message = 'Por favor, selecione pelo menos um ativo.';\r\n    } else if (currentStepData?.name === 'allocation') {\r\n      message = 'Por favor, aloque 100% do seu patrimônio.';\r\n    }\r\n\r\n    this.showToast(message, 'error');\r\n  }\r\n\r\n  showToast(message, type = 'info') {\r\n    // Simple toast implementation\r\n    const toast = document.createElement('div');\r\n    toast.className = `toast toast-${type}`;\r\n    toast.textContent = message;\r\n    toast.style.cssText = `\r\n      position: fixed;\r\n      top: 20px;\r\n      right: 20px;\r\n      padding: 12px 16px;\r\n      background: ${type === 'error' ? '#f56565' : '#4299e1'};\r\n      color: white;\r\n      border-radius: 8px;\r\n      z-index: 10000;\r\n      animation: slideIn 0.3s ease;\r\n    `;\r\n\r\n    document.body.appendChild(toast);\r\n    setTimeout(() => toast.remove(), 3000);\r\n  }\r\n\r\n  parseInputValue(value) {\r\n    return parseFloat(value?.replace(/[^\\d.,]/g, '').replace(',', '.')) || 0;\r\n  }\r\n\r\n  calculateTotalAllocation() {\r\n    const allocationInputs = document.querySelectorAll('[data-allocation]');\r\n    let total = 0;\r\n\r\n    allocationInputs.forEach((input) => {\r\n      const value = this.parseInputValue(input.value);\r\n      total += value;\r\n    });\r\n\r\n    return total;\r\n  }\r\n\r\n  saveStepData(stepIndex) {\r\n    const stepData = this.collectStepData(stepIndex);\r\n    const stepName = this.steps[stepIndex]?.name;\r\n    if (stepName && stepData) {\r\n      if (typeof localStorage !== 'undefined') {\r\n        localStorage.setItem(`reino_calc_${stepName}`, JSON.stringify(stepData));\r\n      }\r\n    }\r\n  }\r\n\r\n  collectStepData(stepIndex) {\r\n    const section = this.sectionCache.get(this.steps[stepIndex]?.id);\r\n    if (!section) return null;\r\n\r\n    const data = {};\r\n    const inputs = section.querySelectorAll('input, select, textarea');\r\n\r\n    inputs.forEach((input) => {\r\n      if (input.name || input.id) {\r\n        const key = input.name || input.id;\r\n        if (input.type === 'checkbox' || input.type === 'radio') {\r\n          data[key] = input.checked;\r\n        } else {\r\n          data[key] = input.value;\r\n        }\r\n      }\r\n    });\r\n\r\n    return data;\r\n  }\r\n\r\n  async submitForm() {\r\n    try {\r\n      const formData = this.collectAllFormData();\r\n      console.warn('📊 Formulário submetido:', formData);\r\n      this.onSubmissionSuccess();\r\n    } catch (error) {\r\n      console.error('❌ Erro ao submeter formulário:', error);\r\n    }\r\n  }\r\n\r\n  collectAllFormData() {\r\n    const data = { submittedAt: new Date().toISOString(), totalTime: this.getTotalTime() };\r\n\r\n    this.steps.forEach((step, index) => {\r\n      const stepData = this.collectStepData(index);\r\n      if (stepData) {\r\n        data[step.name] = stepData;\r\n      }\r\n    });\r\n\r\n    return data;\r\n  }\r\n\r\n  onSubmissionSuccess() {\r\n    this.showToast('Formulário enviado com sucesso!', 'success');\r\n  }\r\n\r\n  debounce(func, wait) {\r\n    let timeout;\r\n    return function executedFunction(...args) {\r\n      const later = () => {\r\n        clearTimeout(timeout);\r\n        func(...args);\r\n      };\r\n      clearTimeout(timeout);\r\n      timeout = setTimeout(later, wait);\r\n    };\r\n  }\r\n\r\n  getTotalTime() {\r\n    return Math.round((Date.now() - this.startTime) / 1000);\r\n  }\r\n\r\n  reinitializeSections() {\r\n    this.setupSections();\r\n  }\r\n\r\n  clearSavedData() {\r\n    if (typeof localStorage !== 'undefined') {\r\n      this.steps.forEach((step) => {\r\n        localStorage.removeItem(`reino_calc_${step.name}`);\r\n      });\r\n    }\r\n  }\r\n\r\n  updateNavigationState() {\r\n    // Update navigation buttons state based on current validation\r\n    // This will be called by other systems\r\n  }\r\n\r\n  getCurrentStep() {\r\n    return this.currentStep;\r\n  }\r\n\r\n  canProceed() {\r\n    return this.canProceedToNext();\r\n  }\r\n\r\n  cleanup() {\r\n    window.removeEventListener('resize', this.debounce);\r\n    this.sectionCache.clear();\r\n    this.currentStep = 0;\r\n    this.isInitialized = false;\r\n  }\r\n}\r\n\r\n// Backward compatibility exports\r\nexport const ProgressBarSystem = StepNavigationProgressSystem;\r\nexport const StepNavigationSystem = StepNavigationProgressSystem;\r\n\r\nexport default StepNavigationProgressSystem;\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACKtF,MAAM,+BAAN,MAAmC;AAAA,IAL1C,OAK0C;AAAA;AAAA;AAAA,IACxC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,cAAc;AACnB,WAAK,oBAAoB,CAAC;AAC1B,WAAK,cAAc;AACnB,WAAK,eAAe,oBAAI,IAAI;AAC5B,WAAK,YAAY,KAAK,IAAI;AAG1B,WAAK,QAAQ;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW,6BAAM,KAAK,kBAAkB,GAA7B;AAAA,QACb;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW,6BAAM,KAAK,kBAAkB,GAA7B;AAAA,QACb;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW,6BAAM,KAAK,mBAAmB,GAA9B;AAAA,QACb;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW,6BAAM,KAAK,uBAAuB,GAAlC;AAAA,QACb;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW,6BAAM,MAAN;AAAA;AAAA,QACb;AAAA,MACF;AAGA,WAAK,qBAAqB,CAAC,OAAO,WAAW,kCAAkC,EAAE;AACjF,WAAK,UAAU,kBAAkB;AACjC,WAAK,WAAW,OAAO,aAAa;AAEpC,WAAK,SAAS;AAAA,QACZ,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB;AAGA,WAAK,sBAAsB,KAAK,SAAS,MAAM;AAC7C,aAAK,sBAAsB;AAAA,MAC7B,GAAG,GAAG;AAAA,IACR;AAAA,IAEA,MAAM,KAAK,SAAS,CAAC,GAAG;AACtB,UAAI,KAAK,eAAe;AACtB,gBAAQ,KAAK,qDAAkD;AAC/D;AAAA,MACF;AAEA,WAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,GAAG,OAAO;AAE1C,UAAI;AACF,cAAM,KAAK,WAAW;AACtB,cAAM,KAAK,cAAc;AACzB,aAAK,cAAc;AACnB,aAAK,cAAc;AACnB,aAAK,oBAAoB;AACzB,aAAK,gCAAgC;AACrC,aAAK,gBAAgB;AACrB,aAAK,kBAAkB;AAGvB,aAAK,SAAS,CAAC;AAEf,aAAK,gBAAgB;AAGrB,eAAO;AAAA,UACL;AAAA,UACA,KAAK,SAAS,MAAM;AAClB,iBAAK,WAAW,OAAO,aAAa;AACpC,iBAAK,qBAAqB;AAAA,UAC5B,GAAG,GAAG;AAAA,QACR;AAGA,mBAAW,MAAM;AACf,eAAK,kBAAkB;AAAA,QACzB,GAAG,EAAE;AAEL,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,kDAA6C;AAAA,QAC5D;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,4DAAuD,KAAK;AAC1E,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IAEA,aAAa;AACX,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAI,SAAS,eAAe,WAAW;AACrC,mBAAS,iBAAiB,oBAAoB,OAAO;AAAA,QACvD,OAAO;AACL,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,MAAM,gBAAgB;AACpB,iBAAW,QAAQ,KAAK,OAAO;AAC7B,cAAM,UAAU,SAAS,cAAc,IAAI,KAAK,EAAE,EAAE;AACpD,YAAI,SAAS;AACX,eAAK,aAAa,IAAI,KAAK,IAAI,OAAO;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAAA,IAEA,gBAAgB;AACd,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,UAAU,KAAK,aAAa,IAAI,KAAK,EAAE;AAC7C,YAAI,SAAS;AACX,eAAK,mBAAmB,SAAS,KAAK;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,mBAAmB,SAAS,OAAO;AACjC,cAAQ,MAAM,UAAU,UAAU,IAAI,UAAU;AAAA,IAClD;AAAA,IAEA,sBAAsB;AAEpB,eAAS,iBAAiB,SAAS,CAAC,UAAU;AAC5C,YAAI,MAAM,OAAO,QAAQ,2BAA2B,GAAG;AACrD,gBAAM,eAAe;AACrB,eAAK,SAAS;AAAA,QAChB;AAEA,YAAI,MAAM,OAAO,QAAQ,+CAA+C,GAAG;AACzE,gBAAM,eAAe;AACrB,eAAK,aAAa;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,kBAAkB;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AAAA,IAEA,kCAAkC;AAEhC,YAAM,oBAAoB,SAAS,iBAAiB,oBAAoB;AAExE,wBAAkB,QAAQ,CAAC,cAAc;AACvC,kBAAU,iBAAiB,SAAS,CAAC,UAAU;AAC7C,gBAAM,eAAe;AAGrB,gBAAM,kBAAkB,UAAU,cAAc,uBAAuB;AACvE,gBAAM,aAAa,kBAAkB,SAAS,gBAAgB,WAAW,IAAI;AAG7E,gBAAM,aAAa,aAAa,aAAa;AAC7C,gBAAM,aAAa,UAAU,UAAU,SAAS,SAAS;AAEzD,cAAI,KAAK,OAAO,eAAe;AAC7B,oBAAQ;AAAA,cACN,0DAA2C,UAAU,YAAY,UAAU,iBAAiB,UAAU,kBAAkB,KAAK,WAAW;AAAA,YAC1I;AAAA,UACF;AAGA,cAAI,cAAc,eAAe,MAAM;AACrC,gBAAI,cAAc,KAAK,cAAc,KAAK,MAAM,QAAQ;AACtD,mBAAK,sBAAsB,UAAU;AAAA,YACvC;AAAA,UACF,OAAO;AACL,gBAAI,KAAK,OAAO,eAAe;AAC7B,sBAAQ,KAAK,qFAAuE;AAAA,YACtF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,IAEA,0BAA0B;AAExB,eAAS,iBAAiB,SAAS,CAAC,UAAU;AAC5C,YAAI,MAAM,OAAO,QAAQ,4BAA4B,GAAG;AACtD,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF,CAAC;AAGD,eAAS,iBAAiB,SAAS,CAAC,UAAU;AAC5C,YAAI,MAAM,OAAO,QAAQ,mBAAmB,GAAG;AAC7C,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,gBAAgB;AAEd,WAAK,cAAc,SAAS,cAAc,eAAe;AACzD,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,IAAI,MAAM,gDAA6C;AAAA,MAC/D;AAGA,WAAK,oBAAoB,MAAM,KAAK,SAAS,iBAAiB,yBAAyB,CAAC;AAExF,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ,KAAK,4BAA4B,KAAK,WAAW;AACzD,gBAAQ,KAAK,4BAA4B,KAAK,kBAAkB,MAAM;AAAA,MACxE;AAAA,IACF;AAAA,IAEA,oBAAoB;AAElB,WAAK,cAAc;AAGnB,UAAI,KAAK,aAAa;AAEpB,aAAK,YAAY,UAAU,OAAO,iBAAiB,eAAe;AAClE,aAAK,YAAY,UAAU,IAAI,eAAe;AAAA,MAChD;AAGA,WAAK,wBAAwB,CAAC;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,uBAAuB,WAAW;AAChC,UAAI,CAAC,KAAK,YAAa;AAEvB,YAAM,eAAe,KAAK;AAC1B,WAAK,cAAc;AAGnB,WAAK,YAAY,UAAU,OAAO,iBAAiB,eAAe;AAGlE,UAAI,cAAc,GAAG;AAEnB,aAAK,YAAY,UAAU,IAAI,eAAe;AAE9C,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,+DAAqD;AAAA,QACpE;AAAA,MACF,WAAW,cAAc,KAAK,cAAc,GAAG;AAE7C,aAAK,YAAY,UAAU,IAAI,eAAe;AAE9C,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,+DAAqD;AAAA,QACpE;AAAA,MACF,OAAO;AAEL,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,+DAAkD;AAAA,QACjE;AAAA,MACF;AAGA,WAAK,wBAAwB,SAAS;AAGtC,WAAK,+BAA+B,SAAS;AAG7C,WAAK,2BAA2B,SAAS;AAGzC,WAAK,kBAAkB,cAAc,SAAS;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,wBAAwB,iBAAiB;AACvC,WAAK,kBAAkB,QAAQ,CAAC,WAAW,UAAU;AACnD,cAAM,cAAc,UAAU,cAAc,gBAAgB;AAC5D,cAAM,mBAAmB,UAAU,cAAc,oBAAoB;AACrE,cAAM,kBAAkB,UAAU,cAAc,mBAAmB;AAEnE,YAAI,CAAC,YAAa;AAElB,cAAM,gBAAgB,SAAS,YAAY,aAAa,cAAc,CAAC,KAAK,QAAQ;AAGpF,YAAI,oBAAoB,GAAG;AAEzB,cAAI,kBAAkB;AACpB,6BAAiB,UAAU,IAAI,QAAQ;AACvC,6BAAiB,UAAU,OAAO,WAAW;AAAA,UAC/C;AAEA,cAAI,iBAAiB;AACnB,4BAAgB,UAAU,IAAI,QAAQ;AACtC,4BAAgB,UAAU,OAAO,WAAW;AAAA,UAC9C;AAGA,gBAAM,kBAAkB,UAAU,cAAc,yBAAyB;AACzE,cAAI,iBAAiB;AACnB,4BAAgB,UAAU,IAAI,QAAQ;AACtC,4BAAgB,UAAU,OAAO,WAAW;AAAA,UAC9C;AAAA,QACF,OAAO;AAEL,gBAAM,WAAW,oBAAoB;AACrC,gBAAM,cAAc,kBAAkB;AAGtC,cAAI,kBAAkB;AACpB,6BAAiB,UAAU,OAAO,UAAU,QAAQ;AACpD,6BAAiB,UAAU,OAAO,aAAa,WAAW;AAAA,UAC5D;AAEA,cAAI,iBAAiB;AACnB,4BAAgB,UAAU,OAAO,UAAU,QAAQ;AACnD,4BAAgB,UAAU,OAAO,aAAa,WAAW;AAAA,UAC3D;AAGA,gBAAM,kBAAkB,UAAU,cAAc,yBAAyB;AACzE,cAAI,iBAAiB;AACnB,4BAAgB,UAAU,OAAO,UAAU,QAAQ;AACnD,4BAAgB,UAAU,OAAO,aAAa,WAAW;AAAA,UAC3D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,+BAA+B,iBAAiB;AAC9C,YAAM,oBAAoB,SAAS,iBAAiB,oBAAoB;AAExE,wBAAkB,QAAQ,CAAC,cAAc;AACvC,YAAI,kBAAkB,GAAG;AAEvB,oBAAU,UAAU,IAAI,SAAS;AAAA,QACnC,OAAO;AAEL,oBAAU,UAAU,OAAO,SAAS;AAAA,QACtC;AAAA,MACF,CAAC;AAED,UAAI,KAAK,OAAO,eAAe;AAC7B,YAAI,kBAAkB,GAAG;AACvB,kBAAQ,KAAK,oEAA6D;AAAA,QAC5E,OAAO;AACL,kBAAQ,KAAK,mEAA4D;AAAA,QAC3E;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,2BAA2B,iBAAiB;AAC1C,YAAM,uBAAuB,SAAS,iBAAiB,wBAAwB;AAE/E,UAAI,kBAAkB,GAAG;AAEvB,6BAAqB,QAAQ,CAAC,SAAS;AACrC,eAAK,UAAU,IAAI,eAAe;AAAA,QACpC,CAAC;AAED,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,6EAAsE;AAAA,QACrF;AAAA,MACF,OAAO;AAEL,6BAAqB,QAAQ,CAAC,SAAS;AAErC,gBAAM,qBAAqB,KAAK,2BAA2B,IAAI;AAE/D,cAAI,CAAC,oBAAoB;AACvB,iBAAK,UAAU,OAAO,eAAe;AAAA,UACvC;AAAA,QACF,CAAC;AAED,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,2BAA2B,MAAM;AAE/B,YAAM,mBAAmB,KAAK,cAAc,yCAAyC;AACrF,aAAO,CAAC,CAAC;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,oBAAoB,WAAW;AAC7B,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,UAAU,IAAI,SAAS;AAExC,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,6BAAwB,SAAS,qBAAkB;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,uBAAuB,WAAW;AAChC,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,UAAU,OAAO,SAAS;AAE3C,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,2BAAsB,SAAS,mBAAmB;AAAA,QACjE;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,oBAAoB,WAAW;AAC7B,aAAO,KAAK,cAAc,KAAK,YAAY,UAAU,SAAS,SAAS,IAAI;AAAA,IAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,oBAAoB,WAAW,OAAO;AACpC,UAAI,CAAC,KAAK,YAAa;AAEvB,YAAM,aAAa,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACxD,YAAM,kBAAkB,WAAW,SAAS,KAAK,WAAW;AAE5D,UAAI,iBAAiB;AACnB,aAAK,oBAAoB,SAAS;AAAA,MACpC,OAAO;AACL,aAAK,uBAAuB,SAAS;AAAA,MACvC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,sBAAsB;AACpB,UAAI,CAAC,KAAK,YAAa,QAAO;AAE9B,aAAO;AAAA,QACL,aAAa,KAAK;AAAA,QAClB,SAAS,MAAM,KAAK,KAAK,YAAY,SAAS;AAAA,QAC9C,iBAAiB,KAAK,oBAAoB,eAAe;AAAA,QACzD,eAAe,KAAK,kBAAkB;AAAA,QACtC,SAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc;AACZ,UAAI,CAAC,KAAK,cAAe;AAEzB,WAAK,uBAAuB,KAAK,WAAW;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,kBAAkB,cAAc,aAAa;AAE3C,YAAM,QAAQ,IAAI,YAAY,0BAA0B;AAAA,QACtD,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA,kBAAkB,KAAK,oBAAoB;AAAA,QAC7C;AAAA,MACF,CAAC;AAED,eAAS,cAAc,KAAK;AAE5B,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ,KAAK,mDAA4C;AAAA,UACvD;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa,WAAW;AACtB,WAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,GAAG,UAAU;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa,WAAW;AACtB,WAAK,uBAAuB,SAAS;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA,IAKA,UAAU;AACR,WAAK,cAAc;AACnB,WAAK,oBAAoB,CAAC;AAC1B,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAErB,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ,KAAK,qDAA2C;AAAA,MAC1D;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,SAAS,WAAW;AACxB,UAAI,YAAY,KAAK,aAAa,KAAK,MAAM,OAAQ;AAErD,YAAM,eAAe,KAAK;AAC1B,WAAK,cAAc;AAGnB,UAAI,iBAAiB,WAAW;AAC9B,aAAK,aAAa,YAAY;AAAA,MAChC;AAEA,WAAK,eAAe,SAAS;AAC7B,WAAK,uBAAuB,SAAS;AACrC,WAAK,oBAAoB,SAAS;AAClC,WAAK,gBAAgB,SAAS;AAG9B,WAAK,0BAA0B;AAG/B,aAAO,SAAS,EAAE,KAAK,GAAG,UAAU,SAAS,CAAC;AAAA,IAChD;AAAA,IAEA,eAAe,WAAW;AACxB,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,UAAU,KAAK,aAAa,IAAI,KAAK,EAAE;AAC7C,YAAI,CAAC,QAAS;AAEd,YAAI,UAAU,WAAW;AACvB,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,YAAY;AAC1B,kBAAQ,MAAM,gBAAgB;AAAA,QAChC,OAAO;AACL,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,UAAU;AACxB,kBAAQ,MAAM,YAAY;AAC1B,kBAAQ,MAAM,gBAAgB;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,WAAW;AACT,UAAI,KAAK,eAAe,KAAK,MAAM,SAAS,GAAG;AAC7C,aAAK,WAAW;AAChB;AAAA,MACF;AAEA,UAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAK,SAAS,KAAK,cAAc,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,eAAe;AACb,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK,SAAS,KAAK,cAAc,CAAC;AAAA,MACpC;AAAA,IACF;AAAA,IAEA,SAAS,WAAW;AAElB,UAAI,aAAa,KAAK,aAAa;AACjC,aAAK,SAAS,SAAS;AAEvB,aAAK,0BAA0B;AAC/B;AAAA,MACF;AAGA,UAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAK,SAAS,SAAS;AAEvB,aAAK,0BAA0B;AAAA,MACjC,OAAO;AACL,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,sBAAsB,eAAe;AAOnC,YAAM,aAAa;AAEnB,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ,KAAK,gDAAmC,aAAa,YAAY,UAAU,EAAE;AAAA,MACvF;AAGA,UAAI,aAAa,KAAK,cAAc,KAAK,MAAM,QAAQ;AACrD,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,eAAU,UAAU,sBAAmB,KAAK,MAAM,SAAS,CAAC,GAAG;AAAA,QAC9E;AACA;AAAA,MACF;AAGA,UAAI,cAAc,KAAK,aAAa;AAClC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAGA,UAAI,eAAe,KAAK,cAAc,GAAG;AAEvC,YAAI,KAAK,iBAAiB,GAAG;AAC3B,eAAK,SAAS,UAAU;AAExB,eAAK,0BAA0B;AAAA,QACjC,OAAO;AACL,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF,OAAO;AAEL,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,gDAAkC,KAAK,WAAW,SAAS,UAAU,EAAE;AAAA,QACtF;AACA,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,4BAA4B;AAC1B,YAAM,gBAAgB,OAAO,iBAAiB,SAAS;AAEvD,UAAI,iBAAiB,OAAO,cAAc,2BAA2B,YAAY;AAE/E,mBAAW,MAAM;AACf,wBAAc,uBAAuB;AACrC,cAAI,OAAO,cAAc,uBAAuB,YAAY;AAC1D,0BAAc,mBAAmB;AAAA,UACnC;AAAA,QACF,GAAG,EAAE;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGA,mBAAmB;AAEjB,aAAO,KAAK,MAAM,KAAK,WAAW,GAAG,UAAU,KAAK;AAAA,IACtD;AAAA,IAEA,oBAAoB;AAClB,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ,KAAK,uCAAkC;AAAA,MACjD;AACA,aAAO;AAAA,IACT;AAAA,IAEA,oBAAoB;AAClB,YAAM,gBAAgB,SAAS,cAAc,4CAA4C;AACzF,UAAI,CAAC,eAAe;AAClB,YAAI,KAAK,OAAO,eAAe;AAC7B,kBAAQ,KAAK,mDAA2C;AAAA,QAC1D;AACA,eAAO;AAAA,MACT;AAEA,YAAM,QAAQ,KAAK,gBAAgB,cAAc,KAAK;AACtD,YAAM,UAAU,QAAQ;AAExB,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ,KAAK,GAAG,UAAU,WAAM,QAAG,6BAA6B,KAAK,eAAY,OAAO,EAAE;AAAA,MAC5F;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,qBAAqB;AACnB,YAAM,iBAAiB,SAAS,iBAAiB,iBAAiB;AAClE,YAAM,UAAU,eAAe,SAAS;AAExC,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ;AAAA,UACN,GAAG,UAAU,WAAM,QAAG,wBAAwB,eAAe,MAAM;AAAA,QACrE;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,yBAAyB;AACvB,YAAM,kBAAkB,KAAK,yBAAyB;AACtD,YAAM,UAAU,KAAK,IAAI,kBAAkB,GAAG,IAAI;AAElD,UAAI,KAAK,OAAO,eAAe;AAC7B,gBAAQ;AAAA,UACN,GAAG,UAAU,WAAM,QAAG,kCAAkC,eAAe,gBAAa,OAAO;AAAA,QAC7F;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,oBAAoB,WAAW;AAC7B,YAAM,kBAAkB,KAAK,MAAM,SAAS;AAC5C,UAAI,iBAAiB;AACnB,iBAAS,QAAQ,sBAAsB,gBAAgB,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA,IAEA,gBAAgB,WAAW;AACzB,iBAAW,MAAM;AACf,cAAM,UAAU,KAAK,aAAa,IAAI,KAAK,MAAM,SAAS,GAAG,EAAE;AAC/D,YAAI,SAAS;AACX,gBAAM,aAAa,QAAQ,cAAc,iCAAiC;AAC1E,cAAI,cAAc,OAAO,WAAW,UAAU,YAAY;AACxD,gBAAI;AACF,yBAAW,MAAM;AAAA,YACnB,QAAQ;AAAA,YAER;AAAA,UACF;AAAA,QACF;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAAA,IAEA,sBAAsB;AACpB,YAAM,kBAAkB,KAAK,MAAM,KAAK,WAAW;AACnD,UAAI,UAAU;AAEd,UAAI,iBAAiB,SAAS,SAAS;AACrC,kBAAU;AAAA,MACZ,WAAW,iBAAiB,SAAS,UAAU;AAC7C,kBAAU;AAAA,MACZ,WAAW,iBAAiB,SAAS,cAAc;AACjD,kBAAU;AAAA,MACZ;AAEA,WAAK,UAAU,SAAS,OAAO;AAAA,IACjC;AAAA,IAEA,UAAU,SAAS,OAAO,QAAQ;AAEhC,YAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,YAAM,YAAY,eAAe,IAAI;AACrC,YAAM,cAAc;AACpB,YAAM,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,oBAKN,SAAS,UAAU,YAAY,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAOxD,eAAS,KAAK,YAAY,KAAK;AAC/B,iBAAW,MAAM,MAAM,OAAO,GAAG,GAAI;AAAA,IACvC;AAAA,IAEA,gBAAgB,OAAO;AACrB,aAAO,WAAW,OAAO,QAAQ,YAAY,EAAE,EAAE,QAAQ,KAAK,GAAG,CAAC,KAAK;AAAA,IACzE;AAAA,IAEA,2BAA2B;AACzB,YAAM,mBAAmB,SAAS,iBAAiB,mBAAmB;AACtE,UAAI,QAAQ;AAEZ,uBAAiB,QAAQ,CAAC,UAAU;AAClC,cAAM,QAAQ,KAAK,gBAAgB,MAAM,KAAK;AAC9C,iBAAS;AAAA,MACX,CAAC;AAED,aAAO;AAAA,IACT;AAAA,IAEA,aAAa,WAAW;AACtB,YAAM,WAAW,KAAK,gBAAgB,SAAS;AAC/C,YAAM,WAAW,KAAK,MAAM,SAAS,GAAG;AACxC,UAAI,YAAY,UAAU;AACxB,YAAI,OAAO,iBAAiB,aAAa;AACvC,uBAAa,QAAQ,cAAc,QAAQ,IAAI,KAAK,UAAU,QAAQ,CAAC;AAAA,QACzE;AAAA,MACF;AAAA,IACF;AAAA,IAEA,gBAAgB,WAAW;AACzB,YAAM,UAAU,KAAK,aAAa,IAAI,KAAK,MAAM,SAAS,GAAG,EAAE;AAC/D,UAAI,CAAC,QAAS,QAAO;AAErB,YAAM,OAAO,CAAC;AACd,YAAM,SAAS,QAAQ,iBAAiB,yBAAyB;AAEjE,aAAO,QAAQ,CAAC,UAAU;AACxB,YAAI,MAAM,QAAQ,MAAM,IAAI;AAC1B,gBAAM,MAAM,MAAM,QAAQ,MAAM;AAChC,cAAI,MAAM,SAAS,cAAc,MAAM,SAAS,SAAS;AACvD,iBAAK,GAAG,IAAI,MAAM;AAAA,UACpB,OAAO;AACL,iBAAK,GAAG,IAAI,MAAM;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA,IAEA,MAAM,aAAa;AACjB,UAAI;AACF,cAAM,WAAW,KAAK,mBAAmB;AACzC,gBAAQ,KAAK,sCAA4B,QAAQ;AACjD,aAAK,oBAAoB;AAAA,MAC3B,SAAS,OAAO;AACd,gBAAQ,MAAM,0CAAkC,KAAK;AAAA,MACvD;AAAA,IACF;AAAA,IAEA,qBAAqB;AACnB,YAAM,OAAO,EAAE,cAAa,oBAAI,KAAK,GAAE,YAAY,GAAG,WAAW,KAAK,aAAa,EAAE;AAErF,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,WAAW,KAAK,gBAAgB,KAAK;AAC3C,YAAI,UAAU;AACZ,eAAK,KAAK,IAAI,IAAI;AAAA,QACpB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA,IAEA,sBAAsB;AACpB,WAAK,UAAU,sCAAmC,SAAS;AAAA,IAC7D;AAAA,IAEA,SAAS,MAAM,MAAM;AACnB,UAAI;AACJ,aAAO,gCAAS,oBAAoB,MAAM;AACxC,cAAM,QAAQ,6BAAM;AAClB,uBAAa,OAAO;AACpB,eAAK,GAAG,IAAI;AAAA,QACd,GAHc;AAId,qBAAa,OAAO;AACpB,kBAAU,WAAW,OAAO,IAAI;AAAA,MAClC,GAPO;AAAA,IAQT;AAAA,IAEA,eAAe;AACb,aAAO,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK,aAAa,GAAI;AAAA,IACxD;AAAA,IAEA,uBAAuB;AACrB,WAAK,cAAc;AAAA,IACrB;AAAA,IAEA,iBAAiB;AACf,UAAI,OAAO,iBAAiB,aAAa;AACvC,aAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,uBAAa,WAAW,cAAc,KAAK,IAAI,EAAE;AAAA,QACnD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,wBAAwB;AAAA,IAGxB;AAAA,IAEA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IAEA,aAAa;AACX,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AAAA,IAEA,UAAU;AACR,aAAO,oBAAoB,UAAU,KAAK,QAAQ;AAClD,WAAK,aAAa,MAAM;AACxB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAGO,MAAM,oBAAoB;AAC1B,MAAM,uBAAuB;AAEpC,MAAO,8BAAQ;", "names": []}