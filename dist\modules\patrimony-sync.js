"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/event-coordinator.js
  var EventCoordinator = class {
    static {
      __name(this, "EventCoordinator");
    }
    constructor() {
      this.input = null;
      this.listeners = /* @__PURE__ */ new Map();
      this.isProcessing = false;
      this.eventQueue = [];
      this.boundHandlers = /* @__PURE__ */ new Map();
      this.isDestroyed = false;
      this.init();
    }
    init() {
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.findAndSetupInput());
      } else {
        this.findAndSetupInput();
      }
    }
    findAndSetupInput() {
      this.input = document.querySelector('[is-main="true"]');
      if (this.input && !this.isDestroyed) {
        this.setupMainListeners();
      }
    }
    setupMainListeners() {
      if (!this.input || this.boundHandlers.has("main")) return;
      const inputHandler = /* @__PURE__ */ __name((e) => this.handleInputEvent(e), "inputHandler");
      const focusHandler = /* @__PURE__ */ __name((e) => this.processFocusEvent(e), "focusHandler");
      const blurHandler = /* @__PURE__ */ __name((e) => this.processBlurEvent(e), "blurHandler");
      const changeHandler = /* @__PURE__ */ __name((e) => this.processChangeEvent(e), "changeHandler");
      this.boundHandlers.set("main", {
        input: inputHandler,
        focus: focusHandler,
        blur: blurHandler,
        change: changeHandler
      });
      this.input.addEventListener("input", inputHandler, { passive: true });
      this.input.addEventListener("focus", focusHandler, { passive: true });
      this.input.addEventListener("blur", blurHandler, { passive: true });
      this.input.addEventListener("change", changeHandler, { passive: true });
    }
    handleInputEvent(e) {
      if (this.isProcessing || this.isDestroyed) {
        return;
      }
      this.isProcessing = true;
      requestAnimationFrame(() => {
        this.processInputEvent(e);
        this.isProcessing = false;
        if (this.eventQueue.length > 0) {
          const nextEvent = this.eventQueue.shift();
          requestAnimationFrame(() => this.handleInputEvent(nextEvent));
        }
      });
    }
    // Registra um listener para um módulo específico
    registerListener(moduleId, eventType, callback) {
      if (this.isDestroyed) return;
      const key = `${moduleId}_${eventType}`;
      this.unregisterListener(moduleId, eventType);
      if (!this.listeners.has(key)) {
        this.listeners.set(key, []);
      }
      this.listeners.get(key).push(callback);
    }
    // Remove listener de um módulo
    unregisterListener(moduleId, eventType, specificCallback = null) {
      const key = `${moduleId}_${eventType}`;
      if (this.listeners.has(key)) {
        if (specificCallback) {
          const callbacks = this.listeners.get(key);
          const index = callbacks.indexOf(specificCallback);
          if (index > -1) {
            callbacks.splice(index, 1);
          }
        } else {
          this.listeners.delete(key);
        }
      }
    }
    // Remove todos os listeners de um módulo
    unregisterModule(moduleId) {
      const keysToRemove = [];
      for (const key of this.listeners.keys()) {
        if (key.startsWith(`${moduleId}_`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => this.listeners.delete(key));
    }
    processInputEvent(e) {
      if (this.isDestroyed) return;
      const inputCallbacks = this.getCallbacksForEvent("input");
      const priorityOrder = ["currency-formatting", "motion-animation", "patrimony-sync"];
      for (const moduleId of priorityOrder) {
        const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);
        for (const callbackInfo of moduleCallbacks) {
          try {
            callbackInfo.callback(e);
          } catch (error) {
            console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);
          }
        }
      }
    }
    processFocusEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("focus", e);
    }
    processBlurEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("blur", e);
    }
    processChangeEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("change", e);
    }
    executeCallbacksForEvent(eventType, e) {
      const callbacks = this.getCallbacksForEvent(eventType);
      callbacks.forEach(({ callback, moduleId }) => {
        try {
          callback(e);
        } catch (error) {
          console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);
        }
      });
    }
    getCallbacksForEvent(eventType) {
      const callbacks = [];
      for (const [key, callbackList] of this.listeners.entries()) {
        if (key.endsWith(`_${eventType}`)) {
          const moduleId = key.replace(`_${eventType}`, "");
          callbackList.forEach((callback) => {
            callbacks.push({ moduleId, callback });
          });
        }
      }
      return callbacks;
    }
    // Método para disparar eventos programaticamente
    dispatchInputEvent(sourceModule = "unknown") {
      if (this.isProcessing || this.isDestroyed || !this.input) {
        return;
      }
      const event = new Event("input", { bubbles: true });
      event.sourceModule = sourceModule;
      this.input.dispatchEvent(event);
    }
    // Método para atualizar valor sem disparar eventos
    setSilentValue(value) {
      if (this.isDestroyed || !this.input) return;
      this.isProcessing = true;
      this.input.value = value;
      requestAnimationFrame(() => {
        this.isProcessing = false;
      });
    }
    // Getter para o valor atual
    getValue() {
      return this.input ? this.input.value : "";
    }
    // Setter que dispara eventos controlados
    setValue(value, sourceModule = "unknown") {
      if (this.isDestroyed || !this.input) return;
      this.input.value = value;
      this.dispatchInputEvent(sourceModule);
    }
    // Método de cleanup para prevenir memory leaks
    destroy() {
      this.isDestroyed = true;
      if (this.input && this.boundHandlers.has("main")) {
        const handlers = this.boundHandlers.get("main");
        this.input.removeEventListener("input", handlers.input);
        this.input.removeEventListener("focus", handlers.focus);
        this.input.removeEventListener("blur", handlers.blur);
        this.input.removeEventListener("change", handlers.change);
      }
      this.listeners.clear();
      this.boundHandlers.clear();
      this.eventQueue.length = 0;
      this.input = null;
      this.isProcessing = false;
    }
    // Método para reinicializar se necessário
    reinitialize() {
      this.destroy();
      this.isDestroyed = false;
      this.init();
    }
  };
  var eventCoordinator = new EventCoordinator();
  window.addEventListener("beforeunload", () => {
    eventCoordinator.destroy();
  });

  // src/modules/patrimony-sync.js
  var CacheManager = {
    set(key, value) {
      try {
        window.localStorage.setItem(key, JSON.stringify(value));
      } catch {
      }
    },
    get(key) {
      try {
        const value = window.localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
      } catch {
        return null;
      }
    },
    remove(key) {
      try {
        window.localStorage.removeItem(key);
      } catch {
      }
    }
  };
  var Utils = {
    formatCurrency(value) {
      return new Intl.NumberFormat("pt-BR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    },
    parseCurrencyValue(value) {
      if (!value || typeof value !== "string") return 0;
      const cleanValue = value.replace(/[^\d,]/g, "").replace(",", ".");
      return parseFloat(cleanValue) || 0;
    },
    calculatePercentage(value, total) {
      if (!total || total === 0) return 0;
      return value / total * 100;
    },
    formatPercentage(value) {
      return `${value.toFixed(1)}%`;
    },
    debounce(func, wait) {
      let timeout;
      return /* @__PURE__ */ __name(function executedFunction(...args) {
        const later = /* @__PURE__ */ __name(() => {
          clearTimeout(timeout);
          func(...args);
        }, "later");
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      }, "executedFunction");
    }
  };
  var PatrimonySync = {
    mainValue: 0,
    cacheKey: "patrimony_main_value",
    allocationsCacheKey: "patrimony_allocations",
    isInitialized: false
  };
  var MainInputSync = {
    input: null,
    init() {
      this.input = document.querySelector('[is-main="true"]');
      if (!this.input) {
        console.warn("Main input not found");
        return;
      }
      const cachedValue = CacheManager.get(PatrimonySync.cacheKey);
      if (cachedValue !== null && cachedValue > 0) {
        PatrimonySync.mainValue = cachedValue;
        this.input.value = Utils.formatCurrency(cachedValue);
      }
      this.setupListeners();
    },
    setupListeners() {
      eventCoordinator.registerListener(
        "patrimony-sync",
        "input",
        Utils.debounce((e) => {
          const value = Utils.parseCurrencyValue(e.target.value);
          this.handleValueChange(value);
        }, 300)
      );
      eventCoordinator.registerListener("patrimony-sync", "change", (e) => {
        const value = Utils.parseCurrencyValue(e.target.value);
        this.handleValueChange(value);
      });
      this.input.addEventListener("currencyChange", (e) => {
        this.handleValueChange(e.detail.value);
      });
    },
    handleValueChange(value) {
      PatrimonySync.mainValue = value;
      CacheManager.set(PatrimonySync.cacheKey, value);
      document.dispatchEvent(
        new CustomEvent("patrimonyMainValueChanged", {
          detail: {
            value,
            formatted: Utils.formatCurrency(value)
          }
        })
      );
      document.dispatchEvent(
        new CustomEvent("totalPatrimonyChanged", {
          detail: {
            value,
            formatted: Utils.formatCurrency(value)
          }
        })
      );
      AllocationSync.updateAllAllocations();
      AllocationSync.validateAllAllocations();
    },
    getValue() {
      return PatrimonySync.mainValue;
    },
    setValue(value) {
      PatrimonySync.mainValue = value;
      if (this.input) {
        this.input.value = Utils.formatCurrency(value);
        this.input.dispatchEvent(new Event("input", { bubbles: true }));
      }
    }
  };
  var AllocationSync = {
    items: [],
    init() {
      const containers = document.querySelectorAll(".patrimonio_interactive_item");
      containers.forEach((container, index) => {
        const activeItem = container.querySelector(".active-produto-item");
        const disabledItem = container.querySelector(".disabled-produto-item");
        if (!activeItem || !disabledItem) return;
        const input = activeItem.querySelector('[input-settings="receive"]');
        const slider = activeItem.querySelector("range-slider");
        const percentageDisplay = activeItem.querySelector(".porcentagem-calculadora");
        const valorProduto = disabledItem.querySelector(".valor-produto");
        const percentageDisabled = disabledItem.querySelector(".porcentagem-calculadora-disabled");
        const backgroundItemAcao = disabledItem.querySelector(".background-item-acao");
        if (input && slider) {
          const item = {
            container,
            activeItem,
            disabledItem,
            input,
            slider,
            percentageDisplay,
            valorProduto,
            percentageDisabled,
            backgroundItemAcao,
            index,
            value: 0,
            percentage: 0,
            maxAllowed: 0
          };
          this.items.push(item);
          this.setupItemListeners(item);
        }
      });
      this.loadCachedAllocations();
    },
    setupItemListeners(item) {
      item.input.addEventListener("currencyChange", (e) => {
        this.handleInputChange(item, e.detail.value);
      });
      item.input.addEventListener(
        "input",
        Utils.debounce((e) => {
          const value = Utils.parseCurrencyValue(e.target.value);
          this.handleInputChange(item, value);
        }, 300)
      );
      item.slider.addEventListener("input", (e) => {
        this.handleSliderChange(item, parseFloat(e.target.value));
      });
      item.input.addEventListener("focus", () => {
        item.container.classList.add("input-focused");
        this.updateMaxAllowed(item);
      });
      item.input.addEventListener("blur", () => {
        item.container.classList.remove("input-focused");
        this.validateAllocation(item);
      });
    },
    handleInputChange(item, value) {
      const mainValue = MainInputSync.getValue();
      const otherAllocations = this.getTotalAllocatedExcept(item);
      const maxAllowed = Math.max(0, mainValue - otherAllocations);
      if (value > maxAllowed) {
        value = maxAllowed;
        item.input.value = Utils.formatCurrency(value);
        VisualFeedback.showAllocationWarning(
          item.container,
          `Valor m\xE1ximo dispon\xEDvel: R$ ${Utils.formatCurrency(maxAllowed)}`
        );
      }
      item.value = value;
      item.percentage = Utils.calculatePercentage(value, mainValue);
      item.maxAllowed = maxAllowed;
      this.updateSlider(item);
      this.updatePercentageDisplay(item);
      this.updateValorProduto(item);
      this.updateBackgroundItemAcao(item);
      this.saveAllocations();
      this.dispatchAllocationChange(item);
      this.checkTotalAllocationStatus();
    },
    handleSliderChange(item, sliderValue) {
      const mainValue = MainInputSync.getValue();
      let value = mainValue * sliderValue;
      const otherAllocations = this.getTotalAllocatedExcept(item);
      const maxAllowed = Math.max(0, mainValue - otherAllocations);
      if (value > maxAllowed) {
        value = maxAllowed;
        const cappedSliderValue = mainValue > 0 ? value / mainValue : 0;
        item.slider.value = cappedSliderValue;
        VisualFeedback.showAllocationWarning(
          item.container,
          `Valor m\xE1ximo dispon\xEDvel: R$ ${Utils.formatCurrency(maxAllowed)}`
        );
      }
      item.value = value;
      item.percentage = value > 0 && mainValue > 0 ? value / mainValue * 100 : 0;
      item.maxAllowed = maxAllowed;
      item.input.value = Utils.formatCurrency(value);
      this.updatePercentageDisplay(item);
      this.updateValorProduto(item);
      this.updateBackgroundItemAcao(item);
      this.saveAllocations();
      this.dispatchAllocationChange(item);
      this.checkTotalAllocationStatus();
    },
    updateSlider(item) {
      const mainValue = MainInputSync.getValue();
      if (mainValue > 0) {
        const sliderValue = item.value / mainValue;
        item.slider.value = Math.min(1, Math.max(0, sliderValue));
      } else {
        item.slider.value = 0;
      }
    },
    updatePercentageDisplay(item) {
      const formattedPercentage = Utils.formatPercentage(item.percentage);
      if (item.percentageDisplay) {
        item.percentageDisplay.textContent = formattedPercentage;
      }
      if (item.percentageDisabled) {
        item.percentageDisabled.textContent = formattedPercentage;
      }
    },
    updateValorProduto(item) {
      if (item.valorProduto) {
        item.valorProduto.textContent = Utils.formatCurrency(item.value);
      }
    },
    updateBackgroundItemAcao(item) {
      if (item.backgroundItemAcao && window.Motion) {
        const { animate } = window.Motion;
        const widthPercentage = Math.max(0, Math.min(100, item.percentage));
        animate(
          item.backgroundItemAcao,
          {
            width: `${widthPercentage}%`
          },
          {
            duration: 0.5,
            easing: "ease-out"
          }
        );
      }
    },
    updateMaxAllowed(item) {
      const mainValue = MainInputSync.getValue();
      const otherAllocations = this.getTotalAllocatedExcept(item);
      item.maxAllowed = Math.max(0, mainValue - otherAllocations);
    },
    validateAllocation(item) {
      const mainValue = MainInputSync.getValue();
      const otherAllocations = this.getTotalAllocatedExcept(item);
      const maxAllowed = Math.max(0, mainValue - otherAllocations);
      if (item.value > maxAllowed) {
        item.value = maxAllowed;
        item.input.value = Utils.formatCurrency(maxAllowed);
        this.updateSlider(item);
        this.updatePercentageDisplay(item);
        this.updateValorProduto(item);
        this.updateBackgroundItemAcao(item);
        this.saveAllocations();
      }
    },
    validateAllAllocations() {
      const mainValue = MainInputSync.getValue();
      const total = this.getTotalAllocated();
      if (total > mainValue) {
        const ratio = mainValue / total;
        this.items.forEach((item) => {
          const newValue = item.value * ratio;
          item.value = newValue;
          item.percentage = Utils.calculatePercentage(newValue, mainValue);
          item.input.value = Utils.formatCurrency(newValue);
          this.updateSlider(item);
          this.updatePercentageDisplay(item);
          this.updateValorProduto(item);
          this.updateBackgroundItemAcao(item);
        });
        this.saveAllocations();
      }
    },
    updateAllAllocations() {
      const mainValue = MainInputSync.getValue();
      this.items.forEach((item) => {
        this.updateMaxAllowed(item);
        if (mainValue > 0) {
          item.percentage = Utils.calculatePercentage(item.value, mainValue);
          this.updateSlider(item);
          this.updatePercentageDisplay(item);
          this.updateValorProduto(item);
          this.updateBackgroundItemAcao(item);
        } else {
          item.value = 0;
          item.percentage = 0;
          item.input.value = Utils.formatCurrency(0);
          item.slider.value = 0;
          this.updatePercentageDisplay(item);
          this.updateValorProduto(item);
          this.updateBackgroundItemAcao(item);
        }
      });
    },
    checkTotalAllocationStatus() {
      const mainValue = MainInputSync.getValue();
      const total = this.getTotalAllocated();
      const remaining = mainValue - total;
      document.dispatchEvent(
        new CustomEvent("allocationStatusChanged", {
          detail: {
            mainValue,
            totalAllocated: total,
            remaining,
            isFullyAllocated: remaining === 0,
            isOverAllocated: remaining < 0,
            percentageAllocated: mainValue > 0 ? total / mainValue * 100 : 0,
            remainingPercentage: mainValue > 0 ? remaining / mainValue * 100 : 0
          }
        })
      );
    },
    getTotalAllocated() {
      return this.items.reduce((sum, item) => sum + item.value, 0);
    },
    getTotalAllocatedExcept(excludeItem) {
      return this.items.reduce((sum, item) => {
        return item === excludeItem ? sum : sum + item.value;
      }, 0);
    },
    getRemainingValue() {
      const mainValue = MainInputSync.getValue();
      const totalAllocated = this.getTotalAllocated();
      return Math.max(0, mainValue - totalAllocated);
    },
    saveAllocations() {
      const allocations = this.items.map((item) => ({
        index: item.index,
        value: item.value,
        percentage: item.percentage
      }));
      CacheManager.set(PatrimonySync.allocationsCacheKey, allocations);
    },
    loadCachedAllocations() {
      const cached = CacheManager.get(PatrimonySync.allocationsCacheKey);
      if (!cached || !Array.isArray(cached)) return;
      cached.forEach((cachedItem) => {
        const item = this.items.find((i) => i.index === cachedItem.index);
        if (item) {
          item.value = cachedItem.value;
          item.percentage = cachedItem.percentage;
          item.input.value = Utils.formatCurrency(item.value);
          this.updateSlider(item);
          this.updatePercentageDisplay(item);
          this.updateValorProduto(item);
          this.updateBackgroundItemAcao(item);
          this.dispatchAllocationChange(item);
        }
      });
      this.validateAllAllocations();
      document.dispatchEvent(
        new CustomEvent("patrimonySystemReady", {
          detail: {
            cacheLoaded: true,
            itemsCount: cached.length
          }
        })
      );
    },
    dispatchAllocationChange(item) {
      const category = item.container.getAttribute("ativo-category") || "";
      const product = item.container.getAttribute("ativo-product") || "";
      document.dispatchEvent(
        new CustomEvent("allocationChanged", {
          detail: {
            index: item.index,
            category,
            product,
            value: item.value,
            percentage: item.percentage,
            formatted: Utils.formatCurrency(item.value),
            remaining: this.getRemainingValue()
          }
        })
      );
      document.dispatchEvent(
        new CustomEvent("patrimonyValueChanged", {
          detail: {
            key: `${category}-${product}`,
            category,
            product,
            value: item.value,
            percentage: item.percentage
          }
        })
      );
    }
  };
  var VisualFeedback = {
    showAllocationWarning(container, message) {
      let warning = container.querySelector(".allocation-warning");
      if (!warning) {
        warning = document.createElement("div");
        warning.className = "allocation-warning";
        warning.style.cssText = `
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        opacity: 0;
        transition: opacity 0.3s ease;
        position: absolute;
        background: white;
        padding: 0.5rem;
        border-radius: 0.25rem;
        box-shadow: 0 2px 10px rgba(239, 68, 68, 0.2);
        z-index: 10;
      `;
        container.style.position = "relative";
        container.appendChild(warning);
      }
      warning.textContent = message;
      warning.style.opacity = "1";
      const input = container.querySelector("input");
      if (input) {
        const rect = input.getBoundingClientRect();
        warning.style.top = `${input.offsetTop + rect.height + 5}px`;
        warning.style.left = `${input.offsetLeft}px`;
      }
      setTimeout(() => {
        warning.style.opacity = "0";
      }, 3e3);
      if (input) {
        input.style.borderColor = "#ef4444";
        setTimeout(() => {
          input.style.borderColor = "";
        }, 3e3);
      }
    }
  };
  var PatrimonySyncSystem = class {
    static {
      __name(this, "PatrimonySyncSystem");
    }
    constructor() {
      this.isInitialized = false;
    }
    init() {
      if (this.isInitialized) {
        return;
      }
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => {
          this.initialize();
        });
      } else {
        this.initialize();
      }
      this.isInitialized = true;
    }
    initialize() {
      if (!window.currency) {
        console.error("Currency.js is required for PatrimonySync");
        return;
      }
      const waitForMotion = /* @__PURE__ */ __name(() => {
        if (window.Motion) {
          this.initializeComponents();
        } else {
          setTimeout(waitForMotion, 50);
        }
      }, "waitForMotion");
      waitForMotion();
    }
    initializeComponents() {
      MainInputSync.init();
      setTimeout(() => {
        AllocationSync.init();
        PatrimonySync.isInitialized = true;
        AllocationSync.checkTotalAllocationStatus();
        document.dispatchEvent(
          new CustomEvent("patrimonySyncReady", {
            detail: {
              mainValue: this.getMainValue(),
              totalAllocated: this.getTotalAllocated(),
              remaining: this.getRemainingValue()
            }
          })
        );
        if (typeof window !== "undefined") {
          window.getAllocationSync = () => AllocationSync;
          console.warn("\u2705 AllocationSync exposto globalmente");
        }
        this.updateWebflowPatrimonyDisplay();
      }, 100);
      this.updateWebflowPatrimonyDisplay();
      document.addEventListener("patrimonyMainValueChanged", () => {
        this.updateWebflowPatrimonyDisplay();
      });
      document.addEventListener("allocationChanged", () => {
        this.updateWebflowPatrimonyDisplay();
      });
      document.addEventListener("allocationStatusChanged", () => {
        this.updateWebflowPatrimonyDisplay();
      });
    }
    // Atualiza os valores do Webflow (patrimonio_money_wrapper e header-and-patrimonio)
    updateWebflowPatrimonyDisplay() {
      const mainValue = this.getMainValue();
      const formattedValue = Utils.formatCurrency(mainValue);
      const restanteEl = document.querySelector(".patrimonio_money_wrapper .patrimonio-restante");
      if (restanteEl) {
        restanteEl.textContent = Utils.formatCurrency(this.getRemainingValue());
      }
      const totalEl = document.querySelector(".patrimonio_money_wrapper .patrimonio-total-value");
      if (totalEl) {
        totalEl.textContent = formattedValue;
      }
      const headerTotalEl = document.querySelector('[data-patrimonio-total="true"]');
      if (headerTotalEl) {
        headerTotalEl.textContent = formattedValue;
      }
      const allTotalElements = document.querySelectorAll(".patrimonio-total-value");
      allTotalElements.forEach((el) => {
        el.textContent = formattedValue;
      });
      const porcentagemRestanteElements = document.querySelectorAll(".porcentagem-restante");
      if (porcentagemRestanteElements.length > 0) {
        const mainValue2 = this.getMainValue();
        const restante = this.getRemainingValue();
        const percent = mainValue2 > 0 ? restante / mainValue2 * 100 : 0;
        const formattedPercent = Utils.formatPercentage(percent);
        porcentagemRestanteElements.forEach((el) => {
          el.textContent = formattedPercent;
        });
      }
    }
    // Public API methods
    getMainValue() {
      return MainInputSync.getValue();
    }
    setMainValue(value) {
      MainInputSync.setValue(value);
    }
    getTotalAllocated() {
      return AllocationSync.getTotalAllocated();
    }
    getRemainingValue() {
      return AllocationSync.getRemainingValue();
    }
    getAllocations() {
      return AllocationSync.items.map((item) => ({
        index: item.index,
        value: item.value,
        percentage: item.percentage,
        formatted: Utils.formatCurrency(item.value),
        maxAllowed: item.maxAllowed
      }));
    }
    reset() {
      CacheManager.remove(PatrimonySync.cacheKey);
      CacheManager.remove(PatrimonySync.allocationsCacheKey);
      CacheManager.remove("asset_selection_filter");
      CacheManager.remove("resultado_sync_data");
      MainInputSync.setValue(0);
      AllocationSync.items.forEach((item) => {
        item.value = 0;
        item.percentage = 0;
        item.maxAllowed = 0;
        item.input.value = Utils.formatCurrency(0);
        item.slider.value = 0;
        AllocationSync.updatePercentageDisplay(item);
        AllocationSync.updateValorProduto(item);
        AllocationSync.updateBackgroundItemAcao(item);
      });
      AllocationSync.checkTotalAllocationStatus();
      document.dispatchEvent(
        new CustomEvent("patrimonySyncReset", {
          detail: {
            timestamp: Date.now()
          }
        })
      );
    }
    destroy() {
      this.isInitialized = false;
      AllocationSync.items = [];
      MainInputSync.input = null;
    }
    // Expõe AllocationSync para integração externa
    getAllocationSync() {
      return AllocationSync;
    }
  };
})();
//# sourceMappingURL=patrimony-sync.js.map
