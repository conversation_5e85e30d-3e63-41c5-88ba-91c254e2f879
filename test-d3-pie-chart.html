<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste D3.js Pie Chart - App Calc <PERSON></title>
    <script src="https://cdn.jsdelivr.net/npm/d3@7/dist/d3.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .main-area-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 600px;
        }
        .test-controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Teste D3.js Pie Chart - App <PERSON></h1>
    
    <div class="test-controls">
        <button onclick="testChart()">Criar Gráfico de Teste</button>
        <button onclick="updateChart()">Atualizar Dados</button>
        <button onclick="clearChart()">Limpar Gráfico</button>
    </div>
    
    <div class="main-area-content"></div>
    
    <script type="module">
        // Mock event coordinator
        const eventCoordinator = {
            events: {},
            on(event, callback) {
                if (!this.events[event]) this.events[event] = [];
                this.events[event].push(callback);
            },
            off(event) {
                delete this.events[event];
            },
            emit(event, data) {
                if (this.events[event]) {
                    this.events[event].forEach(callback => callback(data));
                }
            }
        };

        // Mock D3PieChartWebflowSystem (simplified version for testing)
        class D3PieChartWebflowSystem {
            constructor() {
                this.isInitialized = false;
                this.chartContainer = null;
                this.svg = null;
                this.currentData = null;
                this.tooltip = null;
                this.config = {
                    width: 400,
                    height: 400,
                    radius: 150,
                    colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'],
                    animationDuration: 750,
                    hoverScale: 1.05
                };
            }

            async init() {
                if (this.isInitialized) return;
                this.initializeContainer();
                this.createTooltip();
                this.isInitialized = true;
                console.log('✅ D3PieChart initialized');
            }

            initializeContainer() {
                this.chartContainer = document.querySelector('.main-area-content');
                if (!this.chartContainer) return;
                
                this.chartContainer.style.display = 'flex';
                this.chartContainer.style.flexDirection = 'column';
                this.chartContainer.style.justifyContent = 'center';
                this.chartContainer.style.alignItems = 'center';
                this.chartContainer.style.minHeight = '400px';
                this.chartContainer.style.padding = '20px';
            }

            createChart(data) {
                if (!this.chartContainer || !data) return;
                
                this.chartContainer.innerHTML = '';
                const { width, height, radius } = this.config;

                this.svg = d3.select(this.chartContainer)
                    .append('svg')
                    .attr('width', width)
                    .attr('height', height);

                const g = this.svg.append('g')
                    .attr('transform', `translate(${width / 2}, ${height / 2})`);

                const pie = d3.pie().value(d => d.value);
                const arc = d3.arc().innerRadius(0).outerRadius(radius);
                const hoverArc = d3.arc().innerRadius(0).outerRadius(radius * this.config.hoverScale);

                const processedData = this.processData(data);
                const pieData = pie(processedData);
                const color = d3.scaleOrdinal().range(this.config.colors);

                const slices = g.selectAll('.slice')
                    .data(pieData)
                    .enter()
                    .append('g')
                    .attr('class', 'slice');

                slices.append('path')
                    .attr('d', arc)
                    .attr('fill', (d, i) => this.config.colors[i % this.config.colors.length])
                    .attr('stroke', '#fff')
                    .attr('stroke-width', 2)
                    .style('cursor', 'pointer')
                    .on('mouseover', (event, d) => this.onSliceHover(event, d, hoverArc))
                    .on('mouseout', (event, d) => this.onSliceOut(event, d, arc));

                slices.append('text')
                    .attr('transform', d => `translate(${arc.centroid(d)})`)
                    .attr('text-anchor', 'middle')
                    .attr('font-size', '12px')
                    .attr('font-weight', 'bold')
                    .attr('fill', '#fff')
                    .text(d => `${d.data.percentage.toFixed(1)}%`);

                this.createLegend(processedData);
                this.currentData = data;
            }

            processData(data) {
                if (!data || !Array.isArray(data)) return [];
                return data.map(item => ({
                    name: item.name,
                    value: item.value,
                    percentage: item.percentage
                }));
            }

            createLegend(data) {
                const legendContainer = d3.select(this.chartContainer)
                    .append('div')
                    .style('margin-top', '20px')
                    .style('display', 'flex')
                    .style('flex-wrap', 'wrap')
                    .style('justify-content', 'center')
                    .style('gap', '10px');

                const legendItems = legendContainer.selectAll('.legend-item')
                    .data(data)
                    .enter()
                    .append('div')
                    .style('display', 'flex')
                    .style('align-items', 'center')
                    .style('gap', '5px')
                    .style('padding', '5px 10px')
                    .style('background', '#f8f9fa')
                    .style('border-radius', '15px')
                    .style('font-size', '12px');

                legendItems.append('div')
                    .style('width', '12px')
                    .style('height', '12px')
                    .style('border-radius', '50%')
                    .style('background', (d, i) => this.config.colors[i % this.config.colors.length]);

                legendItems.append('span')
                    .text(d => `${d.name}: ${d.percentage.toFixed(1)}%`);
            }

            createTooltip() {
                this.tooltip = d3.select('body')
                    .append('div')
                    .style('position', 'absolute')
                    .style('background', 'rgba(0, 0, 0, 0.9)')
                    .style('color', '#fff')
                    .style('padding', '10px')
                    .style('border-radius', '5px')
                    .style('font-size', '12px')
                    .style('pointer-events', 'none')
                    .style('opacity', 0)
                    .style('z-index', '1000');
            }

            onSliceHover(event, d, hoverArc) {
                d3.select(event.target).transition().duration(200).attr('d', hoverArc);
                
                if (this.tooltip) {
                    const formatValue = new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                    }).format(d.data.value);

                    this.tooltip
                        .style('opacity', 1)
                        .html(`<strong>${d.data.name}</strong><br/>Valor: ${formatValue}<br/>Percentual: ${d.data.percentage.toFixed(1)}%`)
                        .style('left', (event.pageX + 10) + 'px')
                        .style('top', (event.pageY - 10) + 'px');
                }
            }

            onSliceOut(event, d, arc) {
                d3.select(event.target).transition().duration(200).attr('d', arc);
                if (this.tooltip) {
                    this.tooltip.style('opacity', 0);
                }
            }

            clear() {
                if (this.chartContainer) {
                    this.chartContainer.innerHTML = '';
                }
            }
        }

        // Initialize system
        const pieChart = new D3PieChartWebflowSystem();
        pieChart.init();

        // Test functions
        window.testChart = function() {
            const testData = [
                { name: 'Ações', value: 50000, percentage: 50 },
                { name: 'Renda Fixa', value: 30000, percentage: 30 },
                { name: 'Fundos', value: 15000, percentage: 15 },
                { name: 'Outros', value: 5000, percentage: 5 }
            ];
            pieChart.createChart(testData);
        };

        window.updateChart = function() {
            const newData = [
                { name: 'Ações', value: 60000, percentage: 60 },
                { name: 'Renda Fixa', value: 25000, percentage: 25 },
                { name: 'Fundos', value: 10000, percentage: 10 },
                { name: 'Cripto', value: 5000, percentage: 5 }
            ];
            pieChart.createChart(newData);
        };

        window.clearChart = function() {
            pieChart.clear();
        };
    </script>
</body>
</html>
