{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/product-system.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Product System\r\n * Handles product interaction logic for patrimony allocation items\r\n */\r\nexport class ProductSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.Motion = null;\r\n    this.globalInteracting = false;\r\n    this.activeSlider = null;\r\n    this.items = [];\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.waitForMotion();\r\n    });\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  waitForMotion() {\r\n    if (window.Motion) {\r\n      this.Motion = window.Motion;\r\n      this.initProductSystem();\r\n    } else {\r\n      setTimeout(() => this.waitForMotion(), 50);\r\n    }\r\n  }\r\n\r\n  initProductSystem() {\r\n    const { animate, hover } = this.Motion;\r\n\r\n    // Configuração simplificada\r\n    const config = {\r\n      duration: {\r\n        fast: 0.3,\r\n        normal: 0.5,\r\n        slow: 0.6,\r\n      },\r\n      delay: {\r\n        deactivate: 1,\r\n        display: 0.45,\r\n      },\r\n      animation: {\r\n        blur: 8,\r\n        move: 15,\r\n        rotate: 10,\r\n      },\r\n      ease: 'circOut',\r\n    };\r\n\r\n    // Classe para gerenciar cada item\r\n    class ProductItem {\r\n      constructor(element, index, parentSystem) {\r\n        this.element = element;\r\n        this.index = index;\r\n        this.parentSystem = parentSystem;\r\n        this.activeDiv = element.querySelector('.active-produto-item');\r\n        this.disabledDiv = element.querySelector('.disabled-produto-item');\r\n        this.input = element.querySelector('.currency-input.individual');\r\n        this.slider = element.querySelector('range-slider');\r\n        this.sliderThumb = element.querySelector('[data-thumb]');\r\n        this.pinButton = element.querySelector('.pin-function');\r\n\r\n        this.state = {\r\n          active: false,\r\n          interacting: false,\r\n          sliderDragging: false,\r\n          animating: false,\r\n          pinned: false,\r\n        };\r\n\r\n        this.deactivateTimer = null;\r\n        this.init(animate, config);\r\n      }\r\n\r\n      init(animate, config) {\r\n        if (!this.activeDiv || !this.disabledDiv) return;\r\n\r\n        // Estado inicial\r\n        this.activeDiv.style.display = 'none';\r\n        this.disabledDiv.style.display = 'flex';\r\n\r\n        if (this.pinButton) {\r\n          this.pinButton.style.display = 'none';\r\n        }\r\n\r\n        this.setupEvents(animate, config);\r\n\r\n        // Animação de entrada\r\n        animate(\r\n          this.element,\r\n          {\r\n            opacity: [0, 1],\r\n            y: [30, 0],\r\n          },\r\n          {\r\n            duration: config.duration.normal,\r\n            ease: config.ease,\r\n            delay: this.index * 0.1,\r\n          }\r\n        );\r\n      }\r\n\r\n      setupEvents(animate, config) {\r\n        if (this.pinButton) {\r\n          this.pinButton.addEventListener('click', (e) => {\r\n            e.stopPropagation();\r\n            this.togglePin(animate);\r\n          });\r\n\r\n          this.pinButton.addEventListener('mousedown', (e) => {\r\n            e.stopPropagation();\r\n          });\r\n        }\r\n\r\n        // Eventos do container\r\n        const startInteraction = () => {\r\n          if (this.parentSystem.activeSlider && this.parentSystem.activeSlider !== this) return;\r\n          this.state.interacting = true;\r\n          this.activate(animate, config);\r\n        };\r\n\r\n        const endInteraction = () => {\r\n          if (!this.state.sliderDragging && !this.state.pinned) {\r\n            this.state.interacting = false;\r\n            this.scheduleDeactivate(animate, config);\r\n          }\r\n        };\r\n\r\n        this.element.addEventListener('mouseenter', startInteraction);\r\n        this.element.addEventListener('mouseleave', () => {\r\n          if (\r\n            !this.state.sliderDragging &&\r\n            !this.parentSystem.globalInteracting &&\r\n            !this.state.pinned\r\n          ) {\r\n            endInteraction();\r\n          }\r\n        });\r\n\r\n        this.element.addEventListener('touchstart', startInteraction, { passive: true });\r\n\r\n        // Input events\r\n        if (this.input) {\r\n          this.input.addEventListener('focus', () => {\r\n            this.state.interacting = true;\r\n            this.activate(animate, config);\r\n          });\r\n\r\n          this.input.addEventListener('blur', () => {\r\n            if (!this.state.pinned) {\r\n              this.state.interacting = false;\r\n              this.scheduleDeactivate(animate, config);\r\n            }\r\n          });\r\n\r\n          this.input.addEventListener('mousedown', (e) => {\r\n            e.stopPropagation();\r\n            this.state.interacting = true;\r\n          });\r\n        }\r\n\r\n        // Eventos do Slider\r\n        if (this.slider) {\r\n          const startSliderDrag = () => {\r\n            this.state.sliderDragging = true;\r\n            this.state.interacting = true;\r\n            this.parentSystem.globalInteracting = true;\r\n            this.parentSystem.activeSlider = this;\r\n            this.activate(animate, config);\r\n            this.slider.classList.add('dragging');\r\n          };\r\n\r\n          const endSliderDrag = () => {\r\n            if (this.state.sliderDragging) {\r\n              this.state.sliderDragging = false;\r\n              this.parentSystem.globalInteracting = false;\r\n              this.parentSystem.activeSlider = null;\r\n              this.slider.classList.remove('dragging');\r\n\r\n              const mouseOverElement = this.element.matches(':hover');\r\n              if (!mouseOverElement && !this.state.pinned) {\r\n                this.state.interacting = false;\r\n                this.scheduleDeactivate(animate, config);\r\n              }\r\n            }\r\n          };\r\n\r\n          this.slider.addEventListener('mousedown', startSliderDrag);\r\n          if (this.sliderThumb) {\r\n            this.sliderThumb.addEventListener('mousedown', startSliderDrag);\r\n          }\r\n\r\n          this.slider.addEventListener('touchstart', startSliderDrag, { passive: true });\r\n          if (this.sliderThumb) {\r\n            this.sliderThumb.addEventListener('touchstart', startSliderDrag, { passive: true });\r\n          }\r\n\r\n          document.addEventListener('mouseup', endSliderDrag);\r\n          document.addEventListener('touchend', endSliderDrag);\r\n\r\n          this.slider.addEventListener('click', (e) => {\r\n            e.stopPropagation();\r\n          });\r\n\r\n          this.slider.addEventListener('input', () => {\r\n            this.state.interacting = true;\r\n            this.activate(animate, config);\r\n          });\r\n        }\r\n\r\n        // Hover effect usando Motion\r\n        if (this.parentSystem.Motion.hover && this.parentSystem.Motion.animate) {\r\n          this.parentSystem.Motion.hover(this.element, (element) => {\r\n            this.parentSystem.Motion.animate(\r\n              element,\r\n              {\r\n                scale: 1,\r\n                boxShadow: '0 8px 25px rgba(0,0,0,0.1)',\r\n              },\r\n              {\r\n                duration: 0.2,\r\n                ease: 'circOut',\r\n              }\r\n            );\r\n\r\n            return () => {\r\n              this.parentSystem.Motion.animate(\r\n                element,\r\n                {\r\n                  scale: 1,\r\n                  boxShadow: '0 4px 15px rgba(0,0,0,0.05)',\r\n                },\r\n                {\r\n                  duration: 0.15,\r\n                  ease: 'circOut',\r\n                }\r\n              );\r\n            };\r\n          });\r\n        }\r\n      }\r\n\r\n      togglePin(animate) {\r\n        this.state.pinned = !this.state.pinned;\r\n\r\n        if (this.state.pinned) {\r\n          this.pinButton.classList.add('active');\r\n          clearTimeout(this.deactivateTimer);\r\n        } else {\r\n          this.pinButton.classList.remove('active');\r\n          if (!this.state.interacting && !this.state.sliderDragging) {\r\n            this.scheduleDeactivate(animate, { delay: { deactivate: 1 } });\r\n          }\r\n        }\r\n\r\n        animate(\r\n          this.pinButton,\r\n          {\r\n            scale: [1.2, 1],\r\n            rotate: this.state.pinned ? 45 : 0,\r\n          },\r\n          {\r\n            duration: 0.3,\r\n            ease: 'backOut',\r\n          }\r\n        );\r\n      }\r\n\r\n      async activate(animate, config) {\r\n        if (this.state.active || this.state.animating) return;\r\n\r\n        clearTimeout(this.deactivateTimer);\r\n        this.state.active = true;\r\n        this.state.animating = true;\r\n\r\n        await animate(\r\n          this.disabledDiv,\r\n          {\r\n            opacity: 0,\r\n            y: -config.animation.move,\r\n            filter: `blur(${config.animation.blur}px)`,\r\n          },\r\n          {\r\n            duration: config.duration.fast,\r\n            ease: 'circIn',\r\n          }\r\n        ).finished;\r\n\r\n        this.disabledDiv.style.display = 'none';\r\n        this.activeDiv.style.display = 'block';\r\n\r\n        if (this.pinButton) {\r\n          this.pinButton.style.display = 'block';\r\n          animate(\r\n            this.pinButton,\r\n            {\r\n              opacity: [0, 1],\r\n              scale: [0.8, 1],\r\n            },\r\n            {\r\n              duration: 0.3,\r\n              ease: 'backOut',\r\n              delay: 0.1,\r\n            }\r\n          );\r\n        }\r\n\r\n        await animate(\r\n          this.activeDiv,\r\n          {\r\n            opacity: [0, 1],\r\n            y: [config.animation.move, 0],\r\n            filter: ['blur(5px)', 'blur(0px)'],\r\n          },\r\n          {\r\n            duration: config.duration.normal,\r\n            ease: 'backOut',\r\n          }\r\n        ).finished;\r\n\r\n        this.state.animating = false;\r\n      }\r\n\r\n      scheduleDeactivate(animate, config) {\r\n        clearTimeout(this.deactivateTimer);\r\n\r\n        if (\r\n          this.state.interacting ||\r\n          this.state.sliderDragging ||\r\n          this.parentSystem.globalInteracting ||\r\n          this.state.pinned\r\n        ) {\r\n          return;\r\n        }\r\n\r\n        this.deactivateTimer = setTimeout(() => {\r\n          if (\r\n            !this.state.interacting &&\r\n            !this.state.sliderDragging &&\r\n            !this.parentSystem.globalInteracting &&\r\n            !this.state.pinned\r\n          ) {\r\n            this.deactivate(animate, config);\r\n          }\r\n        }, config.delay.deactivate * 1000);\r\n      }\r\n\r\n      async deactivate(animate, config) {\r\n        if (\r\n          !this.state.active ||\r\n          this.state.animating ||\r\n          this.state.sliderDragging ||\r\n          this.state.pinned\r\n        )\r\n          return;\r\n\r\n        this.state.active = false;\r\n        this.state.animating = true;\r\n\r\n        if (this.pinButton) {\r\n          await animate(\r\n            this.pinButton,\r\n            {\r\n              opacity: 0,\r\n              scale: 0.8,\r\n            },\r\n            {\r\n              duration: 0.2,\r\n              ease: 'circIn',\r\n            }\r\n          ).finished;\r\n          this.pinButton.style.display = 'none';\r\n        }\r\n\r\n        await animate(\r\n          this.activeDiv,\r\n          {\r\n            opacity: 0,\r\n            y: config.animation.move / 2,\r\n            filter: 'blur(5px)',\r\n          },\r\n          {\r\n            duration: config.duration.fast,\r\n            ease: config.ease,\r\n          }\r\n        ).finished;\r\n\r\n        this.activeDiv.style.display = 'none';\r\n        this.disabledDiv.style.display = 'flex';\r\n\r\n        await animate(\r\n          this.disabledDiv,\r\n          {\r\n            opacity: [0, 1],\r\n            y: [0, 0],\r\n            filter: ['blur(5px)', 'blur(0px)'],\r\n          },\r\n          {\r\n            duration: config.duration.normal,\r\n            ease: config.ease,\r\n          }\r\n        ).finished;\r\n\r\n        this.state.animating = false;\r\n      }\r\n    }\r\n\r\n    // Inicializa todos os items\r\n    const items = document.querySelectorAll('.patrimonio_interactive_item');\r\n    items.forEach((item, index) => {\r\n      this.items.push(new ProductItem(item, index, this));\r\n    });\r\n\r\n    // Adiciona estilos para feedback visual durante arraste\r\n    this.addDragStyles();\r\n  }\r\n\r\n  addDragStyles() {\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      range-slider.dragging {\r\n        cursor: grabbing !important;\r\n      }\r\n      range-slider.dragging [data-thumb] {\r\n        cursor: grabbing !important;\r\n        transform: scale(1.1);\r\n        transition: transform 0.2s ease;\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACItF,MAAM,gBAAN,MAAoB;AAAA,IAJ3B,OAI2B;AAAA;AAAA;AAAA,IACzB,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,SAAS;AACd,WAAK,oBAAoB;AACzB,WAAK,eAAe;AACpB,WAAK,QAAQ,CAAC;AAAA,IAChB;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,cAAc;AAAA,MACrB,CAAC;AAED,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,gBAAgB;AACd,UAAI,OAAO,QAAQ;AACjB,aAAK,SAAS,OAAO;AACrB,aAAK,kBAAkB;AAAA,MACzB,OAAO;AACL,mBAAW,MAAM,KAAK,cAAc,GAAG,EAAE;AAAA,MAC3C;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,YAAM,EAAE,SAAS,MAAM,IAAI,KAAK;AAGhC,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,QACR;AAAA,QACA,OAAO;AAAA,UACL,YAAY;AAAA,UACZ,SAAS;AAAA,QACX;AAAA,QACA,WAAW;AAAA,UACT,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,QACA,MAAM;AAAA,MACR;AAAA,MAGA,MAAM,YAAY;AAAA,QAzDtB,OAyDsB;AAAA;AAAA;AAAA,QAChB,YAAY,SAAS,OAAO,cAAc;AACxC,eAAK,UAAU;AACf,eAAK,QAAQ;AACb,eAAK,eAAe;AACpB,eAAK,YAAY,QAAQ,cAAc,sBAAsB;AAC7D,eAAK,cAAc,QAAQ,cAAc,wBAAwB;AACjE,eAAK,QAAQ,QAAQ,cAAc,4BAA4B;AAC/D,eAAK,SAAS,QAAQ,cAAc,cAAc;AAClD,eAAK,cAAc,QAAQ,cAAc,cAAc;AACvD,eAAK,YAAY,QAAQ,cAAc,eAAe;AAEtD,eAAK,QAAQ;AAAA,YACX,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,gBAAgB;AAAA,YAChB,WAAW;AAAA,YACX,QAAQ;AAAA,UACV;AAEA,eAAK,kBAAkB;AACvB,eAAK,KAAK,SAAS,MAAM;AAAA,QAC3B;AAAA,QAEA,KAAKA,UAASC,SAAQ;AACpB,cAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAa;AAG1C,eAAK,UAAU,MAAM,UAAU;AAC/B,eAAK,YAAY,MAAM,UAAU;AAEjC,cAAI,KAAK,WAAW;AAClB,iBAAK,UAAU,MAAM,UAAU;AAAA,UACjC;AAEA,eAAK,YAAYD,UAASC,OAAM;AAGhC,UAAAD;AAAA,YACE,KAAK;AAAA,YACL;AAAA,cACE,SAAS,CAAC,GAAG,CAAC;AAAA,cACd,GAAG,CAAC,IAAI,CAAC;AAAA,YACX;AAAA,YACA;AAAA,cACE,UAAUC,QAAO,SAAS;AAAA,cAC1B,MAAMA,QAAO;AAAA,cACb,OAAO,KAAK,QAAQ;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,QAEA,YAAYD,UAASC,SAAQ;AAC3B,cAAI,KAAK,WAAW;AAClB,iBAAK,UAAU,iBAAiB,SAAS,CAAC,MAAM;AAC9C,gBAAE,gBAAgB;AAClB,mBAAK,UAAUD,QAAO;AAAA,YACxB,CAAC;AAED,iBAAK,UAAU,iBAAiB,aAAa,CAAC,MAAM;AAClD,gBAAE,gBAAgB;AAAA,YACpB,CAAC;AAAA,UACH;AAGA,gBAAM,mBAAmB,6BAAM;AAC7B,gBAAI,KAAK,aAAa,gBAAgB,KAAK,aAAa,iBAAiB,KAAM;AAC/E,iBAAK,MAAM,cAAc;AACzB,iBAAK,SAASA,UAASC,OAAM;AAAA,UAC/B,GAJyB;AAMzB,gBAAM,iBAAiB,6BAAM;AAC3B,gBAAI,CAAC,KAAK,MAAM,kBAAkB,CAAC,KAAK,MAAM,QAAQ;AACpD,mBAAK,MAAM,cAAc;AACzB,mBAAK,mBAAmBD,UAASC,OAAM;AAAA,YACzC;AAAA,UACF,GALuB;AAOvB,eAAK,QAAQ,iBAAiB,cAAc,gBAAgB;AAC5D,eAAK,QAAQ,iBAAiB,cAAc,MAAM;AAChD,gBACE,CAAC,KAAK,MAAM,kBACZ,CAAC,KAAK,aAAa,qBACnB,CAAC,KAAK,MAAM,QACZ;AACA,6BAAe;AAAA,YACjB;AAAA,UACF,CAAC;AAED,eAAK,QAAQ,iBAAiB,cAAc,kBAAkB,EAAE,SAAS,KAAK,CAAC;AAG/E,cAAI,KAAK,OAAO;AACd,iBAAK,MAAM,iBAAiB,SAAS,MAAM;AACzC,mBAAK,MAAM,cAAc;AACzB,mBAAK,SAASD,UAASC,OAAM;AAAA,YAC/B,CAAC;AAED,iBAAK,MAAM,iBAAiB,QAAQ,MAAM;AACxC,kBAAI,CAAC,KAAK,MAAM,QAAQ;AACtB,qBAAK,MAAM,cAAc;AACzB,qBAAK,mBAAmBD,UAASC,OAAM;AAAA,cACzC;AAAA,YACF,CAAC;AAED,iBAAK,MAAM,iBAAiB,aAAa,CAAC,MAAM;AAC9C,gBAAE,gBAAgB;AAClB,mBAAK,MAAM,cAAc;AAAA,YAC3B,CAAC;AAAA,UACH;AAGA,cAAI,KAAK,QAAQ;AACf,kBAAM,kBAAkB,6BAAM;AAC5B,mBAAK,MAAM,iBAAiB;AAC5B,mBAAK,MAAM,cAAc;AACzB,mBAAK,aAAa,oBAAoB;AACtC,mBAAK,aAAa,eAAe;AACjC,mBAAK,SAASD,UAASC,OAAM;AAC7B,mBAAK,OAAO,UAAU,IAAI,UAAU;AAAA,YACtC,GAPwB;AASxB,kBAAM,gBAAgB,6BAAM;AAC1B,kBAAI,KAAK,MAAM,gBAAgB;AAC7B,qBAAK,MAAM,iBAAiB;AAC5B,qBAAK,aAAa,oBAAoB;AACtC,qBAAK,aAAa,eAAe;AACjC,qBAAK,OAAO,UAAU,OAAO,UAAU;AAEvC,sBAAM,mBAAmB,KAAK,QAAQ,QAAQ,QAAQ;AACtD,oBAAI,CAAC,oBAAoB,CAAC,KAAK,MAAM,QAAQ;AAC3C,uBAAK,MAAM,cAAc;AACzB,uBAAK,mBAAmBD,UAASC,OAAM;AAAA,gBACzC;AAAA,cACF;AAAA,YACF,GAbsB;AAetB,iBAAK,OAAO,iBAAiB,aAAa,eAAe;AACzD,gBAAI,KAAK,aAAa;AACpB,mBAAK,YAAY,iBAAiB,aAAa,eAAe;AAAA,YAChE;AAEA,iBAAK,OAAO,iBAAiB,cAAc,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC7E,gBAAI,KAAK,aAAa;AACpB,mBAAK,YAAY,iBAAiB,cAAc,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAAA,YACpF;AAEA,qBAAS,iBAAiB,WAAW,aAAa;AAClD,qBAAS,iBAAiB,YAAY,aAAa;AAEnD,iBAAK,OAAO,iBAAiB,SAAS,CAAC,MAAM;AAC3C,gBAAE,gBAAgB;AAAA,YACpB,CAAC;AAED,iBAAK,OAAO,iBAAiB,SAAS,MAAM;AAC1C,mBAAK,MAAM,cAAc;AACzB,mBAAK,SAASD,UAASC,OAAM;AAAA,YAC/B,CAAC;AAAA,UACH;AAGA,cAAI,KAAK,aAAa,OAAO,SAAS,KAAK,aAAa,OAAO,SAAS;AACtE,iBAAK,aAAa,OAAO,MAAM,KAAK,SAAS,CAAC,YAAY;AACxD,mBAAK,aAAa,OAAO;AAAA,gBACvB;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,kBACE,UAAU;AAAA,kBACV,MAAM;AAAA,gBACR;AAAA,cACF;AAEA,qBAAO,MAAM;AACX,qBAAK,aAAa,OAAO;AAAA,kBACvB;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,WAAW;AAAA,kBACb;AAAA,kBACA;AAAA,oBACE,UAAU;AAAA,oBACV,MAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QAEA,UAAUD,UAAS;AACjB,eAAK,MAAM,SAAS,CAAC,KAAK,MAAM;AAEhC,cAAI,KAAK,MAAM,QAAQ;AACrB,iBAAK,UAAU,UAAU,IAAI,QAAQ;AACrC,yBAAa,KAAK,eAAe;AAAA,UACnC,OAAO;AACL,iBAAK,UAAU,UAAU,OAAO,QAAQ;AACxC,gBAAI,CAAC,KAAK,MAAM,eAAe,CAAC,KAAK,MAAM,gBAAgB;AACzD,mBAAK,mBAAmBA,UAAS,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC;AAAA,YAC/D;AAAA,UACF;AAEA,UAAAA;AAAA,YACE,KAAK;AAAA,YACL;AAAA,cACE,OAAO,CAAC,KAAK,CAAC;AAAA,cACd,QAAQ,KAAK,MAAM,SAAS,KAAK;AAAA,YACnC;AAAA,YACA;AAAA,cACE,UAAU;AAAA,cACV,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QAEA,MAAM,SAASA,UAASC,SAAQ;AAC9B,cAAI,KAAK,MAAM,UAAU,KAAK,MAAM,UAAW;AAE/C,uBAAa,KAAK,eAAe;AACjC,eAAK,MAAM,SAAS;AACpB,eAAK,MAAM,YAAY;AAEvB,gBAAMD;AAAA,YACJ,KAAK;AAAA,YACL;AAAA,cACE,SAAS;AAAA,cACT,GAAG,CAACC,QAAO,UAAU;AAAA,cACrB,QAAQ,QAAQA,QAAO,UAAU,IAAI;AAAA,YACvC;AAAA,YACA;AAAA,cACE,UAAUA,QAAO,SAAS;AAAA,cAC1B,MAAM;AAAA,YACR;AAAA,UACF,EAAE;AAEF,eAAK,YAAY,MAAM,UAAU;AACjC,eAAK,UAAU,MAAM,UAAU;AAE/B,cAAI,KAAK,WAAW;AAClB,iBAAK,UAAU,MAAM,UAAU;AAC/B,YAAAD;AAAA,cACE,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,CAAC,GAAG,CAAC;AAAA,gBACd,OAAO,CAAC,KAAK,CAAC;AAAA,cAChB;AAAA,cACA;AAAA,gBACE,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAEA,gBAAMA;AAAA,YACJ,KAAK;AAAA,YACL;AAAA,cACE,SAAS,CAAC,GAAG,CAAC;AAAA,cACd,GAAG,CAACC,QAAO,UAAU,MAAM,CAAC;AAAA,cAC5B,QAAQ,CAAC,aAAa,WAAW;AAAA,YACnC;AAAA,YACA;AAAA,cACE,UAAUA,QAAO,SAAS;AAAA,cAC1B,MAAM;AAAA,YACR;AAAA,UACF,EAAE;AAEF,eAAK,MAAM,YAAY;AAAA,QACzB;AAAA,QAEA,mBAAmBD,UAASC,SAAQ;AAClC,uBAAa,KAAK,eAAe;AAEjC,cACE,KAAK,MAAM,eACX,KAAK,MAAM,kBACX,KAAK,aAAa,qBAClB,KAAK,MAAM,QACX;AACA;AAAA,UACF;AAEA,eAAK,kBAAkB,WAAW,MAAM;AACtC,gBACE,CAAC,KAAK,MAAM,eACZ,CAAC,KAAK,MAAM,kBACZ,CAAC,KAAK,aAAa,qBACnB,CAAC,KAAK,MAAM,QACZ;AACA,mBAAK,WAAWD,UAASC,OAAM;AAAA,YACjC;AAAA,UACF,GAAGA,QAAO,MAAM,aAAa,GAAI;AAAA,QACnC;AAAA,QAEA,MAAM,WAAWD,UAASC,SAAQ;AAChC,cACE,CAAC,KAAK,MAAM,UACZ,KAAK,MAAM,aACX,KAAK,MAAM,kBACX,KAAK,MAAM;AAEX;AAEF,eAAK,MAAM,SAAS;AACpB,eAAK,MAAM,YAAY;AAEvB,cAAI,KAAK,WAAW;AAClB,kBAAMD;AAAA,cACJ,KAAK;AAAA,cACL;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,UAAU;AAAA,gBACV,MAAM;AAAA,cACR;AAAA,YACF,EAAE;AACF,iBAAK,UAAU,MAAM,UAAU;AAAA,UACjC;AAEA,gBAAMA;AAAA,YACJ,KAAK;AAAA,YACL;AAAA,cACE,SAAS;AAAA,cACT,GAAGC,QAAO,UAAU,OAAO;AAAA,cAC3B,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,UAAUA,QAAO,SAAS;AAAA,cAC1B,MAAMA,QAAO;AAAA,YACf;AAAA,UACF,EAAE;AAEF,eAAK,UAAU,MAAM,UAAU;AAC/B,eAAK,YAAY,MAAM,UAAU;AAEjC,gBAAMD;AAAA,YACJ,KAAK;AAAA,YACL;AAAA,cACE,SAAS,CAAC,GAAG,CAAC;AAAA,cACd,GAAG,CAAC,GAAG,CAAC;AAAA,cACR,QAAQ,CAAC,aAAa,WAAW;AAAA,YACnC;AAAA,YACA;AAAA,cACE,UAAUC,QAAO,SAAS;AAAA,cAC1B,MAAMA,QAAO;AAAA,YACf;AAAA,UACF,EAAE;AAEF,eAAK,MAAM,YAAY;AAAA,QACzB;AAAA,MACF;AAGA,YAAM,QAAQ,SAAS,iBAAiB,8BAA8B;AACtE,YAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,aAAK,MAAM,KAAK,IAAI,YAAY,MAAM,OAAO,IAAI,CAAC;AAAA,MACpD,CAAC;AAGD,WAAK,cAAc;AAAA,IACrB;AAAA,IAEA,gBAAgB;AACd,YAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUpB,eAAS,KAAK,YAAY,KAAK;AAAA,IACjC;AAAA,EACF;", "names": ["animate", "config"]}