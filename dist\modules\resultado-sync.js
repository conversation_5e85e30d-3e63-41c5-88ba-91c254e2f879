"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/config/comissoes-config.js
  var COMISSOES_CONFIG = {
    "Renda Fixa": {
      "CDB": {
        min: 0.5,
        max: 2,
        description: "Certificado de Dep\xF3sito Banc\xE1rio",
        risco: "Baixo"
      },
      "CRI": {
        min: 0.8,
        max: 2.5,
        description: "Certificado de Receb\xEDveis Imobili\xE1rios",
        risco: "M\xE9dio"
      },
      "T\xEDtulos P\xFAblicos": {
        min: 0.3,
        max: 1.5,
        description: "<PERSON><PERSON><PERSON> Di<PERSON>",
        risco: "<PERSON><PERSON>o"
      }
    },
    "Fundo de investimento": {
      "A\xE7\xF5es": {
        min: 1.5,
        max: 3,
        description: "Fundos de A\xE7\xF5es",
        risco: "Alto"
      },
      "Liquidez": {
        min: 0.3,
        max: 1,
        description: "Fundos de Liquidez",
        risco: "Baixo"
      },
      "Renda Fixa": {
        min: 0.8,
        max: 2,
        description: "Fundos de Renda Fixa",
        risco: "Baixo"
      }
    },
    "Renda vari\xE1vel": {
      "A\xE7\xF5es": {
        min: 2,
        max: 4,
        description: "A\xE7\xF5es Individuais",
        risco: "Alto"
      },
      "Estruturada": {
        min: 1,
        max: 3.5,
        description: "Produtos Estruturados",
        risco: "Alto"
      },
      "Carteira administrada": {
        min: 1.5,
        max: 3,
        description: "Carteira Administrada",
        risco: "M\xE9dio-Alto"
      }
    },
    "Outros": {
      "Poupan\xE7a": {
        min: 0,
        max: 0,
        description: "Caderneta de Poupan\xE7a",
        risco: "Muito Baixo"
      },
      "Previd\xEAncia": {
        min: 1,
        max: 2.5,
        description: "Previd\xEAncia Privada",
        risco: "Vari\xE1vel"
      },
      "Im\xF3vel": {
        min: 3,
        max: 6,
        description: "Investimento Imobili\xE1rio",
        risco: "M\xE9dio"
      },
      "COE": {
        min: 0.5,
        max: 2,
        description: "Certificado de Opera\xE7\xF5es Estruturadas",
        risco: "M\xE9dio"
      },
      "Opera\xE7\xE3o compromissada": {
        min: 0.3,
        max: 1.2,
        description: "Opera\xE7\xE3o Compromissada",
        risco: "Baixo"
      },
      "Criptoativos": {
        min: 0.5,
        max: 2,
        description: "Criptomoedas e Tokens",
        risco: "Muito Alto"
      }
    }
  };
  var ComissoesUtils = class {
    static {
      __name(this, "ComissoesUtils");
    }
    /**
     * Obtém dados de comissão para uma categoria e produto específicos
     * @param {string} category - Categoria do ativo
     * @param {string} product - Produto específico
     * @returns {Object|null} Dados de comissão ou null se não encontrado
     */
    static getComissaoData(category, product) {
      return COMISSOES_CONFIG[category]?.[product] || null;
    }
    /**
     * Calcula valores de comissão baseado no valor investido
     * @param {number} valor - Valor investido
     * @param {string} category - Categoria do ativo
     * @param {string} product - Produto específico
     * @returns {Object|null} Valores calculados ou null
     */
    static calcularComissao(valor, category, product) {
      const comissaoData = this.getComissaoData(category, product);
      if (!comissaoData || valor <= 0) {
        return null;
      }
      return {
        valorMinimo: valor * comissaoData.min / 100,
        valorMaximo: valor * comissaoData.max / 100,
        taxaMinima: comissaoData.min,
        taxaMaxima: comissaoData.max,
        description: comissaoData.description,
        risco: comissaoData.risco
      };
    }
    /**
     * Obtém todas as categorias disponíveis
     * @returns {string[]} Array de categorias
     */
    static getCategorias() {
      return Object.keys(COMISSOES_CONFIG);
    }
    /**
     * Obtém todos os produtos de uma categoria
     * @param {string} category - Categoria
     * @returns {string[]} Array de produtos
     */
    static getProdutos(category) {
      return Object.keys(COMISSOES_CONFIG[category] || {});
    }
    /**
     * Obtém estatísticas gerais das comissões
     * @returns {Object} Estatísticas
     */
    static getEstatisticas() {
      const stats = {
        totalCategorias: 0,
        totalProdutos: 0,
        menorTaxa: Infinity,
        maiorTaxa: 0,
        taxaMedia: 0
      };
      let totalTaxas = 0;
      let countTaxas = 0;
      Object.keys(COMISSOES_CONFIG).forEach((category) => {
        stats.totalCategorias++;
        Object.keys(COMISSOES_CONFIG[category]).forEach((product) => {
          stats.totalProdutos++;
          const data = COMISSOES_CONFIG[category][product];
          if (data.min < stats.menorTaxa) stats.menorTaxa = data.min;
          if (data.max > stats.maiorTaxa) stats.maiorTaxa = data.max;
          totalTaxas += data.min + data.max;
          countTaxas += 2;
        });
      });
      stats.taxaMedia = countTaxas > 0 ? totalTaxas / countTaxas : 0;
      return stats;
    }
    /**
     * Valida se uma categoria e produto existem
     * @param {string} category - Categoria
     * @param {string} product - Produto
     * @returns {boolean} True se válido
     */
    static isValid(category, product) {
      return !!COMISSOES_CONFIG[category]?.[product];
    }
    /**
     * Obtém produtos por nível de risco
     * @param {string} risco - Nível de risco
     * @returns {Array} Array de produtos com esse nível de risco
     */
    static getProdutosPorRisco(risco) {
      const produtos = [];
      Object.keys(COMISSOES_CONFIG).forEach((category) => {
        Object.keys(COMISSOES_CONFIG[category]).forEach((product) => {
          const data = COMISSOES_CONFIG[category][product];
          if (data.risco === risco) {
            produtos.push({
              category,
              product,
              ...data
            });
          }
        });
      });
      return produtos;
    }
    /**
     * Obtém range de comissões para um conjunto de ativos
     * @param {Array} ativos - Array de {category, product, valor}
     * @returns {Object} Range total de comissões
     */
    static calcularRangeTotal(ativos) {
      let totalMinimo = 0;
      let totalMaximo = 0;
      ativos.forEach((ativo) => {
        const comissao = this.calcularComissao(ativo.valor, ativo.category, ativo.product);
        if (comissao) {
          totalMinimo += comissao.valorMinimo;
          totalMaximo += comissao.valorMaximo;
        }
      });
      return {
        totalMinimo,
        totalMaximo,
        economia: totalMaximo - totalMinimo
      };
    }
  };
  var comissoes_config_default = COMISSOES_CONFIG;

  // src/modules/resultado-sync.js
  var Utils = {
    formatCurrency(value) {
      return new Intl.NumberFormat("pt-BR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    },
    parseCurrencyValue(value) {
      if (!value || typeof value !== "string") return 0;
      const cleanValue = value.replace(/[^\d,]/g, "").replace(",", ".");
      return parseFloat(cleanValue) || 0;
    },
    calculatePercentage(value, total) {
      if (!total || total === 0) return 0;
      return value / total * 100;
    },
    formatPercentage(value) {
      return `${value.toFixed(1)}%`;
    },
    debounce(func, wait) {
      let timeout;
      return /* @__PURE__ */ __name(function executedFunction(...args) {
        const later = /* @__PURE__ */ __name(() => {
          clearTimeout(timeout);
          func(...args);
        }, "later");
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      }, "executedFunction");
    }
  };
  var ResultadoSyncSystem = class {
    static {
      __name(this, "ResultadoSyncSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.patrimonioItems = /* @__PURE__ */ new Map();
      this.resultadoPatrimonioItems = /* @__PURE__ */ new Map();
      this.resultadoComissaoItems = /* @__PURE__ */ new Map();
      this.selectedAssets = /* @__PURE__ */ new Set();
      this.totalPatrimonio = 0;
      this.cacheKey = "resultado_sync_data";
      this.attributeMapping = {
        // Patrimônio -> Resultado mapping
        "Outros-Popupan\xE7a": "Outros-Poupan\xE7a",
        // Corrige typo "Popupança"
        "Opera\xE7\xE3o compromissada-Criptoativos": "Outros-Criptoativos",
        // Corrige categoria errada
        "Fundo de Investimento-Liquidez": "Fundo de Investimento-Liqudez",
        // Corrige typo no resultado
        "Fundo de Investimento-A\xE7\xF5es": "Fundo de investimento-A\xE7\xF5es",
        "Fundo de Investimento-Renda Fixa": "Fundo de investimento-Renda Fixa",
        "Renda Vari\xE1vel-A\xE7\xF5es": "Renda Vari\xE1vel-A\xE7\xF5es",
        // Mantém capitalização do resultado
        "Renda Vari\xE1vel-Estruturada": "Renda Vari\xE1vel-Estruturada",
        "Renda Vari\xE1vel-Carteira administrada": "Renda Vari\xE1vel-Carteira administrada"
      };
    }
    // Cache Manager - similar to other modules
    get CacheManager() {
      return {
        set: /* @__PURE__ */ __name((key, value) => {
          try {
            window.localStorage.setItem(key, JSON.stringify(value));
          } catch {
          }
        }, "set"),
        get: /* @__PURE__ */ __name((key) => {
          try {
            const value = window.localStorage.getItem(key);
            return value ? JSON.parse(value) : null;
          } catch {
            return null;
          }
        }, "get"),
        remove: /* @__PURE__ */ __name((key) => {
          try {
            window.localStorage.removeItem(key);
          } catch {
          }
        }, "remove")
      };
    }
    init() {
      if (this.isInitialized) {
        return;
      }
      this.waitForDOM().then(() => {
        this.waitForDependencies().then(() => {
          this.cacheElements();
          this.setupEventListeners();
          this.setupCacheListeners();
          this.loadCachedData();
          this.initialSync();
          this.isInitialized = true;
          document.dispatchEvent(
            new CustomEvent("resultadoSyncReady", {
              detail: { system: this }
            })
          );
        });
      });
    }
    waitForDOM() {
      return new Promise((resolve) => {
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", resolve);
        } else {
          resolve();
        }
      });
    }
    // Normaliza chaves para corrigir inconsistências
    normalizeKey(category, product) {
      const originalKey = `${category}-${product}`;
      return this.attributeMapping[originalKey] || originalKey;
    }
    // Obtém chave mapeada para resultado
    getResultKey(patrimonioKey) {
      return this.attributeMapping[patrimonioKey] || patrimonioKey;
    }
    waitForDependencies() {
      return new Promise((resolve) => {
        const checkDependencies = /* @__PURE__ */ __name(() => {
          const patrimonySyncReady = document.querySelector(".patrimonio_interactive_item");
          const resultadoSection = document.querySelector("._4-section-resultado");
          if (patrimonySyncReady && resultadoSection) {
            resolve();
          } else {
            setTimeout(checkDependencies, 100);
          }
        }, "checkDependencies");
        checkDependencies();
      });
    }
    cacheElements() {
      this.patrimonioTotalElement = document.querySelector('[data-patrimonio-total="true"]');
      const patrimonioElements = document.querySelectorAll(".patrimonio_interactive_item");
      patrimonioElements.forEach((element) => {
        const category = element.getAttribute("ativo-category");
        const product = element.getAttribute("ativo-product");
        if (category && product) {
          const key = `${category}-${product}`;
          const input = element.querySelector(".currency-input.individual");
          const slider = element.querySelector("range-slider");
          const percentage = element.querySelector(".porcentagem-calculadora");
          this.patrimonioItems.set(key, {
            element,
            category,
            product,
            input,
            slider,
            percentage,
            key
          });
        }
      });
      const resultadoPatrimonioElements = document.querySelectorAll(
        ".patrimonio-ativos-group .ativos-produtos-item"
      );
      resultadoPatrimonioElements.forEach((element) => {
        const category = element.getAttribute("ativo-category");
        const product = element.getAttribute("ativo-product");
        if (category && product) {
          const key = `${category}-${product}`;
          const valorElement = element.querySelector(".ativo-dinheiro .valor-minimo");
          const percentageElement = element.querySelector(".porcentagem-calculadora.v2");
          this.resultadoPatrimonioItems.set(key, {
            element,
            category,
            product,
            valorElement,
            percentageElement,
            key
          });
        }
      });
      const resultadoComissaoElements = document.querySelectorAll(
        ".ativos-content-float .ativos-produtos-item"
      );
      resultadoComissaoElements.forEach((element) => {
        const category = element.getAttribute("ativo-category");
        const product = element.getAttribute("ativo-product");
        if (category && product) {
          const key = `${category}-${product}`;
          const taxaMinimaElement = element.querySelector(".ativos-produto-porcentagem .taxa-minima");
          const taxaMaximaElement = element.querySelector(".ativos-produto-porcentagem .taxa-maxima");
          const valorMinimoElement = element.querySelector(
            ".ativo-valor-minimo .ativo-dinheiro .valor-minimo"
          );
          const valorMaximoElement = element.querySelector(
            ".ativo-valor-maximo .ativo-dinheiro .valor-maximo"
          );
          this.resultadoComissaoItems.set(key, {
            element,
            category,
            product,
            taxaMinimaElement,
            taxaMaximaElement,
            valorMinimoElement,
            valorMaximoElement,
            key
          });
        }
      });
    }
    setupEventListeners() {
      document.addEventListener("patrimonySyncReady", () => {
        this.syncAllValues();
      });
      document.addEventListener("currencyInputChanged", (event) => {
        this.handlePatrimonioChange(event.detail);
      });
      document.addEventListener("sliderChanged", (event) => {
        this.handleSliderChange(event.detail);
      });
      document.addEventListener("assetSelectionChanged", (event) => {
        this.handleAssetSelectionChange(event.detail);
      });
      document.addEventListener("patrimonyMainValueChanged", (event) => {
        this.handleMainPatrimonioChanged(event.detail);
      });
      document.addEventListener("allocationChanged", () => {
        this.syncAllValues();
      });
      document.addEventListener("allocationStatusChanged", () => {
        this.syncAllValues();
      });
      document.addEventListener("patrimonyValueChanged", (event) => {
        this.handlePatrimonyValueChanged(event.detail);
      });
      this.setupDirectInputListeners();
    }
    setupDirectInputListeners() {
      const mainInput = document.querySelector(".currency-input.main");
      if (mainInput) {
        mainInput.addEventListener(
          "input",
          Utils.debounce(() => {
            this.syncTotalPatrimonio();
          }, 300)
        );
        mainInput.addEventListener("currencyChange", () => {
          this.syncTotalPatrimonio();
        });
      }
      this.patrimonioItems.forEach((item, key) => {
        if (item.input) {
          item.input.addEventListener(
            "input",
            Utils.debounce(() => {
              this.syncPatrimonioItem(key);
              this.syncComissaoItem(key);
            }, 300)
          );
          item.input.addEventListener("currencyChange", () => {
            this.syncPatrimonioItem(key);
            this.syncComissaoItem(key);
          });
        }
        if (item.slider) {
          item.slider.addEventListener("input", () => {
            this.syncPatrimonioItem(key);
            this.syncComissaoItem(key);
          });
        }
      });
    }
    setupCacheListeners() {
      document.addEventListener("patrimonySyncReset", () => {
        this.clearCache();
      });
      document.addEventListener("assetSelectionSystemReset", () => {
        this.clearCache();
      });
    }
    /**
     * Save current resultado data to cache
     */
    saveCachedData() {
      try {
        const cacheData = {
          totalPatrimonio: this.totalPatrimonio,
          selectedAssets: Array.from(this.selectedAssets),
          timestamp: Date.now(),
          // Save current display values for restoration
          displayValues: this.getCurrentDisplayValues()
        };
        this.CacheManager.set(this.cacheKey, cacheData);
      } catch (error) {
        console.warn("Error saving resultado data to cache:", error);
      }
    }
    /**
     * Load resultado data from cache and restore state
     */
    loadCachedData() {
      try {
        const cachedData = this.CacheManager.get(this.cacheKey);
        if (!cachedData) {
          this.updateMainContainersVisibility(false);
          return;
        }
        this.totalPatrimonio = cachedData.totalPatrimonio || 0;
        this.selectedAssets = new Set(cachedData.selectedAssets || []);
        if (cachedData.displayValues) {
          this.restoreDisplayValues(cachedData.displayValues);
        }
        this.updateVisibility();
        this.forceUpdateHeaderTotal();
        setTimeout(() => {
          this.updateGroupHeadersVisibility();
        }, 100);
      } catch (error) {
        console.warn("Error loading resultado data from cache:", error);
        this.updateMainContainersVisibility(false);
      }
    }
    /**
     * Clear resultado cache
     */
    clearCache() {
      try {
        this.CacheManager.remove(this.cacheKey);
      } catch (error) {
        console.warn("Error clearing resultado cache:", error);
      }
    }
    /**
     * Get current display values for caching
     */
    getCurrentDisplayValues() {
      const displayValues = {};
      this.resultadoPatrimonioItems.forEach((item, key) => {
        if (item.valorElement && item.percentageElement) {
          displayValues[key] = {
            valor: item.valorElement.textContent,
            percentage: item.percentageElement.textContent
          };
        }
      });
      this.resultadoComissaoItems.forEach((item, key) => {
        if (item.taxaMinimaElement && item.taxaMaximaElement && item.valorMinimoElement && item.valorMaximoElement) {
          displayValues[`${key}_comissao`] = {
            taxaMinima: item.taxaMinimaElement.textContent,
            taxaMaxima: item.taxaMaximaElement.textContent,
            valorMinimo: item.valorMinimoElement.textContent,
            valorMaximo: item.valorMaximoElement.textContent
          };
        }
      });
      return displayValues;
    }
    /**
     * Restore display values from cache
     */
    restoreDisplayValues(displayValues) {
      this.resultadoPatrimonioItems.forEach((item, key) => {
        const cachedValue = displayValues[key];
        if (cachedValue && item.valorElement && item.percentageElement) {
          item.valorElement.textContent = cachedValue.valor;
          item.percentageElement.textContent = cachedValue.percentage;
        }
      });
      this.resultadoComissaoItems.forEach((item, key) => {
        const cachedValue = displayValues[`${key}_comissao`];
        if (cachedValue && item.taxaMinimaElement && item.taxaMaximaElement && item.valorMinimoElement && item.valorMaximoElement) {
          item.taxaMinimaElement.textContent = cachedValue.taxaMinima;
          item.taxaMaximaElement.textContent = cachedValue.taxaMaxima;
          item.valorMinimoElement.textContent = cachedValue.valorMinimo;
          item.valorMaximoElement.textContent = cachedValue.valorMaximo;
        }
      });
    }
    initialSync() {
      if (window.PatrimonySync && typeof window.PatrimonySync.getMainValue === "function") {
        this.totalPatrimonio = window.PatrimonySync.getMainValue();
      } else {
        const mainInput = document.querySelector(".currency-input.main");
        if (mainInput) {
          this.totalPatrimonio = Utils.parseCurrencyValue(mainInput.value);
        }
      }
      this.syncAllValues();
      this.updateVisibility();
      this.forceUpdateHeaderTotal();
    }
    /**
     * Force update the header total patrimony value
     * This ensures the header-and-patrimonio section displays the correct value
     */
    forceUpdateHeaderTotal() {
      const headerTotalEl = document.querySelector('[data-patrimonio-total="true"]');
      if (headerTotalEl) {
        const currentValue = this.totalPatrimonio || 0;
        headerTotalEl.textContent = Utils.formatCurrency(currentValue);
      }
    }
    syncAllValues() {
      this.syncTotalPatrimonio();
      this.patrimonioItems.forEach((_, key) => {
        this.syncPatrimonioItem(key);
        this.syncComissaoItem(key);
      });
    }
    syncTotalPatrimonio() {
      const totalEl = document.querySelector('[data-patrimonio-total="true"]');
      if (totalEl && window.PatrimonySync) {
        const mainValue = window.PatrimonySync.getMainValue();
        totalEl.textContent = Utils.formatCurrency(mainValue);
      }
    }
    syncPatrimonioItem(key) {
      const patrimonioItem = this.patrimonioItems.get(key);
      const resultKey = this.getResultKey(key);
      const resultadoItem = this.resultadoPatrimonioItems.get(resultKey);
      if (!patrimonioItem || !resultadoItem) {
        return;
      }
      const inputValue = patrimonioItem.input ? Utils.parseCurrencyValue(patrimonioItem.input.value) : 0;
      const sliderValue = patrimonioItem.slider ? parseFloat(patrimonioItem.slider.value) : 0;
      const percentage = sliderValue * 100;
      if (resultadoItem.valorElement) {
        resultadoItem.valorElement.textContent = Utils.formatCurrency(inputValue);
      }
      if (resultadoItem.percentageElement) {
        resultadoItem.percentageElement.textContent = `${percentage.toFixed(1)}%`;
      }
    }
    syncComissaoItem(key) {
      const patrimonioItem = this.patrimonioItems.get(key);
      const resultKey = this.getResultKey(key);
      const comissaoItem = this.resultadoComissaoItems.get(resultKey);
      if (!patrimonioItem || !comissaoItem) {
        return;
      }
      const inputValue = patrimonioItem.input ? Utils.parseCurrencyValue(patrimonioItem.input.value) : 0;
      const comissaoData = this.getComissaoData(patrimonioItem.category, patrimonioItem.product);
      if (comissaoData) {
        if (comissaoItem.taxaMinimaElement) {
          comissaoItem.taxaMinimaElement.textContent = `${comissaoData.min}%`;
        }
        if (comissaoItem.taxaMaximaElement) {
          comissaoItem.taxaMaximaElement.textContent = `${comissaoData.max}%`;
        }
        const valorMinimo = inputValue * comissaoData.min / 100;
        const valorMaximo = inputValue * comissaoData.max / 100;
        if (comissaoItem.valorMinimoElement) {
          comissaoItem.valorMinimoElement.textContent = Utils.formatCurrency(valorMinimo);
        }
        if (comissaoItem.valorMaximoElement) {
          comissaoItem.valorMaximoElement.textContent = Utils.formatCurrency(valorMaximo);
        }
      }
    }
    getComissaoData(category, product) {
      return ComissoesUtils.getComissaoData(category, product);
    }
    handlePatrimonioChange(detail) {
      if (detail.key) {
        this.syncPatrimonioItem(detail.key);
        this.syncComissaoItem(detail.key);
        this.saveCachedData();
      }
    }
    handleSliderChange(detail) {
      if (detail.key) {
        this.syncPatrimonioItem(detail.key);
        this.syncComissaoItem(detail.key);
        this.saveCachedData();
      }
    }
    handleAssetSelectionChange(detail) {
      const selectedAssets = (detail.selectedAssets || []).map((normalizedKey) => {
        return this.convertNormalizedKeyToResultKey(normalizedKey);
      });
      this.selectedAssets = new Set(selectedAssets);
      this.updateVisibility();
      this.saveCachedData();
    }
    /**
     * Convert normalized key from asset selection to resultado key format
     * From: "renda fixa|cdb" (normalized, pipe separator)
     * To: "Renda Fixa-CDB" (proper case, dash separator)
     */
    convertNormalizedKeyToResultKey(normalizedKey) {
      const [category, product] = normalizedKey.split("|");
      for (const [resultKey] of this.resultadoPatrimonioItems) {
        const [resultCategory, resultProduct] = resultKey.split("-");
        if (this.normalizeString(resultCategory) === category && this.normalizeString(resultProduct) === product) {
          return resultKey;
        }
      }
      for (const [resultKey] of this.resultadoComissaoItems) {
        const [resultCategory, resultProduct] = resultKey.split("-");
        if (this.normalizeString(resultCategory) === category && this.normalizeString(resultProduct) === product) {
          return resultKey;
        }
      }
      return `${this.toProperCase(category)}-${this.toProperCase(product)}`;
    }
    /**
     * Normalize string for comparison (same as asset selection filter)
     */
    normalizeString(str) {
      return str.toLowerCase().trim();
    }
    /**
     * Convert string to proper case
     */
    toProperCase(str) {
      return str.split(" ").map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(" ");
    }
    handlePatrimonyValueChanged(detail) {
      if (detail.key) {
        this.syncPatrimonioItem(detail.key);
        this.syncComissaoItem(detail.key);
      } else {
        this.syncAllValues();
      }
      this.saveCachedData();
    }
    handleMainPatrimonioChanged(detail) {
      this.totalPatrimonio = detail.value;
      this.syncTotalPatrimonio();
      this.syncAllValues();
      this.forceUpdateHeaderTotal();
      this.saveCachedData();
    }
    updateVisibility() {
      const hasSelectedAssets = this.selectedAssets.size > 0;
      this.updateMainContainersVisibility(hasSelectedAssets);
      if (!hasSelectedAssets) {
        this.hideAllItems();
        return;
      }
      this.resultadoPatrimonioItems.forEach((item, key) => {
        const hasValue = this.hasValue(key);
        const isSelected = this.isAssetSelectedForResult(key);
        const shouldShow = hasValue || isSelected;
        this.animateVisibility(item.element, shouldShow);
      });
      this.resultadoComissaoItems.forEach((item, key) => {
        const hasValue = this.hasValue(key);
        const isSelected = this.isAssetSelectedForResult(key);
        const shouldShow = hasValue || isSelected;
        this.animateVisibility(item.element, shouldShow);
      });
      this.updateGroupHeadersVisibility();
    }
    /**
     * Update main containers visibility based on asset selection
     */
    updateMainContainersVisibility(hasSelectedAssets) {
      const patrimonioAtivosGroup = document.querySelector(".patrimonio-ativos-group");
      const ativosContentFloat = document.querySelector(".ativos-content-float");
      if (patrimonioAtivosGroup) {
        if (hasSelectedAssets) {
          patrimonioAtivosGroup.style.display = "";
          patrimonioAtivosGroup.style.opacity = "1";
        } else {
          patrimonioAtivosGroup.style.display = "none";
          patrimonioAtivosGroup.style.opacity = "0";
        }
      }
      if (ativosContentFloat) {
        if (hasSelectedAssets) {
          ativosContentFloat.style.display = "";
          ativosContentFloat.style.opacity = "1";
        } else {
          ativosContentFloat.style.display = "none";
          ativosContentFloat.style.opacity = "0";
        }
      }
    }
    /**
     * Hide all individual items when no assets are selected
     */
    hideAllItems() {
      this.resultadoPatrimonioItems.forEach((item) => {
        this.animateVisibility(item.element, false);
      });
      this.resultadoComissaoItems.forEach((item) => {
        this.animateVisibility(item.element, false);
      });
    }
    /**
     * Check if an asset is selected for result display
     * Handles the key format conversion and mapping
     */
    isAssetSelectedForResult(resultKey) {
      if (this.selectedAssets.size === 0) {
        return false;
      }
      if (this.selectedAssets.has(resultKey)) {
        return true;
      }
      const mappedKey = this.getResultKey(resultKey);
      if (this.selectedAssets.has(mappedKey)) {
        return true;
      }
      return false;
    }
    hasValue(key) {
      const patrimonioItem = this.patrimonioItems.get(key);
      if (!patrimonioItem || !patrimonioItem.input) return false;
      const value = Utils.parseCurrencyValue(patrimonioItem.input.value);
      return value > 0;
    }
    animateVisibility(element, shouldShow) {
      if (!element) return;
      if (shouldShow) {
        if (element.style.display === "none") {
          element.style.display = "block";
          element.style.opacity = "0";
          element.style.transform = "translateY(-10px)";
          const animateIn = /* @__PURE__ */ __name(() => {
            element.style.transition = "opacity 0.3s ease, transform 0.3s ease";
            element.style.opacity = "1";
            element.style.transform = "translateY(0)";
          }, "animateIn");
          if (typeof requestAnimationFrame !== "undefined") {
            requestAnimationFrame(animateIn);
          } else {
            setTimeout(animateIn, 16);
          }
        }
      } else {
        if (element.style.display !== "none") {
          element.style.transition = "opacity 0.3s ease, transform 0.3s ease";
          element.style.opacity = "0";
          element.style.transform = "translateY(-10px)";
          setTimeout(() => {
            if (element.style.opacity === "0") {
              element.style.display = "none";
            }
          }, 300);
        }
      }
    }
    updateGroupHeadersVisibility() {
      if (this.selectedAssets.size === 0) {
        this.hideAllGroupHeaders();
        return;
      }
      const patrimonioGroups = document.querySelectorAll(
        ".patrimonio-ativos-group .ativos-group-produtos"
      );
      const comissaoGroups = document.querySelectorAll(".ativos-content-float .ativos-group");
      patrimonioGroups.forEach((group) => {
        const shouldShow = this.shouldShowPatrimonioGroup(group);
        this.animateVisibility(group, shouldShow);
      });
      comissaoGroups.forEach((group) => {
        const shouldShow = this.shouldShowComissaoGroup(group);
        this.animateVisibility(group, shouldShow);
      });
    }
    /**
     * Check if a patrimonio group should be visible based on selected assets and values
     */
    shouldShowPatrimonioGroup(group) {
      const items = group.querySelectorAll(".ativos-produtos-item");
      for (const item of items) {
        const category = item.getAttribute("ativo-category");
        const product = item.getAttribute("ativo-product");
        if (category && product) {
          const key = `${category}-${product}`;
          const mappedKey = this.getResultKey(key);
          const isSelected = this.isAssetSelectedForResult(mappedKey);
          const hasValue = this.hasValue(mappedKey);
          if (isSelected || hasValue) {
            return true;
          }
        }
      }
      return false;
    }
    /**
     * Check if a comissao group should be visible based on selected assets and values
     */
    shouldShowComissaoGroup(group) {
      const items = group.querySelectorAll(".ativos-produtos-item");
      for (const item of items) {
        const category = item.getAttribute("ativo-category");
        const product = item.getAttribute("ativo-product");
        if (category && product) {
          const key = `${category}-${product}`;
          const mappedKey = this.getResultKey(key);
          const isSelected = this.isAssetSelectedForResult(mappedKey);
          const hasValue = this.hasValue(mappedKey);
          if (isSelected || hasValue) {
            return true;
          }
        }
      }
      return false;
    }
    /**
     * Hide all group headers when no assets are selected
     */
    hideAllGroupHeaders() {
      const patrimonioGroups = document.querySelectorAll(
        ".patrimonio-ativos-group .ativos-group-produtos"
      );
      const comissaoGroups = document.querySelectorAll(".ativos-content-float .ativos-group");
      patrimonioGroups.forEach((group) => {
        this.animateVisibility(group, false);
      });
      comissaoGroups.forEach((group) => {
        this.animateVisibility(group, false);
      });
    }
    // Public API methods
    updateTotalPatrimonio(value) {
      this.totalPatrimonio = value;
      this.syncAllValues();
    }
    getResultadoData() {
      const data = {
        patrimonio: {},
        comissoes: {},
        total: this.totalPatrimonio
      };
      this.patrimonioItems.forEach((item, key) => {
        const inputValue = item.input ? Utils.parseCurrencyValue(item.input.value) : 0;
        const sliderValue = item.slider ? parseFloat(item.slider.value) : 0;
        data.patrimonio[key] = {
          valor: inputValue,
          percentual: sliderValue * 100,
          category: item.category,
          product: item.product
        };
        const comissaoData = this.getComissaoData(item.category, item.product);
        if (comissaoData) {
          data.comissoes[key] = {
            taxaMin: comissaoData.min,
            taxaMax: comissaoData.max,
            valorMin: inputValue * comissaoData.min / 100,
            valorMax: inputValue * comissaoData.max / 100
          };
        }
      });
      return data;
    }
    forceSync() {
      this.syncAllValues();
      this.updateVisibility();
    }
    // Debug methods
    debugInfo() {
      const patrimonioKeys = Array.from(this.patrimonioItems.keys());
      const resultadoPatrimonioKeys = Array.from(this.resultadoPatrimonioItems.keys());
      const resultadoComissaoKeys = Array.from(this.resultadoComissaoItems.keys());
      const mappingStatus = {};
      patrimonioKeys.forEach((key) => {
        const resultKey = this.getResultKey(key);
        const hasPatrimonioMatch = this.resultadoPatrimonioItems.has(resultKey);
        const hasComissaoMatch = this.resultadoComissaoItems.has(resultKey);
        mappingStatus[key] = {
          resultKey,
          hasPatrimonioMatch,
          hasComissaoMatch,
          isMapped: key !== resultKey
        };
      });
      return {
        isInitialized: this.isInitialized,
        patrimonioItems: this.patrimonioItems.size,
        resultadoPatrimonioItems: this.resultadoPatrimonioItems.size,
        resultadoComissaoItems: this.resultadoComissaoItems.size,
        selectedAssets: this.selectedAssets.size,
        totalPatrimonio: this.totalPatrimonio,
        patrimonioKeys,
        resultadoPatrimonioKeys,
        resultadoComissaoKeys,
        mappingStatus,
        attributeMapping: this.attributeMapping
      };
    }
    testSync() {
      this.syncTotalPatrimonio();
      const firstKey = this.patrimonioItems.keys().next().value;
      if (firstKey) {
        this.syncPatrimonioItem(firstKey);
        this.syncComissaoItem(firstKey);
      }
      this.updateVisibility();
    }
    /**
     * Reset the entire resultado system and clear cache
     */
    resetSystem() {
      try {
        this.clearCache();
        this.selectedAssets.clear();
        this.totalPatrimonio = 0;
        this.resetAllDisplayValues();
        this.updateVisibility();
        document.dispatchEvent(
          new CustomEvent("resultadoSyncReset", {
            detail: {
              timestamp: Date.now()
            }
          })
        );
      } catch (error) {
        console.warn("Error resetting resultado system:", error);
      }
    }
    /**
     * Reset all display values to zero
     */
    resetAllDisplayValues() {
      this.resultadoPatrimonioItems.forEach((item) => {
        if (item.valorElement) {
          item.valorElement.textContent = "0,00";
        }
        if (item.percentageElement) {
          item.percentageElement.textContent = "0%";
        }
      });
      this.resultadoComissaoItems.forEach((item) => {
        if (item.taxaMinimaElement) {
          item.taxaMinimaElement.textContent = "0%";
        }
        if (item.taxaMaximaElement) {
          item.taxaMaximaElement.textContent = "0%";
        }
        if (item.valorMinimoElement) {
          item.valorMinimoElement.textContent = "0,00";
        }
        if (item.valorMaximoElement) {
          item.valorMaximoElement.textContent = "0,00";
        }
      });
      this.updateMainContainersVisibility(false);
    }
    /**
     * Get cache information for debugging
     */
    getCacheInfo() {
      try {
        const cachedData = this.CacheManager.get(this.cacheKey);
        return {
          hasCachedData: !!cachedData,
          totalPatrimonio: cachedData?.totalPatrimonio || 0,
          selectedAssetsCount: cachedData?.selectedAssets?.length || 0,
          timestamp: cachedData?.timestamp || null,
          currentTotalPatrimonio: this.totalPatrimonio,
          currentSelectedAssets: this.selectedAssets.size,
          displayValuesCount: cachedData?.displayValues ? Object.keys(cachedData.displayValues).length : 0
        };
      } catch (error) {
        console.warn("Error getting cache info:", error);
        return null;
      }
    }
    /**
     * Debug method to check key mappings and visibility
     */
    debugKeyMappings() {
      console.warn("=== RESULTADO SYNC DEBUG ===");
      console.warn("Selected Assets:", Array.from(this.selectedAssets));
      console.warn("Has Selected Assets:", this.selectedAssets.size > 0);
      const patrimonioAtivosGroup = document.querySelector(".patrimonio-ativos-group");
      const ativosContentFloat = document.querySelector(".ativos-content-float");
      console.warn("\n--- MAIN CONTAINERS ---");
      console.warn("patrimonio-ativos-group:", {
        exists: !!patrimonioAtivosGroup,
        display: patrimonioAtivosGroup?.style.display || "default",
        opacity: patrimonioAtivosGroup?.style.opacity || "default"
      });
      console.warn("ativos-content-float:", {
        exists: !!ativosContentFloat,
        display: ativosContentFloat?.style.display || "default",
        opacity: ativosContentFloat?.style.opacity || "default"
      });
      console.warn("\n--- PATRIMONIO ITEMS ---");
      this.resultadoPatrimonioItems.forEach((item, key) => {
        const hasValue = this.hasValue(key);
        const isSelected = this.isAssetSelectedForResult(key);
        const shouldShow = hasValue || isSelected;
        const isVisible = item.element.style.display !== "none";
        console.warn(`${key}:`, {
          hasValue,
          isSelected,
          shouldShow,
          isVisible,
          category: item.category,
          product: item.product
        });
      });
      console.warn("\n--- COMISSAO ITEMS ---");
      this.resultadoComissaoItems.forEach((item, key) => {
        const hasValue = this.hasValue(key);
        const isSelected = this.isAssetSelectedForResult(key);
        const shouldShow = hasValue || isSelected;
        const isVisible = item.element.style.display !== "none";
        console.warn(`${key}:`, {
          hasValue,
          isSelected,
          shouldShow,
          isVisible,
          category: item.category,
          product: item.product
        });
      });
      console.warn("\n--- GROUP HEADERS ---");
      const patrimonioGroups = document.querySelectorAll(
        ".patrimonio-ativos-group .ativos-group-produtos"
      );
      const comissaoGroups = document.querySelectorAll(".ativos-content-float .ativos-group");
      console.warn("Patrimonio Groups:");
      patrimonioGroups.forEach((group, index) => {
        const shouldShow = this.shouldShowPatrimonioGroup(group);
        const isVisible = group.style.display !== "none";
        const header = group.querySelector(".ativos-group-header");
        const headerText = header?.textContent?.trim() || "No header";
        console.warn(`  Group ${index} (${headerText}):`, {
          shouldShow,
          isVisible,
          display: group.style.display || "default"
        });
      });
      console.warn("Comissao Groups:");
      comissaoGroups.forEach((group, index) => {
        const shouldShow = this.shouldShowComissaoGroup(group);
        const isVisible = group.style.display !== "none";
        const header = group.querySelector(".ativos-group-header");
        const headerText = header?.textContent?.trim() || "No header";
        console.warn(`  Group ${index} (${headerText}):`, {
          shouldShow,
          isVisible,
          display: group.style.display || "default"
        });
      });
      console.warn("\n--- ATTRIBUTE MAPPING ---");
      Object.entries(this.attributeMapping).forEach(([original, mapped]) => {
        console.warn(`${original} -> ${mapped}`);
      });
    }
  };
  var resultadoSync = new ResultadoSyncSystem();
  var resultado_sync_default = ResultadoSyncSystem;
  if (typeof window !== "undefined") {
    window.debugResultadoSync = () => {
      const system = window.AppCalcReino?.getModule?.("resultadoSync");
      if (system) {
        return system.debugInfo();
      }
      return null;
    };
    window.testResultadoSync = () => {
      const system = window.AppCalcReino?.getModule?.("resultadoSync");
      if (system) {
        system.testSync();
      }
    };
    window.forceResultadoSync = () => {
      const system = window.AppCalcReino?.getModule?.("resultadoSync");
      if (system) {
        system.forceSync();
      }
    };
    window.debugResultadoKeyMappings = () => {
      const system = window.AppCalcReino?.getModule?.("resultadoSync");
      if (system) {
        system.debugKeyMappings();
      }
    };
    window.forceUpdateGroupHeaders = () => {
      const system = window.AppCalcReino?.getModule?.("resultadoSync");
      if (system) {
        system.updateGroupHeadersVisibility();
        console.warn("Group headers visibility updated manually");
      }
    };
    window.forceUpdateHeaderTotal = () => {
      const system = window.AppCalcReino?.getModule?.("resultadoSync");
      if (system) {
        system.forceUpdateHeaderTotal();
        console.warn("Header total value updated manually");
      }
    };
  }
})();
//# sourceMappingURL=resultado-sync.js.map
