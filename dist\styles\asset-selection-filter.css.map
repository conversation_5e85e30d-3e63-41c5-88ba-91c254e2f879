{"version": 3, "sources": ["../../src/styles/asset-selection-filter.css"], "sourcesContent": ["/* Asset Selection Filter Styles */\r\n\r\n.asset-checkbox-container {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  vertical-align: middle;\r\n}\r\n\r\n.asset-checkbox {\r\n  appearance: none;\r\n  width: 1.2rem;\r\n  height: 1.2rem;\r\n  border: 2px solid #ccc;\r\n  border-radius: 0.25rem;\r\n  margin: 0;\r\n  margin-right: 0.25rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n  background: white;\r\n}\r\n\r\n.asset-checkbox:hover {\r\n  border-color: #000;\r\n}\r\n\r\n.asset-checkbox:checked {\r\n  background-color: #000;\r\n  border-color: #000;\r\n}\r\n\r\n.asset-checkbox:checked::after {\r\n  content: '✓';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  color: white;\r\n  font-size: 0.8rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.asset-checkbox-label {\r\n  cursor: pointer;\r\n  margin: 0;\r\n}\r\n\r\n/* Selected asset styling - removed background color */\r\n.selected-asset {\r\n}\r\n\r\n/* Dropdown items styling */\r\n.ativo-item-subcategory .asset-checkbox-container {\r\n  order: -1;\r\n  margin-left: 0;\r\n}\r\n\r\n/* Individual items styling */\r\n.ativos_item .asset-checkbox-container {\r\n  order: -1;\r\n  margin-left: 0;\r\n}\r\n\r\n.ativos_item {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  padding: 0.75rem;\r\n  border-radius: 0.5rem;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n/* Counter styling enhancement */\r\n.counter_ativos {\r\n  font-weight: bold;\r\n  color: #000;\r\n}\r\n\r\n/* Section 3 filtered states */\r\n.asset-filtered-out {\r\n  opacity: 0;\r\n  transform: scale(0.95);\r\n  transition: all 0.3s ease;\r\n  pointer-events: none;\r\n}\r\n\r\n.asset-filtered-in {\r\n  opacity: 1;\r\n  transform: scale(1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* Clear button enhancement */\r\n.ativos_clean-button {\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.ativos_clean-button:hover {\r\n  transform: scale(1.05);\r\n  background-color: rgba(255, 0, 0, 0.1);\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .asset-checkbox {\r\n    width: 1rem;\r\n    height: 1rem;\r\n  }\r\n\r\n  .asset-checkbox:checked::after {\r\n    font-size: 0.7rem;\r\n  }\r\n\r\n  .asset-checkbox-container {\r\n    margin-right: 0.25rem;\r\n  }\r\n}\r\n\r\n/* Animation for selection - removed scaling */\r\n@keyframes selectAsset {\r\n  0% {\r\n    opacity: 0.8;\r\n  }\r\n  50% {\r\n    opacity: 0.9;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.selected-asset {\r\n  animation: selectAsset 0.3s ease;\r\n}\r\n\r\n/* End of file */\r\n"], "mappings": ";AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,kBAAgB;AAClB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,UAAQ;AACR,gBAAc;AACd,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,YAAU;AACV,cAAY;AACd;AAEA,CAdC,cAcc;AACb,gBAAc;AAChB;AAEA,CAlBC,cAkBc;AACb,oBAAkB;AAClB,gBAAc;AAChB;AAEA,CAvBC,cAuBc,QAAQ;AACrB,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,aAAW,UAAU,IAAI,EAAE;AAC3B,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,UAAQ;AACR,UAAQ;AACV;AAGA,CAAC;AACD;AAGA,CAAC,uBAAuB,CAlDvB;AAmDC,SAAO;AACP,eAAa;AACf;AAGA,CAAC,YAAY,CAxDZ;AAyDC,SAAO;AACP,eAAa;AACf;AAEA,CALC;AAMC,WAAS;AACT,eAAa;AACb,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAGA,CAAC;AACC,eAAa;AACb,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,aAAW,MAAM;AACjB,cAAY,IAAI,KAAK;AACrB,kBAAgB;AAClB;AAEA,CAAC;AACC,WAAS;AACT,aAAW,MAAM;AACjB,cAAY,IAAI,KAAK;AACvB;AAGA,CAAC;AACC,cAAY,IAAI,KAAK;AACvB;AAEA,CAJC,mBAImB;AAClB,aAAW,MAAM;AACjB,oBAAkB,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAhGD;AAiGG,WAAO;AACP,YAAQ;AACV;AAEA,GArGD,cAqGgB,QAAQ;AACrB,eAAW;AACb;AAEA,GA/GD;AAgHG,kBAAc;AAChB;AACF;AAGA,WAAW;AACT;AACE,aAAS;AACX;AACA;AACE,aAAS;AACX;AACA;AACE,aAAS;AACX;AACF;AAEA,CAnFC;AAoFC,aAAW,YAAY,KAAK;AAC9B;", "names": []}