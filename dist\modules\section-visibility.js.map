{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/section-visibility.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Section Visibility System\r\n * Controls showing/hiding of different sections based on user interactions\r\n */\r\nexport class SectionVisibilitySystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.currentSection = 'calculadora';\r\n    this.sections = new Map();\r\n    this.triggers = new Map();\r\n    this.Motion = null;\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.waitForMotion();\r\n    });\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  waitForMotion() {\r\n    if (window.Motion) {\r\n      this.Motion = window.Motion;\r\n      this.initSystem();\r\n    } else {\r\n      setTimeout(() => this.waitForMotion(), 50);\r\n    }\r\n  }\r\n\r\n  initSystem() {\r\n    this.initializeSections();\r\n    this.initializeTriggers();\r\n    this.setupEventListeners();\r\n    this.showInitialSection();\r\n  }\r\n\r\n  initializeSections() {\r\n    // Mapeamento das seções principais (corrigido para refletir HTML real)\r\n    const sectionSelectors = {\r\n      home: '._0-home-section-calc-intro',\r\n      money: '._1-section-calc-money',\r\n      ativos: '._2-section-calc-ativos',\r\n      alocacao: '._3-section-patrimonio-alocation',\r\n      chart: '.section', // Gráfico principal\r\n    };\r\n\r\n    Object.entries(sectionSelectors).forEach(([key, selector]) => {\r\n      const element = document.querySelector(selector);\r\n      if (element) {\r\n        this.sections.set(key, {\r\n          element,\r\n          selector,\r\n          visible: false,\r\n          animating: false,\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  initializeTriggers() {\r\n    // Mapeamento dos elementos que acionam mudanças de seção\r\n    const triggerSelectors = {\r\n      'show-home': '[data-show=\"home\"]',\r\n      'show-money': '[data-show=\"money\"]',\r\n      'show-ativos': '[data-show=\"ativos\"]',\r\n      'show-alocacao': '[data-show=\"alocacao\"]',\r\n      'show-chart': '[data-show=\"chart\"]',\r\n      'toggle-home': '[data-toggle=\"home\"]',\r\n      'toggle-money': '[data-toggle=\"money\"]',\r\n    };\r\n\r\n    Object.entries(triggerSelectors).forEach(([key, selector]) => {\r\n      const elements = document.querySelectorAll(selector);\r\n      if (elements.length > 0) {\r\n        this.triggers.set(key, Array.from(elements));\r\n      }\r\n    });\r\n  }\r\n\r\n  setupEventListeners() {\r\n    // Event listeners para mostrar seções\r\n    this.triggers.forEach((elements, key) => {\r\n      if (key.startsWith('show-')) {\r\n        const sectionName = key.replace('show-', '');\r\n        elements.forEach((element) => {\r\n          element.addEventListener('click', (e) => {\r\n            e.preventDefault();\r\n            this.showSection(sectionName);\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    // Event listeners para toggle de seções\r\n    this.triggers.forEach((elements, key) => {\r\n      if (key.startsWith('toggle-')) {\r\n        const sectionName = key.replace('toggle-', '');\r\n        elements.forEach((element) => {\r\n          element.addEventListener('click', (e) => {\r\n            e.preventDefault();\r\n            this.toggleSection(sectionName);\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    // Listener para teclas de atalho\r\n    document.addEventListener('keydown', (e) => {\r\n      this.handleKeyboardShortcuts(e);\r\n    });\r\n\r\n    // Listener para mudanças de hash na URL\r\n    window.addEventListener('hashchange', () => {\r\n      this.handleHashChange();\r\n    });\r\n  }\r\n\r\n  showInitialSection() {\r\n    // Verifica se há uma seção especificada na URL\r\n    const hash = window.location.hash.replace('#', '');\r\n    const initialSection = hash && this.sections.has(hash) ? hash : 'home';\r\n\r\n    this.showSection(initialSection, false); // false = sem animação inicial\r\n  }\r\n\r\n  async showSection(sectionName, animate = true) {\r\n    if (!this.sections.has(sectionName)) {\r\n      return;\r\n    }\r\n\r\n    const targetSection = this.sections.get(sectionName);\r\n\r\n    if (targetSection.visible && this.currentSection === sectionName) {\r\n      return; // Seção já está visível\r\n    }\r\n\r\n    // Esconde seção atual\r\n    if (this.currentSection && this.currentSection !== sectionName) {\r\n      await this.hideSection(this.currentSection, animate);\r\n    }\r\n\r\n    // Mostra nova seção\r\n    await this.displaySection(sectionName, animate);\r\n\r\n    this.currentSection = sectionName;\r\n    this.updateURL(sectionName);\r\n    this.updateActiveStates(sectionName);\r\n  }\r\n\r\n  async hideSection(sectionName, animate = true) {\r\n    if (!this.sections.has(sectionName)) {\r\n      return;\r\n    }\r\n\r\n    const section = this.sections.get(sectionName);\r\n\r\n    if (!section.visible || section.animating) {\r\n      return;\r\n    }\r\n\r\n    section.animating = true;\r\n\r\n    if (animate && this.Motion) {\r\n      await this.Motion.animate(\r\n        section.element,\r\n        {\r\n          opacity: [1, 0],\r\n          y: [0, -30],\r\n          scale: [1, 0.95],\r\n        },\r\n        {\r\n          duration: 0.3,\r\n          ease: 'circIn',\r\n        }\r\n      ).finished;\r\n    }\r\n\r\n    section.element.style.display = 'none';\r\n    section.visible = false;\r\n    section.animating = false;\r\n  }\r\n\r\n  async displaySection(sectionName, animate = true) {\r\n    if (!this.sections.has(sectionName)) {\r\n      return;\r\n    }\r\n\r\n    const section = this.sections.get(sectionName);\r\n\r\n    if (section.visible || section.animating) {\r\n      return;\r\n    }\r\n\r\n    section.animating = true;\r\n    section.element.style.display = 'block';\r\n\r\n    if (animate && this.Motion) {\r\n      // Prepara elemento para animação\r\n      this.Motion.animate(\r\n        section.element,\r\n        {\r\n          opacity: 0,\r\n          y: 30,\r\n          scale: 0.95,\r\n        },\r\n        { duration: 0 }\r\n      );\r\n\r\n      // Anima entrada\r\n      await this.Motion.animate(\r\n        section.element,\r\n        {\r\n          opacity: [0, 1],\r\n          y: [30, 0],\r\n          scale: [0.95, 1],\r\n        },\r\n        {\r\n          duration: 0.4,\r\n          ease: 'backOut',\r\n        }\r\n      ).finished;\r\n    } else {\r\n      // Mostra sem animação\r\n      section.element.style.opacity = '1';\r\n      section.element.style.transform = 'none';\r\n    }\r\n\r\n    section.visible = true;\r\n    section.animating = false;\r\n  }\r\n\r\n  async toggleSection(sectionName) {\r\n    if (!this.sections.has(sectionName)) {\r\n      return;\r\n    }\r\n\r\n    const section = this.sections.get(sectionName);\r\n\r\n    if (section.visible) {\r\n      await this.hideSection(sectionName);\r\n    } else {\r\n      await this.showSection(sectionName);\r\n    }\r\n  }\r\n\r\n  updateURL(sectionName) {\r\n    if (sectionName !== 'home') {\r\n      window.history.pushState(null, null, `#${sectionName}`);\r\n    } else {\r\n      window.history.pushState(null, null, window.location.pathname);\r\n    }\r\n  }\r\n\r\n  updateActiveStates(sectionName) {\r\n    // Remove estado ativo de todos os triggers\r\n    this.triggers.forEach((elements) => {\r\n      elements.forEach((element) => {\r\n        element.classList.remove('is-active');\r\n      });\r\n    });\r\n\r\n    // Adiciona estado ativo aos triggers da seção atual\r\n    const showTriggers = this.triggers.get(`show-${sectionName}`);\r\n    if (showTriggers) {\r\n      showTriggers.forEach((element) => {\r\n        element.classList.add('is-active');\r\n      });\r\n    }\r\n  }\r\n\r\n  handleKeyboardShortcuts(e) {\r\n    // Atalhos de teclado para navegação\r\n    if (e.ctrlKey || e.metaKey) {\r\n      switch (e.key) {\r\n        case '1':\r\n          e.preventDefault();\r\n          this.showSection('home');\r\n          break;\r\n        case '2':\r\n          e.preventDefault();\r\n          this.showSection('money');\r\n          break;\r\n        case '3':\r\n          e.preventDefault();\r\n          this.showSection('ativos');\r\n          break;\r\n        case '4':\r\n          e.preventDefault();\r\n          this.showSection('alocacao');\r\n          break;\r\n        case '5':\r\n          e.preventDefault();\r\n          this.showSection('chart');\r\n          break;\r\n      }\r\n    }\r\n\r\n    // ESC para voltar ao home\r\n    if (e.key === 'Escape') {\r\n      this.showSection('home');\r\n    }\r\n  }\r\n\r\n  handleHashChange() {\r\n    const hash = window.location.hash.replace('#', '');\r\n    if (hash && this.sections.has(hash) && hash !== this.currentSection) {\r\n      this.showSection(hash);\r\n    } else if (!hash && this.currentSection !== 'home') {\r\n      this.showSection('home');\r\n    }\r\n  }\r\n\r\n  // Métodos públicos para controle externo\r\n\r\n  getCurrentSection() {\r\n    return this.currentSection;\r\n  }\r\n\r\n  isSectionVisible(sectionName) {\r\n    const section = this.sections.get(sectionName);\r\n    return section ? section.visible : false;\r\n  }\r\n\r\n  getSectionElement(sectionName) {\r\n    const section = this.sections.get(sectionName);\r\n    return section ? section.element : null;\r\n  }\r\n\r\n  addCustomSection(name, selector) {\r\n    const element = document.querySelector(selector);\r\n    if (element) {\r\n      this.sections.set(name, {\r\n        element,\r\n        selector,\r\n        visible: false,\r\n        animating: false,\r\n      });\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  addCustomTrigger(name, selector, targetSection) {\r\n    const elements = document.querySelectorAll(selector);\r\n    if (elements.length > 0) {\r\n      this.triggers.set(name, Array.from(elements));\r\n\r\n      // Adiciona event listeners\r\n      elements.forEach((element) => {\r\n        element.addEventListener('click', (e) => {\r\n          e.preventDefault();\r\n          this.showSection(targetSection);\r\n        });\r\n      });\r\n\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACItF,MAAM,0BAAN,MAA8B;AAAA,IAJrC,OAIqC;AAAA;AAAA;AAAA,IACnC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AACtB,WAAK,WAAW,oBAAI,IAAI;AACxB,WAAK,WAAW,oBAAI,IAAI;AACxB,WAAK,SAAS;AAAA,IAChB;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,cAAc;AAAA,MACrB,CAAC;AAED,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,gBAAgB;AACd,UAAI,OAAO,QAAQ;AACjB,aAAK,SAAS,OAAO;AACrB,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,mBAAW,MAAM,KAAK,cAAc,GAAG,EAAE;AAAA,MAC3C;AAAA,IACF;AAAA,IAEA,aAAa;AACX,WAAK,mBAAmB;AACxB,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,IAEA,qBAAqB;AAEnB,YAAM,mBAAmB;AAAA,QACvB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA;AAAA,MACT;AAEA,aAAO,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,CAAC,KAAK,QAAQ,MAAM;AAC5D,cAAM,UAAU,SAAS,cAAc,QAAQ;AAC/C,YAAI,SAAS;AACX,eAAK,SAAS,IAAI,KAAK;AAAA,YACrB;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,qBAAqB;AAEnB,YAAM,mBAAmB;AAAA,QACvB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,eAAe;AAAA,QACf,gBAAgB;AAAA,MAClB;AAEA,aAAO,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,CAAC,KAAK,QAAQ,MAAM;AAC5D,cAAM,WAAW,SAAS,iBAAiB,QAAQ;AACnD,YAAI,SAAS,SAAS,GAAG;AACvB,eAAK,SAAS,IAAI,KAAK,MAAM,KAAK,QAAQ,CAAC;AAAA,QAC7C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,sBAAsB;AAEpB,WAAK,SAAS,QAAQ,CAAC,UAAU,QAAQ;AACvC,YAAI,IAAI,WAAW,OAAO,GAAG;AAC3B,gBAAM,cAAc,IAAI,QAAQ,SAAS,EAAE;AAC3C,mBAAS,QAAQ,CAAC,YAAY;AAC5B,oBAAQ,iBAAiB,SAAS,CAAC,MAAM;AACvC,gBAAE,eAAe;AACjB,mBAAK,YAAY,WAAW;AAAA,YAC9B,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAGD,WAAK,SAAS,QAAQ,CAAC,UAAU,QAAQ;AACvC,YAAI,IAAI,WAAW,SAAS,GAAG;AAC7B,gBAAM,cAAc,IAAI,QAAQ,WAAW,EAAE;AAC7C,mBAAS,QAAQ,CAAC,YAAY;AAC5B,oBAAQ,iBAAiB,SAAS,CAAC,MAAM;AACvC,gBAAE,eAAe;AACjB,mBAAK,cAAc,WAAW;AAAA,YAChC,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAGD,eAAS,iBAAiB,WAAW,CAAC,MAAM;AAC1C,aAAK,wBAAwB,CAAC;AAAA,MAChC,CAAC;AAGD,aAAO,iBAAiB,cAAc,MAAM;AAC1C,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IAEA,qBAAqB;AAEnB,YAAM,OAAO,OAAO,SAAS,KAAK,QAAQ,KAAK,EAAE;AACjD,YAAM,iBAAiB,QAAQ,KAAK,SAAS,IAAI,IAAI,IAAI,OAAO;AAEhE,WAAK,YAAY,gBAAgB,KAAK;AAAA,IACxC;AAAA,IAEA,MAAM,YAAY,aAAa,UAAU,MAAM;AAC7C,UAAI,CAAC,KAAK,SAAS,IAAI,WAAW,GAAG;AACnC;AAAA,MACF;AAEA,YAAM,gBAAgB,KAAK,SAAS,IAAI,WAAW;AAEnD,UAAI,cAAc,WAAW,KAAK,mBAAmB,aAAa;AAChE;AAAA,MACF;AAGA,UAAI,KAAK,kBAAkB,KAAK,mBAAmB,aAAa;AAC9D,cAAM,KAAK,YAAY,KAAK,gBAAgB,OAAO;AAAA,MACrD;AAGA,YAAM,KAAK,eAAe,aAAa,OAAO;AAE9C,WAAK,iBAAiB;AACtB,WAAK,UAAU,WAAW;AAC1B,WAAK,mBAAmB,WAAW;AAAA,IACrC;AAAA,IAEA,MAAM,YAAY,aAAa,UAAU,MAAM;AAC7C,UAAI,CAAC,KAAK,SAAS,IAAI,WAAW,GAAG;AACnC;AAAA,MACF;AAEA,YAAM,UAAU,KAAK,SAAS,IAAI,WAAW;AAE7C,UAAI,CAAC,QAAQ,WAAW,QAAQ,WAAW;AACzC;AAAA,MACF;AAEA,cAAQ,YAAY;AAEpB,UAAI,WAAW,KAAK,QAAQ;AAC1B,cAAM,KAAK,OAAO;AAAA,UAChB,QAAQ;AAAA,UACR;AAAA,YACE,SAAS,CAAC,GAAG,CAAC;AAAA,YACd,GAAG,CAAC,GAAG,GAAG;AAAA,YACV,OAAO,CAAC,GAAG,IAAI;AAAA,UACjB;AAAA,UACA;AAAA,YACE,UAAU;AAAA,YACV,MAAM;AAAA,UACR;AAAA,QACF,EAAE;AAAA,MACJ;AAEA,cAAQ,QAAQ,MAAM,UAAU;AAChC,cAAQ,UAAU;AAClB,cAAQ,YAAY;AAAA,IACtB;AAAA,IAEA,MAAM,eAAe,aAAa,UAAU,MAAM;AAChD,UAAI,CAAC,KAAK,SAAS,IAAI,WAAW,GAAG;AACnC;AAAA,MACF;AAEA,YAAM,UAAU,KAAK,SAAS,IAAI,WAAW;AAE7C,UAAI,QAAQ,WAAW,QAAQ,WAAW;AACxC;AAAA,MACF;AAEA,cAAQ,YAAY;AACpB,cAAQ,QAAQ,MAAM,UAAU;AAEhC,UAAI,WAAW,KAAK,QAAQ;AAE1B,aAAK,OAAO;AAAA,UACV,QAAQ;AAAA,UACR;AAAA,YACE,SAAS;AAAA,YACT,GAAG;AAAA,YACH,OAAO;AAAA,UACT;AAAA,UACA,EAAE,UAAU,EAAE;AAAA,QAChB;AAGA,cAAM,KAAK,OAAO;AAAA,UAChB,QAAQ;AAAA,UACR;AAAA,YACE,SAAS,CAAC,GAAG,CAAC;AAAA,YACd,GAAG,CAAC,IAAI,CAAC;AAAA,YACT,OAAO,CAAC,MAAM,CAAC;AAAA,UACjB;AAAA,UACA;AAAA,YACE,UAAU;AAAA,YACV,MAAM;AAAA,UACR;AAAA,QACF,EAAE;AAAA,MACJ,OAAO;AAEL,gBAAQ,QAAQ,MAAM,UAAU;AAChC,gBAAQ,QAAQ,MAAM,YAAY;AAAA,MACpC;AAEA,cAAQ,UAAU;AAClB,cAAQ,YAAY;AAAA,IACtB;AAAA,IAEA,MAAM,cAAc,aAAa;AAC/B,UAAI,CAAC,KAAK,SAAS,IAAI,WAAW,GAAG;AACnC;AAAA,MACF;AAEA,YAAM,UAAU,KAAK,SAAS,IAAI,WAAW;AAE7C,UAAI,QAAQ,SAAS;AACnB,cAAM,KAAK,YAAY,WAAW;AAAA,MACpC,OAAO;AACL,cAAM,KAAK,YAAY,WAAW;AAAA,MACpC;AAAA,IACF;AAAA,IAEA,UAAU,aAAa;AACrB,UAAI,gBAAgB,QAAQ;AAC1B,eAAO,QAAQ,UAAU,MAAM,MAAM,IAAI,WAAW,EAAE;AAAA,MACxD,OAAO;AACL,eAAO,QAAQ,UAAU,MAAM,MAAM,OAAO,SAAS,QAAQ;AAAA,MAC/D;AAAA,IACF;AAAA,IAEA,mBAAmB,aAAa;AAE9B,WAAK,SAAS,QAAQ,CAAC,aAAa;AAClC,iBAAS,QAAQ,CAAC,YAAY;AAC5B,kBAAQ,UAAU,OAAO,WAAW;AAAA,QACtC,CAAC;AAAA,MACH,CAAC;AAGD,YAAM,eAAe,KAAK,SAAS,IAAI,QAAQ,WAAW,EAAE;AAC5D,UAAI,cAAc;AAChB,qBAAa,QAAQ,CAAC,YAAY;AAChC,kBAAQ,UAAU,IAAI,WAAW;AAAA,QACnC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,wBAAwB,GAAG;AAEzB,UAAI,EAAE,WAAW,EAAE,SAAS;AAC1B,gBAAQ,EAAE,KAAK;AAAA,UACb,KAAK;AACH,cAAE,eAAe;AACjB,iBAAK,YAAY,MAAM;AACvB;AAAA,UACF,KAAK;AACH,cAAE,eAAe;AACjB,iBAAK,YAAY,OAAO;AACxB;AAAA,UACF,KAAK;AACH,cAAE,eAAe;AACjB,iBAAK,YAAY,QAAQ;AACzB;AAAA,UACF,KAAK;AACH,cAAE,eAAe;AACjB,iBAAK,YAAY,UAAU;AAC3B;AAAA,UACF,KAAK;AACH,cAAE,eAAe;AACjB,iBAAK,YAAY,OAAO;AACxB;AAAA,QACJ;AAAA,MACF;AAGA,UAAI,EAAE,QAAQ,UAAU;AACtB,aAAK,YAAY,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,IAEA,mBAAmB;AACjB,YAAM,OAAO,OAAO,SAAS,KAAK,QAAQ,KAAK,EAAE;AACjD,UAAI,QAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,KAAK,gBAAgB;AACnE,aAAK,YAAY,IAAI;AAAA,MACvB,WAAW,CAAC,QAAQ,KAAK,mBAAmB,QAAQ;AAClD,aAAK,YAAY,MAAM;AAAA,MACzB;AAAA,IACF;AAAA;AAAA,IAIA,oBAAoB;AAClB,aAAO,KAAK;AAAA,IACd;AAAA,IAEA,iBAAiB,aAAa;AAC5B,YAAM,UAAU,KAAK,SAAS,IAAI,WAAW;AAC7C,aAAO,UAAU,QAAQ,UAAU;AAAA,IACrC;AAAA,IAEA,kBAAkB,aAAa;AAC7B,YAAM,UAAU,KAAK,SAAS,IAAI,WAAW;AAC7C,aAAO,UAAU,QAAQ,UAAU;AAAA,IACrC;AAAA,IAEA,iBAAiB,MAAM,UAAU;AAC/B,YAAM,UAAU,SAAS,cAAc,QAAQ;AAC/C,UAAI,SAAS;AACX,aAAK,SAAS,IAAI,MAAM;AAAA,UACtB;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,WAAW;AAAA,QACb,CAAC;AACD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IAEA,iBAAiB,MAAM,UAAU,eAAe;AAC9C,YAAM,WAAW,SAAS,iBAAiB,QAAQ;AACnD,UAAI,SAAS,SAAS,GAAG;AACvB,aAAK,SAAS,IAAI,MAAM,MAAM,KAAK,QAAQ,CAAC;AAG5C,iBAAS,QAAQ,CAAC,YAAY;AAC5B,kBAAQ,iBAAiB,SAAS,CAAC,MAAM;AACvC,cAAE,eAAe;AACjB,iBAAK,YAAY,aAAa;AAAA,UAChC,CAAC;AAAA,QACH,CAAC;AAED,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF;", "names": []}