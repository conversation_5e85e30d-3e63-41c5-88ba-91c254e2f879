{"name": "app-calc-reino", "version": "1.0.2", "description": "Sistema modular de calculadora patrimonial para o Reino Capital", "homepage": "https://github.com/joaolucaswork/app-calc-reino#readme", "license": "MIT", "keywords": ["webflow", "calculator", "patrimony", "reino", "modular"], "author": {"name": "Reino Capital", "url": "https://www.reinocapital.com.br/"}, "repository": {"type": "git", "url": "git+https://github.com/joaolucaswork/app-calc-reino.git"}, "bugs": {"url": "https://github.com/joaolucaswork/app-calc-reino/issues"}, "type": "module", "main": "src/index.ts", "module": "src/index.ts", "files": ["dist"], "scripts": {"dev": "cross-env NODE_ENV=development node ./bin/build.js", "build": "cross-env NODE_ENV=production node ./bin/build.js", "build:cdn": "cross-env NODE_ENV=production node ./bin/build.js && echo 'Build completo para CDN! Arquivos em ./dist/'", "lint": "eslint ./src && prettier --check ./src", "lint:fix": "eslint ./src --fix", "check": "tsc --noEmit", "format": "prettier --write ./src", "test": "playwright test", "test:ui": "playwright test --ui", "validate:webflow": "node ./bin/validate-webflow.js", "release": "changeset publish", "update": "pnpm update -i -L -r"}, "devDependencies": {"@changesets/changelog-git": "^0.2.1", "@changesets/cli": "^2.29.5", "@eslint/js": "^9.32.0", "@finsweet/eslint-config": "^3.0.3", "@finsweet/tsconfig": "^1.4.2", "@playwright/test": "^1.54.1", "cross-env": "^7.0.3", "esbuild": "^0.24.2", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-simple-import-sort": "^12.1.1", "prettier": "^3.6.2", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}, "dependencies": {"@finsweet/ts-utils": "^0.40.0", "@supabase/supabase-js": "^2.39.0", "d3": "^7.9.0", "motion": "^12.23.12"}, "engines": {"pnpm": ">=10"}}