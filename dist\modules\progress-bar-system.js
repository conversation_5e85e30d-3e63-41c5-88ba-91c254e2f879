"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/progress-bar-system.js
  var StepNavigationProgressSystem = class {
    static {
      __name(this, "StepNavigationProgressSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.progressBar = null;
      this.sectionIndicators = [];
      this.currentStep = 0;
      this.sectionCache = /* @__PURE__ */ new Map();
      this.startTime = Date.now();
      this.steps = [
        {
          id: "_0-home-section-calc-intro",
          name: "intro",
          title: "Introdu\xE7\xE3o",
          validator: /* @__PURE__ */ __name(() => this.validateIntroStep(), "validator")
        },
        {
          id: "_1-section-calc-money",
          name: "money",
          title: "Renda",
          validator: /* @__PURE__ */ __name(() => this.validateMoneyStep(), "validator")
        },
        {
          id: "_2-section-calc-ativos",
          name: "assets",
          title: "Ativos",
          validator: /* @__PURE__ */ __name(() => this.validateAssetsStep(), "validator")
        },
        {
          id: "_3-section-patrimonio-alocation",
          name: "allocation",
          title: "Aloca\xE7\xE3o",
          validator: /* @__PURE__ */ __name(() => this.validateAllocationStep(), "validator")
        },
        {
          id: "_4-section-resultado",
          name: "results",
          title: "Resultados",
          validator: /* @__PURE__ */ __name(() => true, "validator")
          // Results section is always valid
        }
      ];
      this.supportsAnimations = !window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      this.isTouch = "ontouchstart" in window;
      this.isMobile = window.innerWidth < 768;
      this.config = {
        enableLogging: false,
        animationDuration: 300
      };
      this.debouncedValidation = this.debounce(() => {
        this.updateNavigationState();
      }, 300);
    }
    async init(config = {}) {
      if (this.isInitialized) {
        console.warn("StepNavigationProgressSystem j\xE1 foi inicializado");
        return;
      }
      this.config = { ...this.config, ...config };
      try {
        await this.waitForDOM();
        await this.cacheSections();
        this.cacheElements();
        this.setupSections();
        this.setupEventListeners();
        this.setupSectionIndicatorNavigation();
        this.setupValidation();
        this.setupInitialState();
        this.showStep(0);
        this.isInitialized = true;
        window.addEventListener(
          "resize",
          this.debounce(() => {
            this.isMobile = window.innerWidth < 768;
            this.reinitializeSections();
          }, 250)
        );
        setTimeout(() => {
          this.notifyStateChange();
        }, 50);
        if (this.config.enableLogging) {
          console.warn("\u2705 StepNavigationProgressSystem inicializado");
        }
      } catch (error) {
        console.error("\u274C Erro ao inicializar StepNavigationProgressSystem:", error);
        throw error;
      }
    }
    waitForDOM() {
      return new Promise((resolve) => {
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", resolve);
        } else {
          resolve();
        }
      });
    }
    async cacheSections() {
      for (const step of this.steps) {
        const section = document.querySelector(`.${step.id}`);
        if (section) {
          this.sectionCache.set(step.id, section);
        }
      }
    }
    setupSections() {
      this.steps.forEach((step, index) => {
        const section = this.sectionCache.get(step.id);
        if (section) {
          this.setupSimpleSection(section, index);
        }
      });
    }
    setupSimpleSection(section, index) {
      section.style.display = index === 0 ? "block" : "none";
    }
    setupEventListeners() {
      document.addEventListener("click", (event) => {
        if (event.target.matches('[element-function="next"]')) {
          event.preventDefault();
          this.nextStep();
        }
        if (event.target.matches('.step-btn.prev-btn, [element-function="prev"]')) {
          event.preventDefault();
          this.previousStep();
        }
      });
    }
    setupValidation() {
      this.setupRealtimeValidation();
    }
    setupSectionIndicatorNavigation() {
      const sectionIndicators = document.querySelectorAll(".section-indicator");
      sectionIndicators.forEach((indicator) => {
        indicator.addEventListener("click", (event) => {
          event.preventDefault();
          const numberIndicator = indicator.querySelector(".number-indicator div");
          const stepNumber = numberIndicator ? parseInt(numberIndicator.textContent) : null;
          const targetStep = stepNumber ? stepNumber : null;
          const hasPointer = indicator.classList.contains("pointer");
          if (this.config.enableLogging) {
            console.warn(
              `\u{1F5B1}\uFE0F Clique no section-indicator: n\xFAmero ${stepNumber}, target ${targetStep}, hasPointer: ${hasPointer}, currentStep: ${this.currentStep}`
            );
          }
          if (hasPointer && targetStep !== null) {
            if (targetStep >= 1 && targetStep <= this.steps.length) {
              this.goToStepFromIndicator(targetStep);
            }
          } else {
            if (this.config.enableLogging) {
              console.warn("\u274C Navega\xE7\xE3o bloqueada - indicador sem classe pointer ou step inv\xE1lido");
            }
          }
        });
      });
    }
    setupRealtimeValidation() {
      document.addEventListener("input", (event) => {
        if (event.target.matches("#currency, .currency-input")) {
          this.debouncedValidation();
        }
      });
      document.addEventListener("input", (event) => {
        if (event.target.matches("[data-allocation]")) {
          this.debouncedValidation();
        }
      });
    }
    cacheElements() {
      this.progressBar = document.querySelector(".progress-bar");
      if (!this.progressBar) {
        throw new Error("Progress bar n\xE3o encontrada (.progress-bar)");
      }
      this.sectionIndicators = Array.from(document.querySelectorAll(".item-section-indicator"));
      if (this.config.enableLogging) {
        console.warn("Progress bar encontrada:", this.progressBar);
        console.warn("Indicadores encontrados:", this.sectionIndicators.length);
      }
    }
    setupInitialState() {
      this.currentStep = 0;
      if (this.progressBar) {
        this.progressBar.classList.remove("first-section", "aloca-section");
        this.progressBar.classList.add("first-section");
      }
      this.updateSectionIndicators(0);
    }
    /**
     * Atualiza o estado da progress bar baseado no step atual
     * @param {number} stepIndex - Índice do step atual (0-based)
     */
    updateProgressBarState(stepIndex) {
      if (!this.progressBar) return;
      const previousStep = this.currentStep;
      this.currentStep = stepIndex;
      this.progressBar.classList.remove("first-section", "aloca-section");
      if (stepIndex === 0) {
        this.progressBar.classList.add("first-section");
        if (this.config.enableLogging) {
          console.warn('\u{1F504} Adicionada classe "first-section" \xE0 progress bar');
        }
      } else if (stepIndex === 3 || stepIndex === 4) {
        this.progressBar.classList.add("aloca-section");
        if (this.config.enableLogging) {
          console.warn('\u{1F3AF} Adicionada classe "aloca-section" \xE0 progress bar');
        }
      } else {
        if (this.config.enableLogging) {
          console.warn("\u{1F3AF} Progress bar sem classe especial (se\xE7\xF5es 1-2)");
        }
      }
      this.updateSectionIndicators(stepIndex);
      this.updateSectionIndicatorPointers(stepIndex);
      this.updateInteractiveMainItems(stepIndex);
      this.notifyStateChange(previousStep, stepIndex);
    }
    /**
     * Atualiza os indicadores visuais das seções
     * @param {number} activeStepIndex - Índice do step ativo
     */
    updateSectionIndicators(activeStepIndex) {
      this.sectionIndicators.forEach((indicator, index) => {
        const sectionMain = indicator.querySelector("[section-main]");
        const sectionIndicator = indicator.querySelector(".section-indicator");
        const numberIndicator = indicator.querySelector(".number-indicator");
        if (!sectionMain) return;
        const sectionNumber = parseInt(sectionMain.getAttribute("section-main")) || index + 1;
        if (activeStepIndex === 0) {
          if (sectionIndicator) {
            sectionIndicator.classList.add("active");
            sectionIndicator.classList.remove("completed");
          }
          if (numberIndicator) {
            numberIndicator.classList.add("active");
            numberIndicator.classList.remove("completed");
          }
          const interactiveItem = indicator.querySelector(".interactive-cards-item");
          if (interactiveItem) {
            interactiveItem.classList.add("active");
            interactiveItem.classList.remove("completed");
          }
        } else {
          const isActive = activeStepIndex === sectionNumber;
          const isCompleted = activeStepIndex > sectionNumber;
          if (sectionIndicator) {
            sectionIndicator.classList.toggle("active", isActive);
            sectionIndicator.classList.toggle("completed", isCompleted);
          }
          if (numberIndicator) {
            numberIndicator.classList.toggle("active", isActive);
            numberIndicator.classList.toggle("completed", isCompleted);
          }
          const interactiveItem = indicator.querySelector(".interactive-cards-item");
          if (interactiveItem) {
            interactiveItem.classList.toggle("active", isActive);
            interactiveItem.classList.toggle("completed", isCompleted);
          }
        }
      });
    }
    /**
     * Atualiza as classes pointer nos section-indicator baseado no step atual
     * @param {number} activeStepIndex - Índice do step ativo
     */
    updateSectionIndicatorPointers(activeStepIndex) {
      const sectionIndicators = document.querySelectorAll(".section-indicator");
      sectionIndicators.forEach((indicator) => {
        if (activeStepIndex > 0) {
          indicator.classList.add("pointer");
        } else {
          indicator.classList.remove("pointer");
        }
      });
      if (this.config.enableLogging) {
        if (activeStepIndex > 0) {
          console.warn('\u{1F517} Adicionada classe "pointer" a todos os section-indicator');
        } else {
          console.warn('\u{1F517} Removida classe "pointer" de todos os section-indicator');
        }
      }
    }
    /**
     * Atualiza o estado das divs interactive-main-item
     * @param {number} activeStepIndex - Índice do step ativo
     */
    updateInteractiveMainItems(activeStepIndex) {
      const interactiveMainItems = document.querySelectorAll(".interactive-main-item");
      if (activeStepIndex > 0) {
        interactiveMainItems.forEach((item) => {
          item.classList.add("disabled-item");
        });
        if (this.config.enableLogging) {
          console.warn('\u{1F512} Aplicada classe "disabled-item" em todas as interactive-main-item');
        }
      } else {
        interactiveMainItems.forEach((item) => {
          const shouldStayDisabled = this.shouldKeepOriginalDisabled(item);
          if (!shouldStayDisabled) {
            item.classList.remove("disabled-item");
          }
        });
        if (this.config.enableLogging) {
          console.warn(
            '\u{1F513} Removida classe "disabled-item" das interactive-main-item (exceto originais)'
          );
        }
      }
    }
    /**
     * Verifica se um item deve manter a classe disabled-item originalmente
     * @param {Element} item - Elemento interactive-main-item
     * @returns {boolean}
     */
    shouldKeepOriginalDisabled(item) {
      const animationWrapper = item.querySelector(".animation-source-wrapper.disabled-item");
      return !!animationWrapper;
    }
    /**
     * Adiciona classe personalizada à progress bar
     * @param {string} className - Nome da classe a ser adicionada
     */
    addProgressBarClass(className) {
      if (this.progressBar) {
        this.progressBar.classList.add(className);
        if (this.config.enableLogging) {
          console.warn(`\u2795 Adicionada classe "${className}" \xE0 progress bar`);
        }
      }
    }
    /**
     * Remove classe personalizada da progress bar
     * @param {string} className - Nome da classe a ser removida
     */
    removeProgressBarClass(className) {
      if (this.progressBar) {
        this.progressBar.classList.remove(className);
        if (this.config.enableLogging) {
          console.warn(`\u2796 Removida classe "${className}" da progress bar`);
        }
      }
    }
    /**
     * Verifica se a progress bar tem uma classe específica
     * @param {string} className - Nome da classe a verificar
     * @returns {boolean}
     */
    hasProgressBarClass(className) {
      return this.progressBar ? this.progressBar.classList.contains(className) : false;
    }
    /**
     * Define uma classe condicional baseada no step
     * @param {string} className - Nome da classe
     * @param {number|number[]} steps - Step(s) onde a classe deve estar presente
     */
    setConditionalClass(className, steps) {
      if (!this.progressBar) return;
      const stepsArray = Array.isArray(steps) ? steps : [steps];
      const shouldHaveClass = stepsArray.includes(this.currentStep);
      if (shouldHaveClass) {
        this.addProgressBarClass(className);
      } else {
        this.removeProgressBarClass(className);
      }
    }
    /**
     * Obtém informações sobre o estado atual da progress bar
     * @returns {Object}
     */
    getProgressBarState() {
      if (!this.progressBar) return null;
      return {
        currentStep: this.currentStep,
        classes: Array.from(this.progressBar.classList),
        hasFirstSection: this.hasProgressBarClass("first-section"),
        totalSections: this.sectionIndicators.length,
        element: this.progressBar
      };
    }
    /**
     * Força uma atualização completa do estado da progress bar
     */
    forceUpdate() {
      if (!this.isInitialized) return;
      this.updateProgressBarState(this.currentStep);
    }
    /**
     * Notifica outros sistemas sobre mudanças de estado
     * @param {number} previousStep - Step anterior
     * @param {number} currentStep - Step atual
     */
    notifyStateChange(previousStep, currentStep) {
      const event = new CustomEvent("progressBarStateChange", {
        detail: {
          previousStep,
          currentStep,
          progressBarState: this.getProgressBarState()
        }
      });
      document.dispatchEvent(event);
      if (this.config.enableLogging) {
        console.warn("\u{1F4E1} Progress bar state change notificado:", {
          previousStep,
          currentStep
        });
      }
    }
    /**
     * Atualiza configuração do sistema
     * @param {Object} newConfig - Nova configuração
     */
    updateConfig(newConfig) {
      this.config = { ...this.config, ...newConfig };
    }
    /**
     * Método para ser chamado quando o step muda (interface para outros sistemas)
     * @param {number} stepIndex - Novo step index
     */
    onStepChange(stepIndex) {
      this.updateProgressBarState(stepIndex);
    }
    /**
     * Destrói o sistema e limpa recursos
     */
    destroy() {
      this.progressBar = null;
      this.sectionIndicators = [];
      this.currentStep = 0;
      this.isInitialized = false;
      if (this.config.enableLogging) {
        console.warn("\u{1F504} StepNavigationProgressSystem destru\xEDdo");
      }
    }
    // Navigation methods
    async showStep(stepIndex) {
      if (stepIndex < 0 || stepIndex >= this.steps.length) return;
      const previousStep = this.currentStep;
      this.currentStep = stepIndex;
      if (previousStep !== stepIndex) {
        this.saveStepData(previousStep);
      }
      this.showStepSimple(stepIndex);
      this.updateProgressBarState(stepIndex);
      this.updateAccessibility(stepIndex);
      this.focusManagement(stepIndex);
      this.notifyWebflowButtonSystem();
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
    showStepSimple(stepIndex) {
      this.steps.forEach((step, index) => {
        const section = this.sectionCache.get(step.id);
        if (!section) return;
        if (index === stepIndex) {
          section.style.display = "block";
          section.style.opacity = "1";
          section.style.transform = "translateY(0)";
          section.style.pointerEvents = "auto";
        } else {
          section.style.display = "none";
          section.style.opacity = "0";
          section.style.transform = "translateY(20px)";
          section.style.pointerEvents = "none";
        }
      });
    }
    nextStep() {
      if (this.currentStep >= this.steps.length - 1) {
        this.submitForm();
        return;
      }
      if (this.canProceedToNext()) {
        this.showStep(this.currentStep + 1);
      } else {
        this.showValidationError();
      }
    }
    previousStep() {
      if (this.currentStep > 0) {
        this.showStep(this.currentStep - 1);
      }
    }
    goToStep(stepIndex) {
      if (stepIndex <= this.currentStep) {
        this.showStep(stepIndex);
        this.notifyWebflowButtonSystem();
        return;
      }
      if (this.canProceedToNext()) {
        this.showStep(stepIndex);
        this.notifyWebflowButtonSystem();
      } else {
        this.showValidationError();
      }
    }
    goToStepFromIndicator(sectionNumber) {
      const targetStep = sectionNumber;
      if (this.config.enableLogging) {
        console.warn(`\u{1F504} goToStepFromIndicator: se\xE7\xE3o ${sectionNumber} -> step ${targetStep}`);
      }
      if (targetStep < 0 || targetStep >= this.steps.length) {
        if (this.config.enableLogging) {
          console.warn(`\u274C Step ${targetStep} inv\xE1lido (max: ${this.steps.length - 1})`);
        }
        return;
      }
      if (targetStep <= this.currentStep) {
        this.showStep(targetStep);
        return;
      }
      if (targetStep === this.currentStep + 1) {
        if (this.canProceedToNext()) {
          this.showStep(targetStep);
          this.notifyWebflowButtonSystem();
        } else {
          this.showValidationError();
        }
      } else {
        if (this.config.enableLogging) {
          console.warn(`\u274C N\xE3o \xE9 poss\xEDvel pular do step ${this.currentStep} para ${targetStep}`);
        }
        this.showValidationError();
      }
    }
    /**
     * Notifica o webflow-button system sobre mudanças de navegação
     */
    notifyWebflowButtonSystem() {
      const webflowButton = window.ReinoCalculator?.systems?.webflowButton;
      if (webflowButton && typeof webflowButton.updateNextButtonsState === "function") {
        setTimeout(() => {
          webflowButton.updateNextButtonsState();
          if (typeof webflowButton.forceUpdateButtons === "function") {
            webflowButton.forceUpdateButtons();
          }
        }, 50);
      }
    }
    // Validation methods
    canProceedToNext() {
      return this.steps[this.currentStep]?.validator() || false;
    }
    validateIntroStep() {
      if (this.config.enableLogging) {
        console.warn("\u2705 validateIntroStep: sempre true");
      }
      return true;
    }
    validateMoneyStep() {
      const currencyInput = document.querySelector('#currency, .currency-input[is-main="true"]');
      if (!currencyInput) {
        if (this.config.enableLogging) {
          console.warn("\u274C validateMoneyStep: input n\xE3o encontrado");
        }
        return false;
      }
      const value = this.parseInputValue(currencyInput.value);
      const isValid = value > 0;
      if (this.config.enableLogging) {
        console.warn(`${isValid ? "\u2705" : "\u274C"} validateMoneyStep: valor=${value}, v\xE1lido=${isValid}`);
      }
      return isValid;
    }
    validateAssetsStep() {
      const selectedAssets = document.querySelectorAll(".selected-asset");
      const isValid = selectedAssets.length > 0;
      if (this.config.enableLogging) {
        console.warn(
          `${isValid ? "\u2705" : "\u274C"} validateAssetsStep: ${selectedAssets.length} assets selecionados`
        );
      }
      return isValid;
    }
    validateAllocationStep() {
      const totalAllocation = this.calculateTotalAllocation();
      const isValid = Math.abs(totalAllocation - 100) < 0.01;
      if (this.config.enableLogging) {
        console.warn(
          `${isValid ? "\u2705" : "\u274C"} validateAllocationStep: total=${totalAllocation}%, v\xE1lido=${isValid}`
        );
      }
      return isValid;
    }
    updateAccessibility(stepIndex) {
      const currentStepData = this.steps[stepIndex];
      if (currentStepData) {
        document.title = `Reino Calculator - ${currentStepData.title}`;
      }
    }
    focusManagement(stepIndex) {
      setTimeout(() => {
        const section = this.sectionCache.get(this.steps[stepIndex]?.id);
        if (section) {
          const firstInput = section.querySelector("input, button, select, textarea");
          if (firstInput && typeof firstInput.focus === "function") {
            try {
              firstInput.focus();
            } catch {
            }
          }
        }
      }, 100);
    }
    showValidationError() {
      const currentStepData = this.steps[this.currentStep];
      let message = "Por favor, complete os campos obrigat\xF3rios.";
      if (currentStepData?.name === "money") {
        message = "Por favor, insira um valor v\xE1lido para seu patrim\xF4nio.";
      } else if (currentStepData?.name === "assets") {
        message = "Por favor, selecione pelo menos um ativo.";
      } else if (currentStepData?.name === "allocation") {
        message = "Por favor, aloque 100% do seu patrim\xF4nio.";
      }
      this.showToast(message, "error");
    }
    showToast(message, type = "info") {
      const toast = document.createElement("div");
      toast.className = `toast toast-${type}`;
      toast.textContent = message;
      toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      background: ${type === "error" ? "#f56565" : "#4299e1"};
      color: white;
      border-radius: 8px;
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;
      document.body.appendChild(toast);
      setTimeout(() => toast.remove(), 3e3);
    }
    parseInputValue(value) {
      return parseFloat(value?.replace(/[^\d.,]/g, "").replace(",", ".")) || 0;
    }
    calculateTotalAllocation() {
      const allocationInputs = document.querySelectorAll("[data-allocation]");
      let total = 0;
      allocationInputs.forEach((input) => {
        const value = this.parseInputValue(input.value);
        total += value;
      });
      return total;
    }
    saveStepData(stepIndex) {
      const stepData = this.collectStepData(stepIndex);
      const stepName = this.steps[stepIndex]?.name;
      if (stepName && stepData) {
        if (typeof localStorage !== "undefined") {
          localStorage.setItem(`reino_calc_${stepName}`, JSON.stringify(stepData));
        }
      }
    }
    collectStepData(stepIndex) {
      const section = this.sectionCache.get(this.steps[stepIndex]?.id);
      if (!section) return null;
      const data = {};
      const inputs = section.querySelectorAll("input, select, textarea");
      inputs.forEach((input) => {
        if (input.name || input.id) {
          const key = input.name || input.id;
          if (input.type === "checkbox" || input.type === "radio") {
            data[key] = input.checked;
          } else {
            data[key] = input.value;
          }
        }
      });
      return data;
    }
    async submitForm() {
      try {
        const formData = this.collectAllFormData();
        console.warn("\u{1F4CA} Formul\xE1rio submetido:", formData);
        this.onSubmissionSuccess();
      } catch (error) {
        console.error("\u274C Erro ao submeter formul\xE1rio:", error);
      }
    }
    collectAllFormData() {
      const data = { submittedAt: (/* @__PURE__ */ new Date()).toISOString(), totalTime: this.getTotalTime() };
      this.steps.forEach((step, index) => {
        const stepData = this.collectStepData(index);
        if (stepData) {
          data[step.name] = stepData;
        }
      });
      return data;
    }
    onSubmissionSuccess() {
      this.showToast("Formul\xE1rio enviado com sucesso!", "success");
    }
    debounce(func, wait) {
      let timeout;
      return /* @__PURE__ */ __name(function executedFunction(...args) {
        const later = /* @__PURE__ */ __name(() => {
          clearTimeout(timeout);
          func(...args);
        }, "later");
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      }, "executedFunction");
    }
    getTotalTime() {
      return Math.round((Date.now() - this.startTime) / 1e3);
    }
    reinitializeSections() {
      this.setupSections();
    }
    clearSavedData() {
      if (typeof localStorage !== "undefined") {
        this.steps.forEach((step) => {
          localStorage.removeItem(`reino_calc_${step.name}`);
        });
      }
    }
    updateNavigationState() {
    }
    getCurrentStep() {
      return this.currentStep;
    }
    canProceed() {
      return this.canProceedToNext();
    }
    cleanup() {
      window.removeEventListener("resize", this.debounce);
      this.sectionCache.clear();
      this.currentStep = 0;
      this.isInitialized = false;
    }
  };
  var ProgressBarSystem = StepNavigationProgressSystem;
  var StepNavigationSystem = StepNavigationProgressSystem;
  var progress_bar_system_default = StepNavigationProgressSystem;
})();
//# sourceMappingURL=progress-bar-system.js.map
