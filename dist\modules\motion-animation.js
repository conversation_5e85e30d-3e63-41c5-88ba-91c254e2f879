"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/event-coordinator.js
  var EventCoordinator = class {
    static {
      __name(this, "EventCoordinator");
    }
    constructor() {
      this.input = null;
      this.listeners = /* @__PURE__ */ new Map();
      this.isProcessing = false;
      this.eventQueue = [];
      this.boundHandlers = /* @__PURE__ */ new Map();
      this.isDestroyed = false;
      this.init();
    }
    init() {
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.findAndSetupInput());
      } else {
        this.findAndSetupInput();
      }
    }
    findAndSetupInput() {
      this.input = document.querySelector('[is-main="true"]');
      if (this.input && !this.isDestroyed) {
        this.setupMainListeners();
      }
    }
    setupMainListeners() {
      if (!this.input || this.boundHandlers.has("main")) return;
      const inputHandler = /* @__PURE__ */ __name((e) => this.handleInputEvent(e), "inputHandler");
      const focusHandler = /* @__PURE__ */ __name((e) => this.processFocusEvent(e), "focusHandler");
      const blurHandler = /* @__PURE__ */ __name((e) => this.processBlurEvent(e), "blurHandler");
      const changeHandler = /* @__PURE__ */ __name((e) => this.processChangeEvent(e), "changeHandler");
      this.boundHandlers.set("main", {
        input: inputHandler,
        focus: focusHandler,
        blur: blurHandler,
        change: changeHandler
      });
      this.input.addEventListener("input", inputHandler, { passive: true });
      this.input.addEventListener("focus", focusHandler, { passive: true });
      this.input.addEventListener("blur", blurHandler, { passive: true });
      this.input.addEventListener("change", changeHandler, { passive: true });
    }
    handleInputEvent(e) {
      if (this.isProcessing || this.isDestroyed) {
        return;
      }
      this.isProcessing = true;
      requestAnimationFrame(() => {
        this.processInputEvent(e);
        this.isProcessing = false;
        if (this.eventQueue.length > 0) {
          const nextEvent = this.eventQueue.shift();
          requestAnimationFrame(() => this.handleInputEvent(nextEvent));
        }
      });
    }
    // Registra um listener para um módulo específico
    registerListener(moduleId, eventType, callback) {
      if (this.isDestroyed) return;
      const key = `${moduleId}_${eventType}`;
      this.unregisterListener(moduleId, eventType);
      if (!this.listeners.has(key)) {
        this.listeners.set(key, []);
      }
      this.listeners.get(key).push(callback);
    }
    // Remove listener de um módulo
    unregisterListener(moduleId, eventType, specificCallback = null) {
      const key = `${moduleId}_${eventType}`;
      if (this.listeners.has(key)) {
        if (specificCallback) {
          const callbacks = this.listeners.get(key);
          const index = callbacks.indexOf(specificCallback);
          if (index > -1) {
            callbacks.splice(index, 1);
          }
        } else {
          this.listeners.delete(key);
        }
      }
    }
    // Remove todos os listeners de um módulo
    unregisterModule(moduleId) {
      const keysToRemove = [];
      for (const key of this.listeners.keys()) {
        if (key.startsWith(`${moduleId}_`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => this.listeners.delete(key));
    }
    processInputEvent(e) {
      if (this.isDestroyed) return;
      const inputCallbacks = this.getCallbacksForEvent("input");
      const priorityOrder = ["currency-formatting", "motion-animation", "patrimony-sync"];
      for (const moduleId of priorityOrder) {
        const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);
        for (const callbackInfo of moduleCallbacks) {
          try {
            callbackInfo.callback(e);
          } catch (error) {
            console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);
          }
        }
      }
    }
    processFocusEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("focus", e);
    }
    processBlurEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("blur", e);
    }
    processChangeEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("change", e);
    }
    executeCallbacksForEvent(eventType, e) {
      const callbacks = this.getCallbacksForEvent(eventType);
      callbacks.forEach(({ callback, moduleId }) => {
        try {
          callback(e);
        } catch (error) {
          console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);
        }
      });
    }
    getCallbacksForEvent(eventType) {
      const callbacks = [];
      for (const [key, callbackList] of this.listeners.entries()) {
        if (key.endsWith(`_${eventType}`)) {
          const moduleId = key.replace(`_${eventType}`, "");
          callbackList.forEach((callback) => {
            callbacks.push({ moduleId, callback });
          });
        }
      }
      return callbacks;
    }
    // Método para disparar eventos programaticamente
    dispatchInputEvent(sourceModule = "unknown") {
      if (this.isProcessing || this.isDestroyed || !this.input) {
        return;
      }
      const event = new Event("input", { bubbles: true });
      event.sourceModule = sourceModule;
      this.input.dispatchEvent(event);
    }
    // Método para atualizar valor sem disparar eventos
    setSilentValue(value) {
      if (this.isDestroyed || !this.input) return;
      this.isProcessing = true;
      this.input.value = value;
      requestAnimationFrame(() => {
        this.isProcessing = false;
      });
    }
    // Getter para o valor atual
    getValue() {
      return this.input ? this.input.value : "";
    }
    // Setter que dispara eventos controlados
    setValue(value, sourceModule = "unknown") {
      if (this.isDestroyed || !this.input) return;
      this.input.value = value;
      this.dispatchInputEvent(sourceModule);
    }
    // Método de cleanup para prevenir memory leaks
    destroy() {
      this.isDestroyed = true;
      if (this.input && this.boundHandlers.has("main")) {
        const handlers = this.boundHandlers.get("main");
        this.input.removeEventListener("input", handlers.input);
        this.input.removeEventListener("focus", handlers.focus);
        this.input.removeEventListener("blur", handlers.blur);
        this.input.removeEventListener("change", handlers.change);
      }
      this.listeners.clear();
      this.boundHandlers.clear();
      this.eventQueue.length = 0;
      this.input = null;
      this.isProcessing = false;
    }
    // Método para reinicializar se necessário
    reinitialize() {
      this.destroy();
      this.isDestroyed = false;
      this.init();
    }
  };
  var eventCoordinator = new EventCoordinator();
  window.addEventListener("beforeunload", () => {
    eventCoordinator.destroy();
  });

  // src/modules/motion-animation.js
  var MotionAnimationSystem = class {
    static {
      __name(this, "MotionAnimationSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.Motion = null;
    }
    init() {
      if (this.isInitialized) {
        return;
      }
      document.addEventListener("DOMContentLoaded", () => {
        this.waitForMotion();
      });
      this.isInitialized = true;
    }
    waitForMotion() {
      if (window.Motion) {
        this.Motion = window.Motion;
        this.initMotionEffects();
      } else {
        setTimeout(() => this.waitForMotion(), 50);
      }
    }
    initMotionEffects() {
      const { animate, hover, press } = this.Motion;
      const input = document.querySelector('input[is-main="true"]');
      const interactiveArrow = document.getElementById("interative-arrow");
      if (!input || !interactiveArrow) return;
      const mainContainer = input.closest(".money_content_right-wrapper");
      if (!mainContainer) return;
      const increaseBtn = mainContainer.querySelector('[currency-control="increase"]');
      const decreaseBtn = mainContainer.querySelector('[currency-control="decrease"]');
      if (!increaseBtn || !decreaseBtn) return;
      let hideTimeout;
      let isArrowVisible = true;
      let isButtonInteraction = false;
      const hideArrow = /* @__PURE__ */ __name(() => {
        isArrowVisible = false;
        animate(
          interactiveArrow,
          {
            opacity: 0,
            scale: 0.8
          },
          {
            duration: 0.4,
            ease: "circInOut"
          }
        );
      }, "hideArrow");
      const showArrow = /* @__PURE__ */ __name(() => {
        isArrowVisible = true;
        animate(
          interactiveArrow,
          {
            opacity: 1,
            scale: 1
          },
          {
            duration: 0.4,
            ease: "backOut"
          }
        );
      }, "showArrow");
      const resetHideTimer = /* @__PURE__ */ __name(() => {
        clearTimeout(hideTimeout);
        if (!isArrowVisible) {
          showArrow();
        }
        hideTimeout = setTimeout(() => {
          hideArrow();
        }, 5e3);
      }, "resetHideTimer");
      const createRippleEffect = /* @__PURE__ */ __name((element, color) => {
        const ripple = document.createElement("div");
        ripple.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        width: 10px;
        height: 10px;
        background: ${color};
        border-radius: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
        z-index: 1;
        opacity: 0.4;
      `;
        element.style.position = "relative";
        element.style.overflow = "hidden";
        element.appendChild(ripple);
        animate(
          ripple,
          {
            scale: [0, 4],
            opacity: [0.4, 0]
          },
          {
            duration: 0.5,
            ease: "circOut"
          }
        );
        setTimeout(() => ripple.remove(), 500);
      }, "createRippleEffect");
      const rotateArrowDown = /* @__PURE__ */ __name(() => {
        animate(
          interactiveArrow,
          {
            rotate: 180,
            color: "#ef4444"
          },
          {
            duration: 0.3,
            ease: "backOut"
          }
        );
      }, "rotateArrowDown");
      const rotateArrowUp = /* @__PURE__ */ __name(() => {
        animate(
          interactiveArrow,
          {
            rotate: 0,
            color: "#22c55e"
          },
          {
            duration: 0.3,
            ease: "backOut"
          }
        );
      }, "rotateArrowUp");
      hover(increaseBtn, (element) => {
        if (element.classList.contains("disabled")) return;
        isButtonInteraction = true;
        animate(
          element,
          {
            scale: 1.08,
            y: -3,
            filter: "brightness(1.1)"
          },
          {
            duration: 0.25,
            ease: "circOut"
          }
        );
        rotateArrowUp();
        resetHideTimer();
        const icon = element.querySelector("svg");
        if (icon) {
          animate(
            icon,
            {
              scale: 1.15
            },
            {
              duration: 0.2,
              ease: "backOut"
            }
          );
        }
        return () => {
          isButtonInteraction = false;
          animate(
            element,
            {
              scale: 1,
              y: 0,
              filter: "brightness(1)"
            },
            {
              duration: 0.2,
              ease: "circInOut"
            }
          );
          if (icon) {
            animate(
              icon,
              {
                scale: 1
              },
              {
                duration: 0.15
              }
            );
          }
        };
      });
      hover(decreaseBtn, (element) => {
        if (element.classList.contains("disabled")) return;
        isButtonInteraction = true;
        animate(
          element,
          {
            scale: 1.08,
            y: -3,
            filter: "brightness(1.1)"
          },
          {
            duration: 0.25,
            ease: "circOut"
          }
        );
        rotateArrowDown();
        resetHideTimer();
        const icon = element.querySelector("svg");
        if (icon) {
          animate(
            icon,
            {
              scale: 1.15
            },
            {
              duration: 0.2,
              ease: "backOut"
            }
          );
        }
        return () => {
          isButtonInteraction = false;
          animate(
            element,
            {
              scale: 1,
              y: 0,
              filter: "brightness(1)"
            },
            {
              duration: 0.2,
              ease: "circInOut"
            }
          );
          if (icon) {
            animate(
              icon,
              {
                scale: 1
              },
              {
                duration: 0.15
              }
            );
          }
        };
      });
      press(increaseBtn, (element) => {
        if (element.classList.contains("disabled")) return;
        isButtonInteraction = true;
        animate(
          element,
          {
            scale: 0.92,
            y: 2
          },
          {
            duration: 0.08,
            ease: "circIn"
          }
        );
        createRippleEffect(element, "#9ca3af");
        rotateArrowUp();
        resetHideTimer();
        return () => {
          animate(
            element,
            {
              scale: 1.08,
              y: -3
            },
            {
              duration: 0.12,
              ease: "backOut"
            }
          );
          setTimeout(() => {
            isButtonInteraction = false;
          }, 100);
        };
      });
      press(decreaseBtn, (element) => {
        if (element.classList.contains("disabled")) return;
        isButtonInteraction = true;
        animate(
          element,
          {
            scale: 0.92,
            y: 2
          },
          {
            duration: 0.08,
            ease: "circIn"
          }
        );
        createRippleEffect(element, "#9ca3af");
        rotateArrowDown();
        resetHideTimer();
        return () => {
          animate(
            element,
            {
              scale: 1.08,
              y: -3
            },
            {
              duration: 0.12,
              ease: "backOut"
            }
          );
          setTimeout(() => {
            isButtonInteraction = false;
          }, 100);
        };
      });
      eventCoordinator.registerListener("motion-animation", "input", () => {
        if (!isButtonInteraction) {
          hideArrow();
          clearTimeout(hideTimeout);
        }
      });
      eventCoordinator.registerListener("motion-animation", "focus", () => {
        if (!isButtonInteraction) {
          hideArrow();
          clearTimeout(hideTimeout);
        }
      });
      const updateButtonStates = /* @__PURE__ */ __name(() => {
        const current = parseFloat(input.value.replace(/[^\d,]/g, "").replace(",", ".")) || 0;
        if (current <= 0) {
          decreaseBtn.classList.add("disabled");
        } else {
          decreaseBtn.classList.remove("disabled");
        }
      }, "updateButtonStates");
      eventCoordinator.registerListener("motion-animation", "input", updateButtonStates);
      const observeInputChanges = /* @__PURE__ */ __name(() => {
        let lastValue = input.value;
        const observer = new MutationObserver(() => {
          if (input.value !== lastValue) {
            lastValue = input.value;
            updateButtonStates();
          }
        });
        observer.observe(input, {
          attributes: true,
          attributeFilter: ["value"]
        });
        const intervalCheck = setInterval(() => {
          if (input.value !== lastValue) {
            lastValue = input.value;
            updateButtonStates();
          }
        }, 200);
        return () => {
          observer.disconnect();
          clearInterval(intervalCheck);
        };
      }, "observeInputChanges");
      observeInputChanges();
      updateButtonStates();
      resetHideTimer();
    }
  };
})();
//# sourceMappingURL=motion-animation.js.map
