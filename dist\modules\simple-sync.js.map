{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/simple-sync.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Simple Sync System\r\n * Synchronizes patrimonio_interactive_item with ativos-grafico-item elements\r\n * Focuses on visual bar height synchronization (not width)\r\n * Now respects budget constraints from PatrimonySyncSystem\r\n */\r\n\r\nexport class SimpleSyncSystem {\r\n  constructor() {\r\n    this.pairs = [];\r\n    this.maxBarHeight = 45; // Maximum height in pixels from CSS\r\n    this.isInitialized = false;\r\n    this.patrimonySyncSystem = null; // Reference to PatrimonySyncSystem\r\n  }\r\n\r\n  async init() {\r\n    try {\r\n      // Wait for PatrimonySyncSystem to be available\r\n      this.setupPatrimonySyncReference();\r\n\r\n      // Find and pair elements\r\n      this.findPairs();\r\n\r\n      // Setup listeners\r\n      this.setupListeners();\r\n\r\n      // Setup budget status listeners\r\n      this.setupBudgetListeners();\r\n\r\n      // Setup listener for when PatrimonySyncSystem finishes loading cache\r\n      document.addEventListener('patrimonySystemReady', () => {\r\n        this.syncAllFromCachedValues();\r\n      });\r\n\r\n      // Initial sync after a brief delay to allow cache loading\r\n      setTimeout(() => {\r\n        this.syncAllPairs();\r\n        // console.log('🔄 Initial sync completed after cache loading');\r\n      }, 100);\r\n\r\n      this.isInitialized = true;\r\n      // console.log(`✅ Simple sync initialized with ${this.pairs.length} pairs`);\r\n    } catch (error) {\r\n      console.error('❌ Simple sync initialization failed:', error);\r\n    }\r\n  }\r\n\r\n  setupPatrimonySyncReference() {\r\n    // Try to get PatrimonySyncSystem from global ReinoCalculator\r\n    if (window.ReinoCalculator?.data?.patrimony) {\r\n      this.patrimonySyncSystem = window.ReinoCalculator.data.patrimony;\r\n    } else {\r\n      // Setup a listener to get it when available\r\n      document.addEventListener('reinoCalculatorReady', (event) => {\r\n        this.patrimonySyncSystem = event.detail.systems.patrimonySync;\r\n      });\r\n    }\r\n  }\r\n\r\n  setupBudgetListeners() {\r\n    // Listen for allocation status changes from PatrimonySyncSystem\r\n    document.addEventListener('allocationStatusChanged', (event) => {\r\n      this.handleBudgetStatusChange(event.detail);\r\n    });\r\n\r\n    // Listen for individual allocation changes (including cache restoration)\r\n    document.addEventListener('allocationChanged', (event) => {\r\n      this.handleAllocationChange(event.detail);\r\n    });\r\n  }\r\n\r\n  handleBudgetStatusChange(status) {\r\n    // If budget is fully allocated or over-allocated, prevent further visual updates\r\n    // This helps maintain visual consistency with budget constraints\r\n    this.budgetStatus = status;\r\n  }\r\n\r\n  handleAllocationChange(detail) {\r\n    // Handle individual allocation changes, including cache restoration\r\n    const { category, product, percentage } = detail;\r\n\r\n    if (!category || !product) return;\r\n\r\n    // Find the corresponding pair\r\n    const pair = this.pairs.find((p) => p.category === category && p.product === product);\r\n\r\n    if (pair) {\r\n      // Update slider value to match the restored allocation\r\n      if (pair.patrimonio.slider) {\r\n        pair.patrimonio.slider.value = percentage / 100;\r\n      }\r\n\r\n      // Sync the visual elements\r\n      this.syncFromPatrimonio(pair);\r\n\r\n      // console.log(`🔄 Synced from cache: ${category} - ${product} = ${percentage.toFixed(1)}%`);\r\n    }\r\n  }\r\n\r\n  findPairs() {\r\n    const patrimonioItems = document.querySelectorAll(\r\n      '.patrimonio_interactive_item[ativo-category][ativo-product]'\r\n    );\r\n\r\n    patrimonioItems.forEach((patrimonioEl) => {\r\n      const category = patrimonioEl.getAttribute('ativo-category');\r\n      const product = patrimonioEl.getAttribute('ativo-product');\r\n\r\n      if (!category || !product) return;\r\n\r\n      // Find matching ativos element\r\n      const ativosEl = document.querySelector(\r\n        `.ativos-grafico-item[ativo-category=\"${category}\"][ativo-product=\"${product}\"]`\r\n      );\r\n\r\n      if (ativosEl) {\r\n        const pair = {\r\n          patrimonio: {\r\n            element: patrimonioEl,\r\n            input: patrimonioEl.querySelector('[input-settings=\"receive\"]'),\r\n            slider: patrimonioEl.querySelector('range-slider'),\r\n            percentage: patrimonioEl.querySelector('.porcentagem-calculadora'),\r\n          },\r\n          ativos: {\r\n            element: ativosEl,\r\n            bar: ativosEl.querySelector('.barra-porcentagem-item'),\r\n            percentage: ativosEl.querySelector('.porcentagem-float-alocacao'),\r\n          },\r\n          category,\r\n          product,\r\n        };\r\n\r\n        // Only add if required elements exist\r\n        if (\r\n          pair.patrimonio.input &&\r\n          pair.patrimonio.slider &&\r\n          pair.ativos.bar &&\r\n          pair.ativos.percentage\r\n        ) {\r\n          // Reset any inline width styles that might be causing issues\r\n          pair.ativos.bar.style.width = '';\r\n\r\n          this.pairs.push(pair);\r\n          // console.log(`🔗 Paired: ${category} - ${product}`);\r\n        } else {\r\n          console.warn(`⚠️ Missing elements for pair: ${category} - ${product}`);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  setupListeners() {\r\n    this.pairs.forEach((pair) => {\r\n      // Listen to input changes\r\n      pair.patrimonio.input?.addEventListener('input', () => {\r\n        this.syncFromPatrimonio(pair);\r\n      });\r\n\r\n      // Listen to slider changes\r\n      pair.patrimonio.slider?.addEventListener('input', () => {\r\n        this.syncFromPatrimonio(pair);\r\n      });\r\n\r\n      // Listen to slider change events (when user stops dragging)\r\n      pair.patrimonio.slider?.addEventListener('change', () => {\r\n        this.syncFromPatrimonio(pair);\r\n      });\r\n    });\r\n  }\r\n\r\n  syncFromPatrimonio(pair) {\r\n    try {\r\n      // Get slider value (0-1)\r\n      const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;\r\n      const percentage = sliderValue * 100;\r\n\r\n      // Always sync the visual elements to match the slider value\r\n      // The budget validation should happen at the PatrimonySyncSystem level\r\n      const barHeight = (percentage / 100) * this.maxBarHeight;\r\n\r\n      // Update ativos bar height (this is the key fix - height not width!)\r\n      pair.ativos.bar.style.height = `${barHeight}px`;\r\n\r\n      // Ensure width stays as defined in CSS\r\n      pair.ativos.bar.style.width = '';\r\n\r\n      // Update percentage displays\r\n      const formattedPercentage = `${percentage.toFixed(1)}%`;\r\n\r\n      pair.ativos.percentage.textContent = formattedPercentage;\r\n\r\n      // Update patrimonio percentage display if exists\r\n      if (pair.patrimonio.percentage) {\r\n        pair.patrimonio.percentage.textContent = formattedPercentage;\r\n      }\r\n\r\n      // Debug log for troubleshooting\r\n      // console.log(`📊 Synced ${pair.category}-${pair.product}: ${formattedPercentage} (height: ${barHeight}px)`);\r\n    } catch (error) {\r\n      console.error(`❌ Sync error for ${pair.category}-${pair.product}:`, error);\r\n    }\r\n  }\r\n\r\n  shouldAllowVisualUpdate(pair) {\r\n    // Always allow visual updates to prevent zeroing of existing allocations\r\n    // The budget validation should happen at the PatrimonySyncSystem level, not here\r\n    return true;\r\n  }\r\n\r\n  // Sync all pairs to current values\r\n  syncAllPairs() {\r\n    this.pairs.forEach((pair) => {\r\n      this.syncFromPatrimonio(pair);\r\n    });\r\n  }\r\n\r\n  // Force sync all pairs from their current slider/input values (for cache restoration)\r\n  syncAllFromCachedValues() {\r\n    // console.log('🔄 Syncing all pairs from cached values...');\r\n\r\n    this.pairs.forEach((pair) => {\r\n      if (pair.patrimonio.slider && pair.patrimonio.input) {\r\n        // Get the current slider value (which should be set from cache)\r\n        const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;\r\n\r\n        // Force sync the visual elements\r\n        this.syncFromPatrimonio(pair);\r\n\r\n        // console.log(`📊 Restored: ${pair.category} - ${pair.product} = ${percentage.toFixed(1)}%`);\r\n      }\r\n    });\r\n\r\n    // console.log('✅ All pairs synced from cached values');\r\n  }\r\n\r\n  // Force sync all visual elements based on current slider values (fix zeroing issues)\r\n  forceSyncFromSliders() {\r\n    // console.log('🔧 Force syncing all visual elements from current slider values...');\r\n\r\n    this.pairs.forEach((pair) => {\r\n      if (pair.patrimonio.slider) {\r\n        const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;\r\n        const percentage = sliderValue * 100;\r\n\r\n        // Force update bar height\r\n        const barHeight = (percentage / 100) * this.maxBarHeight;\r\n        pair.ativos.bar.style.height = `${barHeight}px`;\r\n\r\n        // Force update percentage display\r\n        const formattedPercentage = `${percentage.toFixed(1)}%`;\r\n        pair.ativos.percentage.textContent = formattedPercentage;\r\n\r\n        // Update patrimonio percentage display if exists\r\n        if (pair.patrimonio.percentage) {\r\n          pair.patrimonio.percentage.textContent = formattedPercentage;\r\n        }\r\n\r\n        // console.log(`🔧 Forced sync: ${pair.category} - ${pair.product} = ${formattedPercentage}`);\r\n      }\r\n    });\r\n\r\n    // console.log('✅ All visual elements force synced from sliders');\r\n  }\r\n\r\n  // Public method to update specific pair\r\n  updatePair(category, product, percentage) {\r\n    const pair = this.pairs.find((p) => p.category === category && p.product === product);\r\n\r\n    if (pair) {\r\n      // Update slider value\r\n      pair.patrimonio.slider.value = percentage / 100;\r\n\r\n      // Trigger input event to update related systems\r\n      pair.patrimonio.slider.dispatchEvent(new Event('input', { bubbles: true }));\r\n\r\n      // Sync visuals\r\n      this.syncFromPatrimonio(pair);\r\n\r\n      return true;\r\n    }\r\n\r\n    console.warn(`⚠️ Pair not found: ${category} - ${product}`);\r\n    return false;\r\n  }\r\n\r\n  // Update pair by percentage value\r\n  updatePairByValue(category, product, value, totalValue) {\r\n    if (totalValue > 0) {\r\n      const percentage = (value / totalValue) * 100;\r\n      return this.updatePair(category, product, percentage);\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // Reset all pairs to 0%\r\n  resetAll() {\r\n    this.pairs.forEach((pair) => {\r\n      pair.patrimonio.slider.value = 0;\r\n      this.syncFromPatrimonio(pair);\r\n    });\r\n    // console.log('🔄 All pairs reset');\r\n  }\r\n\r\n  // Get current allocations\r\n  getAllocations() {\r\n    return this.pairs.map((pair) => ({\r\n      category: pair.category,\r\n      product: pair.product,\r\n      percentage: parseFloat(pair.patrimonio.slider.value) * 100,\r\n      value: parseFloat(pair.patrimonio.input.value) || 0,\r\n    }));\r\n  }\r\n\r\n  // Get total allocated percentage\r\n  getTotalAllocatedPercentage() {\r\n    return this.pairs.reduce((total, pair) => {\r\n      return total + (parseFloat(pair.patrimonio.slider.value) * 100 || 0);\r\n    }, 0);\r\n  }\r\n\r\n  // Debug method to check bar dimensions\r\n  debugBarDimensions() {\r\n    this.pairs.forEach((pair) => {\r\n      // const barRect = pair.ativos.bar.getBoundingClientRect();\r\n      // const computedStyle = window.getComputedStyle(pair.ativos.bar);\r\n      // console.log(`🔍 ${pair.category}-${pair.product}:`);\r\n      // console.log(`  Computed width: ${computedStyle.width}`);\r\n      // console.log(`  Computed height: ${computedStyle.height}`);\r\n      // console.log(`  Bounding rect: ${barRect.width}x${barRect.height}`);\r\n      // console.log(`  Inline styles:`, pair.ativos.bar.style.cssText);\r\n    });\r\n  }\r\n\r\n  // Debug method to check budget integration\r\n  debugBudgetIntegration() {\r\n    // console.log('🔍 SimpleSyncSystem Budget Integration Status:');\r\n    // console.log(\r\n    //   `  PatrimonySyncSystem reference: ${this.patrimonySyncSystem ? '✅ Available' : '❌ Not available'}`\r\n    // );\r\n    if (this.patrimonySyncSystem) {\r\n      // console.log(`  Remaining budget: ${this.patrimonySyncSystem.getRemainingValue()}`);\r\n      // console.log(`  Main value: ${this.patrimonySyncSystem.getMainValue()}`);\r\n    }\r\n    // console.log(`  Budget status: ${JSON.stringify(this.budgetStatus || 'Not available')}`);\r\n  }\r\n\r\n  // Debug method to validate all element pairings\r\n  debugPairings() {\r\n    console.log('🔍 SimpleSyncSystem Pairing Analysis:');\r\n    console.log(`📊 Total pairs found: ${this.pairs.length}`);\r\n\r\n    if (this.pairs.length === 0) {\r\n      console.warn('❌ No pairs found! Checking for pairing issues...');\r\n      this.debugUnpairedElements();\r\n      return;\r\n    }\r\n\r\n    this.pairs.forEach((pair, index) => {\r\n      console.log(`\\n🔗 Pair ${index + 1}:`);\r\n      console.log(`  Category: \"${pair.category}\"`);\r\n      console.log(`  Product: \"${pair.product}\"`);\r\n      console.log(`  Patrimonio element: ${pair.patrimonio.element ? '✅' : '❌'}`);\r\n      console.log(`  Ativos element: ${pair.ativos.element ? '✅' : '❌'}`);\r\n      console.log(`  Input: ${pair.patrimonio.input ? '✅' : '❌'}`);\r\n      console.log(`  Slider: ${pair.patrimonio.slider ? '✅' : '❌'}`);\r\n      console.log(`  Bar: ${pair.ativos.bar ? '✅' : '❌'}`);\r\n      console.log(`  Percentage: ${pair.ativos.percentage ? '✅' : '❌'}`);\r\n\r\n      // Check current values\r\n      if (pair.patrimonio.slider) {\r\n        const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;\r\n        const percentage = sliderValue * 100;\r\n        console.log(`  Current allocation: ${percentage.toFixed(1)}%`);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Debug method to find unpaired elements\r\n  debugUnpairedElements() {\r\n    console.log('\\n🔍 Analyzing Unpaired Elements:');\r\n\r\n    // Find all patrimonio elements\r\n    const patrimonioElements = document.querySelectorAll(\r\n      '.patrimonio_interactive_item[ativo-category][ativo-product]'\r\n    );\r\n\r\n    // Find all ativos elements\r\n    const ativosElements = document.querySelectorAll(\r\n      '.ativos-grafico-item[ativo-category][ativo-product]'\r\n    );\r\n\r\n    console.log(`\\n📋 Found Elements:`);\r\n    console.log(`  Patrimonio elements: ${patrimonioElements.length}`);\r\n    console.log(`  Ativos elements: ${ativosElements.length}`);\r\n\r\n    console.log(`\\n💼 PATRIMONIO ELEMENTS:`);\r\n    patrimonioElements.forEach((el, index) => {\r\n      const category = el.getAttribute('ativo-category');\r\n      const product = el.getAttribute('ativo-product');\r\n      const hasPair = this.pairs.some((p) => p.category === category && p.product === product);\r\n      console.log(\r\n        `  ${index + 1}. \"${category}\" + \"${product}\" ${hasPair ? '✅ PAIRED' : '❌ UNPAIRED'}`\r\n      );\r\n    });\r\n\r\n    console.log(`\\n📊 ATIVOS ELEMENTS:`);\r\n    ativosElements.forEach((el, index) => {\r\n      const category = el.getAttribute('ativo-category');\r\n      const product = el.getAttribute('ativo-product');\r\n      const hasPair = this.pairs.some((p) => p.category === category && p.product === product);\r\n      console.log(\r\n        `  ${index + 1}. \"${category}\" + \"${product}\" ${hasPair ? '✅ PAIRED' : '❌ UNPAIRED'}`\r\n      );\r\n    });\r\n\r\n    // Find mismatches\r\n    console.log(`\\n🔍 MISMATCH ANALYSIS:`);\r\n    const patrimonioAttribs = Array.from(patrimonioElements).map((el) => ({\r\n      category: el.getAttribute('ativo-category'),\r\n      product: el.getAttribute('ativo-product'),\r\n      type: 'patrimonio',\r\n    }));\r\n\r\n    const ativosAttribs = Array.from(ativosElements).map((el) => ({\r\n      category: el.getAttribute('ativo-category'),\r\n      product: el.getAttribute('ativo-product'),\r\n      type: 'ativos',\r\n    }));\r\n\r\n    // Find patrimonio elements without matching ativos\r\n    patrimonioAttribs.forEach((p) => {\r\n      const hasMatch = ativosAttribs.some(\r\n        (a) => a.category === p.category && a.product === p.product\r\n      );\r\n      if (!hasMatch) {\r\n        console.warn(`❌ PATRIMONIO ORPHAN: \"${p.category}\" + \"${p.product}\"`);\r\n\r\n        // Look for similar matches (case-insensitive)\r\n        const similarMatches = ativosAttribs.filter(\r\n          (a) =>\r\n            a.category.toLowerCase() === p.category.toLowerCase() ||\r\n            a.product.toLowerCase() === p.product.toLowerCase()\r\n        );\r\n\r\n        if (similarMatches.length > 0) {\r\n          console.log(`   🔍 Possible matches:`);\r\n          similarMatches.forEach((match) => {\r\n            console.log(`     - \"${match.category}\" + \"${match.product}\"`);\r\n          });\r\n        }\r\n      }\r\n    });\r\n\r\n    // Find ativos elements without matching patrimonio\r\n    ativosAttribs.forEach((a) => {\r\n      const hasMatch = patrimonioAttribs.some(\r\n        (p) => p.category === a.category && p.product === a.product\r\n      );\r\n      if (!hasMatch) {\r\n        console.warn(`❌ ATIVOS ORPHAN: \"${a.category}\" + \"${a.product}\"`);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Debug method to test synchronization\r\n  debugSyncTest() {\r\n    console.log('🧪 Testing Synchronization for All Pairs:');\r\n\r\n    if (this.pairs.length === 0) {\r\n      console.warn('❌ No pairs to test!');\r\n      return;\r\n    }\r\n\r\n    this.pairs.forEach((pair, index) => {\r\n      console.log(`\\n🧪 Testing Pair ${index + 1}: ${pair.category} - ${pair.product}`);\r\n\r\n      // Test different percentage values\r\n      const testValues = [0, 0.25, 0.5, 0.75, 1.0];\r\n\r\n      testValues.forEach((testValue) => {\r\n        const percentage = testValue * 100;\r\n\r\n        // Set slider value\r\n        if (pair.patrimonio.slider) {\r\n          pair.patrimonio.slider.value = testValue;\r\n\r\n          // Trigger sync\r\n          this.syncFromPatrimonio(pair);\r\n\r\n          // Check results\r\n          const barHeight = pair.ativos.bar ? pair.ativos.bar.style.height : 'N/A';\r\n          const displayedPercentage = pair.ativos.percentage\r\n            ? pair.ativos.percentage.textContent\r\n            : 'N/A';\r\n\r\n          console.log(`  ${percentage}% → Bar: ${barHeight}, Display: ${displayedPercentage}`);\r\n        }\r\n      });\r\n\r\n      // Reset to 0\r\n      if (pair.patrimonio.slider) {\r\n        pair.patrimonio.slider.value = 0;\r\n        this.syncFromPatrimonio(pair);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Debug method to verify cache restoration\r\n  debugCacheSync() {\r\n    console.log('🔍 CACHE SYNCHRONIZATION ANALYSIS');\r\n    console.log('='.repeat(50));\r\n\r\n    if (this.pairs.length === 0) {\r\n      console.warn('❌ No pairs available for cache analysis!');\r\n      return;\r\n    }\r\n\r\n    console.log(`📊 Analyzing ${this.pairs.length} pairs for cache sync status:`);\r\n    console.log('\\nPair | Input Value | Slider % | Bar Height | Display %');\r\n    console.log('-'.repeat(65));\r\n\r\n    this.pairs.forEach((pair, index) => {\r\n      const inputValue = pair.patrimonio.input?.value || '0';\r\n      const sliderValue = parseFloat(pair.patrimonio.slider?.value || 0) * 100;\r\n      const barHeight = pair.ativos.bar?.style.height || '0px';\r\n      const displayText = pair.ativos.percentage?.textContent || '0%';\r\n\r\n      console.log(\r\n        `${(index + 1).toString().padStart(4)} | ${inputValue.padEnd(11)} | ${sliderValue.toFixed(1).padStart(8)}% | ${barHeight.padStart(10)} | ${displayText.padStart(9)}`\r\n      );\r\n    });\r\n\r\n    // Check for sync issues\r\n    console.log('\\n🔍 SYNC ISSUES DETECTED:');\r\n    let issuesFound = 0;\r\n\r\n    this.pairs.forEach((pair, index) => {\r\n      const sliderValue = parseFloat(pair.patrimonio.slider?.value || 0) * 100;\r\n      const displayText = pair.ativos.percentage?.textContent || '0%';\r\n      const displayValue = parseFloat(displayText.replace('%', '')) || 0;\r\n\r\n      const barHeight = pair.ativos.bar?.style.height || '0px';\r\n      const barValue = parseFloat(barHeight.replace('px', '')) || 0;\r\n      const expectedBarHeight = (sliderValue / 100) * this.maxBarHeight;\r\n\r\n      // Check for mismatches\r\n      if (Math.abs(sliderValue - displayValue) > 0.1) {\r\n        console.warn(\r\n          `❌ Pair ${index + 1}: Slider (${sliderValue.toFixed(1)}%) ≠ Display (${displayValue.toFixed(1)}%)`\r\n        );\r\n        issuesFound++;\r\n      }\r\n\r\n      if (Math.abs(barValue - expectedBarHeight) > 1) {\r\n        console.warn(\r\n          `❌ Pair ${index + 1}: Bar height (${barValue}px) ≠ Expected (${expectedBarHeight.toFixed(1)}px)`\r\n        );\r\n        issuesFound++;\r\n      }\r\n    });\r\n\r\n    if (issuesFound === 0) {\r\n      console.log('✅ All pairs are properly synchronized!');\r\n    } else {\r\n      console.warn(`⚠️ Found ${issuesFound} synchronization issues`);\r\n      console.log('\\n🔧 Try running: ReinoCalculator.data.sync.syncAllFromCachedValues()');\r\n    }\r\n  }\r\n\r\n  // Debug method to check all system status\r\n  debugFullStatus() {\r\n    console.log('🔍 COMPLETE SimpleSyncSystem STATUS REPORT');\r\n    console.log('='.repeat(50));\r\n\r\n    // Basic info\r\n    console.log(`📊 Initialization: ${this.isInitialized ? '✅ Complete' : '❌ Failed'}`);\r\n    console.log(`📊 Pairs found: ${this.pairs.length}`);\r\n    console.log(`📊 Max bar height: ${this.maxBarHeight}px`);\r\n\r\n    // Budget integration\r\n    console.log('\\n💰 BUDGET INTEGRATION:');\r\n    this.debugBudgetIntegration();\r\n\r\n    // Pairing analysis\r\n    console.log('\\n🔗 PAIRING ANALYSIS:');\r\n    this.debugPairings();\r\n\r\n    // Element analysis\r\n    if (this.pairs.length === 0) {\r\n      this.debugUnpairedElements();\r\n    }\r\n\r\n    // Current allocations\r\n    console.log('\\n📈 CURRENT ALLOCATIONS:');\r\n    const allocations = this.getAllocations();\r\n    const totalPercentage = this.getTotalAllocatedPercentage();\r\n\r\n    console.log(`📊 Total allocated: ${totalPercentage.toFixed(1)}%`);\r\n    allocations.forEach((alloc) => {\r\n      console.log(\r\n        `  • ${alloc.category} - ${alloc.product}: ${alloc.percentage.toFixed(1)}% (R$ ${alloc.value.toFixed(2)})`\r\n      );\r\n    });\r\n\r\n    console.log('\\n='.repeat(50));\r\n    console.log('🎯 Use these methods for specific debugging:');\r\n    console.log('  • window.ReinoCalculator.data.sync.debugPairings()');\r\n    console.log('  • window.ReinoCalculator.data.sync.debugUnpairedElements()');\r\n    console.log('  • window.ReinoCalculator.data.sync.debugSyncTest()');\r\n    console.log('  • window.ReinoCalculator.data.sync.debugBudgetIntegration()');\r\n    console.log('  • window.ReinoCalculator.data.sync.debugCacheSync() ← Cache Analysis');\r\n    console.log(\r\n      '  • window.ReinoCalculator.data.sync.debugZeroingIssues() ← Detect zeroing problems'\r\n    );\r\n  }\r\n\r\n  // Debug method to detect visual element zeroing issues\r\n  debugZeroingIssues() {\r\n    console.log('🔍 VISUAL ELEMENT ZEROING DETECTION');\r\n    console.log('='.repeat(50));\r\n\r\n    if (this.pairs.length === 0) {\r\n      console.warn('❌ No pairs available for zeroing analysis!');\r\n      return;\r\n    }\r\n\r\n    console.log('Checking for elements that should have values but show 0%...\\n');\r\n\r\n    let zeroingIssues = 0;\r\n\r\n    this.pairs.forEach((pair, index) => {\r\n      const sliderValue = parseFloat(pair.patrimonio.slider?.value || 0) * 100;\r\n      const inputValue = pair.patrimonio.input?.value || '0';\r\n      const barHeight = pair.ativos.bar?.style.height || '0px';\r\n      const displayText = pair.ativos.percentage?.textContent || '0%';\r\n\r\n      const barHeightNum = parseFloat(barHeight.replace('px', '')) || 0;\r\n      const displayPercent = parseFloat(displayText.replace('%', '')) || 0;\r\n\r\n      // Check if input has value but visuals are zero\r\n      const hasInputValue = inputValue !== '0' && inputValue !== '0,00' && inputValue !== '';\r\n      const hasSliderValue = sliderValue > 0;\r\n      const hasVisualValue = barHeightNum > 0 && displayPercent > 0;\r\n\r\n      if ((hasInputValue || hasSliderValue) && !hasVisualValue) {\r\n        console.warn(`❌ ZEROING DETECTED - ${pair.category} - ${pair.product}:`);\r\n        console.warn(`   Input: ${inputValue} | Slider: ${sliderValue.toFixed(1)}%`);\r\n        console.warn(`   Bar: ${barHeight} | Display: ${displayText}`);\r\n        console.warn(\r\n          `   Expected bar height: ${((sliderValue / 100) * this.maxBarHeight).toFixed(1)}px`\r\n        );\r\n        zeroingIssues++;\r\n      } else if (hasVisualValue) {\r\n        console.log(`✅ ${pair.category} - ${pair.product}: ${displayText} (${barHeight})`);\r\n      }\r\n    });\r\n\r\n    console.log(`\\n📊 SUMMARY:`);\r\n    console.log(`Total pairs: ${this.pairs.length}`);\r\n    console.log(`Zeroing issues: ${zeroingIssues}`);\r\n\r\n    if (zeroingIssues > 0) {\r\n      console.warn(`⚠️ Found ${zeroingIssues} elements with zeroing issues!`);\r\n      console.log('\\n🔧 Suggested fixes:');\r\n      console.log('1. Run: ReinoCalculator.data.sync.syncAllFromCachedValues()');\r\n      console.log('2. Check budget constraints affecting visual updates');\r\n      console.log('3. Verify slider and input values are properly synchronized');\r\n    } else {\r\n      console.log('✅ No zeroing issues detected!');\r\n    }\r\n  }\r\n\r\n  // Cleanup method\r\n  cleanup() {\r\n    this.pairs = [];\r\n    this.isInitialized = false;\r\n    console.log('🧹 Simple sync cleaned up');\r\n  }\r\n\r\n  // Get system status\r\n  getStatus() {\r\n    return {\r\n      initialized: this.isInitialized,\r\n      pairs: this.pairs.length,\r\n      totalAllocated: this.getTotalAllocatedPercentage().toFixed(1) + '%',\r\n      patrimonySyncConnected: !!this.patrimonySyncSystem,\r\n      budgetRespecting: true, // Now respects budget constraints\r\n    };\r\n  }\r\n}\r\n\r\n// Export for use in main app\r\nexport default SimpleSyncSystem;\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACOtF,MAAM,mBAAN,MAAuB;AAAA,IAP9B,OAO8B;AAAA;AAAA;AAAA,IAC5B,cAAc;AACZ,WAAK,QAAQ,CAAC;AACd,WAAK,eAAe;AACpB,WAAK,gBAAgB;AACrB,WAAK,sBAAsB;AAAA,IAC7B;AAAA,IAEA,MAAM,OAAO;AACX,UAAI;AAEF,aAAK,4BAA4B;AAGjC,aAAK,UAAU;AAGf,aAAK,eAAe;AAGpB,aAAK,qBAAqB;AAG1B,iBAAS,iBAAiB,wBAAwB,MAAM;AACtD,eAAK,wBAAwB;AAAA,QAC/B,CAAC;AAGD,mBAAW,MAAM;AACf,eAAK,aAAa;AAAA,QAEpB,GAAG,GAAG;AAEN,aAAK,gBAAgB;AAAA,MAEvB,SAAS,OAAO;AACd,gBAAQ,MAAM,6CAAwC,KAAK;AAAA,MAC7D;AAAA,IACF;AAAA,IAEA,8BAA8B;AAE5B,UAAI,OAAO,iBAAiB,MAAM,WAAW;AAC3C,aAAK,sBAAsB,OAAO,gBAAgB,KAAK;AAAA,MACzD,OAAO;AAEL,iBAAS,iBAAiB,wBAAwB,CAAC,UAAU;AAC3D,eAAK,sBAAsB,MAAM,OAAO,QAAQ;AAAA,QAClD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,uBAAuB;AAErB,eAAS,iBAAiB,2BAA2B,CAAC,UAAU;AAC9D,aAAK,yBAAyB,MAAM,MAAM;AAAA,MAC5C,CAAC;AAGD,eAAS,iBAAiB,qBAAqB,CAAC,UAAU;AACxD,aAAK,uBAAuB,MAAM,MAAM;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,IAEA,yBAAyB,QAAQ;AAG/B,WAAK,eAAe;AAAA,IACtB;AAAA,IAEA,uBAAuB,QAAQ;AAE7B,YAAM,EAAE,UAAU,SAAS,WAAW,IAAI;AAE1C,UAAI,CAAC,YAAY,CAAC,QAAS;AAG3B,YAAM,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,aAAa,YAAY,EAAE,YAAY,OAAO;AAEpF,UAAI,MAAM;AAER,YAAI,KAAK,WAAW,QAAQ;AAC1B,eAAK,WAAW,OAAO,QAAQ,aAAa;AAAA,QAC9C;AAGA,aAAK,mBAAmB,IAAI;AAAA,MAG9B;AAAA,IACF;AAAA,IAEA,YAAY;AACV,YAAM,kBAAkB,SAAS;AAAA,QAC/B;AAAA,MACF;AAEA,sBAAgB,QAAQ,CAAC,iBAAiB;AACxC,cAAM,WAAW,aAAa,aAAa,gBAAgB;AAC3D,cAAM,UAAU,aAAa,aAAa,eAAe;AAEzD,YAAI,CAAC,YAAY,CAAC,QAAS;AAG3B,cAAM,WAAW,SAAS;AAAA,UACxB,wCAAwC,QAAQ,qBAAqB,OAAO;AAAA,QAC9E;AAEA,YAAI,UAAU;AACZ,gBAAM,OAAO;AAAA,YACX,YAAY;AAAA,cACV,SAAS;AAAA,cACT,OAAO,aAAa,cAAc,4BAA4B;AAAA,cAC9D,QAAQ,aAAa,cAAc,cAAc;AAAA,cACjD,YAAY,aAAa,cAAc,0BAA0B;AAAA,YACnE;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,KAAK,SAAS,cAAc,yBAAyB;AAAA,cACrD,YAAY,SAAS,cAAc,6BAA6B;AAAA,YAClE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAGA,cACE,KAAK,WAAW,SAChB,KAAK,WAAW,UAChB,KAAK,OAAO,OACZ,KAAK,OAAO,YACZ;AAEA,iBAAK,OAAO,IAAI,MAAM,QAAQ;AAE9B,iBAAK,MAAM,KAAK,IAAI;AAAA,UAEtB,OAAO;AACL,oBAAQ,KAAK,2CAAiC,QAAQ,MAAM,OAAO,EAAE;AAAA,UACvE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,iBAAiB;AACf,WAAK,MAAM,QAAQ,CAAC,SAAS;AAE3B,aAAK,WAAW,OAAO,iBAAiB,SAAS,MAAM;AACrD,eAAK,mBAAmB,IAAI;AAAA,QAC9B,CAAC;AAGD,aAAK,WAAW,QAAQ,iBAAiB,SAAS,MAAM;AACtD,eAAK,mBAAmB,IAAI;AAAA,QAC9B,CAAC;AAGD,aAAK,WAAW,QAAQ,iBAAiB,UAAU,MAAM;AACvD,eAAK,mBAAmB,IAAI;AAAA,QAC9B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,IAEA,mBAAmB,MAAM;AACvB,UAAI;AAEF,cAAM,cAAc,WAAW,KAAK,WAAW,OAAO,KAAK,KAAK;AAChE,cAAM,aAAa,cAAc;AAIjC,cAAM,YAAa,aAAa,MAAO,KAAK;AAG5C,aAAK,OAAO,IAAI,MAAM,SAAS,GAAG,SAAS;AAG3C,aAAK,OAAO,IAAI,MAAM,QAAQ;AAG9B,cAAM,sBAAsB,GAAG,WAAW,QAAQ,CAAC,CAAC;AAEpD,aAAK,OAAO,WAAW,cAAc;AAGrC,YAAI,KAAK,WAAW,YAAY;AAC9B,eAAK,WAAW,WAAW,cAAc;AAAA,QAC3C;AAAA,MAIF,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAoB,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,KAAK;AAAA,MAC3E;AAAA,IACF;AAAA,IAEA,wBAAwB,MAAM;AAG5B,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,eAAe;AACb,WAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,aAAK,mBAAmB,IAAI;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,0BAA0B;AAGxB,WAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,YAAI,KAAK,WAAW,UAAU,KAAK,WAAW,OAAO;AAEnD,gBAAM,cAAc,WAAW,KAAK,WAAW,OAAO,KAAK,KAAK;AAGhE,eAAK,mBAAmB,IAAI;AAAA,QAG9B;AAAA,MACF,CAAC;AAAA,IAGH;AAAA;AAAA,IAGA,uBAAuB;AAGrB,WAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,YAAI,KAAK,WAAW,QAAQ;AAC1B,gBAAM,cAAc,WAAW,KAAK,WAAW,OAAO,KAAK,KAAK;AAChE,gBAAM,aAAa,cAAc;AAGjC,gBAAM,YAAa,aAAa,MAAO,KAAK;AAC5C,eAAK,OAAO,IAAI,MAAM,SAAS,GAAG,SAAS;AAG3C,gBAAM,sBAAsB,GAAG,WAAW,QAAQ,CAAC,CAAC;AACpD,eAAK,OAAO,WAAW,cAAc;AAGrC,cAAI,KAAK,WAAW,YAAY;AAC9B,iBAAK,WAAW,WAAW,cAAc;AAAA,UAC3C;AAAA,QAGF;AAAA,MACF,CAAC;AAAA,IAGH;AAAA;AAAA,IAGA,WAAW,UAAU,SAAS,YAAY;AACxC,YAAM,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,aAAa,YAAY,EAAE,YAAY,OAAO;AAEpF,UAAI,MAAM;AAER,aAAK,WAAW,OAAO,QAAQ,aAAa;AAG5C,aAAK,WAAW,OAAO,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC,CAAC;AAG1E,aAAK,mBAAmB,IAAI;AAE5B,eAAO;AAAA,MACT;AAEA,cAAQ,KAAK,gCAAsB,QAAQ,MAAM,OAAO,EAAE;AAC1D,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,kBAAkB,UAAU,SAAS,OAAO,YAAY;AACtD,UAAI,aAAa,GAAG;AAClB,cAAM,aAAc,QAAQ,aAAc;AAC1C,eAAO,KAAK,WAAW,UAAU,SAAS,UAAU;AAAA,MACtD;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,WAAW;AACT,WAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,aAAK,WAAW,OAAO,QAAQ;AAC/B,aAAK,mBAAmB,IAAI;AAAA,MAC9B,CAAC;AAAA,IAEH;AAAA;AAAA,IAGA,iBAAiB;AACf,aAAO,KAAK,MAAM,IAAI,CAAC,UAAU;AAAA,QAC/B,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,YAAY,WAAW,KAAK,WAAW,OAAO,KAAK,IAAI;AAAA,QACvD,OAAO,WAAW,KAAK,WAAW,MAAM,KAAK,KAAK;AAAA,MACpD,EAAE;AAAA,IACJ;AAAA;AAAA,IAGA,8BAA8B;AAC5B,aAAO,KAAK,MAAM,OAAO,CAAC,OAAO,SAAS;AACxC,eAAO,SAAS,WAAW,KAAK,WAAW,OAAO,KAAK,IAAI,OAAO;AAAA,MACpE,GAAG,CAAC;AAAA,IACN;AAAA;AAAA,IAGA,qBAAqB;AACnB,WAAK,MAAM,QAAQ,CAAC,SAAS;AAAA,MAQ7B,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,yBAAyB;AAKvB,UAAI,KAAK,qBAAqB;AAAA,MAG9B;AAAA,IAEF;AAAA;AAAA,IAGA,gBAAgB;AACd,cAAQ,IAAI,8CAAuC;AACnD,cAAQ,IAAI,gCAAyB,KAAK,MAAM,MAAM,EAAE;AAExD,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,gBAAQ,KAAK,uDAAkD;AAC/D,aAAK,sBAAsB;AAC3B;AAAA,MACF;AAEA,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,gBAAQ,IAAI;AAAA,iBAAa,QAAQ,CAAC,GAAG;AACrC,gBAAQ,IAAI,gBAAgB,KAAK,QAAQ,GAAG;AAC5C,gBAAQ,IAAI,eAAe,KAAK,OAAO,GAAG;AAC1C,gBAAQ,IAAI,yBAAyB,KAAK,WAAW,UAAU,WAAM,QAAG,EAAE;AAC1E,gBAAQ,IAAI,qBAAqB,KAAK,OAAO,UAAU,WAAM,QAAG,EAAE;AAClE,gBAAQ,IAAI,YAAY,KAAK,WAAW,QAAQ,WAAM,QAAG,EAAE;AAC3D,gBAAQ,IAAI,aAAa,KAAK,WAAW,SAAS,WAAM,QAAG,EAAE;AAC7D,gBAAQ,IAAI,UAAU,KAAK,OAAO,MAAM,WAAM,QAAG,EAAE;AACnD,gBAAQ,IAAI,iBAAiB,KAAK,OAAO,aAAa,WAAM,QAAG,EAAE;AAGjE,YAAI,KAAK,WAAW,QAAQ;AAC1B,gBAAM,cAAc,WAAW,KAAK,WAAW,OAAO,KAAK,KAAK;AAChE,gBAAM,aAAa,cAAc;AACjC,kBAAQ,IAAI,yBAAyB,WAAW,QAAQ,CAAC,CAAC,GAAG;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,wBAAwB;AACtB,cAAQ,IAAI,0CAAmC;AAG/C,YAAM,qBAAqB,SAAS;AAAA,QAClC;AAAA,MACF;AAGA,YAAM,iBAAiB,SAAS;AAAA,QAC9B;AAAA,MACF;AAEA,cAAQ,IAAI;AAAA,0BAAsB;AAClC,cAAQ,IAAI,0BAA0B,mBAAmB,MAAM,EAAE;AACjE,cAAQ,IAAI,sBAAsB,eAAe,MAAM,EAAE;AAEzD,cAAQ,IAAI;AAAA,+BAA2B;AACvC,yBAAmB,QAAQ,CAAC,IAAI,UAAU;AACxC,cAAM,WAAW,GAAG,aAAa,gBAAgB;AACjD,cAAM,UAAU,GAAG,aAAa,eAAe;AAC/C,cAAM,UAAU,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,aAAa,YAAY,EAAE,YAAY,OAAO;AACvF,gBAAQ;AAAA,UACN,KAAK,QAAQ,CAAC,MAAM,QAAQ,QAAQ,OAAO,KAAK,UAAU,kBAAa,iBAAY;AAAA,QACrF;AAAA,MACF,CAAC;AAED,cAAQ,IAAI;AAAA,2BAAuB;AACnC,qBAAe,QAAQ,CAAC,IAAI,UAAU;AACpC,cAAM,WAAW,GAAG,aAAa,gBAAgB;AACjD,cAAM,UAAU,GAAG,aAAa,eAAe;AAC/C,cAAM,UAAU,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,aAAa,YAAY,EAAE,YAAY,OAAO;AACvF,gBAAQ;AAAA,UACN,KAAK,QAAQ,CAAC,MAAM,QAAQ,QAAQ,OAAO,KAAK,UAAU,kBAAa,iBAAY;AAAA,QACrF;AAAA,MACF,CAAC;AAGD,cAAQ,IAAI;AAAA,6BAAyB;AACrC,YAAM,oBAAoB,MAAM,KAAK,kBAAkB,EAAE,IAAI,CAAC,QAAQ;AAAA,QACpE,UAAU,GAAG,aAAa,gBAAgB;AAAA,QAC1C,SAAS,GAAG,aAAa,eAAe;AAAA,QACxC,MAAM;AAAA,MACR,EAAE;AAEF,YAAM,gBAAgB,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,QAAQ;AAAA,QAC5D,UAAU,GAAG,aAAa,gBAAgB;AAAA,QAC1C,SAAS,GAAG,aAAa,eAAe;AAAA,QACxC,MAAM;AAAA,MACR,EAAE;AAGF,wBAAkB,QAAQ,CAAC,MAAM;AAC/B,cAAM,WAAW,cAAc;AAAA,UAC7B,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE;AAAA,QACtD;AACA,YAAI,CAAC,UAAU;AACb,kBAAQ,KAAK,8BAAyB,EAAE,QAAQ,QAAQ,EAAE,OAAO,GAAG;AAGpE,gBAAM,iBAAiB,cAAc;AAAA,YACnC,CAAC,MACC,EAAE,SAAS,YAAY,MAAM,EAAE,SAAS,YAAY,KACpD,EAAE,QAAQ,YAAY,MAAM,EAAE,QAAQ,YAAY;AAAA,UACtD;AAEA,cAAI,eAAe,SAAS,GAAG;AAC7B,oBAAQ,IAAI,gCAAyB;AACrC,2BAAe,QAAQ,CAAC,UAAU;AAChC,sBAAQ,IAAI,WAAW,MAAM,QAAQ,QAAQ,MAAM,OAAO,GAAG;AAAA,YAC/D,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAGD,oBAAc,QAAQ,CAAC,MAAM;AAC3B,cAAM,WAAW,kBAAkB;AAAA,UACjC,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE;AAAA,QACtD;AACA,YAAI,CAAC,UAAU;AACb,kBAAQ,KAAK,0BAAqB,EAAE,QAAQ,QAAQ,EAAE,OAAO,GAAG;AAAA,QAClE;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,gBAAgB;AACd,cAAQ,IAAI,kDAA2C;AAEvD,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,gBAAQ,KAAK,0BAAqB;AAClC;AAAA,MACF;AAEA,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,gBAAQ,IAAI;AAAA,yBAAqB,QAAQ,CAAC,KAAK,KAAK,QAAQ,MAAM,KAAK,OAAO,EAAE;AAGhF,cAAM,aAAa,CAAC,GAAG,MAAM,KAAK,MAAM,CAAG;AAE3C,mBAAW,QAAQ,CAAC,cAAc;AAChC,gBAAM,aAAa,YAAY;AAG/B,cAAI,KAAK,WAAW,QAAQ;AAC1B,iBAAK,WAAW,OAAO,QAAQ;AAG/B,iBAAK,mBAAmB,IAAI;AAG5B,kBAAM,YAAY,KAAK,OAAO,MAAM,KAAK,OAAO,IAAI,MAAM,SAAS;AACnE,kBAAM,sBAAsB,KAAK,OAAO,aACpC,KAAK,OAAO,WAAW,cACvB;AAEJ,oBAAQ,IAAI,KAAK,UAAU,iBAAY,SAAS,cAAc,mBAAmB,EAAE;AAAA,UACrF;AAAA,QACF,CAAC;AAGD,YAAI,KAAK,WAAW,QAAQ;AAC1B,eAAK,WAAW,OAAO,QAAQ;AAC/B,eAAK,mBAAmB,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,iBAAiB;AACf,cAAQ,IAAI,0CAAmC;AAC/C,cAAQ,IAAI,IAAI,OAAO,EAAE,CAAC;AAE1B,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,gBAAQ,KAAK,+CAA0C;AACvD;AAAA,MACF;AAEA,cAAQ,IAAI,uBAAgB,KAAK,MAAM,MAAM,+BAA+B;AAC5E,cAAQ,IAAI,0DAA0D;AACtE,cAAQ,IAAI,IAAI,OAAO,EAAE,CAAC;AAE1B,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,aAAa,KAAK,WAAW,OAAO,SAAS;AACnD,cAAM,cAAc,WAAW,KAAK,WAAW,QAAQ,SAAS,CAAC,IAAI;AACrE,cAAM,YAAY,KAAK,OAAO,KAAK,MAAM,UAAU;AACnD,cAAM,cAAc,KAAK,OAAO,YAAY,eAAe;AAE3D,gBAAQ;AAAA,UACN,IAAI,QAAQ,GAAG,SAAS,EAAE,SAAS,CAAC,CAAC,MAAM,WAAW,OAAO,EAAE,CAAC,MAAM,YAAY,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,OAAO,UAAU,SAAS,EAAE,CAAC,MAAM,YAAY,SAAS,CAAC,CAAC;AAAA,QACpK;AAAA,MACF,CAAC;AAGD,cAAQ,IAAI,mCAA4B;AACxC,UAAI,cAAc;AAElB,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,cAAc,WAAW,KAAK,WAAW,QAAQ,SAAS,CAAC,IAAI;AACrE,cAAM,cAAc,KAAK,OAAO,YAAY,eAAe;AAC3D,cAAM,eAAe,WAAW,YAAY,QAAQ,KAAK,EAAE,CAAC,KAAK;AAEjE,cAAM,YAAY,KAAK,OAAO,KAAK,MAAM,UAAU;AACnD,cAAM,WAAW,WAAW,UAAU,QAAQ,MAAM,EAAE,CAAC,KAAK;AAC5D,cAAM,oBAAqB,cAAc,MAAO,KAAK;AAGrD,YAAI,KAAK,IAAI,cAAc,YAAY,IAAI,KAAK;AAC9C,kBAAQ;AAAA,YACN,eAAU,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC,CAAC,sBAAiB,aAAa,QAAQ,CAAC,CAAC;AAAA,UAChG;AACA;AAAA,QACF;AAEA,YAAI,KAAK,IAAI,WAAW,iBAAiB,IAAI,GAAG;AAC9C,kBAAQ;AAAA,YACN,eAAU,QAAQ,CAAC,iBAAiB,QAAQ,wBAAmB,kBAAkB,QAAQ,CAAC,CAAC;AAAA,UAC7F;AACA;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,gBAAgB,GAAG;AACrB,gBAAQ,IAAI,6CAAwC;AAAA,MACtD,OAAO;AACL,gBAAQ,KAAK,sBAAY,WAAW,yBAAyB;AAC7D,gBAAQ,IAAI,8EAAuE;AAAA,MACrF;AAAA,IACF;AAAA;AAAA,IAGA,kBAAkB;AAChB,cAAQ,IAAI,mDAA4C;AACxD,cAAQ,IAAI,IAAI,OAAO,EAAE,CAAC;AAG1B,cAAQ,IAAI,6BAAsB,KAAK,gBAAgB,oBAAe,eAAU,EAAE;AAClF,cAAQ,IAAI,0BAAmB,KAAK,MAAM,MAAM,EAAE;AAClD,cAAQ,IAAI,6BAAsB,KAAK,YAAY,IAAI;AAGvD,cAAQ,IAAI,iCAA0B;AACtC,WAAK,uBAAuB;AAG5B,cAAQ,IAAI,+BAAwB;AACpC,WAAK,cAAc;AAGnB,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,aAAK,sBAAsB;AAAA,MAC7B;AAGA,cAAQ,IAAI,kCAA2B;AACvC,YAAM,cAAc,KAAK,eAAe;AACxC,YAAM,kBAAkB,KAAK,4BAA4B;AAEzD,cAAQ,IAAI,8BAAuB,gBAAgB,QAAQ,CAAC,CAAC,GAAG;AAChE,kBAAY,QAAQ,CAAC,UAAU;AAC7B,gBAAQ;AAAA,UACN,YAAO,MAAM,QAAQ,MAAM,MAAM,OAAO,KAAK,MAAM,WAAW,QAAQ,CAAC,CAAC,SAAS,MAAM,MAAM,QAAQ,CAAC,CAAC;AAAA,QACzG;AAAA,MACF,CAAC;AAED,cAAQ,IAAI,MAAM,OAAO,EAAE,CAAC;AAC5B,cAAQ,IAAI,qDAA8C;AAC1D,cAAQ,IAAI,2DAAsD;AAClE,cAAQ,IAAI,mEAA8D;AAC1E,cAAQ,IAAI,2DAAsD;AAClE,cAAQ,IAAI,oEAA+D;AAC3E,cAAQ,IAAI,kFAAwE;AACpF,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,qBAAqB;AACnB,cAAQ,IAAI,4CAAqC;AACjD,cAAQ,IAAI,IAAI,OAAO,EAAE,CAAC;AAE1B,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,gBAAQ,KAAK,iDAA4C;AACzD;AAAA,MACF;AAEA,cAAQ,IAAI,gEAAgE;AAE5E,UAAI,gBAAgB;AAEpB,WAAK,MAAM,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,cAAc,WAAW,KAAK,WAAW,QAAQ,SAAS,CAAC,IAAI;AACrE,cAAM,aAAa,KAAK,WAAW,OAAO,SAAS;AACnD,cAAM,YAAY,KAAK,OAAO,KAAK,MAAM,UAAU;AACnD,cAAM,cAAc,KAAK,OAAO,YAAY,eAAe;AAE3D,cAAM,eAAe,WAAW,UAAU,QAAQ,MAAM,EAAE,CAAC,KAAK;AAChE,cAAM,iBAAiB,WAAW,YAAY,QAAQ,KAAK,EAAE,CAAC,KAAK;AAGnE,cAAM,gBAAgB,eAAe,OAAO,eAAe,UAAU,eAAe;AACpF,cAAM,iBAAiB,cAAc;AACrC,cAAM,iBAAiB,eAAe,KAAK,iBAAiB;AAE5D,aAAK,iBAAiB,mBAAmB,CAAC,gBAAgB;AACxD,kBAAQ,KAAK,6BAAwB,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG;AACvE,kBAAQ,KAAK,aAAa,UAAU,cAAc,YAAY,QAAQ,CAAC,CAAC,GAAG;AAC3E,kBAAQ,KAAK,WAAW,SAAS,eAAe,WAAW,EAAE;AAC7D,kBAAQ;AAAA,YACN,4BAA6B,cAAc,MAAO,KAAK,cAAc,QAAQ,CAAC,CAAC;AAAA,UACjF;AACA;AAAA,QACF,WAAW,gBAAgB;AACzB,kBAAQ,IAAI,UAAK,KAAK,QAAQ,MAAM,KAAK,OAAO,KAAK,WAAW,KAAK,SAAS,GAAG;AAAA,QACnF;AAAA,MACF,CAAC;AAED,cAAQ,IAAI;AAAA,mBAAe;AAC3B,cAAQ,IAAI,gBAAgB,KAAK,MAAM,MAAM,EAAE;AAC/C,cAAQ,IAAI,mBAAmB,aAAa,EAAE;AAE9C,UAAI,gBAAgB,GAAG;AACrB,gBAAQ,KAAK,sBAAY,aAAa,gCAAgC;AACtE,gBAAQ,IAAI,8BAAuB;AACnC,gBAAQ,IAAI,6DAA6D;AACzE,gBAAQ,IAAI,sDAAsD;AAClE,gBAAQ,IAAI,6DAA6D;AAAA,MAC3E,OAAO;AACL,gBAAQ,IAAI,oCAA+B;AAAA,MAC7C;AAAA,IACF;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,QAAQ,CAAC;AACd,WAAK,gBAAgB;AACrB,cAAQ,IAAI,kCAA2B;AAAA,IACzC;AAAA;AAAA,IAGA,YAAY;AACV,aAAO;AAAA,QACL,aAAa,KAAK;AAAA,QAClB,OAAO,KAAK,MAAM;AAAA,QAClB,gBAAgB,KAAK,4BAA4B,EAAE,QAAQ,CAAC,IAAI;AAAA,QAChE,wBAAwB,CAAC,CAAC,KAAK;AAAA,QAC/B,kBAAkB;AAAA;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAGA,MAAO,sBAAQ;", "names": []}