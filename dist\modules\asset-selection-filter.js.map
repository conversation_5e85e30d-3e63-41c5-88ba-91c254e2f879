{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/asset-selection-filter.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Asset Selection Filter System\r\n * Manages asset selection in Section 2 and filters Section 3 accordingly\r\n */\r\nexport class AssetSelectionFilterSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.selectedAssets = new Set();\r\n    this.section2Assets = [];\r\n    this.section3Assets = [];\r\n\r\n    // Cache dos elementos\r\n    this.section2 = null;\r\n    this.section3 = null;\r\n    this.counterElement = null;\r\n\r\n    // Cache configuration\r\n    this.cacheKey = 'asset_selection_filter';\r\n  }\r\n\r\n  // Cache Manager - similar to patrimony-sync.js\r\n  get CacheManager() {\r\n    return {\r\n      set: (key, value) => {\r\n        try {\r\n          window.localStorage.setItem(key, JSON.stringify(value));\r\n        } catch {\r\n          // Silent fail\r\n        }\r\n      },\r\n      get: (key) => {\r\n        try {\r\n          const value = window.localStorage.getItem(key);\r\n          return value ? JSON.parse(value) : null;\r\n        } catch {\r\n          return null;\r\n        }\r\n      },\r\n      remove: (key) => {\r\n        try {\r\n          window.localStorage.removeItem(key);\r\n        } catch {\r\n          // Silent fail\r\n        }\r\n      },\r\n    };\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.initializeSystem();\r\n    });\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  initializeSystem() {\r\n    this.section2 = document.querySelector('._2-section-calc-ativos');\r\n    this.section3 = document.querySelector('._3-section-patrimonio-alocation');\r\n\r\n    if (!this.section2 || !this.section3) {\r\n      console.warn('Seções 2 ou 3 não encontradas');\r\n      return;\r\n    }\r\n\r\n    this.setupAssetSelection();\r\n    this.setupCounter();\r\n    this.setupClearButton();\r\n    this.initialFilterSetup();\r\n    this.setupSystemListeners();\r\n\r\n    // Load cached selections after setup\r\n    this.loadCachedSelections();\r\n\r\n    // Dispatch system ready event after initialization (with delay to ensure all systems are ready)\r\n    setTimeout(() => {\r\n      document.dispatchEvent(\r\n        new CustomEvent('assetSelectionSystemReady', {\r\n          detail: {\r\n            selectedCount: this.selectedAssets.size,\r\n            selectedAssets: Array.from(this.selectedAssets),\r\n            cacheLoaded: false,\r\n          },\r\n        })\r\n      );\r\n    }, 200);\r\n  }\r\n\r\n  setupAssetSelection() {\r\n    // Encontrar todos os assets selecionáveis na Section 2\r\n\r\n    // Assets em dropdowns\r\n    const dropdownAssets = this.section2.querySelectorAll('.ativo-item-subcategory');\r\n    dropdownAssets.forEach((asset) => this.makeAssetSelectable(asset, 'dropdown'));\r\n\r\n    // Assets individuais\r\n    const individualAssets = this.section2.querySelectorAll('.ativos_item:not(.dropdown)');\r\n    individualAssets.forEach((asset) => this.makeAssetSelectable(asset, 'individual'));\r\n  }\r\n\r\n  makeAssetSelectable(assetElement, type) {\r\n    const category = assetElement.getAttribute('ativo-category');\r\n    const product = assetElement.getAttribute('ativo-product');\r\n\r\n    if (!category || !product) {\r\n      return;\r\n    }\r\n\r\n    // Normalizar as strings para comparação consistente\r\n    const normalizedCategory = this.normalizeString(category);\r\n    const normalizedProduct = this.normalizeString(product);\r\n\r\n    // Criar checkbox\r\n    const checkboxContainer = document.createElement('div');\r\n    checkboxContainer.className = 'asset-checkbox-container';\r\n\r\n    const checkbox = document.createElement('input');\r\n    checkbox.type = 'checkbox';\r\n    checkbox.className = 'asset-checkbox';\r\n    checkbox.id = `asset-${normalizedCategory}-${normalizedProduct}`\r\n      .replace(/\\s+/g, '-')\r\n      .toLowerCase();\r\n\r\n    const label = document.createElement('label');\r\n    label.htmlFor = checkbox.id;\r\n    label.className = 'asset-checkbox-label';\r\n\r\n    checkboxContainer.appendChild(checkbox);\r\n    checkboxContainer.appendChild(label);\r\n\r\n    // Inserir checkbox no elemento\r\n    if (type === 'dropdown') {\r\n      // Para items de dropdown, adicionar no início\r\n      assetElement.insertBefore(checkboxContainer, assetElement.firstChild);\r\n    } else {\r\n      // Para items individuais, adicionar depois do ícone\r\n      const iconElement = assetElement.querySelector('.icon-dragabble');\r\n      if (iconElement) {\r\n        iconElement.parentNode.insertBefore(checkboxContainer, iconElement.nextSibling);\r\n      } else {\r\n        assetElement.insertBefore(checkboxContainer, assetElement.firstChild);\r\n      }\r\n    }\r\n\r\n    // Event listener para seleção\r\n    checkbox.addEventListener('change', (e) => {\r\n      this.handleAssetSelection(e.target.checked, category, product, assetElement);\r\n    });\r\n\r\n    // Tornar o elemento clicável\r\n    assetElement.addEventListener('click', (e) => {\r\n      // Se o clique não foi no checkbox, ativar/desativar o checkbox\r\n      if (!e.target.matches('.asset-checkbox, .asset-checkbox-label')) {\r\n        checkbox.checked = !checkbox.checked;\r\n        checkbox.dispatchEvent(new Event('change'));\r\n      }\r\n    });\r\n\r\n    // Armazenar referência com string normalizada\r\n    this.section2Assets.push({\r\n      element: assetElement,\r\n      checkbox: checkbox,\r\n      category: category,\r\n      product: product,\r\n      normalizedKey: `${normalizedCategory}|${normalizedProduct}`,\r\n      key: `${category}|${product}`, // manter key original para compatibilidade\r\n    });\r\n  }\r\n\r\n  // Método para normalizar strings para comparação consistente\r\n  normalizeString(str) {\r\n    return str.toLowerCase().trim();\r\n  }\r\n\r\n  handleAssetSelection(isSelected, category, product, assetElement) {\r\n    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;\r\n\r\n    if (isSelected) {\r\n      this.selectedAssets.add(normalizedKey);\r\n      assetElement.classList.add('selected-asset');\r\n      // Reset values to ensure clean start when selecting an asset\r\n      this.resetAssetValues(category, product);\r\n    } else {\r\n      this.selectedAssets.delete(normalizedKey);\r\n      assetElement.classList.remove('selected-asset');\r\n      // Reset values when deselecting to prevent them from affecting calculations\r\n      this.resetAssetValues(category, product);\r\n    }\r\n\r\n    this.updateCounter();\r\n    this.filterSection3();\r\n\r\n    // Save to cache\r\n    this.saveSelectionsToCache();\r\n\r\n    // Notificar mudança na seleção para o step navigation\r\n    document.dispatchEvent(\r\n      new CustomEvent('assetSelectionChanged', {\r\n        detail: {\r\n          selectedCount: this.selectedAssets.size,\r\n          selectedAssets: Array.from(this.selectedAssets),\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  setupSystemListeners() {\r\n    // Listen for patrimony sync reset events\r\n    document.addEventListener('patrimonySyncReset', () => {\r\n      // Clear UI selections when patrimony system resets\r\n      this.selectedAssets.clear();\r\n      this.section2Assets.forEach((asset) => {\r\n        asset.checkbox.checked = false;\r\n        asset.element.classList.remove('selected-asset');\r\n      });\r\n      this.updateCounter();\r\n      this.filterSection3();\r\n    });\r\n  }\r\n\r\n  setupCounter() {\r\n    this.counterElement = this.section2.querySelector('.counter_ativos');\r\n    if (this.counterElement) {\r\n      this.updateCounter();\r\n    }\r\n  }\r\n\r\n  updateCounter() {\r\n    if (this.counterElement) {\r\n      this.counterElement.textContent = `(${this.selectedAssets.size})`;\r\n    }\r\n  }\r\n\r\n  setupClearButton() {\r\n    const clearButton = this.section2.querySelector('.ativos_clean-button');\r\n    if (clearButton) {\r\n      clearButton.addEventListener('click', (e) => {\r\n        e.preventDefault();\r\n        this.clearAllSelections();\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset all input values for a specific asset to zero\r\n   * This ensures deselected assets don't affect calculations\r\n   */\r\n  resetAssetValues(category, product) {\r\n    try {\r\n      // Find the corresponding patrimonio_interactive_item in Section 3\r\n      const patrimonioItem = this.section3.querySelector(\r\n        `.patrimonio_interactive_item[ativo-category=\"${category}\"][ativo-product=\"${product}\"]`\r\n      );\r\n\r\n      if (patrimonioItem) {\r\n        // Reset currency input field\r\n        const input = patrimonioItem.querySelector('[input-settings=\"receive\"]');\r\n        if (input) {\r\n          // Set value to formatted zero\r\n          input.value = 'R$ 0,00';\r\n\r\n          // Trigger currencyChange event to update the patrimony sync system\r\n          input.dispatchEvent(\r\n            new CustomEvent('currencyChange', {\r\n              detail: { value: 0 },\r\n              bubbles: true,\r\n            })\r\n          );\r\n\r\n          // Also trigger input event as fallback\r\n          input.dispatchEvent(new Event('input', { bubbles: true }));\r\n        }\r\n\r\n        // Reset slider\r\n        const slider = patrimonioItem.querySelector('range-slider');\r\n        if (slider) {\r\n          slider.value = 0;\r\n          slider.dispatchEvent(new Event('input', { bubbles: true }));\r\n        }\r\n\r\n        // Reset any percentage displays immediately\r\n        const percentageDisplay = patrimonioItem.querySelector('.porcentagem-calculadora');\r\n        if (percentageDisplay) {\r\n          percentageDisplay.textContent = '0%';\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn(`Error resetting values for ${category} - ${product}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save current asset selections to cache\r\n   */\r\n  saveSelectionsToCache() {\r\n    try {\r\n      const selectionsData = {\r\n        selectedAssets: Array.from(this.selectedAssets),\r\n        timestamp: Date.now(),\r\n      };\r\n      this.CacheManager.set(this.cacheKey, selectionsData);\r\n    } catch (error) {\r\n      console.warn('Error saving asset selections to cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Load asset selections from cache and restore UI state\r\n   */\r\n  loadCachedSelections() {\r\n    try {\r\n      const cachedData = this.CacheManager.get(this.cacheKey);\r\n      if (!cachedData || !Array.isArray(cachedData.selectedAssets)) {\r\n        return;\r\n      }\r\n\r\n      // Restore selected assets Set\r\n      this.selectedAssets = new Set(cachedData.selectedAssets);\r\n\r\n      // Restore UI state for each cached selection\r\n      cachedData.selectedAssets.forEach((normalizedKey) => {\r\n        // Find the corresponding asset in section2Assets\r\n        const asset = this.section2Assets.find((a) => a.normalizedKey === normalizedKey);\r\n        if (asset) {\r\n          asset.checkbox.checked = true;\r\n          asset.element.classList.add('selected-asset');\r\n        }\r\n      });\r\n\r\n      // Update UI elements\r\n      this.updateCounter();\r\n      this.filterSection3();\r\n\r\n      // Dispatch event to notify other systems\r\n      document.dispatchEvent(\r\n        new CustomEvent('assetSelectionChanged', {\r\n          detail: {\r\n            selectedCount: this.selectedAssets.size,\r\n            selectedAssets: Array.from(this.selectedAssets),\r\n            fromCache: true,\r\n          },\r\n        })\r\n      );\r\n\r\n      // Dispatch system ready event after cache is loaded\r\n      setTimeout(() => {\r\n        document.dispatchEvent(\r\n          new CustomEvent('assetSelectionSystemReady', {\r\n            detail: {\r\n              selectedCount: this.selectedAssets.size,\r\n              selectedAssets: Array.from(this.selectedAssets),\r\n              cacheLoaded: true,\r\n            },\r\n          })\r\n        );\r\n      }, 100);\r\n\r\n      // console.log(`📦 Asset selections loaded from cache: ${this.selectedAssets.size} assets restored`);\r\n    } catch (error) {\r\n      console.warn('Error loading asset selections from cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear asset selections cache\r\n   */\r\n  clearSelectionsCache() {\r\n    try {\r\n      this.CacheManager.remove(this.cacheKey);\r\n    } catch (error) {\r\n      console.warn('Error clearing asset selections cache:', error);\r\n    }\r\n  }\r\n\r\n  clearAllSelections() {\r\n    this.selectedAssets.clear();\r\n\r\n    this.section2Assets.forEach((asset) => {\r\n      asset.checkbox.checked = false;\r\n      asset.element.classList.remove('selected-asset');\r\n      // Reset values for all assets when clearing selections\r\n      this.resetAssetValues(asset.category, asset.product);\r\n    });\r\n\r\n    this.updateCounter();\r\n    this.filterSection3();\r\n\r\n    // Clear cache\r\n    this.clearSelectionsCache();\r\n  }\r\n\r\n  initialFilterSetup() {\r\n    // Encontrar todos os assets na Section 3\r\n    const section3Assets = this.section3.querySelectorAll(\r\n      '.ativos-grafico-item, .patrimonio_interactive_item'\r\n    );\r\n\r\n    section3Assets.forEach((asset) => {\r\n      const category = asset.getAttribute('ativo-category');\r\n      const product = asset.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        this.section3Assets.push({\r\n          element: asset,\r\n          category: category,\r\n          product: product,\r\n          normalizedKey: `${this.normalizeString(category)}|${this.normalizeString(product)}`,\r\n          key: `${category}|${product}`, // manter key original para compatibilidade\r\n        });\r\n      }\r\n    });\r\n\r\n    // Inicialmente esconder todos os itens da Section 3\r\n    this.filterSection3();\r\n  }\r\n\r\n  filterSection3() {\r\n    this.section3Assets.forEach((asset) => {\r\n      // Usar a normalizedKey para comparação case-insensitive\r\n      const isSelected = this.selectedAssets.has(asset.normalizedKey);\r\n\r\n      if (isSelected) {\r\n        asset.element.style.display = '';\r\n        asset.element.classList.remove('asset-filtered-out');\r\n        asset.element.classList.add('asset-filtered-in');\r\n      } else {\r\n        asset.element.style.display = 'none';\r\n        asset.element.classList.add('asset-filtered-out');\r\n        asset.element.classList.remove('asset-filtered-in');\r\n      }\r\n    });\r\n\r\n    // Emitir evento para outros sistemas\r\n    document.dispatchEvent(\r\n      new CustomEvent('assetFilterChanged', {\r\n        detail: {\r\n          selectedAssets: Array.from(this.selectedAssets),\r\n          selectedCount: this.selectedAssets.size,\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  // Métodos públicos para integração\r\n  getSelectedAssets() {\r\n    return Array.from(this.selectedAssets);\r\n  }\r\n\r\n  isAssetSelected(category, product) {\r\n    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;\r\n    return this.selectedAssets.has(normalizedKey);\r\n  }\r\n\r\n  selectAsset(category, product) {\r\n    const asset = this.section2Assets.find((a) => a.category === category && a.product === product);\r\n    if (asset && !asset.checkbox.checked) {\r\n      asset.checkbox.checked = true;\r\n      asset.checkbox.dispatchEvent(new Event('change'));\r\n    }\r\n  }\r\n\r\n  deselectAsset(category, product) {\r\n    const asset = this.section2Assets.find((a) => a.category === category && a.product === product);\r\n    if (asset && asset.checkbox.checked) {\r\n      asset.checkbox.checked = false;\r\n      asset.checkbox.dispatchEvent(new Event('change'));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset the entire asset selection system and clear all caches\r\n   * This method can be called by other systems for complete reset\r\n   */\r\n  resetSystem() {\r\n    try {\r\n      // Clear all selections (this also clears the cache)\r\n      this.clearAllSelections();\r\n\r\n      // Dispatch event to notify other systems about the reset\r\n      document.dispatchEvent(\r\n        new CustomEvent('assetSelectionSystemReset', {\r\n          detail: {\r\n            timestamp: Date.now(),\r\n          },\r\n        })\r\n      );\r\n\r\n      // console.log('🔄 Asset Selection System reset completed');\r\n    } catch (error) {\r\n      console.warn('Error resetting asset selection system:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cache information for debugging\r\n   */\r\n  getCacheInfo() {\r\n    try {\r\n      const cachedData = this.CacheManager.get(this.cacheKey);\r\n      return {\r\n        hasCachedData: !!cachedData,\r\n        cachedSelections: cachedData?.selectedAssets?.length || 0,\r\n        timestamp: cachedData?.timestamp || null,\r\n        currentSelections: this.selectedAssets.size,\r\n      };\r\n    } catch (error) {\r\n      console.warn('Error getting cache info:', error);\r\n      return null;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACItF,MAAM,6BAAN,MAAiC;AAAA,IAJxC,OAIwC;AAAA;AAAA;AAAA,IACtC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,iBAAiB,oBAAI,IAAI;AAC9B,WAAK,iBAAiB,CAAC;AACvB,WAAK,iBAAiB,CAAC;AAGvB,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,WAAK,iBAAiB;AAGtB,WAAK,WAAW;AAAA,IAClB;AAAA;AAAA,IAGA,IAAI,eAAe;AACjB,aAAO;AAAA,QACL,KAAK,wBAAC,KAAK,UAAU;AACnB,cAAI;AACF,mBAAO,aAAa,QAAQ,KAAK,KAAK,UAAU,KAAK,CAAC;AAAA,UACxD,QAAQ;AAAA,UAER;AAAA,QACF,GANK;AAAA,QAOL,KAAK,wBAAC,QAAQ;AACZ,cAAI;AACF,kBAAM,QAAQ,OAAO,aAAa,QAAQ,GAAG;AAC7C,mBAAO,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,UACrC,QAAQ;AACN,mBAAO;AAAA,UACT;AAAA,QACF,GAPK;AAAA,QAQL,QAAQ,wBAAC,QAAQ;AACf,cAAI;AACF,mBAAO,aAAa,WAAW,GAAG;AAAA,UACpC,QAAQ;AAAA,UAER;AAAA,QACF,GANQ;AAAA,MAOV;AAAA,IACF;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAED,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,mBAAmB;AACjB,WAAK,WAAW,SAAS,cAAc,yBAAyB;AAChE,WAAK,WAAW,SAAS,cAAc,kCAAkC;AAEzE,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,UAAU;AACpC,gBAAQ,KAAK,wCAA+B;AAC5C;AAAA,MACF;AAEA,WAAK,oBAAoB;AACzB,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AACxB,WAAK,qBAAqB;AAG1B,WAAK,qBAAqB;AAG1B,iBAAW,MAAM;AACf,iBAAS;AAAA,UACP,IAAI,YAAY,6BAA6B;AAAA,YAC3C,QAAQ;AAAA,cACN,eAAe,KAAK,eAAe;AAAA,cACnC,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,cAC9C,aAAa;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAAA,IAEA,sBAAsB;AAIpB,YAAM,iBAAiB,KAAK,SAAS,iBAAiB,yBAAyB;AAC/E,qBAAe,QAAQ,CAAC,UAAU,KAAK,oBAAoB,OAAO,UAAU,CAAC;AAG7E,YAAM,mBAAmB,KAAK,SAAS,iBAAiB,6BAA6B;AACrF,uBAAiB,QAAQ,CAAC,UAAU,KAAK,oBAAoB,OAAO,YAAY,CAAC;AAAA,IACnF;AAAA,IAEA,oBAAoB,cAAc,MAAM;AACtC,YAAM,WAAW,aAAa,aAAa,gBAAgB;AAC3D,YAAM,UAAU,aAAa,aAAa,eAAe;AAEzD,UAAI,CAAC,YAAY,CAAC,SAAS;AACzB;AAAA,MACF;AAGA,YAAM,qBAAqB,KAAK,gBAAgB,QAAQ;AACxD,YAAM,oBAAoB,KAAK,gBAAgB,OAAO;AAGtD,YAAM,oBAAoB,SAAS,cAAc,KAAK;AACtD,wBAAkB,YAAY;AAE9B,YAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,eAAS,OAAO;AAChB,eAAS,YAAY;AACrB,eAAS,KAAK,SAAS,kBAAkB,IAAI,iBAAiB,GAC3D,QAAQ,QAAQ,GAAG,EACnB,YAAY;AAEf,YAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,UAAU,SAAS;AACzB,YAAM,YAAY;AAElB,wBAAkB,YAAY,QAAQ;AACtC,wBAAkB,YAAY,KAAK;AAGnC,UAAI,SAAS,YAAY;AAEvB,qBAAa,aAAa,mBAAmB,aAAa,UAAU;AAAA,MACtE,OAAO;AAEL,cAAM,cAAc,aAAa,cAAc,iBAAiB;AAChE,YAAI,aAAa;AACf,sBAAY,WAAW,aAAa,mBAAmB,YAAY,WAAW;AAAA,QAChF,OAAO;AACL,uBAAa,aAAa,mBAAmB,aAAa,UAAU;AAAA,QACtE;AAAA,MACF;AAGA,eAAS,iBAAiB,UAAU,CAAC,MAAM;AACzC,aAAK,qBAAqB,EAAE,OAAO,SAAS,UAAU,SAAS,YAAY;AAAA,MAC7E,CAAC;AAGD,mBAAa,iBAAiB,SAAS,CAAC,MAAM;AAE5C,YAAI,CAAC,EAAE,OAAO,QAAQ,wCAAwC,GAAG;AAC/D,mBAAS,UAAU,CAAC,SAAS;AAC7B,mBAAS,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC5C;AAAA,MACF,CAAC;AAGD,WAAK,eAAe,KAAK;AAAA,QACvB,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,GAAG,kBAAkB,IAAI,iBAAiB;AAAA,QACzD,KAAK,GAAG,QAAQ,IAAI,OAAO;AAAA;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,gBAAgB,KAAK;AACnB,aAAO,IAAI,YAAY,EAAE,KAAK;AAAA,IAChC;AAAA,IAEA,qBAAqB,YAAY,UAAU,SAAS,cAAc;AAChE,YAAM,gBAAgB,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AAExF,UAAI,YAAY;AACd,aAAK,eAAe,IAAI,aAAa;AACrC,qBAAa,UAAU,IAAI,gBAAgB;AAE3C,aAAK,iBAAiB,UAAU,OAAO;AAAA,MACzC,OAAO;AACL,aAAK,eAAe,OAAO,aAAa;AACxC,qBAAa,UAAU,OAAO,gBAAgB;AAE9C,aAAK,iBAAiB,UAAU,OAAO;AAAA,MACzC;AAEA,WAAK,cAAc;AACnB,WAAK,eAAe;AAGpB,WAAK,sBAAsB;AAG3B,eAAS;AAAA,QACP,IAAI,YAAY,yBAAyB;AAAA,UACvC,QAAQ;AAAA,YACN,eAAe,KAAK,eAAe;AAAA,YACnC,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,UAChD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,uBAAuB;AAErB,eAAS,iBAAiB,sBAAsB,MAAM;AAEpD,aAAK,eAAe,MAAM;AAC1B,aAAK,eAAe,QAAQ,CAAC,UAAU;AACrC,gBAAM,SAAS,UAAU;AACzB,gBAAM,QAAQ,UAAU,OAAO,gBAAgB;AAAA,QACjD,CAAC;AACD,aAAK,cAAc;AACnB,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,IAEA,eAAe;AACb,WAAK,iBAAiB,KAAK,SAAS,cAAc,iBAAiB;AACnE,UAAI,KAAK,gBAAgB;AACvB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA,IAEA,gBAAgB;AACd,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,cAAc,IAAI,KAAK,eAAe,IAAI;AAAA,MAChE;AAAA,IACF;AAAA,IAEA,mBAAmB;AACjB,YAAM,cAAc,KAAK,SAAS,cAAc,sBAAsB;AACtE,UAAI,aAAa;AACf,oBAAY,iBAAiB,SAAS,CAAC,MAAM;AAC3C,YAAE,eAAe;AACjB,eAAK,mBAAmB;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,iBAAiB,UAAU,SAAS;AAClC,UAAI;AAEF,cAAM,iBAAiB,KAAK,SAAS;AAAA,UACnC,gDAAgD,QAAQ,qBAAqB,OAAO;AAAA,QACtF;AAEA,YAAI,gBAAgB;AAElB,gBAAM,QAAQ,eAAe,cAAc,4BAA4B;AACvE,cAAI,OAAO;AAET,kBAAM,QAAQ;AAGd,kBAAM;AAAA,cACJ,IAAI,YAAY,kBAAkB;AAAA,gBAChC,QAAQ,EAAE,OAAO,EAAE;AAAA,gBACnB,SAAS;AAAA,cACX,CAAC;AAAA,YACH;AAGA,kBAAM,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC,CAAC;AAAA,UAC3D;AAGA,gBAAM,SAAS,eAAe,cAAc,cAAc;AAC1D,cAAI,QAAQ;AACV,mBAAO,QAAQ;AACf,mBAAO,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC,CAAC;AAAA,UAC5D;AAGA,gBAAM,oBAAoB,eAAe,cAAc,0BAA0B;AACjF,cAAI,mBAAmB;AACrB,8BAAkB,cAAc;AAAA,UAClC;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,8BAA8B,QAAQ,MAAM,OAAO,KAAK,KAAK;AAAA,MAC5E;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,wBAAwB;AACtB,UAAI;AACF,cAAM,iBAAiB;AAAA,UACrB,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,UAC9C,WAAW,KAAK,IAAI;AAAA,QACtB;AACA,aAAK,aAAa,IAAI,KAAK,UAAU,cAAc;AAAA,MACrD,SAAS,OAAO;AACd,gBAAQ,KAAK,2CAA2C,KAAK;AAAA,MAC/D;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,uBAAuB;AACrB,UAAI;AACF,cAAM,aAAa,KAAK,aAAa,IAAI,KAAK,QAAQ;AACtD,YAAI,CAAC,cAAc,CAAC,MAAM,QAAQ,WAAW,cAAc,GAAG;AAC5D;AAAA,QACF;AAGA,aAAK,iBAAiB,IAAI,IAAI,WAAW,cAAc;AAGvD,mBAAW,eAAe,QAAQ,CAAC,kBAAkB;AAEnD,gBAAM,QAAQ,KAAK,eAAe,KAAK,CAAC,MAAM,EAAE,kBAAkB,aAAa;AAC/E,cAAI,OAAO;AACT,kBAAM,SAAS,UAAU;AACzB,kBAAM,QAAQ,UAAU,IAAI,gBAAgB;AAAA,UAC9C;AAAA,QACF,CAAC;AAGD,aAAK,cAAc;AACnB,aAAK,eAAe;AAGpB,iBAAS;AAAA,UACP,IAAI,YAAY,yBAAyB;AAAA,YACvC,QAAQ;AAAA,cACN,eAAe,KAAK,eAAe;AAAA,cACnC,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,cAC9C,WAAW;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH;AAGA,mBAAW,MAAM;AACf,mBAAS;AAAA,YACP,IAAI,YAAY,6BAA6B;AAAA,cAC3C,QAAQ;AAAA,gBACN,eAAe,KAAK,eAAe;AAAA,gBACnC,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,gBAC9C,aAAa;AAAA,cACf;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,GAAG,GAAG;AAAA,MAGR,SAAS,OAAO;AACd,gBAAQ,KAAK,8CAA8C,KAAK;AAAA,MAClE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,uBAAuB;AACrB,UAAI;AACF,aAAK,aAAa,OAAO,KAAK,QAAQ;AAAA,MACxC,SAAS,OAAO;AACd,gBAAQ,KAAK,0CAA0C,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA,IAEA,qBAAqB;AACnB,WAAK,eAAe,MAAM;AAE1B,WAAK,eAAe,QAAQ,CAAC,UAAU;AACrC,cAAM,SAAS,UAAU;AACzB,cAAM,QAAQ,UAAU,OAAO,gBAAgB;AAE/C,aAAK,iBAAiB,MAAM,UAAU,MAAM,OAAO;AAAA,MACrD,CAAC;AAED,WAAK,cAAc;AACnB,WAAK,eAAe;AAGpB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,IAEA,qBAAqB;AAEnB,YAAM,iBAAiB,KAAK,SAAS;AAAA,QACnC;AAAA,MACF;AAEA,qBAAe,QAAQ,CAAC,UAAU;AAChC,cAAM,WAAW,MAAM,aAAa,gBAAgB;AACpD,cAAM,UAAU,MAAM,aAAa,eAAe;AAElD,YAAI,YAAY,SAAS;AACvB,eAAK,eAAe,KAAK;AAAA,YACvB,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA,eAAe,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AAAA,YACjF,KAAK,GAAG,QAAQ,IAAI,OAAO;AAAA;AAAA,UAC7B,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAGD,WAAK,eAAe;AAAA,IACtB;AAAA,IAEA,iBAAiB;AACf,WAAK,eAAe,QAAQ,CAAC,UAAU;AAErC,cAAM,aAAa,KAAK,eAAe,IAAI,MAAM,aAAa;AAE9D,YAAI,YAAY;AACd,gBAAM,QAAQ,MAAM,UAAU;AAC9B,gBAAM,QAAQ,UAAU,OAAO,oBAAoB;AACnD,gBAAM,QAAQ,UAAU,IAAI,mBAAmB;AAAA,QACjD,OAAO;AACL,gBAAM,QAAQ,MAAM,UAAU;AAC9B,gBAAM,QAAQ,UAAU,IAAI,oBAAoB;AAChD,gBAAM,QAAQ,UAAU,OAAO,mBAAmB;AAAA,QACpD;AAAA,MACF,CAAC;AAGD,eAAS;AAAA,QACP,IAAI,YAAY,sBAAsB;AAAA,UACpC,QAAQ;AAAA,YACN,gBAAgB,MAAM,KAAK,KAAK,cAAc;AAAA,YAC9C,eAAe,KAAK,eAAe;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGA,oBAAoB;AAClB,aAAO,MAAM,KAAK,KAAK,cAAc;AAAA,IACvC;AAAA,IAEA,gBAAgB,UAAU,SAAS;AACjC,YAAM,gBAAgB,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AACxF,aAAO,KAAK,eAAe,IAAI,aAAa;AAAA,IAC9C;AAAA,IAEA,YAAY,UAAU,SAAS;AAC7B,YAAM,QAAQ,KAAK,eAAe,KAAK,CAAC,MAAM,EAAE,aAAa,YAAY,EAAE,YAAY,OAAO;AAC9F,UAAI,SAAS,CAAC,MAAM,SAAS,SAAS;AACpC,cAAM,SAAS,UAAU;AACzB,cAAM,SAAS,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,IAEA,cAAc,UAAU,SAAS;AAC/B,YAAM,QAAQ,KAAK,eAAe,KAAK,CAAC,MAAM,EAAE,aAAa,YAAY,EAAE,YAAY,OAAO;AAC9F,UAAI,SAAS,MAAM,SAAS,SAAS;AACnC,cAAM,SAAS,UAAU;AACzB,cAAM,SAAS,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,MAClD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,cAAc;AACZ,UAAI;AAEF,aAAK,mBAAmB;AAGxB,iBAAS;AAAA,UACP,IAAI,YAAY,6BAA6B;AAAA,YAC3C,QAAQ;AAAA,cACN,WAAW,KAAK,IAAI;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MAGF,SAAS,OAAO;AACd,gBAAQ,KAAK,2CAA2C,KAAK;AAAA,MAC/D;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,eAAe;AACb,UAAI;AACF,cAAM,aAAa,KAAK,aAAa,IAAI,KAAK,QAAQ;AACtD,eAAO;AAAA,UACL,eAAe,CAAC,CAAC;AAAA,UACjB,kBAAkB,YAAY,gBAAgB,UAAU;AAAA,UACxD,WAAW,YAAY,aAAa;AAAA,UACpC,mBAAmB,KAAK,eAAe;AAAA,QACzC;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,6BAA6B,KAAK;AAC/C,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;", "names": []}