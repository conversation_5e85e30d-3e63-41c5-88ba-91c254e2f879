"use strict";
(() => {
  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/debug-init.js
  window.ReinoDebug = {
    startTime: Date.now(),
    phases: [],
    errors: [],
    warnings: [],
    systemStatus: {},
    // Add a phase marker
    markPhase(phase, details = {}) {
      const timestamp = Date.now() - this.startTime;
      this.phases.push({
        phase,
        timestamp,
        details,
        time: (/* @__PURE__ */ new Date()).toISOString()
      });
      console.log(`[DEBUG ${timestamp}ms] ${phase}`, details);
    },
    // Log an error
    logError(error, context = "") {
      this.errors.push({
        error: error.message || error,
        stack: error.stack,
        context,
        timestamp: Date.now() - this.startTime
      });
      console.error(`[DEBUG ERROR] ${context}:`, error);
    },
    // Log a warning
    logWarning(message, details = {}) {
      this.warnings.push({
        message,
        details,
        timestamp: Date.now() - this.startTime
      });
      console.warn(`[DEBUG WARNING] ${message}`, details);
    },
    // Check system status
    checkSystemStatus() {
      this.systemStatus = {
        // Check if critical libraries are loaded
        sortableJS: !!window.Sortable,
        webflow: !!window.Webflow,
        motion: !!window.Motion,
        reinoCalculator: !!window.ReinoCalculator,
        // Check DOM elements
        domElements: {
          dropArea: !!document.querySelector(".ativos_main_drop_area"),
          interactiveWrapper: !!document.querySelector(".patrimonio_interactive_content-wrapper"),
          mainList: !!document.querySelector(".ativos_main-list"),
          section2: !!document.querySelector("._2-section-calc-ativos"),
          section3: !!document.querySelector("._3-section-patrimonio-alocation")
        },
        // Check initialization status
        initialized: window.ReinoCalculator?.app?.isInitialized || false,
        // Memory usage
        memory: performance.memory ? {
          used: Math.round(performance.memory.usedJSHeapSize / 1048576) + "MB",
          total: Math.round(performance.memory.totalJSHeapSize / 1048576) + "MB"
        } : "Not available"
      };
      return this.systemStatus;
    },
    // Generate report
    generateReport() {
      const report = {
        totalTime: Date.now() - this.startTime,
        phases: this.phases,
        errors: this.errors,
        warnings: this.warnings,
        systemStatus: this.checkSystemStatus(),
        performanceMetrics: this.getPerformanceMetrics()
      };
      console.group("\u{1F50D} Reino Debug Report");
      console.log("Total initialization time:", report.totalTime + "ms");
      console.log("Phases:", report.phases);
      console.log("Errors:", report.errors);
      console.log("Warnings:", report.warnings);
      console.log("System Status:", report.systemStatus);
      console.log("Performance:", report.performanceMetrics);
      console.groupEnd();
      return report;
    },
    // Get performance metrics
    getPerformanceMetrics() {
      const navigation = performance.getEntriesByType("navigation")[0];
      return {
        domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,
        loadComplete: navigation?.loadEventEnd - navigation?.loadEventStart,
        resourceCount: performance.getEntriesByType("resource").length
      };
    },
    // Monitor for infinite loops
    startLoopMonitor() {
      let lastCheck = Date.now();
      let loopCount = 0;
      const maxLoops = 1e3;
      const monitor = setInterval(() => {
        const now = Date.now();
        const timeDiff = now - lastCheck;
        if (timeDiff < 10) {
          loopCount++;
          if (loopCount > maxLoops) {
            this.logError(new Error("Possible infinite loop detected"), "Loop Monitor");
            clearInterval(monitor);
          }
        } else {
          loopCount = 0;
        }
        lastCheck = now;
      }, 10);
      setTimeout(() => clearInterval(monitor), 3e4);
    },
    // Track initialization timeout
    startTimeoutMonitor(timeout = 15e3) {
      setTimeout(() => {
        if (!window.ReinoCalculator?.app?.isInitialized) {
          this.logError(new Error("Initialization timeout"), `No completion after ${timeout}ms`);
          this.generateReport();
          const systems = window.ReinoCalculator?.systems || {};
          const stuckSystems = [];
          for (const [name, system] of Object.entries(systems)) {
            if (!system.isInitialized) {
              stuckSystems.push(name);
            }
          }
          if (stuckSystems.length > 0) {
            console.error("Systems not initialized:", stuckSystems);
          }
        }
      }, timeout);
    }
  };
  ReinoDebug.markPhase("Debug script loaded");
  ReinoDebug.startLoopMonitor();
  ReinoDebug.startTimeoutMonitor();
  if (document.readyState === "loading") {
    ReinoDebug.markPhase("DOM still loading");
    document.addEventListener("DOMContentLoaded", () => {
      ReinoDebug.markPhase("DOM Content Loaded");
      ReinoDebug.checkSystemStatus();
    });
  } else {
    ReinoDebug.markPhase("DOM already loaded");
  }
  window.addEventListener("load", () => {
    ReinoDebug.markPhase("Window loaded");
    ReinoDebug.checkSystemStatus();
  });
  document.addEventListener("reinoCalculatorReady", (event) => {
    ReinoDebug.markPhase("Reino Calculator Ready", {
      systems: Object.keys(event.detail.systems || {})
    });
    ReinoDebug.generateReport();
  });
  var eventsToMonitor = [
    "dragDropItemAdded",
    "dragDropItemRemoved",
    "dragDropStateChanged",
    "assetsSyncCompleted"
  ];
  eventsToMonitor.forEach((eventName) => {
    document.addEventListener(eventName, (event) => {
      ReinoDebug.markPhase(`Event: ${eventName}`, event.detail);
    });
  });
  window.addEventListener("error", (event) => {
    ReinoDebug.logError(event.error || event, "Window error event");
  });
  window.addEventListener("unhandledrejection", (event) => {
    ReinoDebug.logError(event.reason, "Unhandled promise rejection");
  });
  window.debugReino = {
    report: () => ReinoDebug.generateReport(),
    status: () => ReinoDebug.checkSystemStatus(),
    phases: () => ReinoDebug.phases,
    errors: () => ReinoDebug.errors,
    // Force initialization
    forceInit: async () => {
      console.log("Forcing initialization...");
      if (window.ReinoCalculator?.app) {
        await window.ReinoCalculator.app.init();
      } else {
        console.error("ReinoCalculator app not found");
      }
    },
    // Check specific systems
    checkSystem: (systemName) => {
      const system = window.ReinoCalculator?.systems?.[systemName];
      if (system) {
        console.log(`System ${systemName}:`, {
          initialized: system.isInitialized,
          state: system.getState?.() || "No state method"
        });
      } else {
        console.error(`System ${systemName} not found`);
      }
    },
    // Test SortableJS loading
    testSortable: () => {
      if (window.Sortable) {
        console.log("\u2705 SortableJS is loaded");
        const testEl = document.createElement("div");
        try {
          new window.Sortable(testEl);
          console.log("\u2705 SortableJS can create instances");
        } catch (error) {
          console.error("\u274C SortableJS instance creation failed:", error);
        }
      } else {
        console.error("\u274C SortableJS not loaded");
      }
    }
  };
  console.log(`
\u{1F50D} Reino Debug Mode Active
========================
Use these commands in console:

\u2022 debugReino.report() - Generate full report
\u2022 debugReino.status() - Check system status
\u2022 debugReino.phases - View initialization phases
\u2022 debugReino.errors - View errors
\u2022 debugReino.forceInit() - Force initialization
\u2022 debugReino.checkSystem('systemName') - Check specific system
\u2022 debugReino.testSortable() - Test SortableJS

Report will auto-generate on timeout or completion.
`);
})();
//# sourceMappingURL=debug-init.js.map
