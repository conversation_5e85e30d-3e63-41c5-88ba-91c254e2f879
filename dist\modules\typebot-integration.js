"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/config/typebot.js
  var TYPEBOT_CONFIG = {
    // Your Typebot public ID (get this from your Typebot URL)
    PUBLIC_ID: "relatorio-reino",
    // ✅ Atualizado com ID correto usado no HTML
    // API Base URL (use your self-hosted instance or typebot.io)
    API_BASE_URL: "https://typebot.co/api/v1",
    // ✅ Atualizado para typebot.co
    // Authorization token (optional - needed for private bots)
    AUTH_TOKEN: "Bearer Dza0r0rRB4rzjSeGiKlawQK8",
    // ✅ Configurado
    // Webhook configuration from your Typebot
    WEBHOOK_CONFIG: {
      blockId: "o8gkxhaloh3c3w5ldx94zlwx",
      webhookUrl: "https://typebot.co/api/v1/typebots/mp0wrmwrxpqnycbxlcimcz5n/blocks/o8gkxhaloh3c3w5ldx94zlwx/results/{resultId}/executeWebhook"
    },
    // Typebot Embed Configuration
    EMBED_CONFIG: {
      // Container where the typebot will be embedded
      containerId: "typebot-container",
      // Use official Typebot popup
      usePopup: true,
      // CDN URL for Typebot JS library
      cdnUrl: "https://cdn.jsdelivr.net/npm/@typebot.io/js@0/dist/web.js",
      // Typebot theme customization
      theme: {
        button: {
          backgroundColor: "#0042DA",
          iconColor: "#FFFFFF",
          customIconSrc: null,
          size: "medium",
          dragAndDrop: false,
          position: "right"
        },
        chatWindow: {
          backgroundColor: "#FFFFFF",
          maxWidth: "400px",
          maxHeight: "600px"
        }
      },
      // Auto-open configuration
      autoOpenDelay: 0
      // Set to 0 to not auto-open, or milliseconds to delay
    },
    // Webhook endpoint for completion callbacks
    COMPLETION_WEBHOOK: "/api/typebot-completion",
    // Your endpoint to handle completion
    // Debug mode
    DEBUG: window.location.hostname === "localhost" || window.location.search.includes("debug=true")
  };
  var VARIABLE_MAPPING = {
    // Your form field name -> Typebot variable name
    patrimonio: "patrimonio",
    // Ajustado para minúsculo
    ativosEscolhidos: "ativos",
    // Nome do campo coletado no form
    email: "email",
    // Será coletado pelo Typebot
    nome: "nome",
    // Será coletado pelo Typebot
    alocacao: "alocacao",
    // Dados de alocação
    totalAlocado: "totalAlocado",
    // Total alocado calculado
    patrimonioRestante: "patrimonioRestante",
    // Patrimônio restante
    sessionId: "sessionId"
  };
  var TypebotClient = class {
    static {
      __name(this, "TypebotClient");
    }
    constructor(config = TYPEBOT_CONFIG) {
      this.config = config;
      this.sessionId = null;
      this.resultId = null;
      this.isCompleted = false;
    }
    /**
     * Start a new Typebot chat session
     * @param {Object} prefilledData - Data to prefill in the typebot
     * @returns {Promise<Object>} - Session data
     */
    async startChat(prefilledData = {}) {
      try {
        if (typeof fetch === "undefined") {
          throw new Error("Fetch is not available in this environment");
        }
        const url = `${this.config.API_BASE_URL}/typebots/${this.config.PUBLIC_ID}/startChat`;
        const prefilledVariables = this.mapFormDataToVariables(prefilledData);
        const requestBody = {
          prefilledVariables,
          isStreamEnabled: false,
          textBubbleContentFormat: "richText"
        };
        const headers = {
          "Content-Type": "application/json"
        };
        if (this.config.AUTH_TOKEN) {
          headers["Authorization"] = this.config.AUTH_TOKEN;
        }
        const response = await window.fetch(url, {
          method: "POST",
          headers,
          body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
          throw new Error(`Typebot API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        this.sessionId = data.sessionId;
        this.resultId = data.resultId;
        if (this.config.DEBUG) {
        }
        return data;
      } catch (error) {
        console.error("\u274C Failed to start Typebot chat:", error);
        throw error;
      }
    }
    /**
     * Continue the chat with a message
     * @param {string} message - Message to send
     * @returns {Promise<Object>} - Response data
     */
    async continueChat(message) {
      if (!this.sessionId) {
        throw new Error("No active session. Start chat first.");
      }
      try {
        const url = `${this.config.API_BASE_URL}/sessions/${this.sessionId}/continueChat`;
        const requestBody = {
          message: {
            type: "text",
            text: message
          },
          textBubbleContentFormat: "richText"
        };
        const headers = {
          "Content-Type": "application/json"
        };
        if (this.config.AUTH_TOKEN) {
          headers["Authorization"] = this.config.AUTH_TOKEN;
        }
        const response = await window.fetch(url, {
          method: "POST",
          headers,
          body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
          throw new Error(`Typebot API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (this.config.DEBUG) {
        }
        return data;
      } catch (error) {
        console.error("\u274C Failed to continue Typebot chat:", error);
        throw error;
      }
    }
    /**
     * Map form data to typebot variables
     * @param {Object} formData - Form data from your application
     * @returns {Object} - Mapped variables for typebot
     */
    mapFormDataToVariables(formData) {
      const mappedVariables = {};
      const totalAlocado = formData.alocacao ? Object.values(formData.alocacao).reduce((sum, item) => sum + (item.value || 0), 0) : 0;
      const patrimonioRestante = (formData.patrimonio || 0) - totalAlocado;
      const enhancedFormData = {
        ...formData,
        totalAlocado,
        patrimonioRestante
      };
      for (const [formField, typebotVar] of Object.entries(VARIABLE_MAPPING)) {
        if (enhancedFormData[formField] !== void 0) {
          if (formField === "patrimonio" && typeof enhancedFormData[formField] === "number") {
            mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString("pt-BR")}`;
          } else if (formField === "totalAlocado" && typeof enhancedFormData[formField] === "number") {
            mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString("pt-BR")}`;
          } else if (formField === "patrimonioRestante" && typeof enhancedFormData[formField] === "number") {
            mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString("pt-BR")}`;
          } else if (formField === "ativosEscolhidos" && Array.isArray(enhancedFormData[formField])) {
            const ativosList = enhancedFormData[formField].map((ativo) => `${ativo.product} (${ativo.category})`).join(", ");
            mappedVariables[typebotVar] = ativosList;
          } else if (formField === "alocacao" && typeof enhancedFormData[formField] === "object") {
            const alocacaoList = Object.entries(enhancedFormData[formField]).filter(([, data]) => (data.value || 0) > 0).map(
              ([, data]) => `${data.product}: R$ ${(data.value || 0).toLocaleString("pt-BR")} (${Math.round((data.percentage || 0) * 100) / 100}%)`
            ).join("\n");
            mappedVariables[typebotVar] = alocacaoList || "Nenhuma aloca\xE7\xE3o realizada";
          } else {
            mappedVariables[typebotVar] = String(enhancedFormData[formField]);
          }
        }
      }
      return mappedVariables;
    }
    /**
     * Check if the chat is completed
     * @returns {boolean}
     */
    isSessionCompleted() {
      return this.isCompleted;
    }
    /**
     * Mark session as completed
     */
    markCompleted() {
      this.isCompleted = true;
      if (this.config.DEBUG) {
      }
    }
    /**
     * Reset the client for a new session
     */
    reset() {
      this.sessionId = null;
      this.resultId = null;
      this.isCompleted = false;
    }
  };

  // src/modules/typebot-integration.js
  var TypebotIntegrationSystem = class {
    static {
      __name(this, "TypebotIntegrationSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.typebotClient = new TypebotClient(TYPEBOT_CONFIG);
      this.completionCallbacks = [];
      this.currentFormData = null;
      this.isTypebotActive = false;
      this.typebotLibrary = null;
      this.handleTypebotCompletion = this.handleTypebotCompletion.bind(this);
    }
    async init() {
      if (this.isInitialized) return;
      try {
        await this.loadTypebotLibrary();
        await this.initializeTypebotPopup();
        this.setupCompletionListener();
        this.setupEmbedContainer();
        this.setupGlobalAPI();
        this.isInitialized = true;
      } catch (error) {
        console.error("\u274C TypebotIntegrationSystem initialization failed:", error);
      }
    }
    /**
     * Load Typebot library from CDN
     */
    async loadTypebotLibrary() {
      try {
        if (window.Typebot) {
          this.typebotLibrary = window.Typebot;
          return;
        }
        const { default: Typebot } = await import(TYPEBOT_CONFIG.EMBED_CONFIG.cdnUrl);
        this.typebotLibrary = Typebot;
        window.Typebot = Typebot;
        if (TYPEBOT_CONFIG.DEBUG) {
          console.log("\u{1F916} Typebot library loaded successfully");
        }
      } catch (error) {
        console.error("\u274C Failed to load Typebot library:", error);
        throw error;
      }
    }
    /**
     * Initialize Typebot popup
     */
    async initializeTypebotPopup() {
      if (!this.typebotLibrary) {
        throw new Error("Typebot library not loaded");
      }
      try {
        await this.typebotLibrary.initPopup({
          typebot: TYPEBOT_CONFIG.PUBLIC_ID,
          prefilledVariables: {},
          // Will be set when opening
          onMessage: /* @__PURE__ */ __name((message) => {
            this.handleTypebotMessage(message);
          }, "onMessage"),
          onEnd: /* @__PURE__ */ __name(() => {
            this.handleTypebotEnd();
          }, "onEnd")
        });
        if (TYPEBOT_CONFIG.DEBUG) {
          console.log("\u{1F916} Typebot popup initialized");
        }
      } catch (error) {
        console.error("\u274C Failed to initialize Typebot popup:", error);
        throw error;
      }
    }
    /**
     * Handle messages from Typebot
     */
    handleTypebotMessage(message) {
      if (TYPEBOT_CONFIG.DEBUG) {
        console.log("\u{1F916} Typebot message:", message);
      }
      if (message.type === "completion" || message.data?.completed) {
        this.handleTypebotCompletion(message.data);
      }
    }
    /**
     * Handle when Typebot ends
     */
    handleTypebotEnd() {
      console.log("\u{1F3C1} [TypebotIntegration] Typebot conversation ended (onEnd triggered)");
      this.isTypebotActive = false;
      if (this.currentFormData) {
        console.log("\u{1F4CB} [TypebotIntegration] Auto-triggering completion with form data");
        this.handleTypebotCompletion({ completed: true, autoTriggered: true });
      } else {
        console.warn("\u26A0\uFE0F [TypebotIntegration] No form data available for auto-completion");
      }
    }
    /**
     * Start Typebot with form data instead of sending directly to Supabase
     * @param {Object} formData - Collected form data
     * @param {Function} onCompletion - Callback when typebot is completed
     * @returns {Promise<void>}
     */
    async startTypebotFlow(formData, onCompletion = null) {
      try {
        console.log("\u{1F680} [TypebotIntegration] startTypebotFlow called with:", formData);
        if (!this.typebotLibrary) {
          throw new Error("Typebot library not loaded");
        }
        this.currentFormData = formData;
        if (onCompletion) {
          this.completionCallbacks.push(onCompletion);
          console.log("\u{1F4DD} [TypebotIntegration] Completion callback registered");
        }
        console.log(`\u{1F4DE} [TypebotIntegration] Total callbacks: ${this.completionCallbacks.length}`);
        const prefilledVariables = this.typebotClient.mapFormDataToVariables(formData);
        console.log("\u{1F504} [TypebotIntegration] Prefilled variables:", prefilledVariables);
        if (this.typebotLibrary.setPrefilledVariables) {
          this.typebotLibrary.setPrefilledVariables(prefilledVariables);
        }
        console.log("\u{1F3AF} [TypebotIntegration] Opening Typebot popup...");
        this.typebotLibrary.open();
        this.isTypebotActive = true;
        document.dispatchEvent(
          new CustomEvent("typebotStarted", {
            detail: {
              formData,
              prefilledVariables
            }
          })
        );
        console.log("\u2705 [TypebotIntegration] Typebot popup opened successfully");
        return { success: true, prefilledVariables };
      } catch (error) {
        console.error("\u274C [TypebotIntegration] Failed to start Typebot flow:", error);
        this.handleTypebotError(error);
        throw error;
      }
    }
    /**
     * Handle Typebot completion and trigger Supabase submission
     */
    async handleTypebotCompletion(typebotData = {}) {
      try {
        console.log("\u{1F916} [TypebotIntegration] handleTypebotCompletion called with data:", typebotData);
        console.log(
          "\u{1F50D} [TypebotIntegration] Complete typebotData structure:",
          JSON.stringify(typebotData, null, 2)
        );
        if (!this.currentFormData) {
          console.error("\u274C [TypebotIntegration] No form data available for Supabase submission");
          return;
        }
        console.log("\u{1F4CB} [TypebotIntegration] Current form data:", this.currentFormData);
        this.typebotClient.markCompleted();
        this.isTypebotActive = false;
        let nome = null;
        let email = null;
        console.log(
          "\u{1F50D} [TypebotIntegration] Complete typebotData structure:",
          JSON.stringify(typebotData, null, 2)
        );
        if (typebotData.nome && !this.isEncryptedValue(typebotData.nome)) {
          nome = typebotData.nome;
        } else if (typebotData.nome && this.isEncryptedValue(typebotData.nome)) {
          console.warn(
            "\u26A0\uFE0F [TypebotIntegration] Nome is encrypted, using fallback:",
            typebotData.nome
          );
          nome = "Nome capturado via Typebot (ID: " + typebotData.nome.substring(0, 8) + "...)";
        }
        if (typebotData.email && !this.isEncryptedValue(typebotData.email)) {
          email = typebotData.email;
        } else if (typebotData.email && this.isEncryptedValue(typebotData.email)) {
          console.warn(
            "\u26A0\uFE0F [TypebotIntegration] Email is encrypted, using fallback:",
            typebotData.email
          );
          email = "email-typebot-" + typebotData.email.substring(0, 8) + "@capturado.com";
        }
        if (!nome) {
          nome = typebotData.name || typebotData.nome_usuario || typebotData.userName || null;
          if (nome && this.isEncryptedValue(nome)) nome = null;
        }
        if (!email) {
          email = typebotData.e_mail || typebotData.userEmail || typebotData.email_usuario || null;
          if (email && this.isEncryptedValue(email)) email = null;
        }
        if (typebotData.sessionData) {
          const sessionVars = typebotData.sessionData.variables || {};
          if (!nome && sessionVars.nome && !this.isEncryptedValue(sessionVars.nome)) {
            nome = sessionVars.nome;
          }
          if (!email && sessionVars.email && !this.isEncryptedValue(sessionVars.email)) {
            email = sessionVars.email;
          }
        }
        if (typebotData.resultData) {
          const resultVars = typebotData.resultData.variables || {};
          if (!nome && resultVars.nome && !this.isEncryptedValue(resultVars.nome)) {
            nome = resultVars.nome;
          }
          if (!email && resultVars.email && !this.isEncryptedValue(resultVars.email)) {
            email = resultVars.email;
          }
        }
        if (typebotData.variables) {
          if (!nome && typebotData.variables.nome && !this.isEncryptedValue(typebotData.variables.nome)) {
            nome = typebotData.variables.nome;
          }
          if (!email && typebotData.variables.email && !this.isEncryptedValue(typebotData.variables.email)) {
            email = typebotData.variables.email;
          }
        }
        if (typebotData.answers && Array.isArray(typebotData.answers)) {
          for (const answer of typebotData.answers) {
            if (answer.type === "text" && answer.value && !this.isEncryptedValue(answer.value)) {
              if (!nome && (answer.question?.toLowerCase().includes("nome") || answer.question?.toLowerCase().includes("name") || answer.value.length > 2 && answer.value.length < 50 && !answer.value.includes("@"))) {
                nome = answer.value;
              }
              if (!email && answer.value.includes("@") && answer.value.includes(".")) {
                email = answer.value;
              }
            }
          }
        }
        console.log("\u{1F4DD} [TypebotIntegration] Extracted user info from Typebot:", { nome, email });
        if (nome && this.isEncryptedValue(nome)) {
          console.warn("\u26A0\uFE0F [TypebotIntegration] Nome appears to be encrypted/placeholder:", nome);
          nome = "Nome n\xE3o capturado do Typebot";
        }
        if (email && this.isEncryptedValue(email)) {
          console.warn("\u26A0\uFE0F [TypebotIntegration] Email appears to be encrypted/placeholder:", email);
          email = "<EMAIL>";
        }
        const enhancedFormData = {
          ...this.currentFormData,
          // Add user contact information from Typebot
          nome,
          email,
          // Typebot metadata
          typebotSessionId: this.typebotClient.sessionId,
          typebotResultId: this.typebotClient.resultId,
          typebotData,
          completedAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        console.log("\u{1F504} [TypebotIntegration] Enhanced form data prepared:", enhancedFormData);
        if (!nome || !email) {
          console.warn(
            "\u26A0\uFE0F [TypebotIntegration] Missing user info from Typebot. Nome:",
            nome,
            "Email:",
            email
          );
        }
        console.log(
          `\u{1F4DE} [TypebotIntegration] Executing ${this.completionCallbacks.length} completion callbacks...`
        );
        for (const callback of this.completionCallbacks) {
          try {
            console.log("\u{1F3AF} [TypebotIntegration] Calling completion callback...");
            await callback(enhancedFormData);
            console.log("\u2705 [TypebotIntegration] Completion callback executed successfully");
          } catch (callbackError) {
            console.error("\u274C [TypebotIntegration] Completion callback error:", callbackError);
          }
        }
        this.completionCallbacks = [];
        this.currentFormData = null;
        console.log("\u{1F389} [TypebotIntegration] Dispatching typebotCompleted event...");
        document.dispatchEvent(
          new CustomEvent("typebotCompleted", {
            detail: {
              formData: enhancedFormData,
              typebotData,
              userInfo: { nome, email }
            }
          })
        );
        console.log("\u2705 [TypebotIntegration] handleTypebotCompletion completed successfully");
      } catch (error) {
        console.error("\u274C [TypebotIntegration] Failed to handle Typebot completion:", error);
      }
    }
    /**
     * Setup completion listener for webhooks or client-side actions
     */
    setupCompletionListener() {
      console.log("\u{1F527} [TypebotIntegration] Setting up completion listeners...");
      document.addEventListener("typebotFlowCompleted", (event) => {
        console.log("\u{1F4E8} [TypebotIntegration] typebotFlowCompleted event received:", event.detail);
        this.handleTypebotCompletion(event.detail);
      });
      window.addEventListener("message", (event) => {
        console.log("\u{1F4EC} [TypebotIntegration] postMessage received:", event.data);
        if (event.data && event.data.type === "typebot-completion") {
          console.log("\u{1F3AF} [TypebotIntegration] typebot-completion message detected");
          this.handleTypebotCompletion(event.data.data);
        } else if (event.data && event.data.type === "typebot-close-request") {
          console.log("\u{1F512} [TypebotIntegration] typebot-close-request received");
          this.closeTypebot(event.data.data);
        }
      });
      this.setupWebhookListener();
      this.setupFallbackNavigationListener();
      console.log("\u2705 [TypebotIntegration] Completion listeners setup complete");
    }
    /**
     * Setup webhook listener for server-side completion callbacks
     */
    setupWebhookListener() {
      if (TYPEBOT_CONFIG.COMPLETION_WEBHOOK) {
        this.startCompletionPolling();
      }
    }
    /**
     * Start polling for completion (fallback method)
     */
    startCompletionPolling() {
      const pollInterval = setInterval(() => {
        if (!this.isTypebotActive || this.typebotClient.isSessionCompleted()) {
          clearInterval(pollInterval);
          return;
        }
        this.checkTypebotCompletion();
      }, 5e3);
    }
    /**
     * Check if typebot is completed (simplified implementation)
     */
    async checkTypebotCompletion() {
    }
    /**
     * Setup embed container for typebot
     */
    setupEmbedContainer() {
      const { containerId } = TYPEBOT_CONFIG.EMBED_CONFIG;
      let container = document.getElementById(containerId);
      if (!container) {
        container = document.createElement("div");
        container.id = containerId;
        container.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 9999;
        max-width: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.chatWindow.maxWidth};
        max-height: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.chatWindow.maxHeight};
      `;
        document.body.appendChild(container);
      }
    }
    /**
     * Initialize embedded typebot
     */
    initializeEmbed() {
      const container = document.getElementById(TYPEBOT_CONFIG.EMBED_CONFIG.containerId);
      if (container) {
        container.innerHTML = `
        <div style="
          background: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.chatWindow.backgroundColor};
          border-radius: 10px;
          box-shadow: 0 4px 20px rgba(0,0,0,0.15);
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <h3 style="margin: 0 0 15px 0; color: #333;">Reino Capital</h3>
          <p style="margin: 0 0 15px 0; color: #666; font-size: 14px;">
            Obrigado por preencher o formul\xE1rio! Continue a conversa conosco:
          </p>
          <button onclick="window.openTypebot('${this.typebotClient.sessionId}')" style="
            background: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.button.backgroundColor};
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
          ">
            Continuar Conversa \u2192
          </button>
        </div>
      `;
      }
    }
    /**
     * Determine if embed mode should be used
     */
    shouldUseEmbed() {
      return TYPEBOT_CONFIG.EMBED_CONFIG && TYPEBOT_CONFIG.EMBED_CONFIG.containerId && TYPEBOT_CONFIG.PUBLIC_ID;
    }
    /**
     * Handle typebot errors with fallback
     */
    async handleTypebotError(error) {
      console.error("\u{1F916} Typebot error, falling back to direct submission:", error);
      if (this.currentFormData && this.completionCallbacks.length > 0) {
        const fallbackData = {
          ...this.currentFormData,
          typebotError: error.message,
          fallbackSubmission: true,
          submittedAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        for (const callback of this.completionCallbacks) {
          try {
            await callback(fallbackData);
          } catch (callbackError) {
            console.error("\u274C Fallback callback error:", callbackError);
          }
        }
      }
    }
    /**
     * Setup global API for external integration
     */
    setupGlobalAPI() {
      if (window.ReinoCalculator) {
        window.ReinoCalculator.typebot = {
          start: /* @__PURE__ */ __name((formData, onCompletion) => this.startTypebotFlow(formData, onCompletion), "start"),
          complete: /* @__PURE__ */ __name((data) => this.handleTypebotCompletion(data), "complete"),
          isActive: /* @__PURE__ */ __name(() => this.isTypebotActive, "isActive"),
          client: this.typebotClient,
          config: TYPEBOT_CONFIG
        };
      }
      window.openTypebot = (sessionId) => {
        const typebotUrl = `https://typebot.io/${TYPEBOT_CONFIG.PUBLIC_ID}?sessionId=${sessionId}`;
        window.open(typebotUrl, "typebot", "width=400,height=600,scrollbars=yes,resizable=yes");
      };
      window.triggerTypebotCompletion = (data) => {
        document.dispatchEvent(
          new CustomEvent("typebotFlowCompleted", {
            detail: data
          })
        );
      };
    }
    /**
     * Add completion callback
     */
    onCompletion(callback) {
      this.completionCallbacks.push(callback);
    }
    /**
     * Remove completion callback
     */
    removeCompletion(callback) {
      const index = this.completionCallbacks.indexOf(callback);
      if (index > -1) {
        this.completionCallbacks.splice(index, 1);
      }
    }
    /**
     * Get current status
     */
    getStatus() {
      return {
        isInitialized: this.isInitialized,
        isTypebotActive: this.isTypebotActive,
        hasFormData: !!this.currentFormData,
        sessionId: this.typebotClient.sessionId,
        isCompleted: this.typebotClient.isSessionCompleted(),
        callbackCount: this.completionCallbacks.length
      };
    }
    /**
     * Helper function to detect encrypted/placeholder values from Typebot
     */
    isEncryptedValue(value) {
      if (!value || typeof value !== "string") return false;
      const encryptedPatterns = [
        "XBCHzvp1qAbdX",
        "VFChNVSCXQ2rXv4DrJ8Ah",
        "giiLFGw5xXBCHzvp1qAbdX",
        "v3VFChNVSCXQ2rXv4DrJ8Ah"
      ];
      if (encryptedPatterns.some((pattern) => value.includes(pattern))) {
        return true;
      }
      if (value.length > 15 && !/\s/.test(value) && !/[@.]/.test(value)) {
        return true;
      }
      return false;
    }
    /**
     * Close Typebot popup
     */
    closeTypebot(data = {}) {
      try {
        console.log("\u{1F512} [TypebotIntegration] Closing Typebot popup...", data);
        if (this.typebotLibrary && typeof this.typebotLibrary.close === "function") {
          this.typebotLibrary.close();
          console.log("\u2705 [TypebotIntegration] Typebot closed via library method");
        } else if (this.typebotLibrary && typeof this.typebotLibrary.toggle === "function") {
          this.typebotLibrary.toggle();
          console.log("\u2705 [TypebotIntegration] Typebot toggled (closed) via library method");
        } else {
          const typebotContainer = document.querySelector("#typebot-bubble") || document.querySelector('[data-testid="typebot-bubble"]') || document.querySelector(".typebot-container");
          if (typebotContainer) {
            typebotContainer.style.display = "none";
            console.log("\u2705 [TypebotIntegration] Typebot hidden via DOM manipulation");
          } else {
            console.warn("\u26A0\uFE0F [TypebotIntegration] Could not find Typebot container to close");
          }
        }
        this.isTypebotActive = false;
      } catch (error) {
        console.error("\u274C [TypebotIntegration] Error closing Typebot:", error);
      }
    }
    /**
     * Setup fallback navigation listener for direct navigation requests
     */
    setupFallbackNavigationListener() {
      console.log("\u{1F527} [TypebotIntegration] Setting up fallback navigation listener...");
      document.addEventListener("forceNavigateToResults", (event) => {
        console.log("\u{1F3AF} [TypebotIntegration] Fallback navigation event received:", event.detail);
        try {
          if (window.ReinoCalculator && window.ReinoCalculator.navigation && window.ReinoCalculator.navigation.stepNavigation) {
            console.log("\u2705 [TypebotIntegration] Executing fallback navigation to step 4...");
            window.ReinoCalculator.navigation.stepNavigation.showStep(4);
          } else {
            console.warn(
              "\u26A0\uFE0F [TypebotIntegration] ReinoCalculator navigation not available for fallback"
            );
          }
        } catch (error) {
          console.error("\u274C [TypebotIntegration] Fallback navigation failed:", error);
        }
      });
    }
    /**
     * Cleanup
     */
    cleanup() {
      this.completionCallbacks = [];
      this.currentFormData = null;
      this.isTypebotActive = false;
      this.typebotClient.reset();
      const container = document.getElementById(TYPEBOT_CONFIG.EMBED_CONFIG.containerId);
      if (container) {
        container.remove();
      }
    }
  };
})();
//# sourceMappingURL=typebot-integration.js.map
