{"version": 3, "sources": ["../../bin/live-reload.js", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/array.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/clamp.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/format-error-message.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/errors.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/global-config.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/is-numerical-string.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/is-object.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/is-zero-value-string.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/memo.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/noop.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/pipe.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/progress.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/subscription-manager.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/time-conversion.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/velocity-per-second.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/warn-once.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/wrap.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/back.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/anticipate.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/circ.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/ease.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs", "../../node_modules/.pnpm/motion-utils@12.23.6/node_modules/motion-utils/dist/es/easing/utils/map.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/frameloop/order.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/stats/buffer.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/frameloop/render-step.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/frameloop/batcher.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/frameloop/frame.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/stats/animation-count.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/is-css-variable.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/numbers/index.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/sanitize.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/is-nullish.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/single-color-regex.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/color/utils.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/color/rgba.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/color/hex.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/color/hsla.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/color/index.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/color-regex.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/complex/index.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/color/hsla-to-rgba.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/mix/immediate.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/mix/number.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/mix/color.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/mix/visibility.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/mix/complex.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/mix/index.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/drivers/frame.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/utils/velocity.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/spring/defaults.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/spring/find.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/spring/index.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/inertia.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/interpolate.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/offsets/fill.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/offsets/time.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/keyframes.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/replace-transition-type.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/WithPromise.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/JSAnimation.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/utils/fill-wildcards.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/render/dom/parse-transform.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/render/utils/keys-transform.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/utils/unit-conversion.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/render/dom/is-css-var.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/render/dom/style-set.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/supports/flags.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/supports/memo.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/utils/unsupported-easing.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/NativeAnimationExtended.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/is-animatable.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/can-animate.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/make-animation-instant.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/waapi/supports/waapi.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/css-variables-conversion.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/render/utils/keys-position.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/auto.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/test.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/dimensions.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/utils/is-none.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/complex/filter.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/int.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/maps/transform.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/maps/number.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/maps/defaults.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/animatable-none.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/utils/make-none-animatable.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/animation/keyframes/DOMKeyframesResolver.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/get-as-type.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/index.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/frameloop/microtask.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/is-svg-element.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/is-svg-svg-element.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/utils/stagger.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs", "../../node_modules/.pnpm/motion-dom@12.23.12/node_modules/motion-dom/dist/es/value/types/utils/find.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/sequence/create.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/store.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/setters.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/value/use-will-change/is.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/utils/is-transition-defined.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/projection/utils/measure.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/motion/features/definitions.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/projection/geometry/models.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/utils/is-browser.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/VisualElement.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/html/utils/render.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/animate/single-value.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/animate/subject.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/animate/sequence.mjs", "../../node_modules/.pnpm/framer-motion@12.23.12/node_modules/framer-motion/dist/es/animation/animate/index.mjs", "../../src/modules/ativo-color-tag-animation.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n", "const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n", "function formatErrorMessage(message, errorCode) {\n    return errorCode\n        ? `${message}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${errorCode}`\n        : message;\n}\n\nexport { formatErrorMessage };\n", "import { formatErrorMessage } from './format-error-message.mjs';\n\nlet warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message, errorCode) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(formatErrorMessage(message, errorCode));\n        }\n    };\n    invariant = (check, message, errorCode) => {\n        if (!check) {\n            throw new Error(formatErrorMessage(message, errorCode));\n        }\n    };\n}\n\nexport { invariant, warning };\n", "const MotionGlobalConfig = {};\n\nexport { MotionGlobalConfig };\n", "/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n", "function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n", "/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n", "/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n", "/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n", "/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n", "/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n", "import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n", "/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n", "/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n", "import { formatErrorMessage } from './format-error-message.mjs';\n\nconst warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, errorCode) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(formatErrorMessage(message, errorCode));\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n", "const wrap = (min, max, v) => {\n    const rangeSize = max - min;\n    return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\nexport { wrap };\n", "import { noop } from '../noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from Gaë<PERSON>eau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticeably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n", "// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n", "// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n", "import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n", "import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n", "import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n", "import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = /*@__PURE__*/ cubicBezier(0.42, 0, 1, 1);\nconst easeOut = /*@__PURE__*/ cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = /*@__PURE__*/ cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n", "const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n", "import { wrap } from '../../wrap.mjs';\nimport { isEasingArray } from './is-easing-array.mjs';\n\nfunction getEasingForSegment(easing, i) {\n    return isEasingArray(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\n\nexport { getEasingForSegment };\n", "const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n", "import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`, \"cubic-bezier-length\");\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`, \"invalid-easing-type\");\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n", "const stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\nexport { stepsOrder };\n", "const statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\nexport { statsBuffer };\n", "import { statsBuffer } from '../stats/buffer.mjs';\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && statsBuffer.value) {\n                statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher };\n", "import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, frameSteps };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming\n                ? frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\nexport { time };\n", "const activeAnimations = {\n    layout: 0,\n    mainThread: 0,\n    waapi: 0,\n};\n\nexport { activeAnimations };\n", "const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = \n/*@__PURE__*/ checkStringStartsWith(\"--\");\nconst startsAsVariableToken = \n/*@__PURE__*/ checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\nexport { isCSSVariableName, isCSSVariableToken };\n", "import { clamp } from 'motion-utils';\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => clamp(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\nexport { alpha, number, scale };\n", "// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\n\nexport { sanitize };\n", "const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\nexport { floatRegex };\n", "function isNullish(v) {\n    return v == null;\n}\n\nexport { isNullish };\n", "const singleColorRegex = /^(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))$/iu;\n\nexport { singleColorRegex };\n", "import { floatRegex } from '../utils/float-regex.mjs';\nimport { isNullish } from '../utils/is-nullish.mjs';\nimport { singleColorRegex } from '../utils/single-color-regex.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((typeof v === \"string\" &&\n        singleColorRegex.test(v) &&\n        v.startsWith(type)) ||\n        (testProp &&\n            !isNullish(v) &&\n            Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (typeof v !== \"string\")\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n", "import { clamp } from 'motion-utils';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = (v) => clamp(0, 255, v);\nconst rgbUnit = {\n    ...number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: /*@__PURE__*/ isColorString(\"rgb\", \"red\"),\n    parse: /*@__PURE__*/ splitColor(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        sanitize(alpha.transform(alpha$1)) +\n        \")\",\n};\n\nexport { rgbUnit, rgba };\n", "import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\n\nfunction parseHex(v) {\n    let r = \"\";\n    let g = \"\";\n    let b = \"\";\n    let a = \"\";\n    // If we have 6 characters, ie #FF0000\n    if (v.length > 5) {\n        r = v.substring(1, 3);\n        g = v.substring(3, 5);\n        b = v.substring(5, 7);\n        a = v.substring(7, 9);\n        // Or we have 3 characters, ie #F00\n    }\n    else {\n        r = v.substring(1, 2);\n        g = v.substring(2, 3);\n        b = v.substring(3, 4);\n        a = v.substring(4, 5);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: /*@__PURE__*/ isColorString(\"#\"),\n    parse: parseHex,\n    transform: rgba.transform,\n};\n\nexport { hex };\n", "/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/ (() => ({\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n}))();\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n", "import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: /*@__PURE__*/ isColorString(\"hsl\", \"hue\"),\n    parse: /*@__PURE__*/ splitColor(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            percent.transform(sanitize(saturation)) +\n            \", \" +\n            percent.transform(sanitize(lightness)) +\n            \", \" +\n            sanitize(alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\nexport { hsla };\n", "import { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\n\nconst color = {\n    test: (v) => rgba.test(v) || hex.test(v) || hsla.test(v),\n    parse: (v) => {\n        if (rgba.test(v)) {\n            return rgba.parse(v);\n        }\n        else if (hsla.test(v)) {\n            return hsla.parse(v);\n        }\n        else {\n            return hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return typeof v === \"string\"\n            ? v\n            : v.hasOwnProperty(\"red\")\n                ? rgba.transform(v)\n                : hsla.transform(v);\n    },\n    getAnimatableNone: (v) => {\n        const parsed = color.parse(v);\n        parsed.alpha = 0;\n        return color.transform(parsed);\n    },\n};\n\nexport { color };\n", "const colorRegex = /(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))/giu;\n\nexport { colorRegex };\n", "import { color } from '../color/index.mjs';\nimport { colorRegex } from '../utils/color-regex.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\n\nfunction test(v) {\n    return (isNaN(v) &&\n        typeof v === \"string\" &&\n        (v.match(floatRegex)?.length || 0) +\n            (v.match(colorRegex)?.length || 0) >\n            0);\n}\nconst NUMBER_TOKEN = \"number\";\nconst COLOR_TOKEN = \"color\";\nconst VAR_TOKEN = \"var\";\nconst VAR_FUNCTION_TOKEN = \"var(\";\nconst SPLIT_TOKEN = \"${}\";\n// this regex consists of the `singleCssVariableRegex|rgbHSLValueRegex|digitRegex`\nconst complexRegex = /var\\s*\\(\\s*--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)|#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\)|-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/giu;\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const values = [];\n    const indexes = {\n        color: [],\n        number: [],\n        var: [],\n    };\n    const types = [];\n    let i = 0;\n    const tokenised = originalValue.replace(complexRegex, (parsedValue) => {\n        if (color.test(parsedValue)) {\n            indexes.color.push(i);\n            types.push(COLOR_TOKEN);\n            values.push(color.parse(parsedValue));\n        }\n        else if (parsedValue.startsWith(VAR_FUNCTION_TOKEN)) {\n            indexes.var.push(i);\n            types.push(VAR_TOKEN);\n            values.push(parsedValue);\n        }\n        else {\n            indexes.number.push(i);\n            types.push(NUMBER_TOKEN);\n            values.push(parseFloat(parsedValue));\n        }\n        ++i;\n        return SPLIT_TOKEN;\n    });\n    const split = tokenised.split(SPLIT_TOKEN);\n    return { values, split, indexes, types };\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { split, types } = analyseComplexValue(source);\n    const numSections = split.length;\n    return (v) => {\n        let output = \"\";\n        for (let i = 0; i < numSections; i++) {\n            output += split[i];\n            if (v[i] !== undefined) {\n                const type = types[i];\n                if (type === NUMBER_TOKEN) {\n                    output += sanitize(v[i]);\n                }\n                else if (type === COLOR_TOKEN) {\n                    output += color.transform(v[i]);\n                }\n                else {\n                    output += v[i];\n                }\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : color.test(v) ? color.getAnimatableNone(v) : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\nexport { analyseComplexValue, complex };\n", "// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\nexport { hslaToRgba };\n", "function mixImmediate(a, b) {\n    return (p) => (p > 0 ? b : a);\n}\n\nexport { mixImmediate };\n", "/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mixNumber = (from, to, progress) => {\n    return from + (to - from) * progress;\n};\n\nexport { mixNumber };\n", "import { warning } from 'motion-utils';\nimport { hex } from '../../value/types/color/hex.mjs';\nimport { hsla } from '../../value/types/color/hsla.mjs';\nimport { hslaToRgba } from '../../value/types/color/hsla-to-rgba.mjs';\nimport { rgba } from '../../value/types/color/rgba.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber } from './number.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const expo = v * (to * to - fromExpo) + fromExpo;\n    return expo < 0 ? 0 : Math.sqrt(expo);\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    warning(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`, \"color-not-animatable\");\n    if (!Boolean(type))\n        return false;\n    let model = type.parse(color);\n    if (type === hsla) {\n        // TODO Remove this cast - needed since Motion's stricter typing\n        model = hslaToRgba(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    if (!fromRGBA || !toRGBA) {\n        return mixImmediate(from, to);\n    }\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = mixNumber(fromRGBA.alpha, toRGBA.alpha, v);\n        return rgba.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n", "const invisibleValues = new Set([\"none\", \"hidden\"]);\n/**\n * Returns a function that, when provided a progress value between 0 and 1,\n * will return the \"none\" or \"hidden\" string only when the progress is that of\n * the origin or target.\n */\nfunction mixVisibility(origin, target) {\n    if (invisibleValues.has(origin)) {\n        return (p) => (p <= 0 ? origin : target);\n    }\n    else {\n        return (p) => (p >= 1 ? target : origin);\n    }\n}\n\nexport { invisibleValues, mixVisibility };\n", "import { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\n\nfunction mixNumber(a, b) {\n    return (p) => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n    if (typeof a === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof a === \"string\") {\n        return isCSSVariableToken(a)\n            ? mixImmediate\n            : color.test(a)\n                ? mixColor\n                : mixComplex;\n    }\n    else if (Array.isArray(a)) {\n        return mixArray;\n    }\n    else if (typeof a === \"object\") {\n        return color.test(a) ? mixColor : mixObject;\n    }\n    return mixImmediate;\n}\nfunction mixArray(a, b) {\n    const output = [...a];\n    const numValues = output.length;\n    const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n    return (p) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](p);\n        }\n        return output;\n    };\n}\nfunction mixObject(a, b) {\n    const output = { ...a, ...b };\n    const blendValue = {};\n    for (const key in output) {\n        if (a[key] !== undefined && b[key] !== undefined) {\n            blendValue[key] = getMixer(a[key])(a[key], b[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n}\nfunction matchOrder(origin, target) {\n    const orderedOrigin = [];\n    const pointers = { color: 0, var: 0, number: 0 };\n    for (let i = 0; i < target.values.length; i++) {\n        const type = target.types[i];\n        const originIndex = origin.indexes[type][pointers[type]];\n        const originValue = origin.values[originIndex] ?? 0;\n        orderedOrigin[i] = originValue;\n        pointers[type]++;\n    }\n    return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length &&\n        originStats.indexes.color.length === targetStats.indexes.color.length &&\n        originStats.indexes.number.length >= targetStats.indexes.number.length;\n    if (canInterpolate) {\n        if ((invisibleValues.has(origin) &&\n            !targetStats.values.length) ||\n            (invisibleValues.has(target) &&\n                !originStats.values.length)) {\n            return mixVisibility(origin, target);\n        }\n        return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`, \"complex-values-different\");\n        return mixImmediate(origin, target);\n    }\n};\n\nexport { getMixer, mixArray, mixComplex, mixObject };\n", "import { getMixer } from './complex.mjs';\nimport { mixNumber } from './number.mjs';\n\nfunction mix(from, to, p) {\n    if (typeof from === \"number\" &&\n        typeof to === \"number\" &&\n        typeof p === \"number\") {\n        return mixNumber(from, to, p);\n    }\n    const mixer = getMixer(from);\n    return mixer(from, to);\n}\n\nexport { mix };\n", "import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: (keepAlive = true) => frame.update(passTimestamp, keepAlive),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => (frameData.isProcessing ? frameData.timestamp : time.now()),\n    };\n};\n\nexport { frameloopDriver };\n", "const generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n", "/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\nexport { calcGeneratorDuration, maxGeneratorDuration };\n", "import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n", "import { velocityPerSecond } from 'motion-utils';\n\nconst velocitySampleDuration = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - velocitySampleDuration, 0);\n    return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\n\nexport { calcGeneratorVelocity };\n", "const springDefaults = {\n    // Default spring physics\n    stiffness: 100,\n    damping: 10,\n    mass: 1.0,\n    velocity: 0.0,\n    // Default duration/bounce-based options\n    duration: 800, // in ms\n    bounce: 0.3,\n    visualDuration: 0.3, // in seconds\n    // Rest thresholds\n    restSpeed: {\n        granular: 0.01,\n        default: 2,\n    },\n    restDelta: {\n        granular: 0.005,\n        default: 0.5,\n    },\n    // Limits\n    minDuration: 0.01, // in seconds\n    maxDuration: 10.0, // in seconds\n    minDamping: 0.05,\n    maxDamping: 1,\n};\n\nexport { springDefaults };\n", "import { warning, secondsToMilliseconds, clamp, millisecondsToSeconds } from 'motion-utils';\nimport { springDefaults } from './defaults.mjs';\n\nconst safeMin = 0.001;\nfunction findSpring({ duration = springDefaults.duration, bounce = springDefaults.bounce, velocity = springDefaults.velocity, mass = springDefaults.mass, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= secondsToMilliseconds(springDefaults.maxDuration), \"Spring duration must be 10 seconds or less\", \"spring-duration-limit\");\n    let dampingRatio = 1 - bounce;\n    /**\n     * Restrict dampingRatio and duration to within acceptable ranges.\n     */\n    dampingRatio = clamp(springDefaults.minDamping, springDefaults.maxDamping, dampingRatio);\n    duration = clamp(springDefaults.minDuration, springDefaults.maxDuration, millisecondsToSeconds(duration));\n    if (dampingRatio < 1) {\n        /**\n         * Underdamped spring\n         */\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        /**\n         * Critically-damped spring\n         */\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = secondsToMilliseconds(duration);\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: springDefaults.stiffness,\n            damping: springDefaults.damping,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring };\n", "import { millisecondsToSeconds, secondsToMilliseconds, clamp } from 'motion-utils';\nimport { generateLinearEasing } from '../../waapi/utils/linear.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../utils/calc-duration.mjs';\nimport { createGeneratorEasing } from '../utils/create-generator-easing.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { springDefaults } from './defaults.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = {\n        velocity: springDefaults.velocity,\n        stiffness: springDefaults.stiffness,\n        damping: springDefaults.damping,\n        mass: springDefaults.mass,\n        isResolvedFromDuration: false,\n        ...options,\n    };\n    // stiffness/damping/mass overrides duration/bounce\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        if (options.visualDuration) {\n            const visualDuration = options.visualDuration;\n            const root = (2 * Math.PI) / (visualDuration * 1.2);\n            const stiffness = root * root;\n            const damping = 2 *\n                clamp(0.05, 1, 1 - (options.bounce || 0)) *\n                Math.sqrt(stiffness);\n            springOptions = {\n                ...springOptions,\n                mass: springDefaults.mass,\n                stiffness,\n                damping,\n            };\n        }\n        else {\n            const derived = findSpring(options);\n            springOptions = {\n                ...springOptions,\n                ...derived,\n                mass: springDefaults.mass,\n            };\n            springOptions.isResolvedFromDuration = true;\n        }\n    }\n    return springOptions;\n}\nfunction spring(optionsOrVisualDuration = springDefaults.visualDuration, bounce = springDefaults.bounce) {\n    const options = typeof optionsOrVisualDuration !== \"object\"\n        ? {\n            visualDuration: optionsOrVisualDuration,\n            keyframes: [0, 1],\n            bounce,\n        }\n        : optionsOrVisualDuration;\n    let { restSpeed, restDelta } = options;\n    const origin = options.keyframes[0];\n    const target = options.keyframes[options.keyframes.length - 1];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    const { stiffness, damping, mass, duration, velocity, isResolvedFromDuration, } = getSpringOptions({\n        ...options,\n        velocity: -millisecondsToSeconds(options.velocity || 0),\n    });\n    const initialVelocity = velocity || 0.0;\n    const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n    const initialDelta = target - origin;\n    const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n    /**\n     * If we're working on a granular scale, use smaller defaults for determining\n     * when the spring is finished.\n     *\n     * These defaults have been selected emprically based on what strikes a good\n     * ratio between feeling good and finishing as soon as changes are imperceptible.\n     */\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale\n        ? springDefaults.restSpeed.granular\n        : springDefaults.restSpeed.default);\n    restDelta || (restDelta = isGranularScale\n        ? springDefaults.restDelta.granular\n        : springDefaults.restDelta.default);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n        // Underdamped spring\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            return (target -\n                envelope *\n                    (((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) /\n                        angularFreq) *\n                        Math.sin(angularFreq * t) +\n                        initialDelta * Math.cos(angularFreq * t)));\n        };\n    }\n    else if (dampingRatio === 1) {\n        // Critically damped spring\n        resolveSpring = (t) => target -\n            Math.exp(-undampedAngularFreq * t) *\n                (initialDelta +\n                    (initialVelocity + undampedAngularFreq * initialDelta) * t);\n    }\n    else {\n        // Overdamped spring\n        const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            // When performing sinh or cosh values can hit Infinity so we cap them here\n            const freqForT = Math.min(dampedAngularFreq * t, 300);\n            return (target -\n                (envelope *\n                    ((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) *\n                        Math.sinh(freqForT) +\n                        dampedAngularFreq *\n                            initialDelta *\n                            Math.cosh(freqForT))) /\n                    dampedAngularFreq);\n        };\n    }\n    const generator = {\n        calculatedDuration: isResolvedFromDuration ? duration || null : null,\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                let currentVelocity = t === 0 ? initialVelocity : 0.0;\n                /**\n                 * We only need to calculate velocity for under-damped springs\n                 * as over- and critically-damped springs can't overshoot, so\n                 * checking only for displacement is enough.\n                 */\n                if (dampingRatio < 1) {\n                    currentVelocity =\n                        t === 0\n                            ? secondsToMilliseconds(initialVelocity)\n                            : calcGeneratorVelocity(resolveSpring, t, current);\n                }\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? target : current;\n            return state;\n        },\n        toString: () => {\n            const calculatedDuration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n            const easing = generateLinearEasing((progress) => generator.next(calculatedDuration * progress).value, calculatedDuration, 30);\n            return calculatedDuration + \"ms \" + easing;\n        },\n        toTransition: () => { },\n    };\n    return generator;\n}\nspring.applyToOptions = (options) => {\n    const generatorOptions = createGeneratorEasing(options, 100, spring);\n    options.ease = generatorOptions.ease;\n    options.duration = secondsToMilliseconds(generatorOptions.duration);\n    options.type = \"keyframes\";\n    return options;\n};\n\nexport { spring };\n", "import { spring } from './spring/index.mjs';\nimport { calcGeneratorVelocity } from './utils/velocity.mjs';\n\nfunction inertia({ keyframes, velocity = 0.0, power = 0.8, timeConstant = 325, bounceDamping = 10, bounceStiffness = 500, modifyTarget, min, max, restDelta = 0.5, restSpeed, }) {\n    const origin = keyframes[0];\n    const state = {\n        done: false,\n        value: origin,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = origin + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - origin;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / timeConstant);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDelta;\n        state.value = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.value))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = spring({\n            keyframes: [state.value, nearestBoundary(state.value)],\n            velocity: calcGeneratorVelocity(calcLatest, t, state.value), // TODO: This should be passing * 1000\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDelta,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return {\n        calculatedDuration: null,\n        next: (t) => {\n            /**\n             * We need to resolve the friction to figure out if we need a\n             * spring but we don't want to do this twice per frame. So here\n             * we flag if we updated for this frame and later if we did\n             * we can skip doing it again.\n             */\n            let hasUpdatedFrame = false;\n            if (!spring$1 && timeReachedBoundary === undefined) {\n                hasUpdatedFrame = true;\n                applyFriction(t);\n                checkCatchBoundary(t);\n            }\n            /**\n             * If we have a spring and the provided t is beyond the moment the friction\n             * animation crossed the min/max boundary, use the spring.\n             */\n            if (timeReachedBoundary !== undefined && t >= timeReachedBoundary) {\n                return spring$1.next(t - timeReachedBoundary);\n            }\n            else {\n                !hasUpdatedFrame && applyFriction(t);\n                return state;\n            }\n        },\n    };\n}\n\nexport { inertia };\n", "import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\", \"range-length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && output[0] === output[1])\n        return () => output[1];\n    const isZeroDeltaRange = input[0] === input[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        if (isZeroDeltaRange && v < input[0])\n            return output[0];\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n", "import { progress } from 'motion-utils';\nimport { mixNumber } from '../../../utils/mix/number.mjs';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mixNumber(min, 1, offsetProgress));\n    }\n}\n\nexport { fillOffset };\n", "import { fillOffset } from './fill.mjs';\n\nfunction defaultOffset(arr) {\n    const offset = [0];\n    fillOffset(offset, arr.length - 1);\n    return offset;\n}\n\nexport { defaultOffset };\n", "function convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\n\nexport { convertOffsetToTimes };\n", "import { easeInOut, isEasingArray, easingDefinitionToFunction } from 'motion-utils';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { defaultOffset } from '../keyframes/offsets/default.mjs';\nimport { convertOffsetToTimes } from '../keyframes/offsets/time.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction keyframes({ duration = 300, keyframes: keyframeValues, times, ease = \"easeInOut\", }) {\n    /**\n     * Easing functions can be externally defined as strings. Here we convert them\n     * into actual functions.\n     */\n    const easingFunctions = isEasingArray(ease)\n        ? ease.map(easingDefinitionToFunction)\n        : easingDefinitionToFunction(ease);\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = {\n        done: false,\n        value: keyframeValues[0],\n    };\n    /**\n     * Create a times array based on the provided 0-1 offsets\n     */\n    const absoluteTimes = convertOffsetToTimes(\n    // Only use the provided offsets if they're the correct length\n    // TODO Maybe we should warn here if there's a length mismatch\n    times && times.length === keyframeValues.length\n        ? times\n        : defaultOffset(keyframeValues), duration);\n    const mapTimeToKeyframe = interpolate(absoluteTimes, keyframeValues, {\n        ease: Array.isArray(easingFunctions)\n            ? easingFunctions\n            : defaultEasing(keyframeValues, easingFunctions),\n    });\n    return {\n        calculatedDuration: duration,\n        next: (t) => {\n            state.value = mapTimeToKeyframe(t);\n            state.done = t >= duration;\n            return state;\n        },\n    };\n}\n\nexport { defaultEasing, keyframes };\n", "const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe, speed = 1) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const useFirstKeyframe = speed < 0 || (repeat && repeatType !== \"loop\" && repeat % 2 === 1);\n    const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n", "import { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { spring } from '../generators/spring/index.mjs';\n\nconst transitionTypeMap = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\nfunction replaceTransitionType(transition) {\n    if (typeof transition.type === \"string\") {\n        transition.type = transitionTypeMap[transition.type];\n    }\n}\n\nexport { replaceTransitionType };\n", "class WithPromise {\n    constructor() {\n        this.updateFinished();\n    }\n    get finished() {\n        return this._finished;\n    }\n    updateFinished() {\n        this._finished = new Promise((resolve) => {\n            this.resolve = resolve;\n        });\n    }\n    notifyFinished() {\n        this.resolve();\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve, onReject);\n    }\n}\n\nexport { WithPromise };\n", "import { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\n\nconst percentToProgress = (percent) => percent / 100;\nclass JSAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.state = \"idle\";\n        this.startTime = null;\n        this.isStopped = false;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            const { motionValue } = this.options;\n            if (motionValue && motionValue.updatedAt !== time.now()) {\n                this.tick(time.now());\n            }\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            this.options.onStop?.();\n        };\n        activeAnimations.mainThread++;\n        this.options = options;\n        this.initAnimation();\n        this.play();\n        if (options.autoplay === false)\n            this.pause();\n    }\n    initAnimation() {\n        const { options } = this;\n        replaceTransitionType(options);\n        const { type = keyframes, repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = options;\n        let { keyframes: keyframes$1 } = options;\n        const generatorFactory = type || keyframes;\n        if (process.env.NODE_ENV !== \"production\" &&\n            generatorFactory !== keyframes) {\n            invariant(keyframes$1.length <= 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`, \"spring-two-frames\");\n        }\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            this.mirroredGenerator = generatorFactory({\n                ...options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        this.calculatedDuration = calculatedDuration;\n        this.resolvedDuration = calculatedDuration + repeatDelay;\n        this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n        this.generator = generator;\n    }\n    updateTime(timestamp) {\n        const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n        // Update currentTime\n        if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime = animationTime;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { generator, totalDuration, mixKeyframes, mirroredGenerator, resolvedDuration, calculatedDuration, } = this;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay = 0, keyframes, repeat, repeatType, repeatDelay, type, onUpdate, finalKeyframe, } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else {\n            this.updateTime(timestamp);\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.playbackSpeed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mixKeyframes) {\n            state.value = mixKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.playbackSpeed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        // TODO: The exception for inertia could be cleaner here\n        if (isAnimationFinished && type !== inertia) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    /**\n     * Allows the returned animation to be awaited or promise-chained. Currently\n     * resolves when the animation finishes at all but in a future update could/should\n     * reject if its cancels.\n     */\n    then(resolve, reject) {\n        return this.finished.then(resolve, reject);\n    }\n    get duration() {\n        return millisecondsToSeconds(this.calculatedDuration);\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.startTime === null ||\n            this.holdTime !== null ||\n            this.playbackSpeed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n        }\n        this.driver?.start(false);\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        this.updateTime(time.now());\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, startTime } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        this.options.onPlay?.();\n        const now = this.driver.now();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n            this.startTime = now;\n        }\n        else if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = startTime ?? now;\n        }\n        if (this.state === \"finished\" && this.speed < 0) {\n            this.startTime += this.calculatedDuration;\n        }\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        this.state = \"paused\";\n        this.updateTime(time.now());\n        this.holdTime = this.currentTime;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.notifyFinished();\n        this.teardown();\n        this.state = \"finished\";\n        this.options.onComplete?.();\n    }\n    cancel() {\n        this.holdTime = null;\n        this.startTime = 0;\n        this.tick(0);\n        this.teardown();\n        this.options.onCancel?.();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.startTime = this.holdTime = null;\n        activeAnimations.mainThread--;\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(sampleTime) {\n        this.startTime = 0;\n        return this.tick(sampleTime, true);\n    }\n    attachTimeline(timeline) {\n        if (this.options.allowFlatten) {\n            this.options.type = \"keyframes\";\n            this.options.ease = \"linear\";\n            this.initAnimation();\n        }\n        this.driver?.stop();\n        return timeline.observe(this);\n    }\n}\n// Legacy function support\nfunction animateValue(options) {\n    return new JSAnimation(options);\n}\n\nexport { JSAnimation, animateValue };\n", "function fillWildcards(keyframes) {\n    for (let i = 1; i < keyframes.length; i++) {\n        keyframes[i] ?? (keyframes[i] = keyframes[i - 1]);\n    }\n}\n\nexport { fillWildcards };\n", "const radToDeg = (rad) => (rad * 180) / Math.PI;\nconst rotate = (v) => {\n    const angle = radToDeg(Math.atan2(v[1], v[0]));\n    return rebaseAngle(angle);\n};\nconst matrix2dParsers = {\n    x: 4,\n    y: 5,\n    translateX: 4,\n    translateY: 5,\n    scaleX: 0,\n    scaleY: 3,\n    scale: (v) => (Math.abs(v[0]) + Math.abs(v[3])) / 2,\n    rotate,\n    rotateZ: rotate,\n    skewX: (v) => radToDeg(Math.atan(v[1])),\n    skewY: (v) => radToDeg(Math.atan(v[2])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[2])) / 2,\n};\nconst rebaseAngle = (angle) => {\n    angle = angle % 360;\n    if (angle < 0)\n        angle += 360;\n    return angle;\n};\nconst rotateZ = rotate;\nconst scaleX = (v) => Math.sqrt(v[0] * v[0] + v[1] * v[1]);\nconst scaleY = (v) => Math.sqrt(v[4] * v[4] + v[5] * v[5]);\nconst matrix3dParsers = {\n    x: 12,\n    y: 13,\n    z: 14,\n    translateX: 12,\n    translateY: 13,\n    translateZ: 14,\n    scaleX,\n    scaleY,\n    scale: (v) => (scaleX(v) + scaleY(v)) / 2,\n    rotateX: (v) => rebaseAngle(radToDeg(Math.atan2(v[6], v[5]))),\n    rotateY: (v) => rebaseAngle(radToDeg(Math.atan2(-v[2], v[0]))),\n    rotateZ,\n    rotate: rotateZ,\n    skewX: (v) => radToDeg(Math.atan(v[4])),\n    skewY: (v) => radToDeg(Math.atan(v[1])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[4])) / 2,\n};\nfunction defaultTransformValue(name) {\n    return name.includes(\"scale\") ? 1 : 0;\n}\nfunction parseValueFromTransform(transform, name) {\n    if (!transform || transform === \"none\") {\n        return defaultTransformValue(name);\n    }\n    const matrix3dMatch = transform.match(/^matrix3d\\(([-\\d.e\\s,]+)\\)$/u);\n    let parsers;\n    let match;\n    if (matrix3dMatch) {\n        parsers = matrix3dParsers;\n        match = matrix3dMatch;\n    }\n    else {\n        const matrix2dMatch = transform.match(/^matrix\\(([-\\d.e\\s,]+)\\)$/u);\n        parsers = matrix2dParsers;\n        match = matrix2dMatch;\n    }\n    if (!match) {\n        return defaultTransformValue(name);\n    }\n    const valueParser = parsers[name];\n    const values = match[1].split(\",\").map(convertTransformToNumber);\n    return typeof valueParser === \"function\"\n        ? valueParser(values)\n        : values[valueParser];\n}\nconst readTransformValue = (instance, name) => {\n    const { transform = \"none\" } = getComputedStyle(instance);\n    return parseValueFromTransform(transform, name);\n};\nfunction convertTransformToNumber(value) {\n    return parseFloat(value.trim());\n}\n\nexport { defaultTransformValue, parseValueFromTransform, readTransformValue };\n", "/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\n    \"transformPerspective\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"translateX\",\n    \"translateY\",\n    \"translateZ\",\n    \"scale\",\n    \"scaleX\",\n    \"scaleY\",\n    \"rotate\",\n    \"rotateX\",\n    \"rotateY\",\n    \"rotateZ\",\n    \"skew\",\n    \"skewX\",\n    \"skewY\",\n];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = /*@__PURE__*/ (() => new Set(transformPropOrder))();\n\nexport { transformPropOrder, transformProps };\n", "import { parseValueFromTransform } from '../../../render/dom/parse-transform.mjs';\nimport { transformPropOrder } from '../../../render/utils/keys-transform.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst isNumOrPxType = (v) => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: (_bbox, { transform }) => parseValueFromTransform(transform, \"x\"),\n    y: (_bbox, { transform }) => parseValueFromTransform(transform, \"y\"),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\n\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };\n", "import { fillWildcards } from './utils/fill-wildcards.mjs';\nimport { removeNonTranslationalTransform } from './utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nlet isForced = false;\nfunction measureAllKeyframes() {\n    if (anyNeedsMeasurement) {\n        const resolversToMeasure = Array.from(toResolve).filter((resolver) => resolver.needsMeasurement);\n        const elementsToMeasure = new Set(resolversToMeasure.map((resolver) => resolver.element));\n        const transformsToRestore = new Map();\n        /**\n         * Write pass\n         * If we're measuring elements we want to remove bounding box-changing transforms.\n         */\n        elementsToMeasure.forEach((element) => {\n            const removedTransforms = removeNonTranslationalTransform(element);\n            if (!removedTransforms.length)\n                return;\n            transformsToRestore.set(element, removedTransforms);\n            element.render();\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureInitialState());\n        // Write\n        elementsToMeasure.forEach((element) => {\n            element.render();\n            const restore = transformsToRestore.get(element);\n            if (restore) {\n                restore.forEach(([key, value]) => {\n                    element.getValue(key)?.set(value);\n                });\n            }\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureEndState());\n        // Write\n        resolversToMeasure.forEach((resolver) => {\n            if (resolver.suspendedScrollY !== undefined) {\n                window.scrollTo(0, resolver.suspendedScrollY);\n            }\n        });\n    }\n    anyNeedsMeasurement = false;\n    isScheduled = false;\n    toResolve.forEach((resolver) => resolver.complete(isForced));\n    toResolve.clear();\n}\nfunction readAllKeyframes() {\n    toResolve.forEach((resolver) => {\n        resolver.readKeyframes();\n        if (resolver.needsMeasurement) {\n            anyNeedsMeasurement = true;\n        }\n    });\n}\nfunction flushKeyframeResolvers() {\n    isForced = true;\n    readAllKeyframes();\n    measureAllKeyframes();\n    isForced = false;\n}\nclass KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n        this.state = \"pending\";\n        /**\n         * Track whether this resolver is async. If it is, it'll be added to the\n         * resolver queue and flushed in the next frame. Resolvers that aren't going\n         * to trigger read/write thrashing don't need to be async.\n         */\n        this.isAsync = false;\n        /**\n         * Track whether this resolver needs to perform a measurement\n         * to resolve its keyframes.\n         */\n        this.needsMeasurement = false;\n        this.unresolvedKeyframes = [...unresolvedKeyframes];\n        this.onComplete = onComplete;\n        this.name = name;\n        this.motionValue = motionValue;\n        this.element = element;\n        this.isAsync = isAsync;\n    }\n    scheduleResolve() {\n        this.state = \"scheduled\";\n        if (this.isAsync) {\n            toResolve.add(this);\n            if (!isScheduled) {\n                isScheduled = true;\n                frame.read(readAllKeyframes);\n                frame.resolveKeyframes(measureAllKeyframes);\n            }\n        }\n        else {\n            this.readKeyframes();\n            this.complete();\n        }\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, name, element, motionValue } = this;\n        // If initial keyframe is null we need to read it from the DOM\n        if (unresolvedKeyframes[0] === null) {\n            const currentValue = motionValue?.get();\n            // TODO: This doesn't work if the final keyframe is a wildcard\n            const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n            if (currentValue !== undefined) {\n                unresolvedKeyframes[0] = currentValue;\n            }\n            else if (element && name) {\n                const valueAsRead = element.readValue(name, finalKeyframe);\n                if (valueAsRead !== undefined && valueAsRead !== null) {\n                    unresolvedKeyframes[0] = valueAsRead;\n                }\n            }\n            if (unresolvedKeyframes[0] === undefined) {\n                unresolvedKeyframes[0] = finalKeyframe;\n            }\n            if (motionValue && currentValue === undefined) {\n                motionValue.set(unresolvedKeyframes[0]);\n            }\n        }\n        fillWildcards(unresolvedKeyframes);\n    }\n    setFinalKeyframe() { }\n    measureInitialState() { }\n    renderEndStyles() { }\n    measureEndState() { }\n    complete(isForcedComplete = false) {\n        this.state = \"complete\";\n        this.onComplete(this.unresolvedKeyframes, this.finalKeyframe, isForcedComplete);\n        toResolve.delete(this);\n    }\n    cancel() {\n        if (this.state === \"scheduled\") {\n            toResolve.delete(this);\n            this.state = \"pending\";\n        }\n    }\n    resume() {\n        if (this.state === \"pending\")\n            this.scheduleResolve();\n    }\n}\n\nexport { KeyframeResolver, flushKeyframeResolvers };\n", "const isCSSVar = (name) => name.startsWith(\"--\");\n\nexport { isCSSVar };\n", "import { isCSSVar } from './is-css-var.mjs';\n\nfunction setStyle(element, name, value) {\n    isCSSVar(name)\n        ? element.style.setProperty(name, value)\n        : (element.style[name] = value);\n}\n\nexport { setStyle };\n", "import { memo } from 'motion-utils';\n\nconst supportsScrollTimeline = /* @__PURE__ */ memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n", "/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\nexport { supportsFlags };\n", "import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = memo(callback);\n    return () => supportsFlags[supportsFlag] ?? memoized();\n}\n\nexport { memoSupports };\n", "import { memoSupports } from './memo.mjs';\n\nconst supportsLinearEasing = /*@__PURE__*/ memoSupports(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\nexport { supportsLinearEasing };\n", "const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n", "import { cubicBezierAsString } from './cubic-bezier.mjs';\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\n\nexport { supportedWaapiEasing };\n", "import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from '../utils/linear.mjs';\nimport { cubicBezierAsString } from './cubic-bezier.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\") {\n        return supportsLinearEasing()\n            ? generateLinearEasing(easing, duration)\n            : \"ease-out\";\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { mapEasingToNativeEasing };\n", "import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (statsBuffer.value) {\n        activeAnimations.waapi++;\n    }\n    const options = {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    };\n    if (pseudoElement)\n        options.pseudoElement = pseudoElement;\n    const animation = element.animate(keyframeOptions, options);\n    if (statsBuffer.value) {\n        animation.finished.finally(() => {\n            activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\nexport { startWaapiAnimation };\n", "function isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\nexport { isGenerator };\n", "import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type) && supportsLinearEasing()) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n", "import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { setStyle } from '../render/dom/style-set.mjs';\nimport { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.finishedTime = null;\n        this.isStopped = false;\n        if (!options)\n            return;\n        const { element, name, keyframes, pseudoElement, allowFlatten = false, finalKeyframe, onComplete, } = options;\n        this.isPseudoElement = Boolean(pseudoElement);\n        this.allowFlatten = allowFlatten;\n        this.options = options;\n        invariant(typeof options.type !== \"string\", `Mini animate() doesn't support \"type\" as a string.`, \"mini-spring\");\n        const transition = applyGeneratorOptions(options);\n        this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.animation.onfinish = () => {\n            this.finishedTime = this.time;\n            if (!pseudoElement) {\n                const keyframe = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n                if (this.updateMotionValue) {\n                    this.updateMotionValue(keyframe);\n                }\n                else {\n                    /**\n                     * If we can, we want to commit the final style as set by the user,\n                     * rather than the computed keyframe value supplied by the animation.\n                     */\n                    setStyle(element, name, keyframe);\n                }\n                this.animation.cancel();\n            }\n            onComplete?.();\n            this.notifyFinished();\n        };\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        this.animation.play();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n        }\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish?.();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n    }\n    stop() {\n        if (this.isStopped)\n            return;\n        this.isStopped = true;\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        if (this.updateMotionValue) {\n            this.updateMotionValue();\n        }\n        else {\n            this.commitStyles();\n        }\n        if (!this.isPseudoElement)\n            this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        if (!this.isPseudoElement) {\n            this.animation.commitStyles?.();\n        }\n    }\n    get duration() {\n        const duration = this.animation.effect?.getComputedTiming?.().duration || 0;\n        return millisecondsToSeconds(Number(duration));\n    }\n    get time() {\n        return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.finishedTime = null;\n        this.animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        // Allow backwards playback after finishing\n        if (newSpeed < 0)\n            this.finishedTime = null;\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.finishedTime !== null\n            ? \"finished\"\n            : this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    set startTime(newStartTime) {\n        this.animation.startTime = newStartTime;\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline({ timeline, observe }) {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n        this.animation.onfinish = null;\n        if (timeline && supportsScrollTimeline()) {\n            this.animation.timeline = timeline;\n            return noop;\n        }\n        else {\n            return observe(this);\n        }\n    }\n}\n\nexport { NativeAnimation };\n", "import { anticipate, backInOut, circInOut } from 'motion-utils';\n\nconst unsupportedEasingFunctions = {\n    anticipate,\n    backInOut,\n    circInOut,\n};\nfunction isUnsupportedEase(key) {\n    return key in unsupportedEasingFunctions;\n}\nfunction replaceStringEasing(transition) {\n    if (typeof transition.ease === \"string\" &&\n        isUnsupportedEase(transition.ease)) {\n        transition.ease = unsupportedEasingFunctions[transition.ease];\n    }\n}\n\nexport { replaceStringEasing };\n", "import { secondsToMilliseconds } from 'motion-utils';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { NativeAnimation } from './NativeAnimation.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { replaceStringEasing } from './waapi/utils/unsupported-easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nclass NativeAnimationExtended extends NativeAnimation {\n    constructor(options) {\n        /**\n         * The base NativeAnimation function only supports a subset\n         * of Motion easings, and WAAPI also only supports some\n         * easing functions via string/cubic-bezier definitions.\n         *\n         * This function replaces those unsupported easing functions\n         * with a JS easing function. This will later get compiled\n         * to a linear() easing function.\n         */\n        replaceStringEasing(options);\n        /**\n         * Ensure we replace the transition type with a generator function\n         * before passing to WAAPI.\n         *\n         * TODO: Does this have a better home? It could be shared with\n         * JSAnimation.\n         */\n        replaceTransitionType(options);\n        super(options);\n        if (options.startTime) {\n            this.startTime = options.startTime;\n        }\n        this.options = options;\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * Rather than read commited styles back out of the DOM, we can\n     * create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to calculate velocity for any subsequent animation.\n     */\n    updateMotionValue(value) {\n        const { motionValue, onUpdate, onComplete, element, ...options } = this.options;\n        if (!motionValue)\n            return;\n        if (value !== undefined) {\n            motionValue.set(value);\n            return;\n        }\n        const sampleAnimation = new JSAnimation({\n            ...options,\n            autoplay: false,\n        });\n        const sampleTime = secondsToMilliseconds(this.finishedTime ?? this.time);\n        motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n        sampleAnimation.stop();\n    }\n}\n\nexport { NativeAnimationExtended };\n", "import { complex } from '../../value/types/complex/index.mjs';\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (value, name) => {\n    // If the list of keys that might be non-animatable grows, replace with Set\n    if (name === \"zIndex\")\n        return false;\n    // If it's a number or a keyframes array, we can animate it. We might at some point\n    // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n    // but for now lets leave it like this for performance reasons\n    if (typeof value === \"number\" || Array.isArray(value))\n        return true;\n    if (typeof value === \"string\" && // It's animatable if we have a string\n        (complex.test(value) || value === \"0\") && // And it contains numbers and/or colors\n        !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n    ) {\n        return true;\n    }\n    return false;\n};\n\nexport { isAnimatable };\n", "import { warning } from 'motion-utils';\nimport { isGenerator } from '../generators/utils/is-generator.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\n\nfunction hasKeyframesChanged(keyframes) {\n    const current = keyframes[0];\n    if (keyframes.length === 1)\n        return true;\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] !== current)\n            return true;\n    }\n}\nfunction canAnimate(keyframes, name, type, velocity) {\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    if (originKeyframe === null)\n        return false;\n    /**\n     * These aren't traditionally animatable but we do support them.\n     * In future we could look into making this more generic or replacing\n     * this function with mix() === mixImmediate\n     */\n    if (name === \"display\" || name === \"visibility\")\n        return true;\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(originKeyframe, name);\n    const isTargetAnimatable = isAnimatable(targetKeyframe, name);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${name} from \"${originKeyframe}\" to \"${targetKeyframe}\". \"${isOriginAnimatable ? targetKeyframe : originKeyframe}\" is not an animatable value.`, \"value-not-animatable\");\n    // Always skip if any of these are true\n    if (!isOriginAnimatable || !isTargetAnimatable) {\n        return false;\n    }\n    return (hasKeyframesChanged(keyframes) ||\n        ((type === \"spring\" || isGenerator(type)) && velocity));\n}\n\nexport { canAnimate };\n", "function makeAnimationInstant(options) {\n    options.duration = 0;\n    options.type === \"keyframes\";\n}\n\nexport { makeAnimationInstant };\n", "import { memo } from 'motion-utils';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Could be re-enabled now we have support for linear() easing\n    // \"background-color\"\n]);\nconst supportsWaapi = /*@__PURE__*/ memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\nfunction supportsBrowserAnimation(options) {\n    const { motionValue, name, repeatDelay, repeatType, damping, type } = options;\n    const subject = motionValue?.owner?.current;\n    /**\n     * We use this check instead of isHTMLElement() because we explicitly\n     * **don't** want elements in different timing contexts (i.e. popups)\n     * to be accelerated, as it's not possible to sync these animations\n     * properly with those driven from the main window frameloop.\n     */\n    if (!(subject instanceof HTMLElement)) {\n        return false;\n    }\n    const { onUpdate, transformTemplate } = motionValue.owner.getProps();\n    return (supportsWaapi() &&\n        name &&\n        acceleratedValues.has(name) &&\n        (name !== \"transform\" || !transformTemplate) &&\n        /**\n         * If we're outputting values to onUpdate then we can't use WAAPI as there's\n         * no way to read the value from WAAPI every frame.\n         */\n        !onUpdate &&\n        !repeatDelay &&\n        repeatType !== \"mirror\" &&\n        damping !== 0 &&\n        type !== \"inertia\");\n}\n\nexport { supportsBrowserAnimation };\n", "import { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { makeAnimationInstant } from './utils/make-animation-instant.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n    constructor({ autoplay = true, delay = 0, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", keyframes, name, motionValue, element, ...options }) {\n        super();\n        /**\n         * Bound to support return animation.stop pattern\n         */\n        this.stop = () => {\n            if (this._animation) {\n                this._animation.stop();\n                this.stopTimeline?.();\n            }\n            this.keyframeResolver?.cancel();\n        };\n        this.createdAt = time.now();\n        const optionsWithDefaults = {\n            autoplay,\n            delay,\n            type,\n            repeat,\n            repeatDelay,\n            repeatType,\n            name,\n            motionValue,\n            element,\n            ...options,\n        };\n        const KeyframeResolver$1 = element?.KeyframeResolver || KeyframeResolver;\n        this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n        this.keyframeResolver?.scheduleResolve();\n    }\n    onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n        this.keyframeResolver = undefined;\n        const { name, type, velocity, delay, isHandoff, onUpdate } = options;\n        this.resolvedAt = time.now();\n        /**\n         * If we can't animate this value with the resolved keyframes\n         * then we should complete it immediately.\n         */\n        if (!canAnimate(keyframes, name, type, velocity)) {\n            if (MotionGlobalConfig.instantAnimations || !delay) {\n                onUpdate?.(getFinalKeyframe(keyframes, options, finalKeyframe));\n            }\n            keyframes[0] = keyframes[keyframes.length - 1];\n            makeAnimationInstant(options);\n            options.repeat = 0;\n        }\n        /**\n         * Resolve startTime for the animation.\n         *\n         * This method uses the createdAt and resolvedAt to calculate the\n         * animation startTime. *Ideally*, we would use the createdAt time as t=0\n         * as the following frame would then be the first frame of the animation in\n         * progress, which would feel snappier.\n         *\n         * However, if there's a delay (main thread work) between the creation of\n         * the animation and the first commited frame, we prefer to use resolvedAt\n         * to avoid a sudden jump into the animation.\n         */\n        const startTime = sync\n            ? !this.resolvedAt\n                ? this.createdAt\n                : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY\n                    ? this.resolvedAt\n                    : this.createdAt\n            : undefined;\n        const resolvedOptions = {\n            startTime,\n            finalKeyframe,\n            ...options,\n            keyframes,\n        };\n        /**\n         * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions)\n            ? new NativeAnimationExtended({\n                ...resolvedOptions,\n                element: resolvedOptions.motionValue.owner.current,\n            })\n            : new JSAnimation(resolvedOptions);\n        animation.finished.then(() => this.notifyFinished()).catch(noop);\n        if (this.pendingTimeline) {\n            this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n            this.pendingTimeline = undefined;\n        }\n        this._animation = animation;\n    }\n    get finished() {\n        if (!this._animation) {\n            return this._finished;\n        }\n        else {\n            return this.animation.finished;\n        }\n    }\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n    get animation() {\n        if (!this._animation) {\n            this.keyframeResolver?.resume();\n            flushKeyframeResolvers();\n        }\n        return this._animation;\n    }\n    get duration() {\n        return this.animation.duration;\n    }\n    get time() {\n        return this.animation.time;\n    }\n    set time(newTime) {\n        this.animation.time = newTime;\n    }\n    get speed() {\n        return this.animation.speed;\n    }\n    get state() {\n        return this.animation.state;\n    }\n    set speed(newSpeed) {\n        this.animation.speed = newSpeed;\n    }\n    get startTime() {\n        return this.animation.startTime;\n    }\n    attachTimeline(timeline) {\n        if (this._animation) {\n            this.stopTimeline = this.animation.attachTimeline(timeline);\n        }\n        else {\n            this.pendingTimeline = timeline;\n        }\n        return () => this.stop();\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.complete();\n    }\n    cancel() {\n        if (this._animation) {\n            this.animation.cancel();\n        }\n        this.keyframeResolver?.cancel();\n    }\n}\n\nexport { AsyncMotionValueAnimation };\n", "class GroupAnimation {\n    constructor(animations) {\n        // Bound to accomadate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline) {\n        const subscriptions = this.animations.map((animation) => animation.attachTimeline(timeline));\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get state() {\n        return this.getAll(\"state\");\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\nexport { GroupAnimation };\n", "import { GroupAnimation } from './GroupAnimation.mjs';\n\nclass GroupAnimationWithThen extends GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\nexport { GroupAnimationWithThen };\n", "import { invariant, isNumericalString } from 'motion-utils';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON><PERSON>'s special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = \n// eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive, as it can match a lot of words\n/^var\\(--(?:([\\w-]+)|([\\w-]+), ?([a-zA-Z\\d ()%#.,-]+))\\)/u;\nfunction parseCSSVariable(current) {\n    const match = splitCSSVariableRegex.exec(current);\n    if (!match)\n        return [,];\n    const [, token1, token2, fallback] = match;\n    return [`--${token1 ?? token2}`, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n    invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`, \"max-css-var-depth\");\n    const [token, fallback] = parseCSSVariable(current);\n    // No CSS variable detected\n    if (!token)\n        return;\n    // Attempt to read this CSS variable off the element\n    const resolved = window.getComputedStyle(element).getPropertyValue(token);\n    if (resolved) {\n        const trimmed = resolved.trim();\n        return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n    }\n    return isCSSVariableToken(fallback)\n        ? getVariableValue(fallback, element, depth + 1)\n        : fallback;\n}\n\nexport { getVariableValue, parseCSSVariable };\n", "function getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\nexport { getValueTransition };\n", "import { transformPropOrder } from './keys-transform.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    ...transformPropOrder,\n]);\n\nexport { positionalKeys };\n", "/**\n * ValueType for \"auto\"\n */\nconst auto = {\n    test: (v) => v === \"auto\",\n    parse: (v) => v,\n};\n\nexport { auto };\n", "/**\n * Tests a provided value against a ValueType\n */\nconst testValueType = (v) => (type) => type.test(v);\n\nexport { testValueType };\n", "import { auto } from './auto.mjs';\nimport { number } from './numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from './numbers/units.mjs';\nimport { testValueType } from './test.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = (v) => dimensionValueTypes.find(testValueType(v));\n\nexport { dimensionValueTypes, findDimensionValueType };\n", "import { isZeroValueString } from 'motion-utils';\n\nfunction isNone(value) {\n    if (typeof value === \"number\") {\n        return value === 0;\n    }\n    else if (value !== null) {\n        return value === \"none\" || value === \"0\" || isZeroValueString(value);\n    }\n    else {\n        return true;\n    }\n}\n\nexport { isNone };\n", "import { complex } from './index.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\n\n/**\n * Properties that should default to 1 or 100%\n */\nconst maxDefaults = new Set([\"brightness\", \"contrast\", \"saturate\", \"opacity\"]);\nfunction applyDefaultFilter(v) {\n    const [name, value] = v.slice(0, -1).split(\"(\");\n    if (name === \"drop-shadow\")\n        return v;\n    const [number] = value.match(floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, \"\");\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + \"(\" + defaultValue + unit + \")\";\n}\nconst functionRegex = /\\b([a-z-]*)\\(.*?\\)/gu;\nconst filter = {\n    ...complex,\n    getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(\" \") : v;\n    },\n};\n\nexport { filter };\n", "import { number } from './numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n", "import { scale, alpha } from '../numbers/index.mjs';\nimport { degrees, px, progressPercentage } from '../numbers/units.mjs';\n\nconst transformValueTypes = {\n    rotate: degrees,\n    rotateX: degrees,\n    rotateY: degrees,\n    rotateZ: degrees,\n    scale,\n    scaleX: scale,\n    scaleY: scale,\n    scaleZ: scale,\n    skew: degrees,\n    skewX: degrees,\n    skewY: degrees,\n    distance: px,\n    translateX: px,\n    translateY: px,\n    translateZ: px,\n    x: px,\n    y: px,\n    z: px,\n    perspective: px,\n    transformPerspective: px,\n    opacity: alpha,\n    originX: progressPercentage,\n    originY: progressPercentage,\n    originZ: px,\n};\n\nexport { transformValueTypes };\n", "import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    ...transformValueTypes,\n    zIndex: int,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n", "import { color } from '../color/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n    ...numberValueTypes,\n    // Color props\n    color,\n    backgroundColor: color,\n    outlineColor: color,\n    fill: color,\n    stroke: color,\n    // Border props\n    borderColor: color,\n    borderTopColor: color,\n    borderRightColor: color,\n    borderBottomColor: color,\n    borderLeftColor: color,\n    filter,\n    WebkitFilter: filter,\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = (key) => defaultValueTypes[key];\n\nexport { defaultValueTypes, getDefaultValueType };\n", "import { complex } from '../complex/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { getDefaultValueType } from '../maps/defaults.mjs';\n\nfunction getAnimatableNone(key, value) {\n    let defaultValueType = getDefaultValueType(key);\n    if (defaultValueType !== filter)\n        defaultValueType = complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return defaultValueType.getAnimatableNone\n        ? defaultValueType.getAnimatableNone(value)\n        : undefined;\n}\n\nexport { getAnimatableNone };\n", "import { analyseComplexValue } from '../../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../../../value/types/utils/animatable-none.mjs';\n\n/**\n * If we encounter keyframes like \"none\" or \"0\" and we also have keyframes like\n * \"#fff\" or \"200px 200px\" we want to find a keyframe to serve as a template for\n * the \"none\" keyframes. In this case \"#fff\" or \"200px 200px\" - then these get turned into\n * zero equivalents, i.e. \"#fff0\" or \"0px 0px\".\n */\nconst invalidTemplates = new Set([\"auto\", \"none\", \"0\"]);\nfunction makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name) {\n    let i = 0;\n    let animatableTemplate = undefined;\n    while (i < unresolvedKeyframes.length && !animatableTemplate) {\n        const keyframe = unresolvedKeyframes[i];\n        if (typeof keyframe === \"string\" &&\n            !invalidTemplates.has(keyframe) &&\n            analyseComplexValue(keyframe).values.length) {\n            animatableTemplate = unresolvedKeyframes[i];\n        }\n        i++;\n    }\n    if (animatableTemplate && name) {\n        for (const noneIndex of noneKeyframeIndexes) {\n            unresolvedKeyframes[noneIndex] = getAnimatableNone(name, animatableTemplate);\n        }\n    }\n}\n\nexport { makeNoneKeyframesAnimatable };\n", "import { positionalKeys } from '../../render/utils/keys-position.mjs';\nimport { findDimensionValueType } from '../../value/types/dimensions.mjs';\nimport { getVariableValue } from '../utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from '../utils/is-css-variable.mjs';\nimport { KeyframeResolver } from './KeyframesResolver.mjs';\nimport { isNone } from './utils/is-none.mjs';\nimport { makeNoneKeyframesAnimatable } from './utils/make-none-animatable.mjs';\nimport { isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\n\nclass DOMKeyframesResolver extends KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element) {\n        super(unresolvedKeyframes, onComplete, name, motionValue, element, true);\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, element, name } = this;\n        if (!element || !element.current)\n            return;\n        super.readKeyframes();\n        /**\n         * If any keyframe is a CSS variable, we need to find its value by sampling the element\n         */\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            let keyframe = unresolvedKeyframes[i];\n            if (typeof keyframe === \"string\") {\n                keyframe = keyframe.trim();\n                if (isCSSVariableToken(keyframe)) {\n                    const resolved = getVariableValue(keyframe, element.current);\n                    if (resolved !== undefined) {\n                        unresolvedKeyframes[i] = resolved;\n                    }\n                    if (i === unresolvedKeyframes.length - 1) {\n                        this.finalKeyframe = keyframe;\n                    }\n                }\n            }\n        }\n        /**\n         * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n         * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n         * have a far bigger performance impact.\n         */\n        this.resolveNoneKeyframes();\n        /**\n         * Check to see if unit type has changed. If so schedule jobs that will\n         * temporarily set styles to the destination keyframes.\n         * Skip if we have more than two keyframes or this isn't a positional value.\n         * TODO: We can throw if there are multiple keyframes and the value type changes.\n         */\n        if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n            return;\n        }\n        const [origin, target] = unresolvedKeyframes;\n        const originType = findDimensionValueType(origin);\n        const targetType = findDimensionValueType(target);\n        /**\n         * Either we don't recognise these value types or we can animate between them.\n         */\n        if (originType === targetType)\n            return;\n        /**\n         * If both values are numbers or pixels, we can animate between them by\n         * converting them to numbers.\n         */\n        if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n            for (let i = 0; i < unresolvedKeyframes.length; i++) {\n                const value = unresolvedKeyframes[i];\n                if (typeof value === \"string\") {\n                    unresolvedKeyframes[i] = parseFloat(value);\n                }\n            }\n        }\n        else if (positionalValues[name]) {\n            /**\n             * Else, the only way to resolve this is by measuring the element.\n             */\n            this.needsMeasurement = true;\n        }\n    }\n    resolveNoneKeyframes() {\n        const { unresolvedKeyframes, name } = this;\n        const noneKeyframeIndexes = [];\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            if (unresolvedKeyframes[i] === null ||\n                isNone(unresolvedKeyframes[i])) {\n                noneKeyframeIndexes.push(i);\n            }\n        }\n        if (noneKeyframeIndexes.length) {\n            makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n        }\n    }\n    measureInitialState() {\n        const { element, unresolvedKeyframes, name } = this;\n        if (!element || !element.current)\n            return;\n        if (name === \"height\") {\n            this.suspendedScrollY = window.pageYOffset;\n        }\n        this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        unresolvedKeyframes[0] = this.measuredOrigin;\n        // Set final key frame to measure after next render\n        const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n        if (measureKeyframe !== undefined) {\n            element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n        }\n    }\n    measureEndState() {\n        const { element, name, unresolvedKeyframes } = this;\n        if (!element || !element.current)\n            return;\n        const value = element.getValue(name);\n        value && value.jump(this.measuredOrigin, false);\n        const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n        const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n        unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n            this.finalKeyframe = finalKeyframe;\n        }\n        // If we removed transform values, reapply them before the next render\n        if (this.removedTransforms?.length) {\n            this.removedTransforms.forEach(([unsetTransformName, unsetTransformValue]) => {\n                element\n                    .getValue(unsetTransformName)\n                    .set(unsetTransformValue);\n            });\n        }\n        this.resolveNoneKeyframes();\n    }\n}\n\nexport { DOMKeyframesResolver };\n", "function resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n", "/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n    return type && typeof value === \"number\"\n        ? type.transform(value)\n        : value;\n};\n\nexport { getValueAsType };\n", "import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev) {\n                this.events.change?.notify(this.current);\n                if (this.dependents) {\n                    for (const dependent of this.dependents) {\n                        dependent.dirty();\n                    }\n                }\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v) {\n        if (!this.passiveEffect) {\n            this.updateAndNotify(v);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    dirty() {\n        this.events.change?.notify(this.current);\n    }\n    addDependent(dependent) {\n        if (!this.dependents) {\n            this.dependents = new Set();\n        }\n        this.dependents.add(dependent);\n    }\n    removeDependent(dependent) {\n        if (this.dependents) {\n            this.dependents.delete(dependent);\n        }\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.dependents?.clear();\n        this.events.destroy?.notify();\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n", "import { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ createRenderBatcher(queueMicrotask, false);\n\nexport { cancelMicrotask, microtask };\n", "import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an SVG element in a way\n * that works across iframes\n */\nfunction isSVGElement(element) {\n    return isObject(element) && \"ownerSVGElement\" in element;\n}\n\nexport { isSVGElement };\n", "import { isSVGElement } from './is-svg-element.mjs';\n\n/**\n * Checks if an element is specifically an SVGSVGElement (the root SVG element)\n * in a way that works across iframes\n */\nfunction isSVGSVGElement(element) {\n    return isSVGElement(element) && element.tagName === \"svg\";\n}\n\nexport { isSVGSVGElement };\n", "import { easingDefinitionToFunction } from 'motion-utils';\n\nfunction getOriginIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction stagger(duration = 0.1, { startDelay = 0, from = 0, ease } = {}) {\n    return (i, total) => {\n        const fromIndex = typeof from === \"number\" ? from : getOriginIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (ease) {\n            const maxDelay = total * duration;\n            const easingFunction = easingDefinitionToFunction(ease);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return startDelay + delay;\n    };\n}\n\nexport { getOriginIndex, stagger };\n", "const isMotionValue = (value) => Boolean(value && value.getVelocity);\n\nexport { isMotionValue };\n", "import { color } from '../color/index.mjs';\nimport { complex } from '../complex/index.mjs';\nimport { dimensionValueTypes } from '../dimensions.mjs';\nimport { testValueType } from '../test.mjs';\n\n/**\n * A list of all ValueTypes\n */\nconst valueTypes = [...dimensionValueTypes, color, complex];\n/**\n * Tests a value against the list of ValueTypes\n */\nconst findValueType = (v) => valueTypes.find(testValueType(v));\n\nexport { findValueType };\n", "function isDOMKeyframes(keyframes) {\n    return typeof keyframes === \"object\" && !Array.isArray(keyframes);\n}\n\nexport { isDOMKeyframes };\n", "import { resolveElements } from 'motion-dom';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\n\nfunction resolveSubjects(subject, keyframes, scope, selectorCache) {\n    if (typeof subject === \"string\" && isDOMKeyframes(keyframes)) {\n        return resolveElements(subject, scope, selectorCache);\n    }\n    else if (subject instanceof NodeList) {\n        return Array.from(subject);\n    }\n    else if (Array.isArray(subject)) {\n        return subject;\n    }\n    else {\n        return [subject];\n    }\n}\n\nexport { resolveSubjects };\n", "function calculateRepeatDuration(duration, repeat, _repeatDelay) {\n    return duration * (repeat + 1);\n}\n\nexport { calculateRepeatDuration };\n", "/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n    if (typeof next === \"number\") {\n        return next;\n    }\n    else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n        return Math.max(0, current + parseFloat(next));\n    }\n    else if (next === \"<\") {\n        return prev;\n    }\n    else if (next.startsWith(\"<\")) {\n        return Math.max(0, prev + parseFloat(next.slice(1)));\n    }\n    else {\n        return labels.get(next) ?? current;\n    }\n}\n\nexport { calcNextTime };\n", "import { mixNumber } from 'motion-dom';\nimport { getEasingForSegment, removeItem } from 'motion-utils';\n\nfunction eraseKeyframes(sequence, startTime, endTime) {\n    for (let i = 0; i < sequence.length; i++) {\n        const keyframe = sequence[i];\n        if (keyframe.at > startTime && keyframe.at < endTime) {\n            removeItem(sequence, keyframe);\n            // If we remove this item we have to push the pointer back one\n            i--;\n        }\n    }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n    /**\n     * Erase every existing value between currentTime and targetTime,\n     * this will essentially splice this timeline into any currently\n     * defined ones.\n     */\n    eraseKeyframes(sequence, startTime, endTime);\n    for (let i = 0; i < keyframes.length; i++) {\n        sequence.push({\n            value: keyframes[i],\n            at: mixNumber(startTime, endTime, offset[i]),\n            easing: getEasingForSegment(easing, i),\n        });\n    }\n}\n\nexport { addKeyframes, eraseKeyframes };\n", "/**\n * Take an array of times that represent repeated keyframes. For instance\n * if we have original times of [0, 0.5, 1] then our repeated times will\n * be [0, 0.5, 1, 1, 1.5, 2]. Loop over the times and scale them back\n * down to a 0-1 scale.\n */\nfunction normalizeTimes(times, repeat) {\n    for (let i = 0; i < times.length; i++) {\n        times[i] = times[i] / (repeat + 1);\n    }\n}\n\nexport { normalizeTimes };\n", "function compareByTime(a, b) {\n    if (a.at === b.at) {\n        if (a.value === null)\n            return 1;\n        if (b.value === null)\n            return -1;\n        return 0;\n    }\n    else {\n        return a.at - b.at;\n    }\n}\n\nexport { compareByTime };\n", "import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope, generators) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", repeat, repeatType, repeatDelay = 0, ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numSubjects)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            const createGenerator = isGenerator(type)\n                ? type\n                : generators?.[type || \"keyframes\"];\n            if (numKeyframes <= 2 && createGenerator) {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration ?? (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Handle repeat options\n             */\n            if (repeat) {\n                invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\", \"repeat-count-high\");\n                duration = calculateRepeatDuration(duration, repeat);\n                const originalKeyframes = [...valueKeyframesAsList];\n                const originalTimes = [...times];\n                ease = Array.isArray(ease) ? [...ease] : [ease];\n                const originalEase = [...ease];\n                for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n                    valueKeyframesAsList.push(...originalKeyframes);\n                    for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n                        times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n                        ease.push(keyframeIndex === 0\n                            ? \"linear\"\n                            : getEasingForSegment(originalEase, keyframeIndex - 1));\n                    }\n                }\n                normalizeTimes(times, repeat);\n            }\n            const targetTime = startTime + duration;\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n            const numSubjects = subjects.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const thisSubject = subjects[subjectIndex];\n                const subjectSequence = getSubjectSequence(thisSubject, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition && transition[key]\n        ? {\n            ...transition,\n            ...transition[key],\n        }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n", "const visualElementStore = new WeakMap();\n\nexport { visualElementStore };\n", "const isKeyframesTarget = (v) => {\n    return Array.isArray(v);\n};\n\nexport { isKeyframesTarget };\n", "function getValueState(visualElement) {\n    const state = [{}, {}];\n    visualElement?.values.forEach((value, key) => {\n        state[0][key] = value.get();\n        state[1][key] = value.getVelocity();\n    });\n    return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n", "import { resolveVariantFromProps } from './resolve-variants.mjs';\n\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\n\nexport { resolveVariant };\n", "import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\nexport { setTarget };\n", "import { isMotionValue } from 'motion-dom';\n\nfunction isWillChangeMotionValue(value) {\n    return Boolean(isMotionValue(value) && value.add);\n}\n\nexport { isWillChangeMotionValue };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { isWillChangeMotionValue } from './is.mjs';\n\nfunction addValueToWillChange(visualElement, key) {\n    const willChange = visualElement.getValue(\"willChange\");\n    /**\n     * It could be that a user has set will<PERSON>hange to a regular MotionValue,\n     * in which case we can't add the value to it.\n     */\n    if (isWillChangeMotionValue(willChange)) {\n        return willChange.add(key);\n    }\n    else if (!willChange && MotionGlobalConfig.WillChange) {\n        const newWillChange = new MotionGlobalConfig.WillChange(\"auto\");\n        visualElement.addValue(\"willChange\", newWillChange);\n        newWillChange.add(key);\n    }\n}\n\nexport { addValueToWillChange };\n", "/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/gu, \"$1-$2\").toLowerCase();\n\nexport { camelToDash };\n", "import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\n\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + camelToDash(optimizedAppearDataId);\n\nexport { optimizedAppearDataAttribute, optimizedAppearDataId };\n", "import { optimizedAppearDataAttribute } from './data-id.mjs';\n\nfunction getOptimisedAppearId(visualElement) {\n    return visualElement.props[optimizedAppearDataAttribute];\n}\n\nexport { getOptimisedAppearId };\n", "const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n", "import { transformProps } from 'motion-dom';\n\nconst underDampedSpring = {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n};\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n    type: \"keyframes\",\n    ease: [0.25, 0.1, 0.35, 1],\n    duration: 0.3,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else if (transformProps.has(valueKey)) {\n        return valueKey.startsWith(\"scale\")\n            ? criticallyDampedSpring(keyframes[1])\n            : underDampedSpring;\n    }\n    return ease;\n};\n\nexport { getDefaultTransition };\n", "/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\n\nexport { isTransitionDefined };\n", "import { getValueTransition, makeAnimationInstant, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        makeAnimationInstant(options);\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (MotionGlobalConfig.instantAnimations ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        makeAnimationInstant(options);\n        options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    return valueTransition.isSync\n        ? new JSAnimation(options)\n        : new AsyncMotionValueAnimation(options);\n};\n\nexport { animateMotionValue };\n", "import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = targetAndTransition;\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n        const valueTarget = target[key];\n        if (valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If the value is already at the defined target, skip the animation.\n         */\n        const currentValue = value.get();\n        if (currentValue !== undefined &&\n            !value.isAnimating &&\n            !Array.isArray(valueTarget) &&\n            valueTarget === currentValue &&\n            !valueTransition.velocity) {\n            continue;\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        let isHandoff = false;\n        if (window.MotionHandoffAnimation) {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (appearId) {\n                const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n                if (startTime !== null) {\n                    valueTransition.startTime = startTime;\n                    isHandoff = true;\n                }\n            }\n        }\n        addValueToWillChange(visualElement, key);\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key)\n            ? { type: false }\n            : valueTransition, visualElement, isHandoff));\n        const animation = value.animation;\n        if (animation) {\n            animations.push(animation);\n        }\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            frame.update(() => {\n                transitionEnd && setTarget(visualElement, transitionEnd);\n            });\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n", "/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\nexport { convertBoundingBoxToBox, convertBoxToBoundingBox, transformBoxPoints };\n", "function isIdentityScale(scale) {\n    return scale === undefined || scale === 1;\n}\nfunction hasScale({ scale, scaleX, scaleY }) {\n    return (!isIdentityScale(scale) ||\n        !isIdentityScale(scaleX) ||\n        !isIdentityScale(scaleY));\n}\nfunction hasTransform(values) {\n    return (hasScale(values) ||\n        has2DTranslate(values) ||\n        values.z ||\n        values.rotate ||\n        values.rotateX ||\n        values.rotateY ||\n        values.skewX ||\n        values.skewY);\n}\nfunction has2DTranslate(values) {\n    return is2DTranslate(values.x) || is2DTranslate(values.y);\n}\nfunction is2DTranslate(value) {\n    return value && value !== \"0%\";\n}\n\nexport { has2DTranslate, hasScale, hasTransform };\n", "import { mixNumber } from 'motion-dom';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const { visualElement } = node.options;\n        if (visualElement &&\n            visualElement.props.style &&\n            visualElement.props.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && hasTransform(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    if (treeScale.x < TREE_SCALE_SNAP_MAX &&\n        treeScale.x > TREE_SCALE_SNAP_MIN) {\n        treeScale.x = 1.0;\n    }\n    if (treeScale.y < TREE_SCALE_SNAP_MAX &&\n        treeScale.y > TREE_SCALE_SNAP_MIN) {\n        treeScale.y = 1.0;\n    }\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale, axisOrigin = 0.5) {\n    const originPoint = mixNumber(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n    transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\n\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };\n", "import { convertBoundingBoxToBox, transformBoxPoints } from '../geometry/conversion.mjs';\nimport { translateAxis } from '../geometry/delta-apply.mjs';\n\nfunction measureViewportBox(instance, transformPoint) {\n    return convertBoundingBoxToBox(transformBoxPoints(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n    const viewportBox = measureViewportBox(element, transformPagePoint);\n    const { scroll } = rootProjectionNode;\n    if (scroll) {\n        translateAxis(viewportBox.x, scroll.offset.x);\n        translateAxis(viewportBox.y, scroll.offset.y);\n    }\n    return viewportBox;\n}\n\nexport { measurePageBox, measureViewportBox };\n", "const featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\nexport { featureDefinitions };\n", "const createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\nexport { createAxis, createAxisDelta, createBox, createDelta };\n", "const isBrowser = typeof window !== \"undefined\";\n\nexport { isBrowser };\n", "// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = { current: null };\nconst hasReducedMotionListener = { current: false };\n\nexport { hasReducedMotionListener, prefersReducedMotion };\n", "import { isBrowser } from '../is-browser.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\nfunction initPrefersReducedMotion() {\n    hasReducedMotionListener.current = true;\n    if (!isBrowser)\n        return;\n    if (window.matchMedia) {\n        const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n        const setReducedMotionPreferences = () => (prefersReducedMotion.current = motionMediaQuery.matches);\n        motionMediaQuery.addEventListener(\"change\", setReducedMotionPreferences);\n        setReducedMotionPreferences();\n    }\n    else {\n        prefersReducedMotion.current = false;\n    }\n}\n\nexport { initPrefersReducedMotion };\n", "function isAnimationControls(v) {\n    return (v !== null &&\n        typeof v === \"object\" &&\n        typeof v.start === \"function\");\n}\n\nexport { isAnimationControls };\n", "/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\nexport { isVariantLabel };\n", "const variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\nexport { variantPriorityOrder, variantProps };\n", "import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nfunction isControllingVariants(props) {\n    return (isAnimationControls(props.animate) ||\n        variantProps.some((name) => isVariantLabel(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\nexport { isControllingVariants, isVariantNode };\n", "import { isMotionValue, motionValue } from 'motion-dom';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                if (existingValue.liveStyle === true) {\n                    existingValue.jump(nextValue);\n                }\n                else if (!existingValue.hasAnimated) {\n                    existingValue.set(nextValue);\n                }\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n", "import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone, microtask } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key]);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\", \"reduced-motion-disabled\");\n        }\n        this.parent?.addChild(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent?.removeChild(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    addChild(child) {\n        this.children.add(child);\n        this.enteringChildren ?? (this.enteringChildren = new Set());\n        this.enteringChildren.add(child);\n    }\n    removeChild(child) {\n        this.children.delete(child);\n        this.enteringChildren && this.enteringChildren.delete(child);\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n            this.scheduleRender();\n        });\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in featureDefinitions) {\n            const featureDefinition = featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n    scheduleRenderMicrotask() {\n        microtask.render(this.render);\n    }\n}\n\nexport { VisualElement };\n", "import { DOMKeyframesResolver, isMotionValue } from 'motion-dom';\nimport { VisualElement } from '../VisualElement.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if (isMotionValue(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current) {\n                    this.current.textContent = `${latest}`;\n                }\n            });\n        }\n    }\n}\n\nexport { DOMVisualElement };\n", "import { transformPropOrder, getValueAsType, numberValueTypes } from 'motion-dom';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = transformPropOrder[i];\n        const value = latestValues[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault || transformTemplate) {\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (!valueIsDefault) {\n                transformIsDefault = false;\n                const transformName = translateAlias[key] || key;\n                transformString += `${transformName}(${valueAsType}) `;\n            }\n            if (transformTemplate) {\n                transform[key] = valueAsType;\n            }\n        }\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n", "import { transformProps, isCSSVariableName, getValueAsType, numberValueTypes } from 'motion-dom';\nimport { buildTransform } from './build-transform.mjs';\n\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n    const { style, vars, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept separately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            continue;\n        }\n        else if (isCSSVariableName(key)) {\n            vars[key] = value;\n            continue;\n        }\n        else {\n            // Convert the value to its default value type, ie 0 -> \"0px\"\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (key.startsWith(\"origin\")) {\n                // If this is a transform origin, flag and enable further transform-origin processing\n                hasTransformOrigin = true;\n                transformOrigin[key] =\n                    valueAsType;\n            }\n            else {\n                style[key] = valueAsType;\n            }\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(latestValues, state.transform, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n", "function renderHTML(element, { style, vars }, styleProp, projection) {\n    const elementStyle = element.style;\n    let key;\n    for (key in style) {\n        // CSSStyleDeclaration has [index: number]: string; in the types, so we use that as key type.\n        elementStyle[key] = style[key];\n    }\n    // Write projection styles directly to element style\n    projection?.applyProjectionStyles(elementStyle, styleProp);\n    for (key in vars) {\n        // Loop over any CSS variables and assign those.\n        // They can only be assigned using `setProperty`.\n        elementStyle.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n", "import { isCSSVariableName } from 'motion-dom';\n\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    for (const key in correctors) {\n        scaleCorrectors[key] = correctors[key];\n        if (isCSSVariableName(key)) {\n            scaleCorrectors[key].isCSSVariable = true;\n        }\n    }\n}\n\nexport { addScaleCorrector, scaleCorrectors };\n", "import { transformProps } from 'motion-dom';\nimport { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!scaleCorrectors[key] || key === \"opacity\")));\n}\n\nexport { isForcedMotionValue };\n", "import { isMotionValue } from 'motion-dom';\nimport { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style &&\n                isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props) ||\n            visualElement?.getValue(key)?.liveStyle !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n", "import { transformProps, defaultTransformValue, readTransformValue, isCSSVariableName } from 'motion-dom';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            return this.projection?.isProjecting\n                ? defaultTransformValue(key)\n                : readTransformValue(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n", "import { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\n\nfunction isObjectKey(key, object) {\n    return key in object;\n}\nclass ObjectVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"object\";\n    }\n    readValueFromInstance(instance, key) {\n        if (isObjectKey(key, instance)) {\n            const value = instance[key];\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                return value;\n            }\n        }\n        return undefined;\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    removeValueFromRenderState(key, renderState) {\n        delete renderState.output[key];\n    }\n    measureInstanceViewportBox() {\n        return createBox();\n    }\n    build(renderState, latestValues) {\n        Object.assign(renderState.output, latestValues);\n    }\n    renderInstance(instance, { output }) {\n        Object.assign(instance, output);\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n}\n\nexport { ObjectVisualElement };\n", "import { px } from 'motion-dom';\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = px.transform(-offset);\n    // Build the dash array\n    const pathLength = px.transform(length);\n    const pathSpacing = px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\nexport { buildSVGPath };\n", "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n", "/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\nexport { camelCaseAttributes };\n", "const isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\nexport { isSVGTag };\n", "import { camelToDash } from '../../dom/utils/camel-to-dash.mjs';\nimport { renderHTML } from '../../html/utils/render.mjs';\nimport { camelCaseAttributes } from './camel-case-attrs.mjs';\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    renderHTML(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!camelCaseAttributes.has(key) ? camelToDash(key) : key, renderState.attrs[key]);\n    }\n}\n\nexport { renderSVG };\n", "import { isMotionValue, transformPropOrder } from 'motion-dom';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const newValues = scrapeMotionValuesFromProps$1(props, prevProps, visualElement);\n    for (const key in props) {\n        if (isMotionValue(props[key]) ||\n            isMotionValue(prevProps[key])) {\n            const targetKey = transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n", "import { transformProps, getDefaultValueType } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = createBox;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n    build(renderState, latestValues, props) {\n        buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n", "import { isSVGElement, isSVGSVGElement } from 'motion-dom';\nimport { HTMLVisualElement } from '../../render/html/HTMLVisualElement.mjs';\nimport { ObjectVisualElement } from '../../render/object/ObjectVisualElement.mjs';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { SVGVisualElement } from '../../render/svg/SVGVisualElement.mjs';\n\nfunction createDOMVisualElement(element) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                transform: {},\n                transformOrigin: {},\n                style: {},\n                vars: {},\n                attrs: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = isSVGElement(element) && !isSVGSVGElement(element)\n        ? new SVGVisualElement(options)\n        : new HTMLVisualElement(options);\n    node.mount(element);\n    visualElementStore.set(element, node);\n}\nfunction createObjectVisualElement(subject) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                output: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = new ObjectVisualElement(options);\n    node.mount(subject);\n    visualElementStore.set(subject, node);\n}\n\nexport { createDOMVisualElement, createObjectVisualElement };\n", "import { isMotionValue, motionValue } from 'motion-dom';\nimport { animateMotionValue } from '../interfaces/motion-value.mjs';\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n    motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\nexport { animateSingleValue };\n", "import { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { animateTarget } from '../interfaces/visual-element-target.mjs';\nimport { createDOMVisualElement, createObjectVisualElement } from '../utils/create-visual-element.mjs';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\nimport { resolveSubjects } from './resolve-subjects.mjs';\nimport { animateSingleValue } from './single-value.mjs';\n\nfunction isSingleValue(subject, keyframes) {\n    return (isMotionValue(subject) ||\n        typeof subject === \"number\" ||\n        (typeof subject === \"string\" && !isDOMKeyframes(keyframes)));\n}\n/**\n * Implementation\n */\nfunction animateSubject(subject, keyframes, options, scope) {\n    const animations = [];\n    if (isSingleValue(subject, keyframes)) {\n        animations.push(animateSingleValue(subject, isDOMKeyframes(keyframes)\n            ? keyframes.default || keyframes\n            : keyframes, options ? options.default || options : options));\n    }\n    else {\n        const subjects = resolveSubjects(subject, keyframes, scope);\n        const numSubjects = subjects.length;\n        invariant(Boolean(numSubjects), \"No valid elements provided.\", \"no-valid-elements\");\n        for (let i = 0; i < numSubjects; i++) {\n            const thisSubject = subjects[i];\n            invariant(thisSubject !== null, \"You're trying to perform an animation on null. Ensure that selectors are correctly finding elements and refs are correctly hydrated.\", \"animate-null\");\n            const createVisualElement = thisSubject instanceof Element\n                ? createDOMVisualElement\n                : createObjectVisualElement;\n            if (!visualElementStore.has(thisSubject)) {\n                createVisualElement(thisSubject);\n            }\n            const visualElement = visualElementStore.get(thisSubject);\n            const transition = { ...options };\n            /**\n             * Resolve stagger function if provided.\n             */\n            if (\"delay\" in transition &&\n                typeof transition.delay === \"function\") {\n                transition.delay = transition.delay(i, numSubjects);\n            }\n            animations.push(...animateTarget(visualElement, { ...keyframes, transition }, {}));\n        }\n    }\n    return animations;\n}\n\nexport { animateSubject };\n", "import { spring } from 'motion-dom';\nimport { createAnimationsFromSequence } from '../sequence/create.mjs';\nimport { animateSubject } from './subject.mjs';\n\nfunction animateSequence(sequence, options, scope) {\n    const animations = [];\n    const animationDefinitions = createAnimationsFromSequence(sequence, options, scope, { spring });\n    animationDefinitions.forEach(({ keyframes, transition }, subject) => {\n        animations.push(...animateSubject(subject, keyframes, transition));\n    });\n    return animations;\n}\n\nexport { animateSequence };\n", "import { GroupAnimationWithThen } from 'motion-dom';\nimport { removeItem } from 'motion-utils';\nimport { animateSequence } from './sequence.mjs';\nimport { animateSubject } from './subject.mjs';\n\nfunction isSequence(value) {\n    return Array.isArray(value) && value.some(Array.isArray);\n}\n/**\n * Creates an animation function that is optionally scoped\n * to a specific element.\n */\nfunction createScopedAnimate(scope) {\n    /**\n     * Implementation\n     */\n    function scopedAnimate(subjectOrSequence, optionsOrKeyframes, options) {\n        let animations = [];\n        if (isSequence(subjectOrSequence)) {\n            animations = animateSequence(subjectOrSequence, optionsOrKeyframes, scope);\n        }\n        else {\n            animations = animateSubject(subjectOrSequence, optionsOrKeyframes, options, scope);\n        }\n        const animation = new GroupAnimationWithThen(animations);\n        if (scope) {\n            scope.animations.push(animation);\n            animation.finished.then(() => {\n                removeItem(scope.animations, animation);\n            });\n        }\n        return animation;\n    }\n    return scopedAnimate;\n}\nconst animate = createScopedAnimate();\n\nexport { animate, createScopedAnimate };\n", "/**\r\n * Ativo Color Tag Animation System\r\n * Anima as tags coloridas no container-float com base na seleção de ativos\r\n * Usa Motion One para animações stagger de entrada e saída\r\n * Controla visibilidade baseada nos steps 2 e 3 do formulário\r\n */\r\nimport { animate, stagger } from 'motion';\r\n\r\nexport class AtivoColorTagAnimationSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.colorTags = [];\r\n    this.containerFloat = null;\r\n    this.ativosItensColor = null;\r\n    this.currentStep = 0;\r\n    this.isInValidSteps = false; // steps 2 e 3\r\n    this.selectedAssets = new Set();\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.initializeSystem();\r\n    });\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  initializeSystem() {\r\n    this.containerFloat = document.querySelector('.container-float');\r\n    this.ativosItensColor = document.querySelector('.ativos-itens-color');\r\n\r\n    if (!this.containerFloat || !this.ativosItensColor) {\r\n      return;\r\n    }\r\n\r\n    this.setupColorTags();\r\n    this.setupAssetSelectionListener();\r\n    this.setupStepNavigationListener();\r\n    this.setupViewportDetection();\r\n\r\n    // Inicialmente esconder todas as tags e o container\r\n    this.hideAllTags();\r\n    this.updateContainerVisibility();\r\n\r\n    // Garantir que a classe section-2 esteja correta desde o início\r\n    this.initializeSectionClass();\r\n\r\n    if (this.isInitialized) {\r\n      // Expor método de debug no console (apenas em desenvolvimento)\r\n      if (typeof window !== 'undefined') {\r\n        window.debugAtivoColorTag = this;\r\n      }\r\n    }\r\n  }\r\n\r\n  setupColorTags() {\r\n    const tags = this.containerFloat.querySelectorAll('.ativo-item-color-tag');\r\n\r\n    tags.forEach((tag) => {\r\n      const category = tag.getAttribute('ativo-category');\r\n      const product = tag.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        this.colorTags.push({\r\n          element: tag,\r\n          category: category,\r\n          product: product,\r\n          normalizedKey: `${this.normalizeString(category)}|${this.normalizeString(product)}`,\r\n          isVisible: false,\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  normalizeString(str) {\r\n    return str.toLowerCase().trim();\r\n  }\r\n\r\n  setupStepNavigationListener() {\r\n    // Escutar mudanças de step do sistema de navegação\r\n    document.addEventListener('progressBarStateChange', (event) => {\r\n      this.handleStepChange(event.detail);\r\n    });\r\n  }\r\n\r\n  initializeSectionClass() {\r\n    // Verificar qual seção está ativa inicialmente\r\n    const activeSection = document.querySelector(\r\n      '[data-step].active-step, [data-step][style*=\"display: block\"]'\r\n    );\r\n\r\n    if (activeSection) {\r\n      const stepAttr = activeSection.getAttribute('data-step');\r\n      const initialStep = parseInt(stepAttr); // sistema 1-indexed\r\n      this.updateSectionClass(initialStep);\r\n    } else {\r\n      // Por padrão, começar na seção 1 (step 1)\r\n      this.updateSectionClass(1);\r\n    }\r\n  }\r\n\r\n  setupViewportDetection() {\r\n    // Detectar quando as seções 2 e 3 estão no viewport\r\n    const section2 = document.querySelector('[data-step=\"2\"]');\r\n    const section3 = document.querySelector('[data-step=\"3\"]');\r\n\r\n    if (section2 || section3) {\r\n      // Usar Intersection Observer para detectar viewport\r\n      const observer = new IntersectionObserver(\r\n        (entries) => {\r\n          entries.forEach((entry) => {\r\n            if (entry.isIntersecting) {\r\n              const step = entry.target.getAttribute('data-step');\r\n              if (step === '2' || step === '3') {\r\n                this.handleSectionInView();\r\n              }\r\n            }\r\n          });\r\n        },\r\n        {\r\n          threshold: 0.3, // 30% da seção visível\r\n          rootMargin: '-50px 0px',\r\n        }\r\n      );\r\n\r\n      if (section2) observer.observe(section2);\r\n      if (section3) observer.observe(section3);\r\n    }\r\n  }\r\n\r\n  handleStepChange(detail) {\r\n    const { currentStep } = detail;\r\n    this.currentStep = currentStep;\r\n\r\n    // Steps válidos: 2 (seção de ativos) e 3 (seção de alocação) - sistema 1-indexed\r\n    const wasInValidSteps = this.isInValidSteps;\r\n    this.isInValidSteps = currentStep === 2 || currentStep === 3;\r\n\r\n    // Atualizar classe section-2 no container\r\n    this.updateSectionClass(currentStep);\r\n\r\n    // Se mudou o estado de visibilidade, atualizar\r\n    if (wasInValidSteps !== this.isInValidSteps) {\r\n      this.updateContainerVisibility();\r\n      this.updateTagsVisibility();\r\n\r\n      // Se entramos nos steps válidos e temos seleções armazenadas, aplicá-las\r\n      if (this.isInValidSteps && this.selectedAssets.size > 0) {\r\n        this.applyAssetSelectionToTags();\r\n      }\r\n    } else if (this.isInValidSteps) {\r\n      // Mesmo que não tenha mudado o estado, se estamos em step válido,\r\n      // reaplique a seleção para garantir consistência\r\n      this.applyAssetSelectionToTags();\r\n    }\r\n  }\r\n\r\n  handleSectionInView() {\r\n    // Adicionar lógica adicional baseada na detecção de viewport se necessário\r\n    // Por enquanto, o controle principal é pelo step navigation\r\n  }\r\n\r\n  updateSectionClass(currentStep) {\r\n    if (!this.ativosItensColor) return;\r\n\r\n    // Adicionar classe section-2 APENAS quando estiver no step 2 (seção de ativos)\r\n    // Remover classe section-2 quando estiver no step 3 (seção de alocação) ou qualquer outro step\r\n    if (currentStep === 2) {\r\n      this.ativosItensColor.classList.add('section-2');\r\n    } else {\r\n      this.ativosItensColor.classList.remove('section-2');\r\n    }\r\n  }\r\n\r\n  updateContainerVisibility() {\r\n    if (!this.containerFloat) return;\r\n\r\n    if (this.isInValidSteps) {\r\n      // Mostrar container quando nos steps válidos (2 e 3)\r\n      this.containerFloat.style.display = 'block';\r\n      this.containerFloat.style.opacity = '1';\r\n    } else {\r\n      // Esconder container quando fora dos steps válidos\r\n      this.containerFloat.style.display = 'none';\r\n      this.containerFloat.style.opacity = '0';\r\n    }\r\n  }\r\n\r\n  updateTagsVisibility() {\r\n    if (!this.isInValidSteps) {\r\n      // Se não estamos nos steps válidos, esconder todas as tags\r\n      this.hideAllTagsImmediate();\r\n      return;\r\n    }\r\n\r\n    // Se estamos nos steps válidos, mostrar apenas as tags selecionadas\r\n    this.applyAssetSelectionToTags();\r\n  }\r\n\r\n  setupAssetSelectionListener() {\r\n    // Escutar mudanças na seleção de ativos\r\n    document.addEventListener('assetSelectionChanged', (event) => {\r\n      this.handleAssetSelectionChange(event.detail);\r\n    });\r\n\r\n    // Escutar mudanças no filtro de ativos\r\n    document.addEventListener('assetFilterChanged', (event) => {\r\n      this.handleAssetSelectionChange(event.detail);\r\n    });\r\n\r\n    // Escutar quando o sistema de seleção de assets termina de carregar o cache\r\n    document.addEventListener('assetSelectionSystemReady', () => {\r\n      // Forçar sincronização das tags se estivermos nos steps válidos\r\n      if (this.isInValidSteps && this.selectedAssets.size > 0) {\r\n        this.applyAssetSelectionToTags();\r\n      }\r\n    });\r\n  }\r\n\r\n  handleAssetSelectionChange(detail) {\r\n    const selectedAssets = detail.selectedAssets || [];\r\n    this.selectedAssets = new Set(selectedAssets);\r\n\r\n    // Sempre aplicar mudanças se estivermos nos steps válidos\r\n    if (this.isInValidSteps) {\r\n      this.applyAssetSelectionToTags();\r\n    }\r\n    // Se não estamos nos steps válidos mas o evento vem do cache,\r\n    // as seleções ficam armazenadas para serem aplicadas quando entrarmos nos steps válidos\r\n  }\r\n\r\n  applyAssetSelectionToTags() {\r\n    // Identificar quais tags devem ser mostradas/escondidas\r\n    const tagsToShow = [];\r\n    const tagsToHide = [];\r\n\r\n    this.colorTags.forEach((tag) => {\r\n      const shouldBeVisible = this.selectedAssets.has(tag.normalizedKey);\r\n\r\n      if (shouldBeVisible && !tag.isVisible) {\r\n        tagsToShow.push(tag);\r\n        tag.isVisible = true;\r\n      } else if (!shouldBeVisible && tag.isVisible) {\r\n        tagsToHide.push(tag);\r\n        tag.isVisible = false;\r\n      }\r\n    });\r\n\r\n    // Animar as mudanças\r\n    if (tagsToShow.length > 0) {\r\n      this.animateTagsIn(tagsToShow);\r\n    }\r\n\r\n    if (tagsToHide.length > 0) {\r\n      this.animateTagsOut(tagsToHide);\r\n    }\r\n  }\r\n\r\n  hideAllTags() {\r\n    this.colorTags.forEach((tag) => {\r\n      tag.element.style.opacity = '0';\r\n      tag.element.style.transform = 'translateY(20px)';\r\n      tag.element.style.display = 'none';\r\n      tag.isVisible = false;\r\n    });\r\n  }\r\n\r\n  hideAllTagsImmediate() {\r\n    // Versão sem animação para mudanças de step\r\n    this.colorTags.forEach((tag) => {\r\n      tag.element.style.opacity = '0';\r\n      tag.element.style.transform = 'translateY(20px)';\r\n      tag.element.style.display = 'none';\r\n      tag.isVisible = false; // Importante: resetar o estado\r\n    });\r\n  }\r\n\r\n  animateTagsIn(tags) {\r\n    // Primeiro mostrar os elementos\r\n    tags.forEach((tag) => {\r\n      tag.element.style.display = 'flex';\r\n    });\r\n\r\n    // Animar entrada com stagger\r\n    const elements = tags.map((tag) => tag.element);\r\n\r\n    // Reset initial state\r\n    elements.forEach((element) => {\r\n      element.style.opacity = '0';\r\n      element.style.transform = 'translateY(20px)';\r\n    });\r\n\r\n    // Animate in with stagger\r\n    animate(\r\n      elements,\r\n      {\r\n        opacity: [0, 1],\r\n        transform: ['translateY(20px)', 'translateY(0px)'],\r\n      },\r\n      {\r\n        duration: 0.4,\r\n        easing: 'ease-out',\r\n        delay: stagger(0.1),\r\n      }\r\n    );\r\n  }\r\n\r\n  animateTagsOut(tags) {\r\n    const elements = tags.map((tag) => tag.element);\r\n\r\n    // Animate out with stagger (reverse order for visual appeal)\r\n    animate(\r\n      elements.reverse(),\r\n      {\r\n        opacity: [1, 0],\r\n        transform: ['translateY(0px)', 'translateY(-10px)'],\r\n      },\r\n      {\r\n        duration: 0.3,\r\n        easing: 'ease-in',\r\n        delay: stagger(0.05),\r\n      }\r\n    ).then(() => {\r\n      // Hide elements after animation completes\r\n      tags.forEach((tag) => {\r\n        tag.element.style.display = 'none';\r\n      });\r\n    });\r\n  }\r\n\r\n  // Método para forçar sincronização - útil para debug e integração com cache\r\n  forceSyncState() {\r\n    if (this.isInValidSteps) {\r\n      this.applyAssetSelectionToTags();\r\n    }\r\n  }\r\n\r\n  // Método para forçar sincronização das tags baseado nas seleções atuais\r\n  // Útil para integração com cache e debug\r\n  forceSyncFromCurrentSelections() {\r\n    if (this.selectedAssets.size > 0) {\r\n      // Se estamos nos steps válidos, aplicar imediatamente\r\n      if (this.isInValidSteps) {\r\n        this.applyAssetSelectionToTags();\r\n      }\r\n      // Se não estamos nos steps válidos, as seleções ficam armazenadas\r\n      // e serão aplicadas quando entrarmos nos steps válidos\r\n    }\r\n  }\r\n\r\n  // Métodos públicos para integração\r\n  showTag(category, product) {\r\n    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;\r\n    const tag = this.colorTags.find((t) => t.normalizedKey === normalizedKey);\r\n\r\n    if (tag && !tag.isVisible) {\r\n      this.animateTagsIn([tag]);\r\n    }\r\n  }\r\n\r\n  hideTag(category, product) {\r\n    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;\r\n    const tag = this.colorTags.find((t) => t.normalizedKey === normalizedKey);\r\n\r\n    if (tag && tag.isVisible) {\r\n      this.animateTagsOut([tag]);\r\n    }\r\n  }\r\n\r\n  getAllVisibleTags() {\r\n    return this.colorTags.filter((tag) => tag.isVisible);\r\n  }\r\n\r\n  // Debug method to check tag setup and current state\r\n  debugTagsState() {\r\n    console.log('🏷️ ATIVO COLOR TAG ANIMATION DEBUG');\r\n    console.log('='.repeat(50));\r\n    console.log(`📊 System initialized: ${this.isInitialized}`);\r\n    console.log(`📊 Current step: ${this.currentStep}`);\r\n    console.log(`📊 Is in valid steps: ${this.isInValidSteps}`);\r\n    console.log(`📊 Container float found: ${!!this.containerFloat}`);\r\n    console.log(`📊 Ativos itens color found: ${!!this.ativosItensColor}`);\r\n    console.log(`📊 Total color tags: ${this.colorTags.length}`);\r\n    console.log(`📊 Selected assets: ${this.selectedAssets.size}`);\r\n\r\n    if (this.selectedAssets.size > 0) {\r\n      console.log('📋 Selected assets:');\r\n      Array.from(this.selectedAssets).forEach((asset) => {\r\n        console.log(`  • ${asset}`);\r\n      });\r\n    }\r\n\r\n    if (this.colorTags.length > 0) {\r\n      console.log('\\n🏷️ COLOR TAGS:');\r\n      this.colorTags.forEach((tag, index) => {\r\n        const shouldBeVisible = this.selectedAssets.has(tag.normalizedKey);\r\n        console.log(`  ${index + 1}. ${tag.category} | ${tag.product}`);\r\n        console.log(`     Key: \"${tag.normalizedKey}\"`);\r\n        console.log(`     Should be visible: ${shouldBeVisible}`);\r\n        console.log(`     Is visible: ${tag.isVisible}`);\r\n        console.log(`     Element display: ${tag.element.style.display || 'default'}`);\r\n        console.log(`     Element opacity: ${tag.element.style.opacity || 'default'}`);\r\n      });\r\n    }\r\n\r\n    console.log('\\n🔧 SUGGESTED ACTIONS:');\r\n    if (!this.isInValidSteps && this.selectedAssets.size > 0) {\r\n      console.log('  • Navigate to step 2 or 3 to see tags');\r\n    }\r\n    if (this.isInValidSteps && this.selectedAssets.size > 0) {\r\n      console.log('  • Run: window.debugAtivoColorTag.forceSyncFromCurrentSelections()');\r\n    }\r\n    if (this.selectedAssets.size === 0) {\r\n      console.log('  • Select some assets first');\r\n    }\r\n  }\r\n\r\n  getTagByAsset(category, product) {\r\n    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;\r\n    return this.colorTags.find((t) => t.normalizedKey === normalizedKey);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACA7F,WAAS,cAAc,KAAK,MAAM;AAC9B,QAAI,IAAI,QAAQ,IAAI,MAAM;AACtB,UAAI,KAAK,IAAI;AAAA,EACrB;AAHS;AAIT,WAAS,WAAW,KAAK,MAAM;AAC3B,UAAM,QAAQ,IAAI,QAAQ,IAAI;AAC9B,QAAI,QAAQ;AACR,UAAI,OAAO,OAAO,CAAC;AAAA,EAC3B;AAJS;AAMT,WAAS,SAAS,CAAC,GAAG,GAAG,GAAG,WAAW,SAAS;AAC5C,UAAM,aAAa,YAAY,IAAI,IAAI,SAAS,YAAY;AAC5D,QAAI,cAAc,KAAK,aAAa,IAAI,QAAQ;AAC5C,YAAM,WAAW,UAAU,IAAI,IAAI,SAAS,UAAU;AACtD,YAAM,CAAC,IAAI,IAAI,IAAI,OAAO,WAAW,CAAC;AACtC,UAAI,OAAO,UAAU,GAAG,IAAI;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AARS;;;ACVT,MAAM,QAAQ,wBAAC,KAAK,KAAK,MAAM;AAC3B,QAAI,IAAI;AACJ,aAAO;AACX,QAAI,IAAI;AACJ,aAAO;AACX,WAAO;AAAA,EACX,GANc;;;ACAd,WAAS,mBAAmB,SAAS,WAAW;AAC5C,WAAO,YACD,GAAG,OAAO,0FAA0F,SAAS,KAC7G;AAAA,EACV;AAJS;;;ACET,MAAI,UAAU,6BAAM;AAAA,EAAE,GAAR;AACd,MAAI,YAAY,6BAAM;AAAA,EAAE,GAAR;AAChB,MAAI,MAAuC;AACvC,cAAU,wBAAC,OAAO,SAAS,cAAc;AACrC,UAAI,CAAC,SAAS,OAAO,YAAY,aAAa;AAC1C,gBAAQ,KAAK,mBAAmB,SAAS,SAAS,CAAC;AAAA,MACvD;AAAA,IACJ,GAJU;AAKV,gBAAY,wBAAC,OAAO,SAAS,cAAc;AACvC,UAAI,CAAC,OAAO;AACR,cAAM,IAAI,MAAM,mBAAmB,SAAS,SAAS,CAAC;AAAA,MAC1D;AAAA,IACJ,GAJY;AAAA,EAKhB;;;ACfA,MAAM,qBAAqB,CAAC;;;ACG5B,MAAM,oBAAoB,wBAAC,MAAM,+BAA+B,KAAK,CAAC,GAA5C;;;ACH1B,WAAS,SAAS,OAAO;AACrB,WAAO,OAAO,UAAU,YAAY,UAAU;AAAA,EAClD;AAFS;;;ACGT,MAAM,oBAAoB,wBAAC,MAAM,cAAc,KAAK,CAAC,GAA3B;;;;ACF1B,WAAS,KAAK,UAAU;AACpB,QAAI;AACJ,WAAO,MAAM;AACT,UAAI,WAAW;AACX,iBAAS,SAAS;AACtB,aAAO;AAAA,IACX;AAAA,EACJ;AAPS;;;ACAT,MAAM,OAAO,mDAAC,QAAQ,KAAT;;;ACMb,MAAM,mBAAmB,wBAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAvB;AACzB,MAAM,OAAO,2BAAI,iBAAiB,aAAa,OAAO,gBAAgB,GAAzD;;;ACKb,MAAM,WAAW,mDAAC,MAAM,IAAI,UAAU;AAClC,UAAM,mBAAmB,KAAK;AAC9B,WAAO,qBAAqB,IAAI,KAAK,QAAQ,QAAQ;AAAA,EACzD,GAHiB;;;ACXjB,MAAM,sBAAN,MAA0B;AAAA,IAF1B,OAE0B;AAAA;AAAA;AAAA,IACtB,cAAc;AACV,WAAK,gBAAgB,CAAC;AAAA,IAC1B;AAAA,IACA,IAAI,SAAS;AACT,oBAAc,KAAK,eAAe,OAAO;AACzC,aAAO,MAAM,WAAW,KAAK,eAAe,OAAO;AAAA,IACvD;AAAA,IACA,OAAO,GAAG,GAAG,GAAG;AACZ,YAAM,mBAAmB,KAAK,cAAc;AAC5C,UAAI,CAAC;AACD;AACJ,UAAI,qBAAqB,GAAG;AAIxB,aAAK,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC;AAAA,MACjC,OACK;AACD,iBAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AAKvC,gBAAM,UAAU,KAAK,cAAc,CAAC;AACpC,qBAAW,QAAQ,GAAG,GAAG,CAAC;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,UAAU;AACN,aAAO,KAAK,cAAc;AAAA,IAC9B;AAAA,IACA,QAAQ;AACJ,WAAK,cAAc,SAAS;AAAA,IAChC;AAAA,EACJ;;;AC9BA,MAAM,wBAAwB,mDAAC,YAAY,UAAU,KAAvB;AAE9B,MAAM,wBAAwB,mDAAC,iBAAiB,eAAe,KAAjC;;;ACH9B,WAAS,kBAAkB,UAAU,eAAe;AAChD,WAAO,gBAAgB,YAAY,MAAO,iBAAiB;AAAA,EAC/D;AAFS;;;ACJT,MAAM,SAAS,oBAAI,IAAI;AACvB,WAAS,UAAU,SAAS;AACxB,WAAO,OAAO,IAAI,OAAO;AAAA,EAC7B;AAFS;AAGT,WAAS,SAAS,WAAW,SAAS,WAAW;AAC7C,QAAI,aAAa,OAAO,IAAI,OAAO;AAC/B;AACJ,YAAQ,KAAK,mBAAmB,SAAS,SAAS,CAAC;AACnD,WAAO,IAAI,OAAO;AAAA,EACtB;AALS;;;ACNT,MAAM,OAAO,wBAAC,KAAK,KAAK,MAAM;AAC1B,UAAM,YAAY,MAAM;AACxB,aAAW,IAAI,OAAO,YAAa,aAAa,YAAa;AAAA,EACjE,GAHa;;;ACoBb,MAAM,aAAa,wBAAC,GAAG,IAAI,UAAU,IAAM,IAAM,KAAK,IAAM,MAAM,KAAK,IAAM,KAAK,IAAM,OAAO,IAAI,IAAM,MACrG,GADe;AAEnB,MAAM,uBAAuB;AAC7B,MAAM,2BAA2B;AACjC,WAAS,gBAAgB,GAAG,YAAY,YAAY,KAAK,KAAK;AAC1D,QAAI;AACJ,QAAI;AACJ,QAAI,IAAI;AACR,OAAG;AACC,iBAAW,cAAc,aAAa,cAAc;AACpD,iBAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,UAAI,WAAW,GAAK;AAChB,qBAAa;AAAA,MACjB,OACK;AACD,qBAAa;AAAA,MACjB;AAAA,IACJ,SAAS,KAAK,IAAI,QAAQ,IAAI,wBAC1B,EAAE,IAAI;AACV,WAAO;AAAA,EACX;AAhBS;AAiBT,WAAS,YAAY,KAAK,KAAK,KAAK,KAAK;AAErC,QAAI,QAAQ,OAAO,QAAQ;AACvB,aAAO;AACX,UAAM,WAAW,wBAAC,OAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK,GAAG,GAA1C;AAEjB,WAAO,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,EAC3E;AAPS;;;ACvCT,MAAM,eAAe,wBAAC,WAAW,CAAC,MAAM,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE,KAAK,GAA9E;;;ACArB,MAAM,gBAAgB,wBAAC,WAAW,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,GAAnC;;;ACEtB,MAAM,UAAwB,4BAAY,MAAM,MAAM,MAAM,IAAI;AAChE,MAAM,SAAuB,8BAAc,OAAO;AAClD,MAAM,YAA0B,6BAAa,MAAM;;;ACJnD,MAAM,aAAa,wBAAC,OAAO,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,IAA5E;;;ACCnB,MAAM,SAAS,wBAAC,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC,GAAhC;AACf,MAAM,UAAU,cAAc,MAAM;AACpC,MAAM,YAAY,aAAa,MAAM;;;ACHrC,MAAM,SAAuB,4BAAY,MAAM,GAAG,GAAG,CAAC;AACtD,MAAM,UAAwB,4BAAY,GAAG,GAAG,MAAM,CAAC;AACvD,MAAM,YAA0B,4BAAY,MAAM,GAAG,MAAM,CAAC;;;ACJ5D,MAAM,gBAAgB,wBAACA,UAAS;AAC5B,WAAO,MAAM,QAAQA,KAAI,KAAK,OAAOA,MAAK,CAAC,MAAM;AAAA,EACrD,GAFsB;;;ACGtB,WAAS,oBAAoB,QAAQ,GAAG;AACpC,WAAO,cAAc,MAAM,IAAI,OAAO,KAAK,GAAG,OAAO,QAAQ,CAAC,CAAC,IAAI;AAAA,EACvE;AAFS;;;ACHT,MAAM,qBAAqB,wBAAC,WAAW,MAAM,QAAQ,MAAM,KAAK,OAAO,OAAO,CAAC,MAAM,UAA1D;;;ACS3B,MAAM,eAAe;AAAA,IACjB,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAM,gBAAgB,wBAAC,WAAW;AAC9B,WAAO,OAAO,WAAW;AAAA,EAC7B,GAFsB;AAGtB,MAAM,6BAA6B,wBAAC,eAAe;AAC/C,QAAI,mBAAmB,UAAU,GAAG;AAEhC,gBAAU,WAAW,WAAW,GAAG,2DAA2D,qBAAqB;AACnH,YAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,aAAO,YAAY,IAAI,IAAI,IAAI,EAAE;AAAA,IACrC,WACS,cAAc,UAAU,GAAG;AAEhC,gBAAU,aAAa,UAAU,MAAM,QAAW,wBAAwB,UAAU,KAAK,qBAAqB;AAC9G,aAAO,aAAa,UAAU;AAAA,IAClC;AACA,WAAO;AAAA,EACX,GAbmC;;;ACzBnC,MAAM,aAAa;AAAA,IACf;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,EACJ;;;ACTA,MAAM,cAAc;AAAA,IAChB,OAAO;AAAA,IACP,sBAAsB;AAAA,EAC1B;;;ACDA,WAAS,iBAAiB,cAAc,UAAU;AAK9C,QAAI,YAAY,oBAAI,IAAI;AACxB,QAAI,YAAY,oBAAI,IAAI;AAKxB,QAAI,eAAe;AACnB,QAAI,iBAAiB;AAIrB,UAAM,cAAc,oBAAI,QAAQ;AAChC,QAAI,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,IAClB;AACA,QAAI,WAAW;AACf,aAAS,gBAAgB,UAAU;AAC/B,UAAI,YAAY,IAAI,QAAQ,GAAG;AAC3B,aAAK,SAAS,QAAQ;AACtB,qBAAa;AAAA,MACjB;AACA;AACA,eAAS,eAAe;AAAA,IAC5B;AAPS;AAQT,UAAM,OAAO;AAAA;AAAA;AAAA;AAAA,MAIT,UAAU,wBAAC,UAAU,YAAY,OAAO,YAAY,UAAU;AAC1D,cAAM,oBAAoB,aAAa;AACvC,cAAM,QAAQ,oBAAoB,YAAY;AAC9C,YAAI;AACA,sBAAY,IAAI,QAAQ;AAC5B,YAAI,CAAC,MAAM,IAAI,QAAQ;AACnB,gBAAM,IAAI,QAAQ;AACtB,eAAO;AAAA,MACX,GARU;AAAA;AAAA;AAAA;AAAA,MAYV,QAAQ,wBAAC,aAAa;AAClB,kBAAU,OAAO,QAAQ;AACzB,oBAAY,OAAO,QAAQ;AAAA,MAC/B,GAHQ;AAAA;AAAA;AAAA;AAAA,MAOR,SAAS,wBAACC,eAAc;AACpB,0BAAkBA;AAMlB,YAAI,cAAc;AACd,2BAAiB;AACjB;AAAA,QACJ;AACA,uBAAe;AACf,SAAC,WAAW,SAAS,IAAI,CAAC,WAAW,SAAS;AAE9C,kBAAU,QAAQ,eAAe;AAIjC,YAAI,YAAY,YAAY,OAAO;AAC/B,sBAAY,MAAM,UAAU,QAAQ,EAAE,KAAK,QAAQ;AAAA,QACvD;AACA,mBAAW;AAGX,kBAAU,MAAM;AAChB,uBAAe;AACf,YAAI,gBAAgB;AAChB,2BAAiB;AACjB,eAAK,QAAQA,UAAS;AAAA,QAC1B;AAAA,MACJ,GA9BS;AAAA,IA+Bb;AACA,WAAO;AAAA,EACX;AAvFS;;;ACET,MAAM,aAAa;AACnB,WAAS,oBAAoB,mBAAmB,gBAAgB;AAC5D,QAAI,eAAe;AACnB,QAAI,oBAAoB;AACxB,UAAM,QAAQ;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,IAClB;AACA,UAAM,mBAAmB,6BAAO,eAAe,MAAtB;AACzB,UAAMC,SAAQ,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC1C,UAAI,GAAG,IAAI,iBAAiB,kBAAkB,iBAAiB,MAAM,MAAS;AAC9E,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AACL,UAAM,EAAE,OAAO,MAAM,kBAAkB,WAAW,QAAQ,WAAW,QAAQ,WAAY,IAAIA;AAC7F,UAAM,eAAe,6BAAM;AACvB,YAAM,YAAY,mBAAmB,kBAC/B,MAAM,YACN,YAAY,IAAI;AACtB,qBAAe;AACf,UAAI,CAAC,mBAAmB,iBAAiB;AACrC,cAAM,QAAQ,oBACR,MAAO,KACP,KAAK,IAAI,KAAK,IAAI,YAAY,MAAM,WAAW,UAAU,GAAG,CAAC;AAAA,MACvE;AACA,YAAM,YAAY;AAClB,YAAM,eAAe;AAErB,YAAM,QAAQ,KAAK;AACnB,WAAK,QAAQ,KAAK;AAClB,uBAAiB,QAAQ,KAAK;AAC9B,gBAAU,QAAQ,KAAK;AACvB,aAAO,QAAQ,KAAK;AACpB,gBAAU,QAAQ,KAAK;AACvB,aAAO,QAAQ,KAAK;AACpB,iBAAW,QAAQ,KAAK;AACxB,YAAM,eAAe;AACrB,UAAI,gBAAgB,gBAAgB;AAChC,4BAAoB;AACpB,0BAAkB,YAAY;AAAA,MAClC;AAAA,IACJ,GA1BqB;AA2BrB,UAAM,OAAO,6BAAM;AACf,qBAAe;AACf,0BAAoB;AACpB,UAAI,CAAC,MAAM,cAAc;AACrB,0BAAkB,YAAY;AAAA,MAClC;AAAA,IACJ,GANa;AAOb,UAAM,WAAW,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC7C,YAAM,OAAOA,OAAM,GAAG;AACtB,UAAI,GAAG,IAAI,CAACC,UAAS,YAAY,OAAO,YAAY,UAAU;AAC1D,YAAI,CAAC;AACD,eAAK;AACT,eAAO,KAAK,SAASA,UAAS,WAAW,SAAS;AAAA,MACtD;AACA,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AACL,UAAM,SAAS,wBAACA,aAAY;AACxB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,QAAAD,OAAM,WAAW,CAAC,CAAC,EAAE,OAAOC,QAAO;AAAA,MACvC;AAAA,IACJ,GAJe;AAKf,WAAO,EAAE,UAAU,QAAQ,OAAO,OAAAD,OAAM;AAAA,EAC5C;AA/DS;;;ACFT,MAAM,EAAE,UAAU,OAAO,QAAQ,aAAa,OAAO,WAAW,OAAO,WAAY,IAAoB,oCAAoB,OAAO,0BAA0B,cAAc,wBAAwB,MAAM,IAAI;;;ACA5M,MAAI;AACJ,WAAS,YAAY;AACjB,UAAM;AAAA,EACV;AAFS;AAWT,MAAM,OAAO;AAAA,IACT,KAAK,6BAAM;AACP,UAAI,QAAQ,QAAW;AACnB,aAAK,IAAI,UAAU,gBAAgB,mBAAmB,kBAChD,UAAU,YACV,YAAY,IAAI,CAAC;AAAA,MAC3B;AACA,aAAO;AAAA,IACX,GAPK;AAAA,IAQL,KAAK,wBAAC,YAAY;AACd,YAAM;AACN,qBAAe,SAAS;AAAA,IAC5B,GAHK;AAAA,EAIT;;;AC5BA,MAAM,mBAAmB;AAAA,IACrB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,OAAO;AAAA,EACX;;;ACJA,MAAM,wBAAwB,wBAAC,UAAU,CAAC,QAAQ,OAAO,QAAQ,YAAY,IAAI,WAAW,KAAK,GAAnE;AAC9B,MAAM,oBACQ,sCAAsB,IAAI;AACxC,MAAM,wBACQ,sCAAsB,QAAQ;AAC5C,MAAM,qBAAqB,wBAAC,UAAU;AAClC,UAAM,kBAAkB,sBAAsB,KAAK;AACnD,QAAI,CAAC;AACD,aAAO;AAEX,WAAO,uBAAuB,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC;AAAA,EAClE,GAN2B;AAO3B,MAAM,yBAAyB;;;ACV/B,MAAM,SAAS;AAAA,IACX,MAAM,wBAAC,MAAM,OAAO,MAAM,UAApB;AAAA,IACN,OAAO;AAAA,IACP,WAAW,wBAAC,MAAM,GAAP;AAAA,EACf;AACA,MAAM,QAAQ;AAAA,IACV,GAAG;AAAA,IACH,WAAW,wBAAC,MAAM,MAAM,GAAG,GAAG,CAAC,GAApB;AAAA,EACf;AACA,MAAM,QAAQ;AAAA,IACV,GAAG;AAAA,IACH,SAAS;AAAA,EACb;;;ACZA,MAAM,WAAW,wBAAC,MAAM,KAAK,MAAM,IAAI,GAAM,IAAI,KAAhC;;;ACFjB,MAAM,aAAa;;;ACAnB,WAAS,UAAU,GAAG;AAClB,WAAO,KAAK;AAAA,EAChB;AAFS;;;ACAT,MAAM,mBAAmB;;;ACQzB,MAAM,gBAAgB,wBAAC,MAAM,aAAa,CAAC,MAAM;AAC7C,WAAO,QAAS,OAAO,MAAM,YACzB,iBAAiB,KAAK,CAAC,KACvB,EAAE,WAAW,IAAI,KAChB,YACG,CAAC,UAAU,CAAC,KACZ,OAAO,UAAU,eAAe,KAAK,GAAG,QAAQ,CAAE;AAAA,EAC9D,GAPsB;AAQtB,MAAM,aAAa,wBAAC,OAAO,OAAO,UAAU,CAAC,MAAM;AAC/C,QAAI,OAAO,MAAM;AACb,aAAO;AACX,UAAM,CAAC,GAAG,GAAG,GAAGE,MAAK,IAAI,EAAE,MAAM,UAAU;AAC3C,WAAO;AAAA,MACH,CAAC,KAAK,GAAG,WAAW,CAAC;AAAA,MACrB,CAAC,KAAK,GAAG,WAAW,CAAC;AAAA,MACrB,CAAC,KAAK,GAAG,WAAW,CAAC;AAAA,MACrB,OAAOA,WAAU,SAAY,WAAWA,MAAK,IAAI;AAAA,IACrD;AAAA,EACJ,GAVmB;;;ACXnB,MAAM,eAAe,wBAAC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAtB;AACrB,MAAM,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,WAAW,wBAAC,MAAM,KAAK,MAAM,aAAa,CAAC,CAAC,GAAjC;AAAA,EACf;AACA,MAAM,OAAO;AAAA,IACT,MAAoB,8BAAc,OAAO,KAAK;AAAA,IAC9C,OAAqB,2BAAW,OAAO,SAAS,MAAM;AAAA,IACtD,WAAW,wBAAC,EAAE,KAAK,OAAO,MAAM,OAAO,UAAU,EAAE,MAAM,UACrD,QAAQ,UAAU,GAAG,IACrB,OACA,QAAQ,UAAU,KAAK,IACvB,OACA,QAAQ,UAAU,IAAI,IACtB,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC,KARO;AAAA,EASf;;;ACnBA,WAAS,SAAS,GAAG;AACjB,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,IAAI;AAER,QAAI,EAAE,SAAS,GAAG;AACd,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,UAAI,EAAE,UAAU,GAAG,CAAC;AAAA,IAExB,OACK;AACD,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,UAAI,EAAE,UAAU,GAAG,CAAC;AACpB,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACT;AACA,WAAO;AAAA,MACH,KAAK,SAAS,GAAG,EAAE;AAAA,MACnB,OAAO,SAAS,GAAG,EAAE;AAAA,MACrB,MAAM,SAAS,GAAG,EAAE;AAAA,MACpB,OAAO,IAAI,SAAS,GAAG,EAAE,IAAI,MAAM;AAAA,IACvC;AAAA,EACJ;AA7BS;AA8BT,MAAM,MAAM;AAAA,IACR,MAAoB,8BAAc,GAAG;AAAA,IACrC,OAAO;AAAA,IACP,WAAW,KAAK;AAAA,EACpB;;;ACpCA,MAAM,iBAAiB,mDAAC,UAAU;AAAA,IAC9B,MAAM,wBAAC,MAAM,OAAO,MAAM,YAAY,EAAE,SAAS,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,WAAW,GAA5E;AAAA,IACN,OAAO;AAAA,IACP,WAAW,wBAAC,MAAM,GAAG,CAAC,GAAG,IAAI,IAAlB;AAAA,EACf,IAJuB;AAKvB,MAAM,UAAwB,+BAAe,KAAK;AAClD,MAAM,UAAwB,+BAAe,GAAG;AAChD,MAAM,KAAmB,+BAAe,IAAI;AAC5C,MAAM,KAAmB,+BAAe,IAAI;AAC5C,MAAM,KAAmB,+BAAe,IAAI;AAC5C,MAAM,qBAAoC,wBAAO;AAAA,IAC7C,GAAG;AAAA,IACH,OAAO,wBAAC,MAAM,QAAQ,MAAM,CAAC,IAAI,KAA1B;AAAA,IACP,WAAW,wBAAC,MAAM,QAAQ,UAAU,IAAI,GAAG,GAAhC;AAAA,EACf,IAAI;;;ACVJ,MAAM,OAAO;AAAA,IACT,MAAoB,8BAAc,OAAO,KAAK;AAAA,IAC9C,OAAqB,2BAAW,OAAO,cAAc,WAAW;AAAA,IAChE,WAAW,wBAAC,EAAE,KAAK,YAAY,WAAW,OAAO,UAAU,EAAE,MAAM;AAC/D,aAAQ,UACJ,KAAK,MAAM,GAAG,IACd,OACA,QAAQ,UAAU,SAAS,UAAU,CAAC,IACtC,OACA,QAAQ,UAAU,SAAS,SAAS,CAAC,IACrC,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AAAA,IACR,GAVW;AAAA,EAWf;;;ACfA,MAAM,QAAQ;AAAA,IACV,MAAM,wBAAC,MAAM,KAAK,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,GAAjD;AAAA,IACN,OAAO,wBAAC,MAAM;AACV,UAAI,KAAK,KAAK,CAAC,GAAG;AACd,eAAO,KAAK,MAAM,CAAC;AAAA,MACvB,WACS,KAAK,KAAK,CAAC,GAAG;AACnB,eAAO,KAAK,MAAM,CAAC;AAAA,MACvB,OACK;AACD,eAAO,IAAI,MAAM,CAAC;AAAA,MACtB;AAAA,IACJ,GAVO;AAAA,IAWP,WAAW,wBAAC,MAAM;AACd,aAAO,OAAO,MAAM,WACd,IACA,EAAE,eAAe,KAAK,IAClB,KAAK,UAAU,CAAC,IAChB,KAAK,UAAU,CAAC;AAAA,IAC9B,GANW;AAAA,IAOX,mBAAmB,wBAAC,MAAM;AACtB,YAAM,SAAS,MAAM,MAAM,CAAC;AAC5B,aAAO,QAAQ;AACf,aAAO,MAAM,UAAU,MAAM;AAAA,IACjC,GAJmB;AAAA,EAKvB;;;AC7BA,MAAM,aAAa;;;ACKnB,WAAS,KAAK,GAAG;AACb,WAAQ,MAAM,CAAC,KACX,OAAO,MAAM,aACZ,EAAE,MAAM,UAAU,GAAG,UAAU,MAC3B,EAAE,MAAM,UAAU,GAAG,UAAU,KAChC;AAAA,EACZ;AANS;AAOT,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,MAAM,qBAAqB;AAC3B,MAAM,cAAc;AAEpB,MAAM,eAAe;AACrB,WAAS,oBAAoB,OAAO;AAChC,UAAM,gBAAgB,MAAM,SAAS;AACrC,UAAM,SAAS,CAAC;AAChB,UAAM,UAAU;AAAA,MACZ,OAAO,CAAC;AAAA,MACR,QAAQ,CAAC;AAAA,MACT,KAAK,CAAC;AAAA,IACV;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,IAAI;AACR,UAAM,YAAY,cAAc,QAAQ,cAAc,CAAC,gBAAgB;AACnE,UAAI,MAAM,KAAK,WAAW,GAAG;AACzB,gBAAQ,MAAM,KAAK,CAAC;AACpB,cAAM,KAAK,WAAW;AACtB,eAAO,KAAK,MAAM,MAAM,WAAW,CAAC;AAAA,MACxC,WACS,YAAY,WAAW,kBAAkB,GAAG;AACjD,gBAAQ,IAAI,KAAK,CAAC;AAClB,cAAM,KAAK,SAAS;AACpB,eAAO,KAAK,WAAW;AAAA,MAC3B,OACK;AACD,gBAAQ,OAAO,KAAK,CAAC;AACrB,cAAM,KAAK,YAAY;AACvB,eAAO,KAAK,WAAW,WAAW,CAAC;AAAA,MACvC;AACA,QAAE;AACF,aAAO;AAAA,IACX,CAAC;AACD,UAAM,QAAQ,UAAU,MAAM,WAAW;AACzC,WAAO,EAAE,QAAQ,OAAO,SAAS,MAAM;AAAA,EAC3C;AA/BS;AAgCT,WAAS,kBAAkB,GAAG;AAC1B,WAAO,oBAAoB,CAAC,EAAE;AAAA,EAClC;AAFS;AAGT,WAAS,kBAAkB,QAAQ;AAC/B,UAAM,EAAE,OAAO,MAAM,IAAI,oBAAoB,MAAM;AACnD,UAAM,cAAc,MAAM;AAC1B,WAAO,CAAC,MAAM;AACV,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,kBAAU,MAAM,CAAC;AACjB,YAAI,EAAE,CAAC,MAAM,QAAW;AACpB,gBAAM,OAAO,MAAM,CAAC;AACpB,cAAI,SAAS,cAAc;AACvB,sBAAU,SAAS,EAAE,CAAC,CAAC;AAAA,UAC3B,WACS,SAAS,aAAa;AAC3B,sBAAU,MAAM,UAAU,EAAE,CAAC,CAAC;AAAA,UAClC,OACK;AACD,sBAAU,EAAE,CAAC;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAtBS;AAuBT,MAAM,uBAAuB,wBAAC,MAAM,OAAO,MAAM,WAAW,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM,kBAAkB,CAAC,IAAI,GAAhF;AAC7B,WAAS,kBAAkB,GAAG;AAC1B,UAAM,SAAS,kBAAkB,CAAC;AAClC,UAAM,cAAc,kBAAkB,CAAC;AACvC,WAAO,YAAY,OAAO,IAAI,oBAAoB,CAAC;AAAA,EACvD;AAJS;AAKT,MAAM,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACJ;;;ACvFA,WAAS,SAAS,GAAG,GAAG,GAAG;AACvB,QAAI,IAAI;AACJ,WAAK;AACT,QAAI,IAAI;AACJ,WAAK;AACT,QAAI,IAAI,IAAI;AACR,aAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,QAAI,IAAI,IAAI;AACR,aAAO;AACX,QAAI,IAAI,IAAI;AACR,aAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AACvC,WAAO;AAAA,EACX;AAZS;AAaT,WAAS,WAAW,EAAE,KAAK,YAAY,WAAW,OAAAC,OAAM,GAAG;AACvD,WAAO;AACP,kBAAc;AACd,iBAAa;AACb,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,QAAI,CAAC,YAAY;AACb,YAAM,QAAQ,OAAO;AAAA,IACzB,OACK;AACD,YAAM,IAAI,YAAY,MAChB,aAAa,IAAI,cACjB,YAAY,aAAa,YAAY;AAC3C,YAAM,IAAI,IAAI,YAAY;AAC1B,YAAM,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAChC,cAAQ,SAAS,GAAG,GAAG,GAAG;AAC1B,aAAO,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAAA,IACrC;AACA,WAAO;AAAA,MACH,KAAK,KAAK,MAAM,MAAM,GAAG;AAAA,MACzB,OAAO,KAAK,MAAM,QAAQ,GAAG;AAAA,MAC7B,MAAM,KAAK,MAAM,OAAO,GAAG;AAAA,MAC3B,OAAAA;AAAA,IACJ;AAAA,EACJ;AAzBS;;;ACdT,WAAS,aAAa,GAAG,GAAG;AACxB,WAAO,CAAC,MAAO,IAAI,IAAI,IAAI;AAAA,EAC/B;AAFS;;;ACqBT,MAAM,YAAY,wBAAC,MAAM,IAAIC,cAAa;AACtC,WAAO,QAAQ,KAAK,QAAQA;AAAA,EAChC,GAFkB;;;ACVlB,MAAM,iBAAiB,wBAAC,MAAM,IAAI,MAAM;AACpC,UAAM,WAAW,OAAO;AACxB,UAAM,OAAO,KAAK,KAAK,KAAK,YAAY;AACxC,WAAO,OAAO,IAAI,IAAI,KAAK,KAAK,IAAI;AAAA,EACxC,GAJuB;AAKvB,MAAM,aAAa,CAAC,KAAK,MAAM,IAAI;AACnC,MAAM,eAAe,wBAAC,MAAM,WAAW,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,GAA7C;AACrB,WAAS,OAAOC,QAAO;AACnB,UAAM,OAAO,aAAaA,MAAK;AAC/B,YAAQ,QAAQ,IAAI,GAAG,IAAIA,MAAK,wEAAwE,sBAAsB;AAC9H,QAAI,CAAC,QAAQ,IAAI;AACb,aAAO;AACX,QAAI,QAAQ,KAAK,MAAMA,MAAK;AAC5B,QAAI,SAAS,MAAM;AAEf,cAAQ,WAAW,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AAXS;AAYT,MAAM,WAAW,wBAAC,MAAM,OAAO;AAC3B,UAAM,WAAW,OAAO,IAAI;AAC5B,UAAM,SAAS,OAAO,EAAE;AACxB,QAAI,CAAC,YAAY,CAAC,QAAQ;AACtB,aAAO,aAAa,MAAM,EAAE;AAAA,IAChC;AACA,UAAM,UAAU,EAAE,GAAG,SAAS;AAC9B,WAAO,CAAC,MAAM;AACV,cAAQ,MAAM,eAAe,SAAS,KAAK,OAAO,KAAK,CAAC;AACxD,cAAQ,QAAQ,eAAe,SAAS,OAAO,OAAO,OAAO,CAAC;AAC9D,cAAQ,OAAO,eAAe,SAAS,MAAM,OAAO,MAAM,CAAC;AAC3D,cAAQ,QAAQ,UAAU,SAAS,OAAO,OAAO,OAAO,CAAC;AACzD,aAAO,KAAK,UAAU,OAAO;AAAA,IACjC;AAAA,EACJ,GAdiB;;;AC9BjB,MAAM,kBAAkB,oBAAI,IAAI,CAAC,QAAQ,QAAQ,CAAC;AAMlD,WAAS,cAAc,QAAQ,QAAQ;AACnC,QAAI,gBAAgB,IAAI,MAAM,GAAG;AAC7B,aAAO,CAAC,MAAO,KAAK,IAAI,SAAS;AAAA,IACrC,OACK;AACD,aAAO,CAAC,MAAO,KAAK,IAAI,SAAS;AAAA,IACrC;AAAA,EACJ;AAPS;;;ACGT,WAASC,WAAU,GAAG,GAAG;AACrB,WAAO,CAAC,MAAM,UAAY,GAAG,GAAG,CAAC;AAAA,EACrC;AAFS,SAAAA,YAAA;AAGT,WAAS,SAAS,GAAG;AACjB,QAAI,OAAO,MAAM,UAAU;AACvB,aAAOA;AAAA,IACX,WACS,OAAO,MAAM,UAAU;AAC5B,aAAO,mBAAmB,CAAC,IACrB,eACA,MAAM,KAAK,CAAC,IACR,WACA;AAAA,IACd,WACS,MAAM,QAAQ,CAAC,GAAG;AACvB,aAAO;AAAA,IACX,WACS,OAAO,MAAM,UAAU;AAC5B,aAAO,MAAM,KAAK,CAAC,IAAI,WAAW;AAAA,IACtC;AACA,WAAO;AAAA,EACX;AAlBS;AAmBT,WAAS,SAAS,GAAG,GAAG;AACpB,UAAM,SAAS,CAAC,GAAG,CAAC;AACpB,UAAM,YAAY,OAAO;AACzB,UAAM,aAAa,EAAE,IAAI,CAAC,GAAG,MAAM,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD,WAAO,CAAC,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,eAAO,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AAAA,MAC/B;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAVS;AAWT,WAAS,UAAU,GAAG,GAAG;AACrB,UAAM,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE;AAC5B,UAAM,aAAa,CAAC;AACpB,eAAW,OAAO,QAAQ;AACtB,UAAI,EAAE,GAAG,MAAM,UAAa,EAAE,GAAG,MAAM,QAAW;AAC9C,mBAAW,GAAG,IAAI,SAAS,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,MACrD;AAAA,IACJ;AACA,WAAO,CAAC,MAAM;AACV,iBAAW,OAAO,YAAY;AAC1B,eAAO,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAdS;AAeT,WAAS,WAAW,QAAQ,QAAQ;AAChC,UAAM,gBAAgB,CAAC;AACvB,UAAM,WAAW,EAAE,OAAO,GAAG,KAAK,GAAG,QAAQ,EAAE;AAC/C,aAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAK;AAC3C,YAAM,OAAO,OAAO,MAAM,CAAC;AAC3B,YAAM,cAAc,OAAO,QAAQ,IAAI,EAAE,SAAS,IAAI,CAAC;AACvD,YAAM,cAAc,OAAO,OAAO,WAAW,KAAK;AAClD,oBAAc,CAAC,IAAI;AACnB,eAAS,IAAI;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AAXS;AAYT,MAAM,aAAa,wBAAC,QAAQ,WAAW;AACnC,UAAM,WAAW,QAAQ,kBAAkB,MAAM;AACjD,UAAM,cAAc,oBAAoB,MAAM;AAC9C,UAAM,cAAc,oBAAoB,MAAM;AAC9C,UAAM,iBAAiB,YAAY,QAAQ,IAAI,WAAW,YAAY,QAAQ,IAAI,UAC9E,YAAY,QAAQ,MAAM,WAAW,YAAY,QAAQ,MAAM,UAC/D,YAAY,QAAQ,OAAO,UAAU,YAAY,QAAQ,OAAO;AACpE,QAAI,gBAAgB;AAChB,UAAK,gBAAgB,IAAI,MAAM,KAC3B,CAAC,YAAY,OAAO,UACnB,gBAAgB,IAAI,MAAM,KACvB,CAAC,YAAY,OAAO,QAAS;AACjC,eAAO,cAAc,QAAQ,MAAM;AAAA,MACvC;AACA,aAAO,KAAK,SAAS,WAAW,aAAa,WAAW,GAAG,YAAY,MAAM,GAAG,QAAQ;AAAA,IAC5F,OACK;AACD,cAAQ,MAAM,mBAAmB,MAAM,UAAU,MAAM,4KAA4K,0BAA0B;AAC7P,aAAO,aAAa,QAAQ,MAAM;AAAA,IACtC;AAAA,EACJ,GApBmB;;;AClEnB,WAAS,IAAI,MAAM,IAAI,GAAG;AACtB,QAAI,OAAO,SAAS,YAChB,OAAO,OAAO,YACd,OAAO,MAAM,UAAU;AACvB,aAAO,UAAU,MAAM,IAAI,CAAC;AAAA,IAChC;AACA,UAAM,QAAQ,SAAS,IAAI;AAC3B,WAAO,MAAM,MAAM,EAAE;AAAA,EACzB;AARS;;;ACAT,MAAM,kBAAkB,wBAAC,WAAW;AAChC,UAAM,gBAAgB,wBAAC,EAAE,UAAU,MAAM,OAAO,SAAS,GAAnC;AACtB,WAAO;AAAA,MACH,OAAO,wBAAC,YAAY,SAAS,MAAM,OAAO,eAAe,SAAS,GAA3D;AAAA,MACP,MAAM,6BAAM,YAAY,aAAa,GAA/B;AAAA;AAAA;AAAA;AAAA;AAAA,MAKN,KAAK,6BAAO,UAAU,eAAe,UAAU,YAAY,KAAK,IAAI,GAA/D;AAAA,IACT;AAAA,EACJ,GAXwB;;;ACHxB,MAAM,uBAAuB,wBAAC,QAAQ,UACtC,aAAa,OACR;AACD,QAAI,SAAS;AACb,UAAM,YAAY,KAAK,IAAI,KAAK,MAAM,WAAW,UAAU,GAAG,CAAC;AAC/D,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,gBAAU,KAAK,MAAM,OAAO,KAAK,YAAY,EAAE,IAAI,GAAK,IAAI,MAAQ;AAAA,IACxE;AACA,WAAO,UAAU,OAAO,UAAU,GAAG,OAAO,SAAS,CAAC,CAAC;AAAA,EAC3D,GAT6B;;;ACI7B,MAAM,uBAAuB;AAC7B,WAAS,sBAAsB,WAAW;AACtC,QAAI,WAAW;AACf,UAAM,WAAW;AACjB,QAAI,QAAQ,UAAU,KAAK,QAAQ;AACnC,WAAO,CAAC,MAAM,QAAQ,WAAW,sBAAsB;AACnD,kBAAY;AACZ,cAAQ,UAAU,KAAK,QAAQ;AAAA,IACnC;AACA,WAAO,YAAY,uBAAuB,WAAW;AAAA,EACzD;AATS;;;ACCT,WAAS,sBAAsB,SAASC,SAAQ,KAAK,iBAAiB;AAClE,UAAM,YAAY,gBAAgB,EAAE,GAAG,SAAS,WAAW,CAAC,GAAGA,MAAK,EAAE,CAAC;AACvE,UAAM,WAAW,KAAK,IAAI,sBAAsB,SAAS,GAAG,oBAAoB;AAChF,WAAO;AAAA,MACH,MAAM;AAAA,MACN,MAAM,wBAACC,cAAa;AAChB,eAAO,UAAU,KAAK,WAAWA,SAAQ,EAAE,QAAQD;AAAA,MACvD,GAFM;AAAA,MAGN,UAAU,sBAAsB,QAAQ;AAAA,IAC5C;AAAA,EACJ;AAVS;;;ACJT,MAAM,yBAAyB;AAC/B,WAAS,sBAAsB,cAAc,GAAG,SAAS;AACrD,UAAM,QAAQ,KAAK,IAAI,IAAI,wBAAwB,CAAC;AACpD,WAAO,kBAAkB,UAAU,aAAa,KAAK,GAAG,IAAI,KAAK;AAAA,EACrE;AAHS;;;ACHT,MAAM,iBAAiB;AAAA;AAAA,IAEnB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA;AAAA,IAEV,UAAU;AAAA;AAAA,IACV,QAAQ;AAAA,IACR,gBAAgB;AAAA;AAAA;AAAA,IAEhB,WAAW;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,IACb;AAAA,IACA,WAAW;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,IACb;AAAA;AAAA,IAEA,aAAa;AAAA;AAAA,IACb,aAAa;AAAA;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EAChB;;;ACrBA,MAAM,UAAU;AAChB,WAAS,WAAW,EAAE,WAAW,eAAe,UAAU,SAAS,eAAe,QAAQ,WAAW,eAAe,UAAU,OAAO,eAAe,KAAM,GAAG;AACzJ,QAAI;AACJ,QAAI;AACJ,YAAQ,YAAY,sBAAsB,eAAe,WAAW,GAAG,8CAA8C,uBAAuB;AAC5I,QAAI,eAAe,IAAI;AAIvB,mBAAe,MAAM,eAAe,YAAY,eAAe,YAAY,YAAY;AACvF,eAAW,MAAM,eAAe,aAAa,eAAe,aAAa,sBAAsB,QAAQ,CAAC;AACxG,QAAI,eAAe,GAAG;AAIlB,iBAAW,wBAACE,kBAAiB;AACzB,cAAM,mBAAmBA,gBAAe;AACxC,cAAM,QAAQ,mBAAmB;AACjC,cAAM,IAAI,mBAAmB;AAC7B,cAAM,IAAI,gBAAgBA,eAAc,YAAY;AACpD,cAAM,IAAI,KAAK,IAAI,CAAC,KAAK;AACzB,eAAO,UAAW,IAAI,IAAK;AAAA,MAC/B,GAPW;AAQX,mBAAa,wBAACA,kBAAiB;AAC3B,cAAM,mBAAmBA,gBAAe;AACxC,cAAM,QAAQ,mBAAmB;AACjC,cAAM,IAAI,QAAQ,WAAW;AAC7B,cAAM,IAAI,KAAK,IAAI,cAAc,CAAC,IAAI,KAAK,IAAIA,eAAc,CAAC,IAAI;AAClE,cAAM,IAAI,KAAK,IAAI,CAAC,KAAK;AACzB,cAAM,IAAI,gBAAgB,KAAK,IAAIA,eAAc,CAAC,GAAG,YAAY;AACjE,cAAM,SAAS,CAAC,SAASA,aAAY,IAAI,UAAU,IAAI,KAAK;AAC5D,eAAQ,WAAW,IAAI,KAAK,KAAM;AAAA,MACtC,GATa;AAAA,IAUjB,OACK;AAID,iBAAW,wBAACA,kBAAiB;AACzB,cAAM,IAAI,KAAK,IAAI,CAACA,gBAAe,QAAQ;AAC3C,cAAM,KAAKA,gBAAe,YAAY,WAAW;AACjD,eAAO,CAAC,UAAU,IAAI;AAAA,MAC1B,GAJW;AAKX,mBAAa,wBAACA,kBAAiB;AAC3B,cAAM,IAAI,KAAK,IAAI,CAACA,gBAAe,QAAQ;AAC3C,cAAM,KAAK,WAAWA,kBAAiB,WAAW;AAClD,eAAO,IAAI;AAAA,MACf,GAJa;AAAA,IAKjB;AACA,UAAM,eAAe,IAAI;AACzB,UAAM,eAAe,gBAAgB,UAAU,YAAY,YAAY;AACvE,eAAW,sBAAsB,QAAQ;AACzC,QAAI,MAAM,YAAY,GAAG;AACrB,aAAO;AAAA,QACH,WAAW,eAAe;AAAA,QAC1B,SAAS,eAAe;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,YAAM,YAAY,KAAK,IAAI,cAAc,CAAC,IAAI;AAC9C,aAAO;AAAA,QACH;AAAA,QACA,SAAS,eAAe,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,QACtD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAlES;AAmET,MAAM,iBAAiB;AACvB,WAAS,gBAAgB,UAAU,YAAY,cAAc;AACzD,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,eAAS,SAAS,SAAS,MAAM,IAAI,WAAW,MAAM;AAAA,IAC1D;AACA,WAAO;AAAA,EACX;AANS;AAOT,WAAS,gBAAgB,cAAc,cAAc;AACjD,WAAO,eAAe,KAAK,KAAK,IAAI,eAAe,YAAY;AAAA,EACnE;AAFS;;;ACvET,MAAM,eAAe,CAAC,YAAY,QAAQ;AAC1C,MAAM,cAAc,CAAC,aAAa,WAAW,MAAM;AACnD,WAAS,aAAa,SAAS,MAAM;AACjC,WAAO,KAAK,KAAK,CAAC,QAAQ,QAAQ,GAAG,MAAM,MAAS;AAAA,EACxD;AAFS;AAGT,WAAS,iBAAiB,SAAS;AAC/B,QAAI,gBAAgB;AAAA,MAChB,UAAU,eAAe;AAAA,MACzB,WAAW,eAAe;AAAA,MAC1B,SAAS,eAAe;AAAA,MACxB,MAAM,eAAe;AAAA,MACrB,wBAAwB;AAAA,MACxB,GAAG;AAAA,IACP;AAEA,QAAI,CAAC,aAAa,SAAS,WAAW,KAClC,aAAa,SAAS,YAAY,GAAG;AACrC,UAAI,QAAQ,gBAAgB;AACxB,cAAM,iBAAiB,QAAQ;AAC/B,cAAM,OAAQ,IAAI,KAAK,MAAO,iBAAiB;AAC/C,cAAM,YAAY,OAAO;AACzB,cAAM,UAAU,IACZ,MAAM,MAAM,GAAG,KAAK,QAAQ,UAAU,EAAE,IACxC,KAAK,KAAK,SAAS;AACvB,wBAAgB;AAAA,UACZ,GAAG;AAAA,UACH,MAAM,eAAe;AAAA,UACrB;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,OACK;AACD,cAAM,UAAU,WAAW,OAAO;AAClC,wBAAgB;AAAA,UACZ,GAAG;AAAA,UACH,GAAG;AAAA,UACH,MAAM,eAAe;AAAA,QACzB;AACA,sBAAc,yBAAyB;AAAA,MAC3C;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AArCS;AAsCT,WAAS,OAAO,0BAA0B,eAAe,gBAAgB,SAAS,eAAe,QAAQ;AACrG,UAAM,UAAU,OAAO,4BAA4B,WAC7C;AAAA,MACE,gBAAgB;AAAA,MAChB,WAAW,CAAC,GAAG,CAAC;AAAA,MAChB;AAAA,IACJ,IACE;AACN,QAAI,EAAE,WAAW,UAAU,IAAI;AAC/B,UAAM,SAAS,QAAQ,UAAU,CAAC;AAClC,UAAM,SAAS,QAAQ,UAAU,QAAQ,UAAU,SAAS,CAAC;AAK7D,UAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,OAAO;AAC3C,UAAM,EAAE,WAAW,SAAS,MAAM,UAAU,UAAU,uBAAwB,IAAI,iBAAiB;AAAA,MAC/F,GAAG;AAAA,MACH,UAAU,CAAC,sBAAsB,QAAQ,YAAY,CAAC;AAAA,IAC1D,CAAC;AACD,UAAM,kBAAkB,YAAY;AACpC,UAAM,eAAe,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI;AAC9D,UAAM,eAAe,SAAS;AAC9B,UAAM,sBAAsB,sBAAsB,KAAK,KAAK,YAAY,IAAI,CAAC;AAQ7E,UAAM,kBAAkB,KAAK,IAAI,YAAY,IAAI;AACjD,kBAAc,YAAY,kBACpB,eAAe,UAAU,WACzB,eAAe,UAAU;AAC/B,kBAAc,YAAY,kBACpB,eAAe,UAAU,WACzB,eAAe,UAAU;AAC/B,QAAI;AACJ,QAAI,eAAe,GAAG;AAClB,YAAM,cAAc,gBAAgB,qBAAqB,YAAY;AAErE,sBAAgB,wBAAC,MAAM;AACnB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,eAAQ,SACJ,aACO,kBACC,eAAe,sBAAsB,gBACrC,cACA,KAAK,IAAI,cAAc,CAAC,IACxB,eAAe,KAAK,IAAI,cAAc,CAAC;AAAA,MACvD,GATgB;AAAA,IAUpB,WACS,iBAAiB,GAAG;AAEzB,sBAAgB,wBAAC,MAAM,SACnB,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAC5B,gBACI,kBAAkB,sBAAsB,gBAAgB,IAHrD;AAAA,IAIpB,OACK;AAED,YAAM,oBAAoB,sBAAsB,KAAK,KAAK,eAAe,eAAe,CAAC;AACzF,sBAAgB,wBAAC,MAAM;AACnB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AAEjE,cAAM,WAAW,KAAK,IAAI,oBAAoB,GAAG,GAAG;AACpD,eAAQ,SACH,aACK,kBACE,eAAe,sBAAsB,gBACrC,KAAK,KAAK,QAAQ,IAClB,oBACI,eACA,KAAK,KAAK,QAAQ,KAC1B;AAAA,MACZ,GAbgB;AAAA,IAcpB;AACA,UAAM,YAAY;AAAA,MACd,oBAAoB,yBAAyB,YAAY,OAAO;AAAA,MAChE,MAAM,wBAAC,MAAM;AACT,cAAM,UAAU,cAAc,CAAC;AAC/B,YAAI,CAAC,wBAAwB;AACzB,cAAI,kBAAkB,MAAM,IAAI,kBAAkB;AAMlD,cAAI,eAAe,GAAG;AAClB,8BACI,MAAM,IACA,sBAAsB,eAAe,IACrC,sBAAsB,eAAe,GAAG,OAAO;AAAA,UAC7D;AACA,gBAAM,2BAA2B,KAAK,IAAI,eAAe,KAAK;AAC9D,gBAAM,+BAA+B,KAAK,IAAI,SAAS,OAAO,KAAK;AACnE,gBAAM,OACF,4BAA4B;AAAA,QACpC,OACK;AACD,gBAAM,OAAO,KAAK;AAAA,QACtB;AACA,cAAM,QAAQ,MAAM,OAAO,SAAS;AACpC,eAAO;AAAA,MACX,GAzBM;AAAA,MA0BN,UAAU,6BAAM;AACZ,cAAM,qBAAqB,KAAK,IAAI,sBAAsB,SAAS,GAAG,oBAAoB;AAC1F,cAAM,SAAS,qBAAqB,CAACC,cAAa,UAAU,KAAK,qBAAqBA,SAAQ,EAAE,OAAO,oBAAoB,EAAE;AAC7H,eAAO,qBAAqB,QAAQ;AAAA,MACxC,GAJU;AAAA,MAKV,cAAc,6BAAM;AAAA,MAAE,GAAR;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AAlHS;AAmHT,SAAO,iBAAiB,CAAC,YAAY;AACjC,UAAM,mBAAmB,sBAAsB,SAAS,KAAK,MAAM;AACnE,YAAQ,OAAO,iBAAiB;AAChC,YAAQ,WAAW,sBAAsB,iBAAiB,QAAQ;AAClE,YAAQ,OAAO;AACf,WAAO;AAAA,EACX;;;ACzKA,WAAS,QAAQ,EAAE,WAAAC,YAAW,WAAW,GAAK,QAAQ,KAAK,eAAe,KAAK,gBAAgB,IAAI,kBAAkB,KAAK,cAAc,KAAK,KAAK,YAAY,KAAK,UAAW,GAAG;AAC7K,UAAM,SAASA,WAAU,CAAC;AAC1B,UAAM,QAAQ;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,IACX;AACA,UAAM,gBAAgB,wBAAC,MAAO,QAAQ,UAAa,IAAI,OAAS,QAAQ,UAAa,IAAI,KAAnE;AACtB,UAAM,kBAAkB,wBAAC,MAAM;AAC3B,UAAI,QAAQ;AACR,eAAO;AACX,UAAI,QAAQ;AACR,eAAO;AACX,aAAO,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM;AAAA,IACzD,GANwB;AAOxB,QAAI,YAAY,QAAQ;AACxB,UAAM,QAAQ,SAAS;AACvB,UAAM,SAAS,iBAAiB,SAAY,QAAQ,aAAa,KAAK;AAKtE,QAAI,WAAW;AACX,kBAAY,SAAS;AACzB,UAAM,YAAY,wBAAC,MAAM,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,YAAY,GAA9C;AAClB,UAAM,aAAa,wBAAC,MAAM,SAAS,UAAU,CAAC,GAA3B;AACnB,UAAM,gBAAgB,wBAAC,MAAM;AACzB,YAAM,QAAQ,UAAU,CAAC;AACzB,YAAM,SAAS,WAAW,CAAC;AAC3B,YAAM,OAAO,KAAK,IAAI,KAAK,KAAK;AAChC,YAAM,QAAQ,MAAM,OAAO,SAAS;AAAA,IACxC,GALsB;AAYtB,QAAI;AACJ,QAAI;AACJ,UAAM,qBAAqB,wBAAC,MAAM;AAC9B,UAAI,CAAC,cAAc,MAAM,KAAK;AAC1B;AACJ,4BAAsB;AACtB,iBAAW,OAAO;AAAA,QACd,WAAW,CAAC,MAAM,OAAO,gBAAgB,MAAM,KAAK,CAAC;AAAA,QACrD,UAAU,sBAAsB,YAAY,GAAG,MAAM,KAAK;AAAA;AAAA,QAC1D,SAAS;AAAA,QACT,WAAW;AAAA,QACX;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL,GAZ2B;AAa3B,uBAAmB,CAAC;AACpB,WAAO;AAAA,MACH,oBAAoB;AAAA,MACpB,MAAM,wBAAC,MAAM;AAOT,YAAI,kBAAkB;AACtB,YAAI,CAAC,YAAY,wBAAwB,QAAW;AAChD,4BAAkB;AAClB,wBAAc,CAAC;AACf,6BAAmB,CAAC;AAAA,QACxB;AAKA,YAAI,wBAAwB,UAAa,KAAK,qBAAqB;AAC/D,iBAAO,SAAS,KAAK,IAAI,mBAAmB;AAAA,QAChD,OACK;AACD,WAAC,mBAAmB,cAAc,CAAC;AACnC,iBAAO;AAAA,QACX;AAAA,MACJ,GAxBM;AAAA,IAyBV;AAAA,EACJ;AAjFS;;;ACAT,WAAS,aAAa,QAAQC,OAAM,aAAa;AAC7C,UAAM,SAAS,CAAC;AAChB,UAAM,eAAe,eAAe,mBAAmB,OAAO;AAC9D,UAAM,YAAY,OAAO,SAAS;AAClC,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,UAAI,QAAQ,aAAa,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACjD,UAAIA,OAAM;AACN,cAAM,iBAAiB,MAAM,QAAQA,KAAI,IAAIA,MAAK,CAAC,KAAK,OAAOA;AAC/D,gBAAQ,KAAK,gBAAgB,KAAK;AAAA,MACtC;AACA,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AAbS;AAiCT,WAAS,YAAY,OAAO,QAAQ,EAAE,OAAO,UAAU,MAAM,MAAAA,OAAM,MAAM,IAAI,CAAC,GAAG;AAC7E,UAAM,cAAc,MAAM;AAC1B,cAAU,gBAAgB,OAAO,QAAQ,wDAAwD,cAAc;AAK/G,QAAI,gBAAgB;AAChB,aAAO,MAAM,OAAO,CAAC;AACzB,QAAI,gBAAgB,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC;AAC3C,aAAO,MAAM,OAAO,CAAC;AACzB,UAAM,mBAAmB,MAAM,CAAC,MAAM,MAAM,CAAC;AAE7C,QAAI,MAAM,CAAC,IAAI,MAAM,cAAc,CAAC,GAAG;AACnC,cAAQ,CAAC,GAAG,KAAK,EAAE,QAAQ;AAC3B,eAAS,CAAC,GAAG,MAAM,EAAE,QAAQ;AAAA,IACjC;AACA,UAAM,SAAS,aAAa,QAAQA,OAAM,KAAK;AAC/C,UAAM,YAAY,OAAO;AACzB,UAAM,eAAe,wBAAC,MAAM;AACxB,UAAI,oBAAoB,IAAI,MAAM,CAAC;AAC/B,eAAO,OAAO,CAAC;AACnB,UAAI,IAAI;AACR,UAAI,YAAY,GAAG;AACf,eAAO,IAAI,MAAM,SAAS,GAAG,KAAK;AAC9B,cAAI,IAAI,MAAM,IAAI,CAAC;AACf;AAAA,QACR;AAAA,MACJ;AACA,YAAM,kBAAkB,SAAS,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC;AAC1D,aAAO,OAAO,CAAC,EAAE,eAAe;AAAA,IACpC,GAZqB;AAarB,WAAO,UACD,CAAC,MAAM,aAAa,MAAM,MAAM,CAAC,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,IAC9D;AAAA,EACV;AAnCS;;;ACjCT,WAAS,WAAW,QAAQ,WAAW;AACnC,UAAM,MAAM,OAAO,OAAO,SAAS,CAAC;AACpC,aAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACjC,YAAM,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC/C,aAAO,KAAK,UAAU,KAAK,GAAG,cAAc,CAAC;AAAA,IACjD;AAAA,EACJ;AANS;;;ACDT,WAAS,cAAc,KAAK;AACxB,UAAM,SAAS,CAAC,CAAC;AACjB,eAAW,QAAQ,IAAI,SAAS,CAAC;AACjC,WAAO;AAAA,EACX;AAJS;;;ACFT,WAAS,qBAAqB,QAAQ,UAAU;AAC5C,WAAO,OAAO,IAAI,CAAC,MAAM,IAAI,QAAQ;AAAA,EACzC;AAFS;;;ACKT,WAAS,cAAc,QAAQ,QAAQ;AACnC,WAAO,OAAO,IAAI,MAAM,UAAU,SAAS,EAAE,OAAO,GAAG,OAAO,SAAS,CAAC;AAAA,EAC5E;AAFS;AAGT,WAAS,UAAU,EAAE,WAAW,KAAK,WAAW,gBAAgB,OAAO,MAAAC,QAAO,YAAa,GAAG;AAK1F,UAAM,kBAAkB,cAAcA,KAAI,IACpCA,MAAK,IAAI,0BAA0B,IACnC,2BAA2BA,KAAI;AAKrC,UAAM,QAAQ;AAAA,MACV,MAAM;AAAA,MACN,OAAO,eAAe,CAAC;AAAA,IAC3B;AAIA,UAAM,gBAAgB;AAAA;AAAA;AAAA,MAGtB,SAAS,MAAM,WAAW,eAAe,SACnC,QACA,cAAc,cAAc;AAAA,MAAG;AAAA,IAAQ;AAC7C,UAAM,oBAAoB,YAAY,eAAe,gBAAgB;AAAA,MACjE,MAAM,MAAM,QAAQ,eAAe,IAC7B,kBACA,cAAc,gBAAgB,eAAe;AAAA,IACvD,CAAC;AACD,WAAO;AAAA,MACH,oBAAoB;AAAA,MACpB,MAAM,wBAAC,MAAM;AACT,cAAM,QAAQ,kBAAkB,CAAC;AACjC,cAAM,OAAO,KAAK;AAClB,eAAO;AAAA,MACX,GAJM;AAAA,IAKV;AAAA,EACJ;AAtCS;;;ACRT,MAAM,YAAY,wBAAC,UAAU,UAAU,MAArB;AAClB,WAAS,iBAAiBC,YAAW,EAAE,QAAQ,aAAa,OAAO,GAAG,eAAe,QAAQ,GAAG;AAC5F,UAAM,oBAAoBA,WAAU,OAAO,SAAS;AACpD,UAAM,mBAAmB,QAAQ,KAAM,UAAU,eAAe,UAAU,SAAS,MAAM;AACzF,UAAM,QAAQ,mBAAmB,IAAI,kBAAkB,SAAS;AAChE,WAAO,CAAC,SAAS,kBAAkB,SAC7B,kBAAkB,KAAK,IACvB;AAAA,EACV;AAPS;;;ACGT,MAAM,oBAAoB;AAAA,IACtB,OAAO;AAAA,IACP;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACJ;AACA,WAAS,sBAAsB,YAAY;AACvC,QAAI,OAAO,WAAW,SAAS,UAAU;AACrC,iBAAW,OAAO,kBAAkB,WAAW,IAAI;AAAA,IACvD;AAAA,EACJ;AAJS;;;ACXT,MAAM,cAAN,MAAkB;AAAA,IAAlB,OAAkB;AAAA;AAAA;AAAA,IACd,cAAc;AACV,WAAK,eAAe;AAAA,IACxB;AAAA,IACA,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,iBAAiB;AACb,WAAK,YAAY,IAAI,QAAQ,CAAC,YAAY;AACtC,aAAK,UAAU;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,IACA,iBAAiB;AACb,WAAK,QAAQ;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,KAAK,WAAW,UAAU;AACtB,aAAO,KAAK,SAAS,KAAK,WAAW,QAAQ;AAAA,IACjD;AAAA,EACJ;;;ACXA,MAAM,oBAAoB,wBAACC,aAAYA,WAAU,KAAvB;AAC1B,MAAM,cAAN,cAA0B,YAAY;AAAA,IAbtC,OAasC;AAAA;AAAA;AAAA,IAClC,YAAY,SAAS;AACjB,YAAM;AACN,WAAK,QAAQ;AACb,WAAK,YAAY;AACjB,WAAK,YAAY;AAIjB,WAAK,cAAc;AAInB,WAAK,WAAW;AAIhB,WAAK,gBAAgB;AAKrB,WAAK,OAAO,MAAM;AACd,cAAM,EAAE,aAAAC,aAAY,IAAI,KAAK;AAC7B,YAAIA,gBAAeA,aAAY,cAAc,KAAK,IAAI,GAAG;AACrD,eAAK,KAAK,KAAK,IAAI,CAAC;AAAA,QACxB;AACA,aAAK,YAAY;AACjB,YAAI,KAAK,UAAU;AACf;AACJ,aAAK,SAAS;AACd,aAAK,QAAQ,SAAS;AAAA,MAC1B;AACA,uBAAiB;AACjB,WAAK,UAAU;AACf,WAAK,cAAc;AACnB,WAAK,KAAK;AACV,UAAI,QAAQ,aAAa;AACrB,aAAK,MAAM;AAAA,IACnB;AAAA,IACA,gBAAgB;AACZ,YAAM,EAAE,QAAQ,IAAI;AACpB,4BAAsB,OAAO;AAC7B,YAAM,EAAE,OAAO,WAAW,SAAS,GAAG,cAAc,GAAG,YAAY,WAAW,EAAG,IAAI;AACrF,UAAI,EAAE,WAAW,YAAY,IAAI;AACjC,YAAM,mBAAmB,QAAQ;AACjC,UACI,qBAAqB,WAAW;AAChC,kBAAU,YAAY,UAAU,GAAG,gGAAgG,WAAW,IAAI,mBAAmB;AAAA,MACzK;AACA,UAAI,qBAAqB,aACrB,OAAO,YAAY,CAAC,MAAM,UAAU;AACpC,aAAK,eAAe,KAAK,mBAAmB,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAC/E,sBAAc,CAAC,GAAG,GAAG;AAAA,MACzB;AACA,YAAM,YAAY,iBAAiB,EAAE,GAAG,SAAS,WAAW,YAAY,CAAC;AAKzE,UAAI,eAAe,UAAU;AACzB,aAAK,oBAAoB,iBAAiB;AAAA,UACtC,GAAG;AAAA,UACH,WAAW,CAAC,GAAG,WAAW,EAAE,QAAQ;AAAA,UACpC,UAAU,CAAC;AAAA,QACf,CAAC;AAAA,MACL;AASA,UAAI,UAAU,uBAAuB,MAAM;AACvC,kBAAU,qBAAqB,sBAAsB,SAAS;AAAA,MAClE;AACA,YAAM,EAAE,mBAAmB,IAAI;AAC/B,WAAK,qBAAqB;AAC1B,WAAK,mBAAmB,qBAAqB;AAC7C,WAAK,gBAAgB,KAAK,oBAAoB,SAAS,KAAK;AAC5D,WAAK,YAAY;AAAA,IACrB;AAAA,IACA,WAAW,WAAW;AAClB,YAAM,gBAAgB,KAAK,MAAM,YAAY,KAAK,SAAS,IAAI,KAAK;AAEpE,UAAI,KAAK,aAAa,MAAM;AACxB,aAAK,cAAc,KAAK;AAAA,MAC5B,OACK;AAID,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AAAA,IACA,KAAK,WAAW,SAAS,OAAO;AAC5B,YAAM,EAAE,WAAW,eAAe,cAAc,mBAAmB,kBAAkB,mBAAoB,IAAI;AAC7G,UAAI,KAAK,cAAc;AACnB,eAAO,UAAU,KAAK,CAAC;AAC3B,YAAM,EAAE,QAAQ,GAAG,WAAAC,YAAW,QAAQ,YAAY,aAAa,MAAM,UAAU,cAAe,IAAI,KAAK;AAOvG,UAAI,KAAK,QAAQ,GAAG;AAChB,aAAK,YAAY,KAAK,IAAI,KAAK,WAAW,SAAS;AAAA,MACvD,WACS,KAAK,QAAQ,GAAG;AACrB,aAAK,YAAY,KAAK,IAAI,YAAY,gBAAgB,KAAK,OAAO,KAAK,SAAS;AAAA,MACpF;AACA,UAAI,QAAQ;AACR,aAAK,cAAc;AAAA,MACvB,OACK;AACD,aAAK,WAAW,SAAS;AAAA,MAC7B;AAEA,YAAM,mBAAmB,KAAK,cAAc,SAAS,KAAK,iBAAiB,IAAI,IAAI;AACnF,YAAM,iBAAiB,KAAK,iBAAiB,IACvC,mBAAmB,IACnB,mBAAmB;AACzB,WAAK,cAAc,KAAK,IAAI,kBAAkB,CAAC;AAE/C,UAAI,KAAK,UAAU,cAAc,KAAK,aAAa,MAAM;AACrD,aAAK,cAAc;AAAA,MACvB;AACA,UAAI,UAAU,KAAK;AACnB,UAAI,iBAAiB;AACrB,UAAI,QAAQ;AAMR,cAAMC,YAAW,KAAK,IAAI,KAAK,aAAa,aAAa,IAAI;AAK7D,YAAI,mBAAmB,KAAK,MAAMA,SAAQ;AAK1C,YAAI,oBAAoBA,YAAW;AAKnC,YAAI,CAAC,qBAAqBA,aAAY,GAAG;AACrC,8BAAoB;AAAA,QACxB;AACA,8BAAsB,KAAK;AAC3B,2BAAmB,KAAK,IAAI,kBAAkB,SAAS,CAAC;AAIxD,cAAM,iBAAiB,QAAQ,mBAAmB,CAAC;AACnD,YAAI,gBAAgB;AAChB,cAAI,eAAe,WAAW;AAC1B,gCAAoB,IAAI;AACxB,gBAAI,aAAa;AACb,mCAAqB,cAAc;AAAA,YACvC;AAAA,UACJ,WACS,eAAe,UAAU;AAC9B,6BAAiB;AAAA,UACrB;AAAA,QACJ;AACA,kBAAU,MAAM,GAAG,GAAG,iBAAiB,IAAI;AAAA,MAC/C;AAMA,YAAM,QAAQ,iBACR,EAAE,MAAM,OAAO,OAAOD,WAAU,CAAC,EAAE,IACnC,eAAe,KAAK,OAAO;AACjC,UAAI,cAAc;AACd,cAAM,QAAQ,aAAa,MAAM,KAAK;AAAA,MAC1C;AACA,UAAI,EAAE,KAAK,IAAI;AACf,UAAI,CAAC,kBAAkB,uBAAuB,MAAM;AAChD,eACI,KAAK,iBAAiB,IAChB,KAAK,eAAe,gBACpB,KAAK,eAAe;AAAA,MAClC;AACA,YAAM,sBAAsB,KAAK,aAAa,SACzC,KAAK,UAAU,cAAe,KAAK,UAAU,aAAa;AAE/D,UAAI,uBAAuB,SAAS,SAAS;AACzC,cAAM,QAAQ,iBAAiBA,YAAW,KAAK,SAAS,eAAe,KAAK,KAAK;AAAA,MACrF;AACA,UAAI,UAAU;AACV,iBAAS,MAAM,KAAK;AAAA,MACxB;AACA,UAAI,qBAAqB;AACrB,aAAK,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,KAAK,SAAS,QAAQ;AAClB,aAAO,KAAK,SAAS,KAAK,SAAS,MAAM;AAAA,IAC7C;AAAA,IACA,IAAI,WAAW;AACX,aAAO,sBAAsB,KAAK,kBAAkB;AAAA,IACxD;AAAA,IACA,IAAI,OAAO;AACP,aAAO,sBAAsB,KAAK,WAAW;AAAA,IACjD;AAAA,IACA,IAAI,KAAK,SAAS;AACd,gBAAU,sBAAsB,OAAO;AACvC,WAAK,cAAc;AACnB,UAAI,KAAK,cAAc,QACnB,KAAK,aAAa,QAClB,KAAK,kBAAkB,GAAG;AAC1B,aAAK,WAAW;AAAA,MACpB,WACS,KAAK,QAAQ;AAClB,aAAK,YAAY,KAAK,OAAO,IAAI,IAAI,UAAU,KAAK;AAAA,MACxD;AACA,WAAK,QAAQ,MAAM,KAAK;AAAA,IAC5B;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,MAAM,UAAU;AAChB,WAAK,WAAW,KAAK,IAAI,CAAC;AAC1B,YAAM,aAAa,KAAK,kBAAkB;AAC1C,WAAK,gBAAgB;AACrB,UAAI,YAAY;AACZ,aAAK,OAAO,sBAAsB,KAAK,WAAW;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,OAAO;AACH,UAAI,KAAK;AACL;AACJ,YAAM,EAAE,SAAS,iBAAiB,UAAU,IAAI,KAAK;AACrD,UAAI,CAAC,KAAK,QAAQ;AACd,aAAK,SAAS,OAAO,CAAC,cAAc,KAAK,KAAK,SAAS,CAAC;AAAA,MAC5D;AACA,WAAK,QAAQ,SAAS;AACtB,YAAME,OAAM,KAAK,OAAO,IAAI;AAC5B,UAAI,KAAK,UAAU,YAAY;AAC3B,aAAK,eAAe;AACpB,aAAK,YAAYA;AAAA,MACrB,WACS,KAAK,aAAa,MAAM;AAC7B,aAAK,YAAYA,OAAM,KAAK;AAAA,MAChC,WACS,CAAC,KAAK,WAAW;AACtB,aAAK,YAAY,aAAaA;AAAA,MAClC;AACA,UAAI,KAAK,UAAU,cAAc,KAAK,QAAQ,GAAG;AAC7C,aAAK,aAAa,KAAK;AAAA,MAC3B;AACA,WAAK,WAAW;AAKhB,WAAK,QAAQ;AACb,WAAK,OAAO,MAAM;AAAA,IACtB;AAAA,IACA,QAAQ;AACJ,WAAK,QAAQ;AACb,WAAK,WAAW,KAAK,IAAI,CAAC;AAC1B,WAAK,WAAW,KAAK;AAAA,IACzB;AAAA,IACA,WAAW;AACP,UAAI,KAAK,UAAU,WAAW;AAC1B,aAAK,KAAK;AAAA,MACd;AACA,WAAK,QAAQ;AACb,WAAK,WAAW;AAAA,IACpB;AAAA,IACA,SAAS;AACL,WAAK,eAAe;AACpB,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ,aAAa;AAAA,IAC9B;AAAA,IACA,SAAS;AACL,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,KAAK,CAAC;AACX,WAAK,SAAS;AACd,WAAK,QAAQ,WAAW;AAAA,IAC5B;AAAA,IACA,WAAW;AACP,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,WAAK,YAAY,KAAK,WAAW;AACjC,uBAAiB;AAAA,IACrB;AAAA,IACA,aAAa;AACT,UAAI,CAAC,KAAK;AACN;AACJ,WAAK,OAAO,KAAK;AACjB,WAAK,SAAS;AAAA,IAClB;AAAA,IACA,OAAO,YAAY;AACf,WAAK,YAAY;AACjB,aAAO,KAAK,KAAK,YAAY,IAAI;AAAA,IACrC;AAAA,IACA,eAAe,UAAU;AACrB,UAAI,KAAK,QAAQ,cAAc;AAC3B,aAAK,QAAQ,OAAO;AACpB,aAAK,QAAQ,OAAO;AACpB,aAAK,cAAc;AAAA,MACvB;AACA,WAAK,QAAQ,KAAK;AAClB,aAAO,SAAS,QAAQ,IAAI;AAAA,IAChC;AAAA,EACJ;AAEA,WAAS,aAAa,SAAS;AAC3B,WAAO,IAAI,YAAY,OAAO;AAAA,EAClC;AAFS;;;ACpVT,WAAS,cAAcC,YAAW;AAC9B,aAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,MAAAA,WAAU,CAAC,MAAMA,WAAU,CAAC,IAAIA,WAAU,IAAI,CAAC;AAAA,IACnD;AAAA,EACJ;AAJS;;;ACAT,MAAM,WAAW,wBAAC,QAAS,MAAM,MAAO,KAAK,IAA5B;AACjB,MAAM,SAAS,wBAAC,MAAM;AAClB,UAAM,QAAQ,SAAS,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC7C,WAAO,YAAY,KAAK;AAAA,EAC5B,GAHe;AAIf,MAAM,kBAAkB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO,wBAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK,GAA3C;AAAA,IACP;AAAA,IACA,SAAS;AAAA,IACT,OAAO,wBAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,GAA/B;AAAA,IACP,OAAO,wBAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,GAA/B;AAAA,IACP,MAAM,wBAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK,GAA3C;AAAA,EACV;AACA,MAAM,cAAc,wBAAC,UAAU;AAC3B,YAAQ,QAAQ;AAChB,QAAI,QAAQ;AACR,eAAS;AACb,WAAO;AAAA,EACX,GALoB;AAMpB,MAAM,UAAU;AAChB,MAAM,SAAS,wBAAC,MAAM,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAA1C;AACf,MAAM,SAAS,wBAAC,MAAM,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAA1C;AACf,MAAM,kBAAkB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,OAAO,wBAAC,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,GAAjC;AAAA,IACP,SAAS,wBAAC,MAAM,YAAY,SAAS,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAnD;AAAA,IACT,SAAS,wBAAC,MAAM,YAAY,SAAS,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAApD;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,wBAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,GAA/B;AAAA,IACP,OAAO,wBAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,GAA/B;AAAA,IACP,MAAM,wBAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK,GAA3C;AAAA,EACV;AACA,WAAS,sBAAsB,MAAM;AACjC,WAAO,KAAK,SAAS,OAAO,IAAI,IAAI;AAAA,EACxC;AAFS;AAGT,WAAS,wBAAwBC,YAAW,MAAM;AAC9C,QAAI,CAACA,cAAaA,eAAc,QAAQ;AACpC,aAAO,sBAAsB,IAAI;AAAA,IACrC;AACA,UAAM,gBAAgBA,WAAU,MAAM,8BAA8B;AACpE,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe;AACf,gBAAU;AACV,cAAQ;AAAA,IACZ,OACK;AACD,YAAM,gBAAgBA,WAAU,MAAM,4BAA4B;AAClE,gBAAU;AACV,cAAQ;AAAA,IACZ;AACA,QAAI,CAAC,OAAO;AACR,aAAO,sBAAsB,IAAI;AAAA,IACrC;AACA,UAAM,cAAc,QAAQ,IAAI;AAChC,UAAM,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,wBAAwB;AAC/D,WAAO,OAAO,gBAAgB,aACxB,YAAY,MAAM,IAClB,OAAO,WAAW;AAAA,EAC5B;AAxBS;AAyBT,MAAM,qBAAqB,wBAAC,UAAU,SAAS;AAC3C,UAAM,EAAE,WAAAA,aAAY,OAAO,IAAI,iBAAiB,QAAQ;AACxD,WAAO,wBAAwBA,YAAW,IAAI;AAAA,EAClD,GAH2B;AAI3B,WAAS,yBAAyB,OAAO;AACrC,WAAO,WAAW,MAAM,KAAK,CAAC;AAAA,EAClC;AAFS;;;AC3ET,MAAM,qBAAqB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAIA,MAAM,iBAAgC,uBAAM,IAAI,IAAI,kBAAkB,GAAG;;;ACpBzE,MAAM,gBAAgB,wBAAC,MAAM,MAAM,UAAU,MAAM,IAA7B;AACtB,MAAM,gBAAgB,oBAAI,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7C,MAAM,gCAAgC,mBAAmB,OAAO,CAAC,QAAQ,CAAC,cAAc,IAAI,GAAG,CAAC;AAChG,WAAS,gCAAgC,eAAe;AACpD,UAAM,oBAAoB,CAAC;AAC3B,kCAA8B,QAAQ,CAAC,QAAQ;AAC3C,YAAM,QAAQ,cAAc,SAAS,GAAG;AACxC,UAAI,UAAU,QAAW;AACrB,0BAAkB,KAAK,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC;AACzC,cAAM,IAAI,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,MAC7C;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AAVS;AAWT,MAAM,mBAAmB;AAAA;AAAA,IAErB,OAAO,wBAAC,EAAE,EAAE,GAAG,EAAE,cAAc,KAAK,eAAe,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,WAAW,IAAI,WAAW,YAAY,GAAvH;AAAA,IACP,QAAQ,wBAAC,EAAE,EAAE,GAAG,EAAE,aAAa,KAAK,gBAAgB,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,UAAU,IAAI,WAAW,aAAa,GAAvH;AAAA,IACR,KAAK,wBAAC,OAAO,EAAE,IAAI,MAAM,WAAW,GAAG,GAAlC;AAAA,IACL,MAAM,wBAAC,OAAO,EAAE,KAAK,MAAM,WAAW,IAAI,GAApC;AAAA,IACN,QAAQ,wBAAC,EAAE,EAAE,GAAG,EAAE,IAAI,MAAM,WAAW,GAAG,KAAK,EAAE,MAAM,EAAE,MAAjD;AAAA,IACR,OAAO,wBAAC,EAAE,EAAE,GAAG,EAAE,KAAK,MAAM,WAAW,IAAI,KAAK,EAAE,MAAM,EAAE,MAAnD;AAAA;AAAA,IAEP,GAAG,wBAAC,OAAO,EAAE,WAAAC,WAAU,MAAM,wBAAwBA,YAAW,GAAG,GAAhE;AAAA,IACH,GAAG,wBAAC,OAAO,EAAE,WAAAA,WAAU,MAAM,wBAAwBA,YAAW,GAAG,GAAhE;AAAA,EACP;AAEA,mBAAiB,aAAa,iBAAiB;AAC/C,mBAAiB,aAAa,iBAAiB;;;AC7B/C,MAAM,YAAY,oBAAI,IAAI;AAC1B,MAAI,cAAc;AAClB,MAAI,sBAAsB;AAC1B,MAAI,WAAW;AACf,WAAS,sBAAsB;AAC3B,QAAI,qBAAqB;AACrB,YAAM,qBAAqB,MAAM,KAAK,SAAS,EAAE,OAAO,CAAC,aAAa,SAAS,gBAAgB;AAC/F,YAAM,oBAAoB,IAAI,IAAI,mBAAmB,IAAI,CAAC,aAAa,SAAS,OAAO,CAAC;AACxF,YAAM,sBAAsB,oBAAI,IAAI;AAKpC,wBAAkB,QAAQ,CAAC,YAAY;AACnC,cAAM,oBAAoB,gCAAgC,OAAO;AACjE,YAAI,CAAC,kBAAkB;AACnB;AACJ,4BAAoB,IAAI,SAAS,iBAAiB;AAClD,gBAAQ,OAAO;AAAA,MACnB,CAAC;AAED,yBAAmB,QAAQ,CAAC,aAAa,SAAS,oBAAoB,CAAC;AAEvE,wBAAkB,QAAQ,CAAC,YAAY;AACnC,gBAAQ,OAAO;AACf,cAAM,UAAU,oBAAoB,IAAI,OAAO;AAC/C,YAAI,SAAS;AACT,kBAAQ,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC9B,oBAAQ,SAAS,GAAG,GAAG,IAAI,KAAK;AAAA,UACpC,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAED,yBAAmB,QAAQ,CAAC,aAAa,SAAS,gBAAgB,CAAC;AAEnE,yBAAmB,QAAQ,CAAC,aAAa;AACrC,YAAI,SAAS,qBAAqB,QAAW;AACzC,iBAAO,SAAS,GAAG,SAAS,gBAAgB;AAAA,QAChD;AAAA,MACJ,CAAC;AAAA,IACL;AACA,0BAAsB;AACtB,kBAAc;AACd,cAAU,QAAQ,CAAC,aAAa,SAAS,SAAS,QAAQ,CAAC;AAC3D,cAAU,MAAM;AAAA,EACpB;AAzCS;AA0CT,WAAS,mBAAmB;AACxB,cAAU,QAAQ,CAAC,aAAa;AAC5B,eAAS,cAAc;AACvB,UAAI,SAAS,kBAAkB;AAC3B,8BAAsB;AAAA,MAC1B;AAAA,IACJ,CAAC;AAAA,EACL;AAPS;AAQT,WAAS,yBAAyB;AAC9B,eAAW;AACX,qBAAiB;AACjB,wBAAoB;AACpB,eAAW;AAAA,EACf;AALS;AAMT,MAAM,mBAAN,MAAuB;AAAA,IAhEvB,OAgEuB;AAAA;AAAA;AAAA,IACnB,YAAY,qBAAqB,YAAY,MAAMC,cAAa,SAAS,UAAU,OAAO;AACtF,WAAK,QAAQ;AAMb,WAAK,UAAU;AAKf,WAAK,mBAAmB;AACxB,WAAK,sBAAsB,CAAC,GAAG,mBAAmB;AAClD,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,cAAcA;AACnB,WAAK,UAAU;AACf,WAAK,UAAU;AAAA,IACnB;AAAA,IACA,kBAAkB;AACd,WAAK,QAAQ;AACb,UAAI,KAAK,SAAS;AACd,kBAAU,IAAI,IAAI;AAClB,YAAI,CAAC,aAAa;AACd,wBAAc;AACd,gBAAM,KAAK,gBAAgB;AAC3B,gBAAM,iBAAiB,mBAAmB;AAAA,QAC9C;AAAA,MACJ,OACK;AACD,aAAK,cAAc;AACnB,aAAK,SAAS;AAAA,MAClB;AAAA,IACJ;AAAA,IACA,gBAAgB;AACZ,YAAM,EAAE,qBAAqB,MAAM,SAAS,aAAAA,aAAY,IAAI;AAE5D,UAAI,oBAAoB,CAAC,MAAM,MAAM;AACjC,cAAM,eAAeA,cAAa,IAAI;AAEtC,cAAM,gBAAgB,oBAAoB,oBAAoB,SAAS,CAAC;AACxE,YAAI,iBAAiB,QAAW;AAC5B,8BAAoB,CAAC,IAAI;AAAA,QAC7B,WACS,WAAW,MAAM;AACtB,gBAAM,cAAc,QAAQ,UAAU,MAAM,aAAa;AACzD,cAAI,gBAAgB,UAAa,gBAAgB,MAAM;AACnD,gCAAoB,CAAC,IAAI;AAAA,UAC7B;AAAA,QACJ;AACA,YAAI,oBAAoB,CAAC,MAAM,QAAW;AACtC,8BAAoB,CAAC,IAAI;AAAA,QAC7B;AACA,YAAIA,gBAAe,iBAAiB,QAAW;AAC3C,UAAAA,aAAY,IAAI,oBAAoB,CAAC,CAAC;AAAA,QAC1C;AAAA,MACJ;AACA,oBAAc,mBAAmB;AAAA,IACrC;AAAA,IACA,mBAAmB;AAAA,IAAE;AAAA,IACrB,sBAAsB;AAAA,IAAE;AAAA,IACxB,kBAAkB;AAAA,IAAE;AAAA,IACpB,kBAAkB;AAAA,IAAE;AAAA,IACpB,SAAS,mBAAmB,OAAO;AAC/B,WAAK,QAAQ;AACb,WAAK,WAAW,KAAK,qBAAqB,KAAK,eAAe,gBAAgB;AAC9E,gBAAU,OAAO,IAAI;AAAA,IACzB;AAAA,IACA,SAAS;AACL,UAAI,KAAK,UAAU,aAAa;AAC5B,kBAAU,OAAO,IAAI;AACrB,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AACL,UAAI,KAAK,UAAU;AACf,aAAK,gBAAgB;AAAA,IAC7B;AAAA,EACJ;;;AChJA,MAAM,WAAW,wBAAC,SAAS,KAAK,WAAW,IAAI,GAA9B;;;ACEjB,WAAS,SAAS,SAAS,MAAM,OAAO;AACpC,aAAS,IAAI,IACP,QAAQ,MAAM,YAAY,MAAM,KAAK,IACpC,QAAQ,MAAM,IAAI,IAAI;AAAA,EACjC;AAJS;;;ACAT,MAAM,yBAAyC,qBAAK,MAAM,OAAO,mBAAmB,MAAS;;;ACE7F,MAAM,gBAAgB,CAAC;;;ACDvB,WAAS,aAAa,UAAU,cAAc;AAC1C,UAAM,WAAW,KAAK,QAAQ;AAC9B,WAAO,MAAM,cAAc,YAAY,KAAK,SAAS;AAAA,EACzD;AAHS;;;ACDT,MAAM,uBAAqC,6BAAa,MAAM;AAC1D,QAAI;AACA,eACK,cAAc,KAAK,EACnB,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,IAC3D,SACO,GAAG;AACN,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,GAAG,cAAc;;;ACZjB,MAAM,sBAAsB,wBAAC,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAArD;;;ACE5B,MAAM,uBAAuB;AAAA,IACzB,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAsB,oCAAoB,CAAC,GAAG,MAAM,MAAM,CAAC,CAAC;AAAA,IAC5D,SAAuB,oCAAoB,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC7D,QAAsB,oCAAoB,CAAC,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,IACnE,SAAuB,oCAAoB,CAAC,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,EACvE;;;ACNA,WAAS,wBAAwB,QAAQ,UAAU;AAC/C,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA,IACX,WACS,OAAO,WAAW,YAAY;AACnC,aAAO,qBAAqB,IACtB,qBAAqB,QAAQ,QAAQ,IACrC;AAAA,IACV,WACS,mBAAmB,MAAM,GAAG;AACjC,aAAO,oBAAoB,MAAM;AAAA,IACrC,WACS,MAAM,QAAQ,MAAM,GAAG;AAC5B,aAAO,OAAO,IAAI,CAAC,kBAAkB,wBAAwB,eAAe,QAAQ,KAChF,qBAAqB,OAAO;AAAA,IACpC,OACK;AACD,aAAO,qBAAqB,MAAM;AAAA,IACtC;AAAA,EACJ;AAnBS;;;ACFT,WAAS,oBAAoB,SAAS,WAAWC,YAAW,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,GAAG,aAAa,QAAQ,MAAAC,QAAO,WAAW,MAAO,IAAI,CAAC,GAAG,gBAAgB,QAAW;AAClL,UAAM,kBAAkB;AAAA,MACpB,CAAC,SAAS,GAAGD;AAAA,IACjB;AACA,QAAI;AACA,sBAAgB,SAAS;AAC7B,UAAM,SAAS,wBAAwBC,OAAM,QAAQ;AAIrD,QAAI,MAAM,QAAQ,MAAM;AACpB,sBAAgB,SAAS;AAC7B,QAAI,YAAY,OAAO;AACnB,uBAAiB;AAAA,IACrB;AACA,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA,QAAQ,CAAC,MAAM,QAAQ,MAAM,IAAI,SAAS;AAAA,MAC1C,MAAM;AAAA,MACN,YAAY,SAAS;AAAA,MACrB,WAAW,eAAe,YAAY,cAAc;AAAA,IACxD;AACA,QAAI;AACA,cAAQ,gBAAgB;AAC5B,UAAM,YAAY,QAAQ,QAAQ,iBAAiB,OAAO;AAC1D,QAAI,YAAY,OAAO;AACnB,gBAAU,SAAS,QAAQ,MAAM;AAC7B,yBAAiB;AAAA,MACrB,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAhCS;;;ACJT,WAAS,YAAY,MAAM;AACvB,WAAO,OAAO,SAAS,cAAc,oBAAoB;AAAA,EAC7D;AAFS;;;ACGT,WAAS,sBAAsB,EAAE,MAAM,GAAG,QAAQ,GAAG;AACjD,QAAI,YAAY,IAAI,KAAK,qBAAqB,GAAG;AAC7C,aAAO,KAAK,eAAe,OAAO;AAAA,IACtC,OACK;AACD,cAAQ,aAAa,QAAQ,WAAW;AACxC,cAAQ,SAAS,QAAQ,OAAO;AAAA,IACpC;AACA,WAAO;AAAA,EACX;AATS;;;ACQT,MAAM,kBAAN,cAA8B,YAAY;AAAA,IAX1C,OAW0C;AAAA;AAAA;AAAA,IACtC,YAAY,SAAS;AACjB,YAAM;AACN,WAAK,eAAe;AACpB,WAAK,YAAY;AACjB,UAAI,CAAC;AACD;AACJ,YAAM,EAAE,SAAS,MAAM,WAAAC,YAAW,eAAe,eAAe,OAAO,eAAe,WAAY,IAAI;AACtG,WAAK,kBAAkB,QAAQ,aAAa;AAC5C,WAAK,eAAe;AACpB,WAAK,UAAU;AACf,gBAAU,OAAO,QAAQ,SAAS,UAAU,sDAAsD,aAAa;AAC/G,YAAM,aAAa,sBAAsB,OAAO;AAChD,WAAK,YAAY,oBAAoB,SAAS,MAAMA,YAAW,YAAY,aAAa;AACxF,UAAI,WAAW,aAAa,OAAO;AAC/B,aAAK,UAAU,MAAM;AAAA,MACzB;AACA,WAAK,UAAU,WAAW,MAAM;AAC5B,aAAK,eAAe,KAAK;AACzB,YAAI,CAAC,eAAe;AAChB,gBAAM,WAAW,iBAAiBA,YAAW,KAAK,SAAS,eAAe,KAAK,KAAK;AACpF,cAAI,KAAK,mBAAmB;AACxB,iBAAK,kBAAkB,QAAQ;AAAA,UACnC,OACK;AAKD,qBAAS,SAAS,MAAM,QAAQ;AAAA,UACpC;AACA,eAAK,UAAU,OAAO;AAAA,QAC1B;AACA,qBAAa;AACb,aAAK,eAAe;AAAA,MACxB;AAAA,IACJ;AAAA,IACA,OAAO;AACH,UAAI,KAAK;AACL;AACJ,WAAK,UAAU,KAAK;AACpB,UAAI,KAAK,UAAU,YAAY;AAC3B,aAAK,eAAe;AAAA,MACxB;AAAA,IACJ;AAAA,IACA,QAAQ;AACJ,WAAK,UAAU,MAAM;AAAA,IACzB;AAAA,IACA,WAAW;AACP,WAAK,UAAU,SAAS;AAAA,IAC5B;AAAA,IACA,SAAS;AACL,UAAI;AACA,aAAK,UAAU,OAAO;AAAA,MAC1B,SACO,GAAG;AAAA,MAAE;AAAA,IAChB;AAAA,IACA,OAAO;AACH,UAAI,KAAK;AACL;AACJ,WAAK,YAAY;AACjB,YAAM,EAAE,MAAM,IAAI;AAClB,UAAI,UAAU,UAAU,UAAU,YAAY;AAC1C;AAAA,MACJ;AACA,UAAI,KAAK,mBAAmB;AACxB,aAAK,kBAAkB;AAAA,MAC3B,OACK;AACD,aAAK,aAAa;AAAA,MACtB;AACA,UAAI,CAAC,KAAK;AACN,aAAK,OAAO;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,eAAe;AACX,UAAI,CAAC,KAAK,iBAAiB;AACvB,aAAK,UAAU,eAAe;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,IAAI,WAAW;AACX,YAAM,WAAW,KAAK,UAAU,QAAQ,oBAAoB,EAAE,YAAY;AAC1E,aAAO,sBAAsB,OAAO,QAAQ,CAAC;AAAA,IACjD;AAAA,IACA,IAAI,OAAO;AACP,aAAO,sBAAsB,OAAO,KAAK,UAAU,WAAW,KAAK,CAAC;AAAA,IACxE;AAAA,IACA,IAAI,KAAK,SAAS;AACd,WAAK,eAAe;AACpB,WAAK,UAAU,cAAc,sBAAsB,OAAO;AAAA,IAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,QAAQ;AACR,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,IAAI,MAAM,UAAU;AAEhB,UAAI,WAAW;AACX,aAAK,eAAe;AACxB,WAAK,UAAU,eAAe;AAAA,IAClC;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK,iBAAiB,OACvB,aACA,KAAK,UAAU;AAAA,IACzB;AAAA,IACA,IAAI,YAAY;AACZ,aAAO,OAAO,KAAK,UAAU,SAAS;AAAA,IAC1C;AAAA,IACA,IAAI,UAAU,cAAc;AACxB,WAAK,UAAU,YAAY;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,IAIA,eAAe,EAAE,UAAU,QAAQ,GAAG;AAClC,UAAI,KAAK,cAAc;AACnB,aAAK,UAAU,QAAQ,aAAa,EAAE,QAAQ,SAAS,CAAC;AAAA,MAC5D;AACA,WAAK,UAAU,WAAW;AAC1B,UAAI,YAAY,uBAAuB,GAAG;AACtC,aAAK,UAAU,WAAW;AAC1B,eAAO;AAAA,MACX,OACK;AACD,eAAO,QAAQ,IAAI;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;;;ACvJA,MAAM,6BAA6B;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,WAAS,kBAAkB,KAAK;AAC5B,WAAO,OAAO;AAAA,EAClB;AAFS;AAGT,WAAS,oBAAoB,YAAY;AACrC,QAAI,OAAO,WAAW,SAAS,YAC3B,kBAAkB,WAAW,IAAI,GAAG;AACpC,iBAAW,OAAO,2BAA2B,WAAW,IAAI;AAAA,IAChE;AAAA,EACJ;AALS;;;ACCT,MAAM,cAAc;AACpB,MAAM,0BAAN,cAAsC,gBAAgB;AAAA,IAZtD,OAYsD;AAAA;AAAA;AAAA,IAClD,YAAY,SAAS;AAUjB,0BAAoB,OAAO;AAQ3B,4BAAsB,OAAO;AAC7B,YAAM,OAAO;AACb,UAAI,QAAQ,WAAW;AACnB,aAAK,YAAY,QAAQ;AAAA,MAC7B;AACA,WAAK,UAAU;AAAA,IACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,kBAAkB,OAAO;AACrB,YAAM,EAAE,aAAAC,cAAa,UAAU,YAAY,SAAS,GAAG,QAAQ,IAAI,KAAK;AACxE,UAAI,CAACA;AACD;AACJ,UAAI,UAAU,QAAW;AACrB,QAAAA,aAAY,IAAI,KAAK;AACrB;AAAA,MACJ;AACA,YAAM,kBAAkB,IAAI,YAAY;AAAA,QACpC,GAAG;AAAA,QACH,UAAU;AAAA,MACd,CAAC;AACD,YAAM,aAAa,sBAAsB,KAAK,gBAAgB,KAAK,IAAI;AACvE,MAAAA,aAAY,gBAAgB,gBAAgB,OAAO,aAAa,WAAW,EAAE,OAAO,gBAAgB,OAAO,UAAU,EAAE,OAAO,WAAW;AACzI,sBAAgB,KAAK;AAAA,IACzB;AAAA,EACJ;;;ACnDA,MAAM,eAAe,wBAAC,OAAO,SAAS;AAElC,QAAI,SAAS;AACT,aAAO;AAIX,QAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK;AAChD,aAAO;AACX,QAAI,OAAO,UAAU;AAAA,KAChB,QAAQ,KAAK,KAAK,KAAK,UAAU;AAAA,IAClC,CAAC,MAAM,WAAW,MAAM,GAC1B;AACE,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,GAhBqB;;;ACPrB,WAAS,oBAAoBC,YAAW;AACpC,UAAM,UAAUA,WAAU,CAAC;AAC3B,QAAIA,WAAU,WAAW;AACrB,aAAO;AACX,aAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,UAAIA,WAAU,CAAC,MAAM;AACjB,eAAO;AAAA,IACf;AAAA,EACJ;AARS;AAST,WAAS,WAAWA,YAAW,MAAM,MAAM,UAAU;AAMjD,UAAM,iBAAiBA,WAAU,CAAC;AAClC,QAAI,mBAAmB;AACnB,aAAO;AAMX,QAAI,SAAS,aAAa,SAAS;AAC/B,aAAO;AACX,UAAM,iBAAiBA,WAAUA,WAAU,SAAS,CAAC;AACrD,UAAM,qBAAqB,aAAa,gBAAgB,IAAI;AAC5D,UAAM,qBAAqB,aAAa,gBAAgB,IAAI;AAC5D,YAAQ,uBAAuB,oBAAoB,6BAA6B,IAAI,UAAU,cAAc,SAAS,cAAc,OAAO,qBAAqB,iBAAiB,cAAc,iCAAiC,sBAAsB;AAErP,QAAI,CAAC,sBAAsB,CAAC,oBAAoB;AAC5C,aAAO;AAAA,IACX;AACA,WAAQ,oBAAoBA,UAAS,MAC/B,SAAS,YAAY,YAAY,IAAI,MAAM;AAAA,EACrD;AA1BS;;;ACbT,WAAS,qBAAqB,SAAS;AACnC,YAAQ,WAAW;AACnB,YAAQ,SAAS;AAAA,EACrB;AAHS;;;ACKT,MAAM,oBAAoB,oBAAI,IAAI;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,EAGJ,CAAC;AACD,MAAM,gBAA8B,qBAAK,MAAM,OAAO,eAAe,KAAK,QAAQ,WAAW,SAAS,CAAC;AACvG,WAAS,yBAAyB,SAAS;AACvC,UAAM,EAAE,aAAAC,cAAa,MAAM,aAAa,YAAY,SAAS,KAAK,IAAI;AACtE,UAAM,UAAUA,cAAa,OAAO;AAOpC,QAAI,EAAE,mBAAmB,cAAc;AACnC,aAAO;AAAA,IACX;AACA,UAAM,EAAE,UAAU,kBAAkB,IAAIA,aAAY,MAAM,SAAS;AACnE,WAAQ,cAAc,KAClB,QACA,kBAAkB,IAAI,IAAI,MACzB,SAAS,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA,IAK1B,CAAC,YACD,CAAC,eACD,eAAe,YACf,YAAY,KACZ,SAAS;AAAA,EACjB;AA1BS;;;ACKT,MAAM,oBAAoB;AAC1B,MAAM,4BAAN,cAAwC,YAAY;AAAA,IApBpD,OAoBoD;AAAA;AAAA;AAAA,IAChD,YAAY,EAAE,WAAW,MAAM,QAAQ,GAAG,OAAO,aAAa,SAAS,GAAG,cAAc,GAAG,aAAa,QAAQ,WAAAC,YAAW,MAAM,aAAAC,cAAa,SAAS,GAAG,QAAQ,GAAG;AACjK,YAAM;AAIN,WAAK,OAAO,MAAM;AACd,YAAI,KAAK,YAAY;AACjB,eAAK,WAAW,KAAK;AACrB,eAAK,eAAe;AAAA,QACxB;AACA,aAAK,kBAAkB,OAAO;AAAA,MAClC;AACA,WAAK,YAAY,KAAK,IAAI;AAC1B,YAAM,sBAAsB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAAA;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACP;AACA,YAAM,qBAAqB,SAAS,oBAAoB;AACxD,WAAK,mBAAmB,IAAI,mBAAmBD,YAAW,CAAC,mBAAmB,eAAe,WAAW,KAAK,oBAAoB,mBAAmB,eAAe,qBAAqB,CAAC,MAAM,GAAG,MAAMC,cAAa,OAAO;AAC5N,WAAK,kBAAkB,gBAAgB;AAAA,IAC3C;AAAA,IACA,oBAAoBD,YAAW,eAAe,SAASE,OAAM;AACzD,WAAK,mBAAmB;AACxB,YAAM,EAAE,MAAM,MAAM,UAAU,OAAO,WAAW,SAAS,IAAI;AAC7D,WAAK,aAAa,KAAK,IAAI;AAK3B,UAAI,CAAC,WAAWF,YAAW,MAAM,MAAM,QAAQ,GAAG;AAC9C,YAAI,mBAAmB,qBAAqB,CAAC,OAAO;AAChD,qBAAW,iBAAiBA,YAAW,SAAS,aAAa,CAAC;AAAA,QAClE;AACA,QAAAA,WAAU,CAAC,IAAIA,WAAUA,WAAU,SAAS,CAAC;AAC7C,6BAAqB,OAAO;AAC5B,gBAAQ,SAAS;AAAA,MACrB;AAaA,YAAM,YAAYE,QACZ,CAAC,KAAK,aACF,KAAK,YACL,KAAK,aAAa,KAAK,YAAY,oBAC/B,KAAK,aACL,KAAK,YACb;AACN,YAAM,kBAAkB;AAAA,QACpB;AAAA,QACA;AAAA,QACA,GAAG;AAAA,QACH,WAAAF;AAAA,MACJ;AAMA,YAAM,YAAY,CAAC,aAAa,yBAAyB,eAAe,IAClE,IAAI,wBAAwB;AAAA,QAC1B,GAAG;AAAA,QACH,SAAS,gBAAgB,YAAY,MAAM;AAAA,MAC/C,CAAC,IACC,IAAI,YAAY,eAAe;AACrC,gBAAU,SAAS,KAAK,MAAM,KAAK,eAAe,CAAC,EAAE,MAAM,IAAI;AAC/D,UAAI,KAAK,iBAAiB;AACtB,aAAK,eAAe,UAAU,eAAe,KAAK,eAAe;AACjE,aAAK,kBAAkB;AAAA,MAC3B;AACA,WAAK,aAAa;AAAA,IACtB;AAAA,IACA,IAAI,WAAW;AACX,UAAI,CAAC,KAAK,YAAY;AAClB,eAAO,KAAK;AAAA,MAChB,OACK;AACD,eAAO,KAAK,UAAU;AAAA,MAC1B;AAAA,IACJ;AAAA,IACA,KAAK,WAAW,WAAW;AACvB,aAAO,KAAK,SAAS,QAAQ,SAAS,EAAE,KAAK,MAAM;AAAA,MAAE,CAAC;AAAA,IAC1D;AAAA,IACA,IAAI,YAAY;AACZ,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,kBAAkB,OAAO;AAC9B,+BAAuB;AAAA,MAC3B;AACA,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,WAAW;AACX,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,IAAI,OAAO;AACP,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,IAAI,KAAK,SAAS;AACd,WAAK,UAAU,OAAO;AAAA,IAC1B;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,IAAI,MAAM,UAAU;AAChB,WAAK,UAAU,QAAQ;AAAA,IAC3B;AAAA,IACA,IAAI,YAAY;AACZ,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,eAAe,UAAU;AACrB,UAAI,KAAK,YAAY;AACjB,aAAK,eAAe,KAAK,UAAU,eAAe,QAAQ;AAAA,MAC9D,OACK;AACD,aAAK,kBAAkB;AAAA,MAC3B;AACA,aAAO,MAAM,KAAK,KAAK;AAAA,IAC3B;AAAA,IACA,OAAO;AACH,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,IACA,QAAQ;AACJ,WAAK,UAAU,MAAM;AAAA,IACzB;AAAA,IACA,WAAW;AACP,WAAK,UAAU,SAAS;AAAA,IAC5B;AAAA,IACA,SAAS;AACL,UAAI,KAAK,YAAY;AACjB,aAAK,UAAU,OAAO;AAAA,MAC1B;AACA,WAAK,kBAAkB,OAAO;AAAA,IAClC;AAAA,EACJ;;;AC5KA,MAAM,iBAAN,MAAqB;AAAA,IAArB,OAAqB;AAAA;AAAA;AAAA,IACjB,YAAY,YAAY;AAEpB,WAAK,OAAO,MAAM,KAAK,OAAO,MAAM;AACpC,WAAK,aAAa,WAAW,OAAO,OAAO;AAAA,IAC/C;AAAA,IACA,IAAI,WAAW;AACX,aAAO,QAAQ,IAAI,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,QAAQ,CAAC;AAAA,IAC7E;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,UAAU;AACb,aAAO,KAAK,WAAW,CAAC,EAAE,QAAQ;AAAA,IACtC;AAAA,IACA,OAAO,UAAU,UAAU;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,aAAK,WAAW,CAAC,EAAE,QAAQ,IAAI;AAAA,MACnC;AAAA,IACJ;AAAA,IACA,eAAe,UAAU;AACrB,YAAM,gBAAgB,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,eAAe,QAAQ,CAAC;AAC3F,aAAO,MAAM;AACT,sBAAc,QAAQ,CAAC,QAAQ,MAAM;AACjC,oBAAU,OAAO;AACjB,eAAK,WAAW,CAAC,EAAE,KAAK;AAAA,QAC5B,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,IACA,IAAI,OAAO;AACP,aAAO,KAAK,OAAO,MAAM;AAAA,IAC7B;AAAA,IACA,IAAI,KAAKG,OAAM;AACX,WAAK,OAAO,QAAQA,KAAI;AAAA,IAC5B;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK,OAAO,OAAO;AAAA,IAC9B;AAAA,IACA,IAAI,MAAM,OAAO;AACb,WAAK,OAAO,SAAS,KAAK;AAAA,IAC9B;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK,OAAO,OAAO;AAAA,IAC9B;AAAA,IACA,IAAI,YAAY;AACZ,aAAO,KAAK,OAAO,WAAW;AAAA,IAClC;AAAA,IACA,IAAI,WAAW;AACX,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,cAAM,KAAK,IAAI,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ;AAAA,MACnD;AACA,aAAO;AAAA,IACX;AAAA,IACA,OAAO,YAAY;AACf,WAAK,WAAW,QAAQ,CAAC,aAAa,SAAS,UAAU,EAAE,CAAC;AAAA,IAChE;AAAA,IACA,OAAO;AACH,WAAK,OAAO,MAAM;AAAA,IACtB;AAAA,IACA,QAAQ;AACJ,WAAK,OAAO,OAAO;AAAA,IACvB;AAAA,IACA,SAAS;AACL,WAAK,OAAO,QAAQ;AAAA,IACxB;AAAA,IACA,WAAW;AACP,WAAK,OAAO,UAAU;AAAA,IAC1B;AAAA,EACJ;;;ACnEA,MAAM,yBAAN,cAAqC,eAAe;AAAA,IAFpD,OAEoD;AAAA;AAAA;AAAA,IAChD,KAAK,WAAW,WAAW;AACvB,aAAO,KAAK,SAAS,QAAQ,SAAS,EAAE,KAAK,MAAM;AAAA,MAAE,CAAC;AAAA,IAC1D;AAAA,EACJ;;;ACMA,MAAM;AAAA;AAAA,IAEN;AAAA;AACA,WAAS,iBAAiB,SAAS;AAC/B,UAAM,QAAQ,sBAAsB,KAAK,OAAO;AAChD,QAAI,CAAC;AACD,aAAO,CAAC,CAAC;AACb,UAAM,CAAC,EAAE,QAAQ,QAAQ,QAAQ,IAAI;AACrC,WAAO,CAAC,KAAK,UAAU,MAAM,IAAI,QAAQ;AAAA,EAC7C;AANS;AAOT,MAAM,WAAW;AACjB,WAAS,iBAAiB,SAAS,SAAS,QAAQ,GAAG;AACnD,cAAU,SAAS,UAAU,yDAAyD,OAAO,wDAAwD,mBAAmB;AACxK,UAAM,CAAC,OAAO,QAAQ,IAAI,iBAAiB,OAAO;AAElD,QAAI,CAAC;AACD;AAEJ,UAAM,WAAW,OAAO,iBAAiB,OAAO,EAAE,iBAAiB,KAAK;AACxE,QAAI,UAAU;AACV,YAAM,UAAU,SAAS,KAAK;AAC9B,aAAO,kBAAkB,OAAO,IAAI,WAAW,OAAO,IAAI;AAAA,IAC9D;AACA,WAAO,mBAAmB,QAAQ,IAC5B,iBAAiB,UAAU,SAAS,QAAQ,CAAC,IAC7C;AAAA,EACV;AAfS;;;ACvBT,WAAS,mBAAmB,YAAY,KAAK;AACzC,WAAQ,aAAa,GAAG,KACpB,aAAa,SAAS,KACtB;AAAA,EACR;AAJS;;;ACET,MAAM,iBAAiB,oBAAI,IAAI;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,CAAC;;;ACPD,MAAM,OAAO;AAAA,IACT,MAAM,wBAAC,MAAM,MAAM,QAAb;AAAA,IACN,OAAO,wBAAC,MAAM,GAAP;AAAA,EACX;;;ACHA,MAAM,gBAAgB,wBAAC,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC,GAA5B;;;ACKtB,MAAM,sBAAsB,CAAC,QAAQ,IAAI,SAAS,SAAS,IAAI,IAAI,IAAI;AAIvE,MAAM,yBAAyB,wBAAC,MAAM,oBAAoB,KAAK,cAAc,CAAC,CAAC,GAAhD;;;ACV/B,WAAS,OAAO,OAAO;AACnB,QAAI,OAAO,UAAU,UAAU;AAC3B,aAAO,UAAU;AAAA,IACrB,WACS,UAAU,MAAM;AACrB,aAAO,UAAU,UAAU,UAAU,OAAO,kBAAkB,KAAK;AAAA,IACvE,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAVS;;;ACIT,MAAM,cAAc,oBAAI,IAAI,CAAC,cAAc,YAAY,YAAY,SAAS,CAAC;AAC7E,WAAS,mBAAmB,GAAG;AAC3B,UAAM,CAAC,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC9C,QAAI,SAAS;AACT,aAAO;AACX,UAAM,CAACC,OAAM,IAAI,MAAM,MAAM,UAAU,KAAK,CAAC;AAC7C,QAAI,CAACA;AACD,aAAO;AACX,UAAM,OAAO,MAAM,QAAQA,SAAQ,EAAE;AACrC,QAAI,eAAe,YAAY,IAAI,IAAI,IAAI,IAAI;AAC/C,QAAIA,YAAW;AACX,sBAAgB;AACpB,WAAO,OAAO,MAAM,eAAe,OAAO;AAAA,EAC9C;AAZS;AAaT,MAAM,gBAAgB;AACtB,MAAM,SAAS;AAAA,IACX,GAAG;AAAA,IACH,mBAAmB,wBAAC,MAAM;AACtB,YAAM,YAAY,EAAE,MAAM,aAAa;AACvC,aAAO,YAAY,UAAU,IAAI,kBAAkB,EAAE,KAAK,GAAG,IAAI;AAAA,IACrE,GAHmB;AAAA,EAIvB;;;ACzBA,MAAM,MAAM;AAAA,IACR,GAAG;AAAA,IACH,WAAW,KAAK;AAAA,EACpB;;;ACFA,MAAM,sBAAsB;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,EACb;;;ACvBA,MAAM,mBAAmB;AAAA;AAAA,IAErB,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,yBAAyB;AAAA,IACzB,wBAAwB;AAAA;AAAA,IAExB,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA;AAAA,IAEN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA;AAAA,IAEZ,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,GAAG;AAAA,IACH,QAAQ;AAAA;AAAA,IAER,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,EAChB;;;ACxCA,MAAM,oBAAoB;AAAA,IACtB,GAAG;AAAA;AAAA,IAEH;AAAA,IACA,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA,IAER,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,EAClB;AAIA,MAAM,sBAAsB,wBAAC,QAAQ,kBAAkB,GAAG,GAA9B;;;ACvB5B,WAASC,mBAAkB,KAAK,OAAO;AACnC,QAAI,mBAAmB,oBAAoB,GAAG;AAC9C,QAAI,qBAAqB;AACrB,yBAAmB;AAEvB,WAAO,iBAAiB,oBAClB,iBAAiB,kBAAkB,KAAK,IACxC;AAAA,EACV;AARS,SAAAA,oBAAA;;;ACKT,MAAM,mBAAmB,oBAAI,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAC;AACtD,WAAS,4BAA4B,qBAAqB,qBAAqB,MAAM;AACjF,QAAI,IAAI;AACR,QAAI,qBAAqB;AACzB,WAAO,IAAI,oBAAoB,UAAU,CAAC,oBAAoB;AAC1D,YAAM,WAAW,oBAAoB,CAAC;AACtC,UAAI,OAAO,aAAa,YACpB,CAAC,iBAAiB,IAAI,QAAQ,KAC9B,oBAAoB,QAAQ,EAAE,OAAO,QAAQ;AAC7C,6BAAqB,oBAAoB,CAAC;AAAA,MAC9C;AACA;AAAA,IACJ;AACA,QAAI,sBAAsB,MAAM;AAC5B,iBAAW,aAAa,qBAAqB;AACzC,4BAAoB,SAAS,IAAIC,mBAAkB,MAAM,kBAAkB;AAAA,MAC/E;AAAA,IACJ;AAAA,EACJ;AAjBS;;;ACDT,MAAM,uBAAN,cAAmC,iBAAiB;AAAA,IATpD,OASoD;AAAA;AAAA;AAAA,IAChD,YAAY,qBAAqB,YAAY,MAAMC,cAAa,SAAS;AACrE,YAAM,qBAAqB,YAAY,MAAMA,cAAa,SAAS,IAAI;AAAA,IAC3E;AAAA,IACA,gBAAgB;AACZ,YAAM,EAAE,qBAAqB,SAAS,KAAK,IAAI;AAC/C,UAAI,CAAC,WAAW,CAAC,QAAQ;AACrB;AACJ,YAAM,cAAc;AAIpB,eAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,YAAI,WAAW,oBAAoB,CAAC;AACpC,YAAI,OAAO,aAAa,UAAU;AAC9B,qBAAW,SAAS,KAAK;AACzB,cAAI,mBAAmB,QAAQ,GAAG;AAC9B,kBAAM,WAAW,iBAAiB,UAAU,QAAQ,OAAO;AAC3D,gBAAI,aAAa,QAAW;AACxB,kCAAoB,CAAC,IAAI;AAAA,YAC7B;AACA,gBAAI,MAAM,oBAAoB,SAAS,GAAG;AACtC,mBAAK,gBAAgB;AAAA,YACzB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAMA,WAAK,qBAAqB;AAO1B,UAAI,CAAC,eAAe,IAAI,IAAI,KAAK,oBAAoB,WAAW,GAAG;AAC/D;AAAA,MACJ;AACA,YAAM,CAAC,QAAQ,MAAM,IAAI;AACzB,YAAM,aAAa,uBAAuB,MAAM;AAChD,YAAM,aAAa,uBAAuB,MAAM;AAIhD,UAAI,eAAe;AACf;AAKJ,UAAI,cAAc,UAAU,KAAK,cAAc,UAAU,GAAG;AACxD,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,gBAAM,QAAQ,oBAAoB,CAAC;AACnC,cAAI,OAAO,UAAU,UAAU;AAC3B,gCAAoB,CAAC,IAAI,WAAW,KAAK;AAAA,UAC7C;AAAA,QACJ;AAAA,MACJ,WACS,iBAAiB,IAAI,GAAG;AAI7B,aAAK,mBAAmB;AAAA,MAC5B;AAAA,IACJ;AAAA,IACA,uBAAuB;AACnB,YAAM,EAAE,qBAAqB,KAAK,IAAI;AACtC,YAAM,sBAAsB,CAAC;AAC7B,eAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,YAAI,oBAAoB,CAAC,MAAM,QAC3B,OAAO,oBAAoB,CAAC,CAAC,GAAG;AAChC,8BAAoB,KAAK,CAAC;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,oBAAoB,QAAQ;AAC5B,oCAA4B,qBAAqB,qBAAqB,IAAI;AAAA,MAC9E;AAAA,IACJ;AAAA,IACA,sBAAsB;AAClB,YAAM,EAAE,SAAS,qBAAqB,KAAK,IAAI;AAC/C,UAAI,CAAC,WAAW,CAAC,QAAQ;AACrB;AACJ,UAAI,SAAS,UAAU;AACnB,aAAK,mBAAmB,OAAO;AAAA,MACnC;AACA,WAAK,iBAAiB,iBAAiB,IAAI,EAAE,QAAQ,mBAAmB,GAAG,OAAO,iBAAiB,QAAQ,OAAO,CAAC;AACnH,0BAAoB,CAAC,IAAI,KAAK;AAE9B,YAAM,kBAAkB,oBAAoB,oBAAoB,SAAS,CAAC;AAC1E,UAAI,oBAAoB,QAAW;AAC/B,gBAAQ,SAAS,MAAM,eAAe,EAAE,KAAK,iBAAiB,KAAK;AAAA,MACvE;AAAA,IACJ;AAAA,IACA,kBAAkB;AACd,YAAM,EAAE,SAAS,MAAM,oBAAoB,IAAI;AAC/C,UAAI,CAAC,WAAW,CAAC,QAAQ;AACrB;AACJ,YAAM,QAAQ,QAAQ,SAAS,IAAI;AACnC,eAAS,MAAM,KAAK,KAAK,gBAAgB,KAAK;AAC9C,YAAM,qBAAqB,oBAAoB,SAAS;AACxD,YAAM,gBAAgB,oBAAoB,kBAAkB;AAC5D,0BAAoB,kBAAkB,IAAI,iBAAiB,IAAI,EAAE,QAAQ,mBAAmB,GAAG,OAAO,iBAAiB,QAAQ,OAAO,CAAC;AACvI,UAAI,kBAAkB,QAAQ,KAAK,kBAAkB,QAAW;AAC5D,aAAK,gBAAgB;AAAA,MACzB;AAEA,UAAI,KAAK,mBAAmB,QAAQ;AAChC,aAAK,kBAAkB,QAAQ,CAAC,CAAC,oBAAoB,mBAAmB,MAAM;AAC1E,kBACK,SAAS,kBAAkB,EAC3B,IAAI,mBAAmB;AAAA,QAChC,CAAC;AAAA,MACL;AACA,WAAK,qBAAqB;AAAA,IAC9B;AAAA,EACJ;;;AChIA,WAAS,gBAAgB,mBAAmB,OAAO,eAAe;AAC9D,QAAI,6BAA6B,aAAa;AAC1C,aAAO,CAAC,iBAAiB;AAAA,IAC7B,WACS,OAAO,sBAAsB,UAAU;AAC5C,UAAI,OAAO;AACX,UAAI,OAAO;AACP,eAAO,MAAM;AAAA,MACjB;AACA,YAAM,WAAW,gBAAgB,iBAAiB,KAC9C,KAAK,iBAAiB,iBAAiB;AAC3C,aAAO,WAAW,MAAM,KAAK,QAAQ,IAAI,CAAC;AAAA,IAC9C;AACA,WAAO,MAAM,KAAK,iBAAiB;AAAA,EACvC;AAdS;;;ACGT,MAAM,iBAAiB,wBAAC,OAAO,SAAS;AACpC,WAAO,QAAQ,OAAO,UAAU,WAC1B,KAAK,UAAU,KAAK,IACpB;AAAA,EACV,GAJuB;;;ACKvB,MAAM,qBAAqB;AAC3B,MAAM,UAAU,wBAAC,UAAU;AACvB,WAAO,CAAC,MAAM,WAAW,KAAK,CAAC;AAAA,EACnC,GAFgB;AAGhB,MAAM,sBAAsB;AAAA,IACxB,SAAS;AAAA,EACb;AAMA,MAAM,cAAN,MAAkB;AAAA,IApBlB,OAoBkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOd,YAAY,MAAM,UAAU,CAAC,GAAG;AAQ5B,WAAK,mBAAmB;AAIxB,WAAK,SAAS,CAAC;AACf,WAAK,kBAAkB,CAAC,MAAM;AAC1B,cAAM,cAAc,KAAK,IAAI;AAM7B,YAAI,KAAK,cAAc,aAAa;AAChC,eAAK,kBAAkB;AAAA,QAC3B;AACA,aAAK,OAAO,KAAK;AACjB,aAAK,WAAW,CAAC;AAEjB,YAAI,KAAK,YAAY,KAAK,MAAM;AAC5B,eAAK,OAAO,QAAQ,OAAO,KAAK,OAAO;AACvC,cAAI,KAAK,YAAY;AACjB,uBAAW,aAAa,KAAK,YAAY;AACrC,wBAAU,MAAM;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,cAAc;AACnB,WAAK,WAAW,IAAI;AACpB,WAAK,QAAQ,QAAQ;AAAA,IACzB;AAAA,IACA,WAAW,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,YAAY,KAAK,IAAI;AAC1B,UAAI,KAAK,qBAAqB,QAAQ,YAAY,QAAW;AACzD,aAAK,mBAAmB,QAAQ,KAAK,OAAO;AAAA,MAChD;AAAA,IACJ;AAAA,IACA,kBAAkB,iBAAiB,KAAK,SAAS;AAC7C,WAAK,iBAAiB;AACtB,WAAK,gBAAgB,KAAK;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAyCA,SAAS,cAAc;AACnB,UAAI,MAAuC;AACvC,iBAAS,OAAO,iFAAiF;AAAA,MACrG;AACA,aAAO,KAAK,GAAG,UAAU,YAAY;AAAA,IACzC;AAAA,IACA,GAAG,WAAW,UAAU;AACpB,UAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AACzB,aAAK,OAAO,SAAS,IAAI,IAAI,oBAAoB;AAAA,MACrD;AACA,YAAM,cAAc,KAAK,OAAO,SAAS,EAAE,IAAI,QAAQ;AACvD,UAAI,cAAc,UAAU;AACxB,eAAO,MAAM;AACT,sBAAY;AAKZ,gBAAM,KAAK,MAAM;AACb,gBAAI,CAAC,KAAK,OAAO,OAAO,QAAQ,GAAG;AAC/B,mBAAK,KAAK;AAAA,YACd;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,iBAAiB;AACb,iBAAW,iBAAiB,KAAK,QAAQ;AACrC,aAAK,OAAO,aAAa,EAAE,MAAM;AAAA,MACrC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,eAAe,mBAAmB;AACrC,WAAK,gBAAgB;AACrB,WAAK,oBAAoB;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBA,IAAI,GAAG;AACH,UAAI,CAAC,KAAK,eAAe;AACrB,aAAK,gBAAgB,CAAC;AAAA,MAC1B,OACK;AACD,aAAK,cAAc,GAAG,KAAK,eAAe;AAAA,MAC9C;AAAA,IACJ;AAAA,IACA,gBAAgB,MAAM,SAAS,OAAO;AAClC,WAAK,IAAI,OAAO;AAChB,WAAK,OAAO;AACZ,WAAK,iBAAiB;AACtB,WAAK,gBAAgB,KAAK,YAAY;AAAA,IAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,KAAK,GAAG,eAAe,MAAM;AACzB,WAAK,gBAAgB,CAAC;AACtB,WAAK,OAAO;AACZ,WAAK,gBAAgB,KAAK,iBAAiB;AAC3C,sBAAgB,KAAK,KAAK;AAC1B,UAAI,KAAK;AACL,aAAK,kBAAkB;AAAA,IAC/B;AAAA,IACA,QAAQ;AACJ,WAAK,OAAO,QAAQ,OAAO,KAAK,OAAO;AAAA,IAC3C;AAAA,IACA,aAAa,WAAW;AACpB,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,aAAa,oBAAI,IAAI;AAAA,MAC9B;AACA,WAAK,WAAW,IAAI,SAAS;AAAA,IACjC;AAAA,IACA,gBAAgB,WAAW;AACvB,UAAI,KAAK,YAAY;AACjB,aAAK,WAAW,OAAO,SAAS;AAAA,MACpC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,MAAM;AACF,UAAI,oBAAoB,SAAS;AAC7B,4BAAoB,QAAQ,KAAK,IAAI;AAAA,MACzC;AACA,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA,IAIA,cAAc;AACV,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,cAAc;AACV,YAAM,cAAc,KAAK,IAAI;AAC7B,UAAI,CAAC,KAAK,oBACN,KAAK,mBAAmB,UACxB,cAAc,KAAK,YAAY,oBAAoB;AACnD,eAAO;AAAA,MACX;AACA,YAAM,QAAQ,KAAK,IAAI,KAAK,YAAY,KAAK,eAAe,kBAAkB;AAE9E,aAAO,kBAAkB,WAAW,KAAK,OAAO,IAC5C,WAAW,KAAK,cAAc,GAAG,KAAK;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,MAAM,gBAAgB;AAClB,WAAK,KAAK;AACV,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,aAAK,cAAc;AACnB,aAAK,YAAY,eAAe,OAAO;AACvC,YAAI,KAAK,OAAO,gBAAgB;AAC5B,eAAK,OAAO,eAAe,OAAO;AAAA,QACtC;AAAA,MACJ,CAAC,EAAE,KAAK,MAAM;AACV,YAAI,KAAK,OAAO,mBAAmB;AAC/B,eAAK,OAAO,kBAAkB,OAAO;AAAA,QACzC;AACA,aAAK,eAAe;AAAA,MACxB,CAAC;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,OAAO;AACH,UAAI,KAAK,WAAW;AAChB,aAAK,UAAU,KAAK;AACpB,YAAI,KAAK,OAAO,iBAAiB;AAC7B,eAAK,OAAO,gBAAgB,OAAO;AAAA,QACvC;AAAA,MACJ;AACA,WAAK,eAAe;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,cAAc;AACV,aAAO,CAAC,CAAC,KAAK;AAAA,IAClB;AAAA,IACA,iBAAiB;AACb,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UAAU;AACN,WAAK,YAAY,MAAM;AACvB,WAAK,OAAO,SAAS,OAAO;AAC5B,WAAK,eAAe;AACpB,WAAK,KAAK;AACV,UAAI,KAAK,mBAAmB;AACxB,aAAK,kBAAkB;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,YAAY,MAAM,SAAS;AAChC,WAAO,IAAI,YAAY,MAAM,OAAO;AAAA,EACxC;AAFS;;;AC5TT,MAAM,EAAE,UAAU,WAAW,QAAQ,gBAAgB,IACrC,oCAAoB,gBAAgB,KAAK;;;ACGzD,WAAS,aAAa,SAAS;AAC3B,WAAO,SAAS,OAAO,KAAK,qBAAqB;AAAA,EACrD;AAFS;;;ACAT,WAAS,gBAAgB,SAAS;AAC9B,WAAO,aAAa,OAAO,KAAK,QAAQ,YAAY;AAAA,EACxD;AAFS;;;ACJT,WAAS,eAAe,MAAM,OAAO;AACjC,QAAI,SAAS,SAAS;AAClB,aAAO;AAAA,IACX,OACK;AACD,YAAM,YAAY,QAAQ;AAC1B,aAAO,SAAS,SAAS,YAAY,YAAY;AAAA,IACrD;AAAA,EACJ;AARS;AAST,WAAS,QAAQ,WAAW,KAAK,EAAE,aAAa,GAAG,OAAO,GAAG,MAAAC,MAAK,IAAI,CAAC,GAAG;AACtE,WAAO,CAAC,GAAG,UAAU;AACjB,YAAM,YAAY,OAAO,SAAS,WAAW,OAAO,eAAe,MAAM,KAAK;AAC9E,YAAMC,YAAW,KAAK,IAAI,YAAY,CAAC;AACvC,UAAI,QAAQ,WAAWA;AACvB,UAAID,OAAM;AACN,cAAM,WAAW,QAAQ;AACzB,cAAM,iBAAiB,2BAA2BA,KAAI;AACtD,gBAAQ,eAAe,QAAQ,QAAQ,IAAI;AAAA,MAC/C;AACA,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ;AAZS;;;ACXT,MAAM,gBAAgB,wBAAC,UAAU,QAAQ,SAAS,MAAM,WAAW,GAA7C;;;ACQtB,MAAM,aAAa,CAAC,GAAG,qBAAqB,OAAO,OAAO;AAI1D,MAAM,gBAAgB,wBAAC,MAAM,WAAW,KAAK,cAAc,CAAC,CAAC,GAAvC;;;ACZtB,WAAS,eAAeE,YAAW;AAC/B,WAAO,OAAOA,eAAc,YAAY,CAAC,MAAM,QAAQA,UAAS;AAAA,EACpE;AAFS;;;ACGT,WAAS,gBAAgB,SAASC,YAAW,OAAO,eAAe;AAC/D,QAAI,OAAO,YAAY,YAAY,eAAeA,UAAS,GAAG;AAC1D,aAAO,gBAAgB,SAAS,OAAO,aAAa;AAAA,IACxD,WACS,mBAAmB,UAAU;AAClC,aAAO,MAAM,KAAK,OAAO;AAAA,IAC7B,WACS,MAAM,QAAQ,OAAO,GAAG;AAC7B,aAAO;AAAA,IACX,OACK;AACD,aAAO,CAAC,OAAO;AAAA,IACnB;AAAA,EACJ;AAbS;;;ACHT,WAAS,wBAAwB,UAAU,QAAQ,cAAc;AAC7D,WAAO,YAAY,SAAS;AAAA,EAChC;AAFS;;;ACIT,WAAS,aAAa,SAAS,MAAM,MAAM,QAAQ;AAC/C,QAAI,OAAO,SAAS,UAAU;AAC1B,aAAO;AAAA,IACX,WACS,KAAK,WAAW,GAAG,KAAK,KAAK,WAAW,GAAG,GAAG;AACnD,aAAO,KAAK,IAAI,GAAG,UAAU,WAAW,IAAI,CAAC;AAAA,IACjD,WACS,SAAS,KAAK;AACnB,aAAO;AAAA,IACX,WACS,KAAK,WAAW,GAAG,GAAG;AAC3B,aAAO,KAAK,IAAI,GAAG,OAAO,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,IACvD,OACK;AACD,aAAO,OAAO,IAAI,IAAI,KAAK;AAAA,IAC/B;AAAA,EACJ;AAhBS;;;ACDT,WAAS,eAAe,UAAU,WAAW,SAAS;AAClD,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAM,WAAW,SAAS,CAAC;AAC3B,UAAI,SAAS,KAAK,aAAa,SAAS,KAAK,SAAS;AAClD,mBAAW,UAAU,QAAQ;AAE7B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AATS;AAUT,WAAS,aAAa,UAAUC,YAAW,QAAQ,QAAQ,WAAW,SAAS;AAM3E,mBAAe,UAAU,WAAW,OAAO;AAC3C,aAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,eAAS,KAAK;AAAA,QACV,OAAOA,WAAU,CAAC;AAAA,QAClB,IAAI,UAAU,WAAW,SAAS,OAAO,CAAC,CAAC;AAAA,QAC3C,QAAQ,oBAAoB,QAAQ,CAAC;AAAA,MACzC,CAAC;AAAA,IACL;AAAA,EACJ;AAdS;;;ACPT,WAAS,eAAe,OAAO,QAAQ;AACnC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,CAAC,IAAI,MAAM,CAAC,KAAK,SAAS;AAAA,IACpC;AAAA,EACJ;AAJS;;;ACNT,WAAS,cAAc,GAAG,GAAG;AACzB,QAAI,EAAE,OAAO,EAAE,IAAI;AACf,UAAI,EAAE,UAAU;AACZ,eAAO;AACX,UAAI,EAAE,UAAU;AACZ,eAAO;AACX,aAAO;AAAA,IACX,OACK;AACD,aAAO,EAAE,KAAK,EAAE;AAAA,IACpB;AAAA,EACJ;AAXS;;;ACST,MAAM,uBAAuB;AAC7B,MAAM,aAAa;AACnB,WAAS,6BAA6B,UAAU,EAAE,oBAAoB,CAAC,GAAG,GAAG,mBAAmB,IAAI,CAAC,GAAG,OAAO,YAAY;AACvH,UAAM,kBAAkB,kBAAkB,YAAY;AACtD,UAAM,uBAAuB,oBAAI,IAAI;AACrC,UAAM,YAAY,oBAAI,IAAI;AAC1B,UAAM,eAAe,CAAC;AACtB,UAAM,aAAa,oBAAI,IAAI;AAC3B,QAAI,WAAW;AACf,QAAI,cAAc;AAClB,QAAI,gBAAgB;AAMpB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAM,UAAU,SAAS,CAAC;AAI1B,UAAI,OAAO,YAAY,UAAU;AAC7B,mBAAW,IAAI,SAAS,WAAW;AACnC;AAAA,MACJ,WACS,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC9B,mBAAW,IAAI,QAAQ,MAAM,aAAa,aAAa,QAAQ,IAAI,UAAU,UAAU,CAAC;AACxF;AAAA,MACJ;AACA,UAAI,CAAC,SAASC,YAAW,aAAa,CAAC,CAAC,IAAI;AAK5C,UAAI,WAAW,OAAO,QAAW;AAC7B,sBAAc,aAAa,aAAa,WAAW,IAAI,UAAU,UAAU;AAAA,MAC/E;AAKA,UAAI,cAAc;AAClB,YAAM,uBAAuB,wBAAC,gBAAgB,iBAAiB,eAAe,eAAe,GAAG,cAAc,MAAM;AAChH,cAAM,uBAAuB,gBAAgB,cAAc;AAC3D,cAAM,EAAE,QAAQ,GAAG,QAAQ,cAAc,oBAAoB,GAAG,OAAO,aAAa,QAAQ,YAAY,cAAc,GAAG,GAAG,oBAAoB,IAAI;AACpJ,YAAI,EAAE,MAAAC,QAAO,kBAAkB,QAAQ,WAAW,SAAS,IAAI;AAI/D,cAAM,kBAAkB,OAAO,UAAU,aACnC,MAAM,cAAc,WAAW,IAC/B;AAIN,cAAM,eAAe,qBAAqB;AAC1C,cAAM,kBAAkB,YAAY,IAAI,IAClC,OACA,aAAa,QAAQ,WAAW;AACtC,YAAI,gBAAgB,KAAK,iBAAiB;AAOtC,cAAI,gBAAgB;AACpB,cAAI,iBAAiB,KACjB,uBAAuB,oBAAoB,GAAG;AAC9C,kBAAM,QAAQ,qBAAqB,CAAC,IAAI,qBAAqB,CAAC;AAC9D,4BAAgB,KAAK,IAAI,KAAK;AAAA,UAClC;AACA,gBAAM,mBAAmB,EAAE,GAAG,oBAAoB;AAClD,cAAI,aAAa,QAAW;AACxB,6BAAiB,WAAW,sBAAsB,QAAQ;AAAA,UAC9D;AACA,gBAAM,eAAe,sBAAsB,kBAAkB,eAAe,eAAe;AAC3F,UAAAA,QAAO,aAAa;AACpB,qBAAW,aAAa;AAAA,QAC5B;AACA,qBAAa,WAAW;AACxB,cAAM,YAAY,cAAc;AAIhC,YAAI,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,GAAG;AACtC,gBAAM,CAAC,IAAI;AAAA,QACf;AAIA,cAAM,YAAY,MAAM,SAAS,qBAAqB;AACtD,oBAAY,KAAK,WAAW,OAAO,SAAS;AAM5C,6BAAqB,WAAW,KAC5B,qBAAqB,QAAQ,IAAI;AAIrC,YAAI,QAAQ;AACR,oBAAU,SAAS,YAAY,+CAA+C,mBAAmB;AACjG,qBAAW,wBAAwB,UAAU,MAAM;AACnD,gBAAM,oBAAoB,CAAC,GAAG,oBAAoB;AAClD,gBAAM,gBAAgB,CAAC,GAAG,KAAK;AAC/B,UAAAA,QAAO,MAAM,QAAQA,KAAI,IAAI,CAAC,GAAGA,KAAI,IAAI,CAACA,KAAI;AAC9C,gBAAM,eAAe,CAAC,GAAGA,KAAI;AAC7B,mBAAS,cAAc,GAAG,cAAc,QAAQ,eAAe;AAC3D,iCAAqB,KAAK,GAAG,iBAAiB;AAC9C,qBAAS,gBAAgB,GAAG,gBAAgB,kBAAkB,QAAQ,iBAAiB;AACnF,oBAAM,KAAK,cAAc,aAAa,KAAK,cAAc,EAAE;AAC3D,cAAAA,MAAK,KAAK,kBAAkB,IACtB,WACA,oBAAoB,cAAc,gBAAgB,CAAC,CAAC;AAAA,YAC9D;AAAA,UACJ;AACA,yBAAe,OAAO,MAAM;AAAA,QAChC;AACA,cAAM,aAAa,YAAY;AAI/B,qBAAa,eAAe,sBAAsBA,OAAM,OAAO,WAAW,UAAU;AACpF,sBAAc,KAAK,IAAI,kBAAkB,UAAU,WAAW;AAC9D,wBAAgB,KAAK,IAAI,YAAY,aAAa;AAAA,MACtD,GAtF6B;AAuF7B,UAAI,cAAc,OAAO,GAAG;AACxB,cAAM,kBAAkB,mBAAmB,SAAS,SAAS;AAC7D,6BAAqBD,YAAW,YAAY,iBAAiB,WAAW,eAAe,CAAC;AAAA,MAC5F,OACK;AACD,cAAM,WAAW,gBAAgB,SAASA,YAAW,OAAO,YAAY;AACxE,cAAM,cAAc,SAAS;AAI7B,iBAAS,eAAe,GAAG,eAAe,aAAa,gBAAgB;AAInE,UAAAA,aAAYA;AACZ,uBAAa;AACb,gBAAM,cAAc,SAAS,YAAY;AACzC,gBAAM,kBAAkB,mBAAmB,aAAa,SAAS;AACjE,qBAAW,OAAOA,YAAW;AACzB,iCAAqBA,WAAU,GAAG,GAAGE,oBAAmB,YAAY,GAAG,GAAG,iBAAiB,KAAK,eAAe,GAAG,cAAc,WAAW;AAAA,UAC/I;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW;AACX,qBAAe;AAAA,IACnB;AAIA,cAAU,QAAQ,CAAC,gBAAgB,YAAY;AAC3C,iBAAW,OAAO,gBAAgB;AAC9B,cAAM,gBAAgB,eAAe,GAAG;AAIxC,sBAAc,KAAK,aAAa;AAChC,cAAMF,aAAY,CAAC;AACnB,cAAM,cAAc,CAAC;AACrB,cAAM,cAAc,CAAC;AAKrB,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,gBAAM,EAAE,IAAI,OAAO,OAAO,IAAI,cAAc,CAAC;AAC7C,UAAAA,WAAU,KAAK,KAAK;AACpB,sBAAY,KAAK,SAAS,GAAG,eAAe,EAAE,CAAC;AAC/C,sBAAY,KAAK,UAAU,SAAS;AAAA,QACxC;AAMA,YAAI,YAAY,CAAC,MAAM,GAAG;AACtB,sBAAY,QAAQ,CAAC;AACrB,UAAAA,WAAU,QAAQA,WAAU,CAAC,CAAC;AAC9B,sBAAY,QAAQ,oBAAoB;AAAA,QAC5C;AAMA,YAAI,YAAY,YAAY,SAAS,CAAC,MAAM,GAAG;AAC3C,sBAAY,KAAK,CAAC;AAClB,UAAAA,WAAU,KAAK,IAAI;AAAA,QACvB;AACA,YAAI,CAAC,qBAAqB,IAAI,OAAO,GAAG;AACpC,+BAAqB,IAAI,SAAS;AAAA,YAC9B,WAAW,CAAC;AAAA,YACZ,YAAY,CAAC;AAAA,UACjB,CAAC;AAAA,QACL;AACA,cAAM,aAAa,qBAAqB,IAAI,OAAO;AACnD,mBAAW,UAAU,GAAG,IAAIA;AAC5B,mBAAW,WAAW,GAAG,IAAI;AAAA,UACzB,GAAG;AAAA,UACH,UAAU;AAAA,UACV,MAAM;AAAA,UACN,OAAO;AAAA,UACP,GAAG;AAAA,QACP;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AArNS;AAsNT,WAAS,mBAAmB,SAAS,WAAW;AAC5C,KAAC,UAAU,IAAI,OAAO,KAAK,UAAU,IAAI,SAAS,CAAC,CAAC;AACpD,WAAO,UAAU,IAAI,OAAO;AAAA,EAChC;AAHS;AAIT,WAAS,iBAAiB,MAAM,WAAW;AACvC,QAAI,CAAC,UAAU,IAAI;AACf,gBAAU,IAAI,IAAI,CAAC;AACvB,WAAO,UAAU,IAAI;AAAA,EACzB;AAJS;AAKT,WAAS,gBAAgBA,YAAW;AAChC,WAAO,MAAM,QAAQA,UAAS,IAAIA,aAAY,CAACA,UAAS;AAAA,EAC5D;AAFS;AAGT,WAASE,oBAAmB,YAAY,KAAK;AACzC,WAAO,cAAc,WAAW,GAAG,IAC7B;AAAA,MACE,GAAG;AAAA,MACH,GAAG,WAAW,GAAG;AAAA,IACrB,IACE,EAAE,GAAG,WAAW;AAAA,EAC1B;AAPS,SAAAA,qBAAA;AAQT,MAAM,WAAW,wBAAC,aAAa,OAAO,aAAa,UAAlC;AACjB,MAAM,yBAAyB,wBAACF,eAAcA,WAAU,MAAM,QAAQ,GAAvC;;;ACtP/B,MAAM,qBAAqB,oBAAI,QAAQ;;;ACAvC,MAAM,oBAAoB,wBAAC,MAAM;AAC7B,WAAO,MAAM,QAAQ,CAAC;AAAA,EAC1B,GAF0B;;;ACA1B,WAAS,cAAc,eAAe;AAClC,UAAM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACrB,mBAAe,OAAO,QAAQ,CAAC,OAAO,QAAQ;AAC1C,YAAM,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI;AAC1B,YAAM,CAAC,EAAE,GAAG,IAAI,MAAM,YAAY;AAAA,IACtC,CAAC;AACD,WAAO;AAAA,EACX;AAPS;AAQT,WAAS,wBAAwB,OAAO,YAAY,QAAQ,eAAe;AAIvE,QAAI,OAAO,eAAe,YAAY;AAClC,YAAM,CAAC,SAAS,QAAQ,IAAI,cAAc,aAAa;AACvD,mBAAa,WAAW,WAAW,SAAY,SAAS,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC3F;AAKA,QAAI,OAAO,eAAe,UAAU;AAChC,mBAAa,MAAM,YAAY,MAAM,SAAS,UAAU;AAAA,IAC5D;AAMA,QAAI,OAAO,eAAe,YAAY;AAClC,YAAM,CAAC,SAAS,QAAQ,IAAI,cAAc,aAAa;AACvD,mBAAa,WAAW,WAAW,SAAY,SAAS,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC3F;AACA,WAAO;AAAA,EACX;AAzBS;;;ACNT,WAAS,eAAe,eAAe,YAAY,QAAQ;AACvD,UAAM,QAAQ,cAAc,SAAS;AACrC,WAAO,wBAAwB,OAAO,YAAY,WAAW,SAAY,SAAS,MAAM,QAAQ,aAAa;AAAA,EACjH;AAHS;;;ACMT,WAAS,eAAe,eAAe,KAAK,OAAO;AAC/C,QAAI,cAAc,SAAS,GAAG,GAAG;AAC7B,oBAAc,SAAS,GAAG,EAAE,IAAI,KAAK;AAAA,IACzC,OACK;AACD,oBAAc,SAAS,KAAK,YAAY,KAAK,CAAC;AAAA,IAClD;AAAA,EACJ;AAPS;AAQT,WAAS,6BAA6B,GAAG;AAErC,WAAO,kBAAkB,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,KAAK,IAAI;AAAA,EACzD;AAHS;AAIT,WAAS,UAAU,eAAe,YAAY;AAC1C,UAAM,WAAW,eAAe,eAAe,UAAU;AACzD,QAAI,EAAE,gBAAgB,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,OAAO,IAAI,YAAY,CAAC;AACtE,aAAS,EAAE,GAAG,QAAQ,GAAG,cAAc;AACvC,eAAW,OAAO,QAAQ;AACtB,YAAM,QAAQ,6BAA6B,OAAO,GAAG,CAAC;AACtD,qBAAe,eAAe,KAAK,KAAK;AAAA,IAC5C;AAAA,EACJ;AARS;;;AClBT,WAAS,wBAAwB,OAAO;AACpC,WAAO,QAAQ,cAAc,KAAK,KAAK,MAAM,GAAG;AAAA,EACpD;AAFS;;;ACCT,WAAS,qBAAqB,eAAe,KAAK;AAC9C,UAAM,aAAa,cAAc,SAAS,YAAY;AAKtD,QAAI,wBAAwB,UAAU,GAAG;AACrC,aAAO,WAAW,IAAI,GAAG;AAAA,IAC7B,WACS,CAAC,cAAc,mBAAmB,YAAY;AACnD,YAAM,gBAAgB,IAAI,mBAAmB,WAAW,MAAM;AAC9D,oBAAc,SAAS,cAAc,aAAa;AAClD,oBAAc,IAAI,GAAG;AAAA,IACzB;AAAA,EACJ;AAdS;;;ACAT,MAAM,cAAc,wBAAC,QAAQ,IAAI,QAAQ,oBAAoB,OAAO,EAAE,YAAY,GAA9D;;;ACDpB,MAAM,wBAAwB;AAC9B,MAAM,+BAA+B,UAAU,YAAY,qBAAqB;;;ACDhF,WAAS,qBAAqB,eAAe;AACzC,WAAO,cAAc,MAAM,4BAA4B;AAAA,EAC3D;AAFS;;;ACFT,MAAMG,aAAY,wBAAC,UAAU,UAAU,MAArB;AAClB,WAASC,kBAAiBC,YAAW,EAAE,QAAQ,aAAa,OAAO,GAAG,eAAe;AACjF,UAAM,oBAAoBA,WAAU,OAAOF,UAAS;AACpD,UAAM,QAAQ,UAAU,eAAe,UAAU,SAAS,MAAM,IAC1D,IACA,kBAAkB,SAAS;AACjC,WAAO,CAAC,SAAS,kBAAkB,SAC7B,kBAAkB,KAAK,IACvB;AAAA,EACV;AARS,SAAAC,mBAAA;;;ACCT,MAAM,oBAAoB;AAAA,IACtB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AACA,MAAM,yBAAyB,wBAAC,YAAY;AAAA,IACxC,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,WAAW,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AAAA,IAC7C,WAAW;AAAA,EACf,IAL+B;AAM/B,MAAM,sBAAsB;AAAA,IACxB,MAAM;AAAA,IACN,UAAU;AAAA,EACd;AAKA,MAAM,OAAO;AAAA,IACT,MAAM;AAAA,IACN,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AAAA,IACzB,UAAU;AAAA,EACd;AACA,MAAM,uBAAuB,wBAAC,UAAU,EAAE,WAAAE,WAAU,MAAM;AACtD,QAAIA,WAAU,SAAS,GAAG;AACtB,aAAO;AAAA,IACX,WACS,eAAe,IAAI,QAAQ,GAAG;AACnC,aAAO,SAAS,WAAW,OAAO,IAC5B,uBAAuBA,WAAU,CAAC,CAAC,IACnC;AAAA,IACV;AACA,WAAO;AAAA,EACX,GAV6B;;;ACtB7B,WAAS,oBAAoB,EAAE,MAAM,OAAO,QAAQ,eAAe,iBAAiB,kBAAkB,QAAQ,YAAY,aAAa,MAAM,SAAS,GAAG,WAAW,GAAG;AACnK,WAAO,CAAC,CAAC,OAAO,KAAK,UAAU,EAAE;AAAA,EACrC;AAFS;;;ACCT,MAAM,qBAAqB,wBAAC,MAAM,OAAO,QAAQ,aAAa,CAAC,GAAG,SAAS,cAAc,CAAC,eAAe;AACrG,UAAM,kBAAkB,mBAAmB,YAAY,IAAI,KAAK,CAAC;AAMjE,UAAM,QAAQ,gBAAgB,SAAS,WAAW,SAAS;AAK3D,QAAI,EAAE,UAAU,EAAE,IAAI;AACtB,cAAU,UAAU,sBAAsB,KAAK;AAC/C,UAAM,UAAU;AAAA,MACZ,WAAW,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,MAAM;AAAA,MACzD,MAAM;AAAA,MACN,UAAU,MAAM,YAAY;AAAA,MAC5B,GAAG;AAAA,MACH,OAAO,CAAC;AAAA,MACR,UAAU,wBAAC,MAAM;AACb,cAAM,IAAI,CAAC;AACX,wBAAgB,YAAY,gBAAgB,SAAS,CAAC;AAAA,MAC1D,GAHU;AAAA,MAIV,YAAY,6BAAM;AACd,mBAAW;AACX,wBAAgB,cAAc,gBAAgB,WAAW;AAAA,MAC7D,GAHY;AAAA,MAIZ;AAAA,MACA,aAAa;AAAA,MACb,SAAS,YAAY,SAAY;AAAA,IACrC;AAKA,QAAI,CAAC,oBAAoB,eAAe,GAAG;AACvC,aAAO,OAAO,SAAS,qBAAqB,MAAM,OAAO,CAAC;AAAA,IAC9D;AAMA,YAAQ,aAAa,QAAQ,WAAW,sBAAsB,QAAQ,QAAQ;AAC9E,YAAQ,gBAAgB,QAAQ,cAAc,sBAAsB,QAAQ,WAAW;AAIvF,QAAI,QAAQ,SAAS,QAAW;AAC5B,cAAQ,UAAU,CAAC,IAAI,QAAQ;AAAA,IACnC;AACA,QAAI,aAAa;AACjB,QAAI,QAAQ,SAAS,SAChB,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAc;AAClD,2BAAqB,OAAO;AAC5B,UAAI,QAAQ,UAAU,GAAG;AACrB,qBAAa;AAAA,MACjB;AAAA,IACJ;AACA,QAAI,mBAAmB,qBACnB,mBAAmB,gBAAgB;AACnC,mBAAa;AACb,2BAAqB,OAAO;AAC5B,cAAQ,QAAQ;AAAA,IACpB;AAKA,YAAQ,eAAe,CAAC,gBAAgB,QAAQ,CAAC,gBAAgB;AAMjE,QAAI,cAAc,CAAC,aAAa,MAAM,IAAI,MAAM,QAAW;AACvD,YAAM,gBAAgBC,kBAAiB,QAAQ,WAAW,eAAe;AACzE,UAAI,kBAAkB,QAAW;AAC7B,cAAM,OAAO,MAAM;AACf,kBAAQ,SAAS,aAAa;AAC9B,kBAAQ,WAAW;AAAA,QACvB,CAAC;AACD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,gBAAgB,SACjB,IAAI,YAAY,OAAO,IACvB,IAAI,0BAA0B,OAAO;AAAA,EAC/C,GAzF2B;;;ACM3B,WAAS,qBAAqB,EAAE,eAAe,eAAe,GAAG,KAAK;AAClE,UAAM,cAAc,cAAc,eAAe,GAAG,KAAK,eAAe,GAAG,MAAM;AACjF,mBAAe,GAAG,IAAI;AACtB,WAAO;AAAA,EACX;AAJS;AAKT,WAAS,cAAc,eAAe,qBAAqB,EAAE,QAAQ,GAAG,oBAAoB,KAAK,IAAI,CAAC,GAAG;AACrG,QAAI,EAAE,aAAa,cAAc,qBAAqB,GAAG,eAAe,GAAG,OAAO,IAAI;AACtF,QAAI;AACA,mBAAa;AACjB,UAAM,aAAa,CAAC;AACpB,UAAM,qBAAqB,QACvB,cAAc,kBACd,cAAc,eAAe,SAAS,EAAE,IAAI;AAChD,eAAW,OAAO,QAAQ;AACtB,YAAM,QAAQ,cAAc,SAAS,KAAK,cAAc,aAAa,GAAG,KAAK,IAAI;AACjF,YAAM,cAAc,OAAO,GAAG;AAC9B,UAAI,gBAAgB,UACf,sBACG,qBAAqB,oBAAoB,GAAG,GAAI;AACpD;AAAA,MACJ;AACA,YAAM,kBAAkB;AAAA,QACpB;AAAA,QACA,GAAG,mBAAmB,cAAc,CAAC,GAAG,GAAG;AAAA,MAC/C;AAIA,YAAM,eAAe,MAAM,IAAI;AAC/B,UAAI,iBAAiB,UACjB,CAAC,MAAM,eACP,CAAC,MAAM,QAAQ,WAAW,KAC1B,gBAAgB,gBAChB,CAAC,gBAAgB,UAAU;AAC3B;AAAA,MACJ;AAKA,UAAI,YAAY;AAChB,UAAI,OAAO,wBAAwB;AAC/B,cAAM,WAAW,qBAAqB,aAAa;AACnD,YAAI,UAAU;AACV,gBAAM,YAAY,OAAO,uBAAuB,UAAU,KAAK,KAAK;AACpE,cAAI,cAAc,MAAM;AACpB,4BAAgB,YAAY;AAC5B,wBAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AACA,2BAAqB,eAAe,GAAG;AACvC,YAAM,MAAM,mBAAmB,KAAK,OAAO,aAAa,cAAc,sBAAsB,eAAe,IAAI,GAAG,IAC5G,EAAE,MAAM,MAAM,IACd,iBAAiB,eAAe,SAAS,CAAC;AAChD,YAAM,YAAY,MAAM;AACxB,UAAI,WAAW;AACX,mBAAW,KAAK,SAAS;AAAA,MAC7B;AAAA,IACJ;AACA,QAAI,eAAe;AACf,cAAQ,IAAI,UAAU,EAAE,KAAK,MAAM;AAC/B,cAAM,OAAO,MAAM;AACf,2BAAiB,UAAU,eAAe,aAAa;AAAA,QAC3D,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AA/DS;;;ACZT,WAAS,wBAAwB,EAAE,KAAK,MAAM,OAAO,OAAQ,GAAG;AAC5D,WAAO;AAAA,MACH,GAAG,EAAE,KAAK,MAAM,KAAK,MAAM;AAAA,MAC3B,GAAG,EAAE,KAAK,KAAK,KAAK,OAAO;AAAA,IAC/B;AAAA,EACJ;AALS;AAMT,WAAS,wBAAwB,EAAE,GAAG,EAAE,GAAG;AACvC,WAAO,EAAE,KAAK,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,KAAK,MAAM,EAAE,IAAI;AAAA,EAClE;AAFS;AAQT,WAAS,mBAAmB,OAAO,gBAAgB;AAC/C,QAAI,CAAC;AACD,aAAO;AACX,UAAM,UAAU,eAAe,EAAE,GAAG,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC;AAC9D,UAAM,cAAc,eAAe,EAAE,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC;AACtE,WAAO;AAAA,MACH,KAAK,QAAQ;AAAA,MACb,MAAM,QAAQ;AAAA,MACd,QAAQ,YAAY;AAAA,MACpB,OAAO,YAAY;AAAA,IACvB;AAAA,EACJ;AAXS;;;ACnBT,WAAS,gBAAgBC,QAAO;AAC5B,WAAOA,WAAU,UAAaA,WAAU;AAAA,EAC5C;AAFS;AAGT,WAAS,SAAS,EAAE,OAAAA,QAAO,QAAAC,SAAQ,QAAAC,QAAO,GAAG;AACzC,WAAQ,CAAC,gBAAgBF,MAAK,KAC1B,CAAC,gBAAgBC,OAAM,KACvB,CAAC,gBAAgBC,OAAM;AAAA,EAC/B;AAJS;AAKT,WAAS,aAAa,QAAQ;AAC1B,WAAQ,SAAS,MAAM,KACnB,eAAe,MAAM,KACrB,OAAO,KACP,OAAO,UACP,OAAO,WACP,OAAO,WACP,OAAO,SACP,OAAO;AAAA,EACf;AATS;AAUT,WAAS,eAAe,QAAQ;AAC5B,WAAO,cAAc,OAAO,CAAC,KAAK,cAAc,OAAO,CAAC;AAAA,EAC5D;AAFS;AAGT,WAAS,cAAc,OAAO;AAC1B,WAAO,SAAS,UAAU;AAAA,EAC9B;AAFS;;;ACfT,WAAS,WAAW,OAAOC,QAAO,aAAa;AAC3C,UAAM,qBAAqB,QAAQ;AACnC,UAAM,SAASA,SAAQ;AACvB,WAAO,cAAc;AAAA,EACzB;AAJS;AAQT,WAAS,gBAAgB,OAAO,WAAWA,QAAO,aAAa,UAAU;AACrE,QAAI,aAAa,QAAW;AACxB,cAAQ,WAAW,OAAO,UAAU,WAAW;AAAA,IACnD;AACA,WAAO,WAAW,OAAOA,QAAO,WAAW,IAAI;AAAA,EACnD;AALS;AAST,WAAS,eAAe,MAAM,YAAY,GAAGA,SAAQ,GAAG,aAAa,UAAU;AAC3E,SAAK,MAAM,gBAAgB,KAAK,KAAK,WAAWA,QAAO,aAAa,QAAQ;AAC5E,SAAK,MAAM,gBAAgB,KAAK,KAAK,WAAWA,QAAO,aAAa,QAAQ;AAAA,EAChF;AAHS;AAOT,WAAS,cAAc,KAAK,EAAE,GAAG,EAAE,GAAG;AAClC,mBAAe,IAAI,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW;AACzD,mBAAe,IAAI,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW;AAAA,EAC7D;AAHS;AAIT,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAO5B,WAAS,gBAAgB,KAAK,WAAW,UAAU,qBAAqB,OAAO;AAC3E,UAAM,aAAa,SAAS;AAC5B,QAAI,CAAC;AACD;AAEJ,cAAU,IAAI,UAAU,IAAI;AAC5B,QAAI;AACJ,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,aAAO,SAAS,CAAC;AACjB,cAAQ,KAAK;AAKb,YAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAI,iBACA,cAAc,MAAM,SACpB,cAAc,MAAM,MAAM,YAAY,YAAY;AAClD;AAAA,MACJ;AACA,UAAI,sBACA,KAAK,QAAQ,gBACb,KAAK,UACL,SAAS,KAAK,MAAM;AACpB,qBAAa,KAAK;AAAA,UACd,GAAG,CAAC,KAAK,OAAO,OAAO;AAAA,UACvB,GAAG,CAAC,KAAK,OAAO,OAAO;AAAA,QAC3B,CAAC;AAAA,MACL;AACA,UAAI,OAAO;AAEP,kBAAU,KAAK,MAAM,EAAE;AACvB,kBAAU,KAAK,MAAM,EAAE;AAEvB,sBAAc,KAAK,KAAK;AAAA,MAC5B;AACA,UAAI,sBAAsB,aAAa,KAAK,YAAY,GAAG;AACvD,qBAAa,KAAK,KAAK,YAAY;AAAA,MACvC;AAAA,IACJ;AAKA,QAAI,UAAU,IAAI,uBACd,UAAU,IAAI,qBAAqB;AACnC,gBAAU,IAAI;AAAA,IAClB;AACA,QAAI,UAAU,IAAI,uBACd,UAAU,IAAI,qBAAqB;AACnC,gBAAU,IAAI;AAAA,IAClB;AAAA,EACJ;AArDS;AAsDT,WAAS,cAAc,MAAMC,WAAU;AACnC,SAAK,MAAM,KAAK,MAAMA;AACtB,SAAK,MAAM,KAAK,MAAMA;AAAA,EAC1B;AAHS;AAST,WAAS,cAAc,MAAM,eAAe,WAAW,UAAU,aAAa,KAAK;AAC/E,UAAM,cAAc,UAAU,KAAK,KAAK,KAAK,KAAK,UAAU;AAE5D,mBAAe,MAAM,eAAe,WAAW,aAAa,QAAQ;AAAA,EACxE;AAJS;AAQT,WAAS,aAAa,KAAKC,YAAW;AAClC,kBAAc,IAAI,GAAGA,WAAU,GAAGA,WAAU,QAAQA,WAAU,OAAOA,WAAU,OAAO;AACtF,kBAAc,IAAI,GAAGA,WAAU,GAAGA,WAAU,QAAQA,WAAU,OAAOA,WAAU,OAAO;AAAA,EAC1F;AAHS;;;AC9GT,WAAS,mBAAmB,UAAU,gBAAgB;AAClD,WAAO,wBAAwB,mBAAmB,SAAS,sBAAsB,GAAG,cAAc,CAAC;AAAA,EACvG;AAFS;AAGT,WAAS,eAAe,SAAS,oBAAoB,oBAAoB;AACrE,UAAM,cAAc,mBAAmB,SAAS,kBAAkB;AAClE,UAAM,EAAE,QAAAC,QAAO,IAAI;AACnB,QAAIA,SAAQ;AACR,oBAAc,YAAY,GAAGA,QAAO,OAAO,CAAC;AAC5C,oBAAc,YAAY,GAAGA,QAAO,OAAO,CAAC;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AARS;;;ACNT,MAAM,eAAe;AAAA,IACjB,WAAW;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,MAAM,CAAC,MAAM;AAAA,IACb,MAAM,CAAC,QAAQ,cAAc;AAAA,IAC7B,OAAO,CAAC,YAAY;AAAA,IACpB,OAAO,CAAC,cAAc,gBAAgB,YAAY;AAAA,IAClD,KAAK,CAAC,YAAY,SAAS,cAAc,aAAa;AAAA,IACtD,KAAK,CAAC,SAAS,cAAc,qBAAqB,UAAU;AAAA,IAC5D,QAAQ,CAAC,eAAe,mBAAmB,iBAAiB;AAAA,IAC5D,QAAQ,CAAC,UAAU,UAAU;AAAA,EACjC;AACA,MAAM,qBAAqB,CAAC;AAC5B,aAAW,OAAO,cAAc;AAC5B,uBAAmB,GAAG,IAAI;AAAA,MACtB,WAAW,wBAAC,UAAU,aAAa,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,GAAzD;AAAA,IACf;AAAA,EACJ;;;ACzBA,MAAM,kBAAkB,8BAAO;AAAA,IAC3B,WAAW;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,EACjB,IALwB;AAMxB,MAAM,cAAc,8BAAO;AAAA,IACvB,GAAG,gBAAgB;AAAA,IACnB,GAAG,gBAAgB;AAAA,EACvB,IAHoB;AAIpB,MAAM,aAAa,8BAAO,EAAE,KAAK,GAAG,KAAK,EAAE,IAAxB;AACnB,MAAM,YAAY,8BAAO;AAAA,IACrB,GAAG,WAAW;AAAA,IACd,GAAG,WAAW;AAAA,EAClB,IAHkB;;;ACXlB,MAAM,YAAY,OAAO,WAAW;;;ACCpC,MAAM,uBAAuB,EAAE,SAAS,KAAK;AAC7C,MAAM,2BAA2B,EAAE,SAAS,MAAM;;;ACClD,WAAS,2BAA2B;AAChC,6BAAyB,UAAU;AACnC,QAAI,CAAC;AACD;AACJ,QAAI,OAAO,YAAY;AACnB,YAAM,mBAAmB,OAAO,WAAW,0BAA0B;AACrE,YAAM,8BAA8B,6BAAO,qBAAqB,UAAU,iBAAiB,SAAvD;AACpC,uBAAiB,iBAAiB,UAAU,2BAA2B;AACvE,kCAA4B;AAAA,IAChC,OACK;AACD,2BAAqB,UAAU;AAAA,IACnC;AAAA,EACJ;AAbS;;;ACHT,WAAS,oBAAoB,GAAG;AAC5B,WAAQ,MAAM,QACV,OAAO,MAAM,YACb,OAAO,EAAE,UAAU;AAAA,EAC3B;AAJS;;;ACGT,WAAS,eAAe,GAAG;AACvB,WAAO,OAAO,MAAM,YAAY,MAAM,QAAQ,CAAC;AAAA,EACnD;AAFS;;;ACHT,MAAM,uBAAuB;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAM,eAAe,CAAC,WAAW,GAAG,oBAAoB;;;ACLxD,WAAS,sBAAsB,OAAO;AAClC,WAAQ,oBAAoB,MAAM,OAAO,KACrC,aAAa,KAAK,CAAC,SAAS,eAAe,MAAM,IAAI,CAAC,CAAC;AAAA,EAC/D;AAHS;AAIT,WAAS,cAAc,OAAO;AAC1B,WAAO,QAAQ,sBAAsB,KAAK,KAAK,MAAM,QAAQ;AAAA,EACjE;AAFS;;;ACNT,WAAS,4BAA4B,SAAS,MAAM,MAAM;AACtD,eAAW,OAAO,MAAM;AACpB,YAAM,YAAY,KAAK,GAAG;AAC1B,YAAM,YAAY,KAAK,GAAG;AAC1B,UAAI,cAAc,SAAS,GAAG;AAK1B,gBAAQ,SAAS,KAAK,SAAS;AAAA,MACnC,WACS,cAAc,SAAS,GAAG;AAK/B,gBAAQ,SAAS,KAAK,YAAY,WAAW,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpE,WACS,cAAc,WAAW;AAM9B,YAAI,QAAQ,SAAS,GAAG,GAAG;AACvB,gBAAM,gBAAgB,QAAQ,SAAS,GAAG;AAC1C,cAAI,cAAc,cAAc,MAAM;AAClC,0BAAc,KAAK,SAAS;AAAA,UAChC,WACS,CAAC,cAAc,aAAa;AACjC,0BAAc,IAAI,SAAS;AAAA,UAC/B;AAAA,QACJ,OACK;AACD,gBAAM,cAAc,QAAQ,eAAe,GAAG;AAC9C,kBAAQ,SAAS,KAAK,YAAY,gBAAgB,SAAY,cAAc,WAAW,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,QAC9G;AAAA,MACJ;AAAA,IACJ;AAEA,eAAW,OAAO,MAAM;AACpB,UAAI,KAAK,GAAG,MAAM;AACd,gBAAQ,YAAY,GAAG;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AA7CS;;;ACST,MAAM,oBAAoB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAKA,MAAM,gBAAN,MAAoB;AAAA,IAxBpB,OAwBoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQhB,4BAA4B,QAAQ,YAAY,gBAAgB;AAC5D,aAAO,CAAC;AAAA,IACZ;AAAA,IACA,YAAY,EAAE,QAAQ,OAAO,iBAAiB,qBAAqB,uBAAuB,YAAa,GAAG,UAAU,CAAC,GAAG;AAKpH,WAAK,UAAU;AAIf,WAAK,WAAW,oBAAI,IAAI;AAIxB,WAAK,gBAAgB;AACrB,WAAK,wBAAwB;AAQ7B,WAAK,qBAAqB;AAM1B,WAAK,SAAS,oBAAI,IAAI;AACtB,WAAK,mBAAmB;AAIxB,WAAK,WAAW,CAAC;AAKjB,WAAK,qBAAqB,oBAAI,IAAI;AAMlC,WAAK,mBAAmB,CAAC;AAIzB,WAAK,SAAS,CAAC;AAMf,WAAK,yBAAyB,CAAC;AAC/B,WAAK,eAAe,MAAM,KAAK,OAAO,UAAU,KAAK,YAAY;AACjE,WAAK,SAAS,MAAM;AAChB,YAAI,CAAC,KAAK;AACN;AACJ,aAAK,aAAa;AAClB,aAAK,eAAe,KAAK,SAAS,KAAK,aAAa,KAAK,MAAM,OAAO,KAAK,UAAU;AAAA,MACzF;AACA,WAAK,oBAAoB;AACzB,WAAK,iBAAiB,MAAM;AACxB,cAAMC,OAAM,KAAK,IAAI;AACrB,YAAI,KAAK,oBAAoBA,MAAK;AAC9B,eAAK,oBAAoBA;AACzB,gBAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AAAA,QACzC;AAAA,MACJ;AACA,YAAM,EAAE,cAAc,YAAY,IAAI;AACtC,WAAK,eAAe;AACpB,WAAK,aAAa,EAAE,GAAG,aAAa;AACpC,WAAK,gBAAgB,MAAM,UAAU,EAAE,GAAG,aAAa,IAAI,CAAC;AAC5D,WAAK,cAAc;AACnB,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,WAAK,kBAAkB;AACvB,WAAK,QAAQ,SAAS,OAAO,QAAQ,IAAI;AACzC,WAAK,sBAAsB;AAC3B,WAAK,UAAU;AACf,WAAK,wBAAwB,QAAQ,qBAAqB;AAC1D,WAAK,wBAAwB,sBAAsB,KAAK;AACxD,WAAK,gBAAgB,cAAc,KAAK;AACxC,UAAI,KAAK,eAAe;AACpB,aAAK,kBAAkB,oBAAI,IAAI;AAAA,MACnC;AACA,WAAK,yBAAyB,QAAQ,UAAU,OAAO,OAAO;AAW9D,YAAM,EAAE,YAAY,GAAG,oBAAoB,IAAI,KAAK,4BAA4B,OAAO,CAAC,GAAG,IAAI;AAC/F,iBAAW,OAAO,qBAAqB;AACnC,cAAM,QAAQ,oBAAoB,GAAG;AACrC,YAAI,aAAa,GAAG,MAAM,UAAa,cAAc,KAAK,GAAG;AACzD,gBAAM,IAAI,aAAa,GAAG,CAAC;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,MAAM,UAAU;AACZ,WAAK,UAAU;AACf,yBAAmB,IAAI,UAAU,IAAI;AACrC,UAAI,KAAK,cAAc,CAAC,KAAK,WAAW,UAAU;AAC9C,aAAK,WAAW,MAAM,QAAQ;AAAA,MAClC;AACA,UAAI,KAAK,UAAU,KAAK,iBAAiB,CAAC,KAAK,uBAAuB;AAClE,aAAK,wBAAwB,KAAK,OAAO,gBAAgB,IAAI;AAAA,MACjE;AACA,WAAK,OAAO,QAAQ,CAAC,OAAO,QAAQ,KAAK,kBAAkB,KAAK,KAAK,CAAC;AACtE,UAAI,CAAC,yBAAyB,SAAS;AACnC,iCAAyB;AAAA,MAC7B;AACA,WAAK,qBACD,KAAK,wBAAwB,UACvB,QACA,KAAK,wBAAwB,WACzB,OACA,qBAAqB;AACnC,UAAI,MAAuC;AACvC,iBAAS,KAAK,uBAAuB,MAAM,0FAA0F,yBAAyB;AAAA,MAClK;AACA,WAAK,QAAQ,SAAS,IAAI;AAC1B,WAAK,OAAO,KAAK,OAAO,KAAK,eAAe;AAAA,IAChD;AAAA,IACA,UAAU;AACN,WAAK,cAAc,KAAK,WAAW,QAAQ;AAC3C,kBAAY,KAAK,YAAY;AAC7B,kBAAY,KAAK,MAAM;AACvB,WAAK,mBAAmB,QAAQ,CAAC,WAAW,OAAO,CAAC;AACpD,WAAK,mBAAmB,MAAM;AAC9B,WAAK,yBAAyB,KAAK,sBAAsB;AACzD,WAAK,QAAQ,YAAY,IAAI;AAC7B,iBAAW,OAAO,KAAK,QAAQ;AAC3B,aAAK,OAAO,GAAG,EAAE,MAAM;AAAA,MAC3B;AACA,iBAAW,OAAO,KAAK,UAAU;AAC7B,cAAM,UAAU,KAAK,SAAS,GAAG;AACjC,YAAI,SAAS;AACT,kBAAQ,QAAQ;AAChB,kBAAQ,YAAY;AAAA,QACxB;AAAA,MACJ;AACA,WAAK,UAAU;AAAA,IACnB;AAAA,IACA,SAAS,OAAO;AACZ,WAAK,SAAS,IAAI,KAAK;AACvB,WAAK,qBAAqB,KAAK,mBAAmB,oBAAI,IAAI;AAC1D,WAAK,iBAAiB,IAAI,KAAK;AAAA,IACnC;AAAA,IACA,YAAY,OAAO;AACf,WAAK,SAAS,OAAO,KAAK;AAC1B,WAAK,oBAAoB,KAAK,iBAAiB,OAAO,KAAK;AAAA,IAC/D;AAAA,IACA,kBAAkB,KAAK,OAAO;AAC1B,UAAI,KAAK,mBAAmB,IAAI,GAAG,GAAG;AAClC,aAAK,mBAAmB,IAAI,GAAG,EAAE;AAAA,MACrC;AACA,YAAM,mBAAmB,eAAe,IAAI,GAAG;AAC/C,UAAI,oBAAoB,KAAK,iBAAiB;AAC1C,aAAK,gBAAgB;AAAA,MACzB;AACA,YAAM,iBAAiB,MAAM,GAAG,UAAU,CAAC,gBAAgB;AACvD,aAAK,aAAa,GAAG,IAAI;AACzB,aAAK,MAAM,YAAY,MAAM,UAAU,KAAK,YAAY;AACxD,YAAI,oBAAoB,KAAK,YAAY;AACrC,eAAK,WAAW,mBAAmB;AAAA,QACvC;AACA,aAAK,eAAe;AAAA,MACxB,CAAC;AACD,UAAI;AACJ,UAAI,OAAO,uBAAuB;AAC9B,0BAAkB,OAAO,sBAAsB,MAAM,KAAK,KAAK;AAAA,MACnE;AACA,WAAK,mBAAmB,IAAI,KAAK,MAAM;AACnC,uBAAe;AACf,YAAI;AACA,0BAAgB;AACpB,YAAI,MAAM;AACN,gBAAM,KAAK;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,IACA,iBAAiB,OAAO;AAIpB,UAAI,CAAC,KAAK,WACN,CAAC,KAAK,4BACN,KAAK,SAAS,MAAM,MAAM;AAC1B,eAAO;AAAA,MACX;AACA,aAAO,KAAK,yBAAyB,KAAK,SAAS,MAAM,OAAO;AAAA,IACpE;AAAA,IACA,iBAAiB;AACb,UAAI,MAAM;AACV,WAAK,OAAO,oBAAoB;AAC5B,cAAM,oBAAoB,mBAAmB,GAAG;AAChD,YAAI,CAAC;AACD;AACJ,cAAM,EAAE,WAAW,SAAS,mBAAmB,IAAI;AAInD,YAAI,CAAC,KAAK,SAAS,GAAG,KAClB,sBACA,UAAU,KAAK,KAAK,GAAG;AACvB,eAAK,SAAS,GAAG,IAAI,IAAI,mBAAmB,IAAI;AAAA,QACpD;AAIA,YAAI,KAAK,SAAS,GAAG,GAAG;AACpB,gBAAM,UAAU,KAAK,SAAS,GAAG;AACjC,cAAI,QAAQ,WAAW;AACnB,oBAAQ,OAAO;AAAA,UACnB,OACK;AACD,oBAAQ,MAAM;AACd,oBAAQ,YAAY;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,eAAe;AACX,WAAK,MAAM,KAAK,aAAa,KAAK,cAAc,KAAK,KAAK;AAAA,IAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,qBAAqB;AACjB,aAAO,KAAK,UACN,KAAK,2BAA2B,KAAK,SAAS,KAAK,KAAK,IACxD,UAAU;AAAA,IACpB;AAAA,IACA,eAAe,KAAK;AAChB,aAAO,KAAK,aAAa,GAAG;AAAA,IAChC;AAAA,IACA,eAAe,KAAK,OAAO;AACvB,WAAK,aAAa,GAAG,IAAI;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO,OAAO,iBAAiB;AAC3B,UAAI,MAAM,qBAAqB,KAAK,MAAM,mBAAmB;AACzD,aAAK,eAAe;AAAA,MACxB;AACA,WAAK,YAAY,KAAK;AACtB,WAAK,QAAQ;AACb,WAAK,sBAAsB,KAAK;AAChC,WAAK,kBAAkB;AAIvB,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAC/C,cAAM,MAAM,kBAAkB,CAAC;AAC/B,YAAI,KAAK,uBAAuB,GAAG,GAAG;AAClC,eAAK,uBAAuB,GAAG,EAAE;AACjC,iBAAO,KAAK,uBAAuB,GAAG;AAAA,QAC1C;AACA,cAAM,eAAgB,OAAO;AAC7B,cAAM,WAAW,MAAM,YAAY;AACnC,YAAI,UAAU;AACV,eAAK,uBAAuB,GAAG,IAAI,KAAK,GAAG,KAAK,QAAQ;AAAA,QAC5D;AAAA,MACJ;AACA,WAAK,mBAAmB,4BAA4B,MAAM,KAAK,4BAA4B,OAAO,KAAK,WAAW,IAAI,GAAG,KAAK,gBAAgB;AAC9I,UAAI,KAAK,wBAAwB;AAC7B,aAAK,uBAAuB;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,WAAW;AACP,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA,IAIA,WAAW,MAAM;AACb,aAAO,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI,IAAI;AAAA,IAC7D;AAAA;AAAA;AAAA;AAAA,IAIA,uBAAuB;AACnB,aAAO,KAAK,MAAM;AAAA,IACtB;AAAA,IACA,wBAAwB;AACpB,aAAO,KAAK,MAAM;AAAA,IACtB;AAAA,IACA,wBAAwB;AACpB,aAAO,KAAK,gBACN,OACA,KAAK,SACD,KAAK,OAAO,sBAAsB,IAClC;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAIA,gBAAgB,OAAO;AACnB,YAAM,qBAAqB,KAAK,sBAAsB;AACtD,UAAI,oBAAoB;AACpB,2BAAmB,mBACf,mBAAmB,gBAAgB,IAAI,KAAK;AAChD,eAAO,MAAM,mBAAmB,gBAAgB,OAAO,KAAK;AAAA,MAChE;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS,KAAK,OAAO;AAEjB,YAAM,gBAAgB,KAAK,OAAO,IAAI,GAAG;AACzC,UAAI,UAAU,eAAe;AACzB,YAAI;AACA,eAAK,YAAY,GAAG;AACxB,aAAK,kBAAkB,KAAK,KAAK;AACjC,aAAK,OAAO,IAAI,KAAK,KAAK;AAC1B,aAAK,aAAa,GAAG,IAAI,MAAM,IAAI;AAAA,MACvC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA,IAIA,YAAY,KAAK;AACb,WAAK,OAAO,OAAO,GAAG;AACtB,YAAM,cAAc,KAAK,mBAAmB,IAAI,GAAG;AACnD,UAAI,aAAa;AACb,oBAAY;AACZ,aAAK,mBAAmB,OAAO,GAAG;AAAA,MACtC;AACA,aAAO,KAAK,aAAa,GAAG;AAC5B,WAAK,2BAA2B,KAAK,KAAK,WAAW;AAAA,IACzD;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS,KAAK;AACV,aAAO,KAAK,OAAO,IAAI,GAAG;AAAA,IAC9B;AAAA,IACA,SAAS,KAAK,cAAc;AACxB,UAAI,KAAK,MAAM,UAAU,KAAK,MAAM,OAAO,GAAG,GAAG;AAC7C,eAAO,KAAK,MAAM,OAAO,GAAG;AAAA,MAChC;AACA,UAAI,QAAQ,KAAK,OAAO,IAAI,GAAG;AAC/B,UAAI,UAAU,UAAa,iBAAiB,QAAW;AACnD,gBAAQ,YAAY,iBAAiB,OAAO,SAAY,cAAc,EAAE,OAAO,KAAK,CAAC;AACrF,aAAK,SAAS,KAAK,KAAK;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,UAAU,KAAK,QAAQ;AACnB,UAAI,QAAQ,KAAK,aAAa,GAAG,MAAM,UAAa,CAAC,KAAK,UACpD,KAAK,aAAa,GAAG,IACrB,KAAK,uBAAuB,KAAK,OAAO,GAAG,KACzC,KAAK,sBAAsB,KAAK,SAAS,KAAK,KAAK,OAAO;AAClE,UAAI,UAAU,UAAa,UAAU,MAAM;AACvC,YAAI,OAAO,UAAU,aAChB,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,IAAI;AAExD,kBAAQ,WAAW,KAAK;AAAA,QAC5B,WACS,CAAC,cAAc,KAAK,KAAK,QAAQ,KAAK,MAAM,GAAG;AACpD,kBAAQC,mBAAkB,KAAK,MAAM;AAAA,QACzC;AACA,aAAK,cAAc,KAAK,cAAc,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,MACtE;AACA,aAAO,cAAc,KAAK,IAAI,MAAM,IAAI,IAAI;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc,KAAK,OAAO;AACtB,WAAK,WAAW,GAAG,IAAI;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc,KAAK;AACf,YAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,UAAI;AACJ,UAAI,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU;AAC5D,cAAM,UAAU,wBAAwB,KAAK,OAAO,SAAS,KAAK,iBAAiB,MAAM;AACzF,YAAI,SAAS;AACT,6BAAmB,QAAQ,GAAG;AAAA,QAClC;AAAA,MACJ;AAIA,UAAI,WAAW,qBAAqB,QAAW;AAC3C,eAAO;AAAA,MACX;AAKA,YAAM,SAAS,KAAK,uBAAuB,KAAK,OAAO,GAAG;AAC1D,UAAI,WAAW,UAAa,CAAC,cAAc,MAAM;AAC7C,eAAO;AAKX,aAAO,KAAK,cAAc,GAAG,MAAM,UAC/B,qBAAqB,SACnB,SACA,KAAK,WAAW,GAAG;AAAA,IAC7B;AAAA,IACA,GAAG,WAAW,UAAU;AACpB,UAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AACzB,aAAK,OAAO,SAAS,IAAI,IAAI,oBAAoB;AAAA,MACrD;AACA,aAAO,KAAK,OAAO,SAAS,EAAE,IAAI,QAAQ;AAAA,IAC9C;AAAA,IACA,OAAO,cAAc,MAAM;AACvB,UAAI,KAAK,OAAO,SAAS,GAAG;AACxB,aAAK,OAAO,SAAS,EAAE,OAAO,GAAG,IAAI;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,0BAA0B;AACtB,gBAAU,OAAO,KAAK,MAAM;AAAA,IAChC;AAAA,EACJ;;;ACvdA,MAAM,mBAAN,cAA+B,cAAc;AAAA,IAH7C,OAG6C;AAAA;AAAA;AAAA,IACzC,cAAc;AACV,YAAM,GAAG,SAAS;AAClB,WAAK,mBAAmB;AAAA,IAC5B;AAAA,IACA,yBAAyB,GAAG,GAAG;AAM3B,aAAO,EAAE,wBAAwB,CAAC,IAAI,IAAI,IAAI;AAAA,IAClD;AAAA,IACA,uBAAuB,OAAO,KAAK;AAC/B,aAAO,MAAM,QACP,MAAM,MAAM,GAAG,IACf;AAAA,IACV;AAAA,IACA,2BAA2B,KAAK,EAAE,MAAM,MAAM,GAAG;AAC7C,aAAO,KAAK,GAAG;AACf,aAAO,MAAM,GAAG;AAAA,IACpB;AAAA,IACA,yBAAyB;AACrB,UAAI,KAAK,mBAAmB;AACxB,aAAK,kBAAkB;AACvB,eAAO,KAAK;AAAA,MAChB;AACA,YAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,UAAI,cAAc,QAAQ,GAAG;AACzB,aAAK,oBAAoB,SAAS,GAAG,UAAU,CAAC,WAAW;AACvD,cAAI,KAAK,SAAS;AACd,iBAAK,QAAQ,cAAc,GAAG,MAAM;AAAA,UACxC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;;;ACrCA,MAAM,iBAAiB;AAAA,IACnB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,sBAAsB;AAAA,EAC1B;AACA,MAAM,gBAAgB,mBAAmB;AAOzC,WAAS,eAAe,cAAcC,YAAW,mBAAmB;AAEhE,QAAI,kBAAkB;AACtB,QAAI,qBAAqB;AAKzB,aAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACpC,YAAM,MAAM,mBAAmB,CAAC;AAChC,YAAM,QAAQ,aAAa,GAAG;AAC9B,UAAI,UAAU;AACV;AACJ,UAAI,iBAAiB;AACrB,UAAI,OAAO,UAAU,UAAU;AAC3B,yBAAiB,WAAW,IAAI,WAAW,OAAO,IAAI,IAAI;AAAA,MAC9D,OACK;AACD,yBAAiB,WAAW,KAAK,MAAM;AAAA,MAC3C;AACA,UAAI,CAAC,kBAAkB,mBAAmB;AACtC,cAAM,cAAc,eAAe,OAAO,iBAAiB,GAAG,CAAC;AAC/D,YAAI,CAAC,gBAAgB;AACjB,+BAAqB;AACrB,gBAAM,gBAAgB,eAAe,GAAG,KAAK;AAC7C,6BAAmB,GAAG,aAAa,IAAI,WAAW;AAAA,QACtD;AACA,YAAI,mBAAmB;AACnB,UAAAA,WAAU,GAAG,IAAI;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,sBAAkB,gBAAgB,KAAK;AAGvC,QAAI,mBAAmB;AACnB,wBAAkB,kBAAkBA,YAAW,qBAAqB,KAAK,eAAe;AAAA,IAC5F,WACS,oBAAoB;AACzB,wBAAkB;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AA1CS;;;ACZT,WAAS,gBAAgB,OAAO,cAAc,mBAAmB;AAC7D,UAAM,EAAE,OAAO,MAAM,gBAAgB,IAAI;AAEzC,QAAIC,gBAAe;AACnB,QAAI,qBAAqB;AAOzB,eAAW,OAAO,cAAc;AAC5B,YAAM,QAAQ,aAAa,GAAG;AAC9B,UAAI,eAAe,IAAI,GAAG,GAAG;AAEzB,QAAAA,gBAAe;AACf;AAAA,MACJ,WACS,kBAAkB,GAAG,GAAG;AAC7B,aAAK,GAAG,IAAI;AACZ;AAAA,MACJ,OACK;AAED,cAAM,cAAc,eAAe,OAAO,iBAAiB,GAAG,CAAC;AAC/D,YAAI,IAAI,WAAW,QAAQ,GAAG;AAE1B,+BAAqB;AACrB,0BAAgB,GAAG,IACf;AAAA,QACR,OACK;AACD,gBAAM,GAAG,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,aAAa,WAAW;AACzB,UAAIA,iBAAgB,mBAAmB;AACnC,cAAM,YAAY,eAAe,cAAc,MAAM,WAAW,iBAAiB;AAAA,MACrF,WACS,MAAM,WAAW;AAKtB,cAAM,YAAY;AAAA,MACtB;AAAA,IACJ;AAKA,QAAI,oBAAoB;AACpB,YAAM,EAAE,UAAU,OAAO,UAAU,OAAO,UAAU,EAAG,IAAI;AAC3D,YAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,IAC5D;AAAA,EACJ;AAxDS;;;ACHT,WAAS,WAAW,SAAS,EAAE,OAAO,KAAK,GAAG,WAAW,YAAY;AACjE,UAAM,eAAe,QAAQ;AAC7B,QAAI;AACJ,SAAK,OAAO,OAAO;AAEf,mBAAa,GAAG,IAAI,MAAM,GAAG;AAAA,IACjC;AAEA,gBAAY,sBAAsB,cAAc,SAAS;AACzD,SAAK,OAAO,MAAM;AAGd,mBAAa,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,IAC3C;AAAA,EACJ;AAdS;;;ACET,MAAM,kBAAkB,CAAC;AACzB,WAAS,kBAAkB,YAAY;AACnC,eAAW,OAAO,YAAY;AAC1B,sBAAgB,GAAG,IAAI,WAAW,GAAG;AACrC,UAAI,kBAAkB,GAAG,GAAG;AACxB,wBAAgB,GAAG,EAAE,gBAAgB;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AAPS;;;ACAT,WAAS,oBAAoB,KAAK,EAAE,QAAQ,SAAS,GAAG;AACpD,WAAQ,eAAe,IAAI,GAAG,KAC1B,IAAI,WAAW,QAAQ,MACrB,UAAU,aAAa,YACpB,CAAC,CAAC,gBAAgB,GAAG,KAAK,QAAQ;AAAA,EAC/C;AALS;;;ACAT,WAAS,4BAA4B,OAAO,WAAW,eAAe;AAClE,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,YAAY,CAAC;AACnB,eAAW,OAAO,OAAO;AACrB,UAAI,cAAc,MAAM,GAAG,CAAC,KACvB,UAAU,SACP,cAAc,UAAU,MAAM,GAAG,CAAC,KACtC,oBAAoB,KAAK,KAAK,KAC9B,eAAe,SAAS,GAAG,GAAG,cAAc,QAAW;AACvD,kBAAU,GAAG,IAAI,MAAM,GAAG;AAAA,MAC9B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAbS;;;ACIT,WAASC,kBAAiB,SAAS;AAC/B,WAAO,OAAO,iBAAiB,OAAO;AAAA,EAC1C;AAFS,SAAAA,mBAAA;AAGT,MAAM,oBAAN,cAAgC,iBAAiB;AAAA,IAVjD,OAUiD;AAAA;AAAA;AAAA,IAC7C,cAAc;AACV,YAAM,GAAG,SAAS;AAClB,WAAK,OAAO;AACZ,WAAK,iBAAiB;AAAA,IAC1B;AAAA,IACA,sBAAsB,UAAU,KAAK;AACjC,UAAI,eAAe,IAAI,GAAG,GAAG;AACzB,eAAO,KAAK,YAAY,eAClB,sBAAsB,GAAG,IACzB,mBAAmB,UAAU,GAAG;AAAA,MAC1C,OACK;AACD,cAAM,gBAAgBA,kBAAiB,QAAQ;AAC/C,cAAM,SAAS,kBAAkB,GAAG,IAC9B,cAAc,iBAAiB,GAAG,IAClC,cAAc,GAAG,MAAM;AAC7B,eAAO,OAAO,UAAU,WAAW,MAAM,KAAK,IAAI;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,2BAA2B,UAAU,EAAE,mBAAmB,GAAG;AACzD,aAAO,mBAAmB,UAAU,kBAAkB;AAAA,IAC1D;AAAA,IACA,MAAM,aAAa,cAAc,OAAO;AACpC,sBAAgB,aAAa,cAAc,MAAM,iBAAiB;AAAA,IACtE;AAAA,IACA,4BAA4B,OAAO,WAAW,eAAe;AACzD,aAAO,4BAA4B,OAAO,WAAW,aAAa;AAAA,IACtE;AAAA,EACJ;;;ACpCA,WAAS,YAAY,KAAK,QAAQ;AAC9B,WAAO,OAAO;AAAA,EAClB;AAFS;AAGT,MAAM,sBAAN,cAAkC,cAAc;AAAA,IANhD,OAMgD;AAAA;AAAA;AAAA,IAC5C,cAAc;AACV,YAAM,GAAG,SAAS;AAClB,WAAK,OAAO;AAAA,IAChB;AAAA,IACA,sBAAsB,UAAU,KAAK;AACjC,UAAI,YAAY,KAAK,QAAQ,GAAG;AAC5B,cAAM,QAAQ,SAAS,GAAG;AAC1B,YAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AACxD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,yBAAyB;AACrB,aAAO;AAAA,IACX;AAAA,IACA,2BAA2B,KAAK,aAAa;AACzC,aAAO,YAAY,OAAO,GAAG;AAAA,IACjC;AAAA,IACA,6BAA6B;AACzB,aAAO,UAAU;AAAA,IACrB;AAAA,IACA,MAAM,aAAa,cAAc;AAC7B,aAAO,OAAO,YAAY,QAAQ,YAAY;AAAA,IAClD;AAAA,IACA,eAAe,UAAU,EAAE,OAAO,GAAG;AACjC,aAAO,OAAO,UAAU,MAAM;AAAA,IAClC;AAAA,IACA,2BAA2B;AACvB,aAAO;AAAA,IACX;AAAA,EACJ;;;ACpCA,MAAM,WAAW;AAAA,IACb,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AACA,MAAM,YAAY;AAAA,IACd,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AAQA,WAAS,aAAa,OAAO,QAAQ,UAAU,GAAG,SAAS,GAAG,cAAc,MAAM;AAE9E,UAAM,aAAa;AAGnB,UAAM,OAAO,cAAc,WAAW;AAEtC,UAAM,KAAK,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM;AAEzC,UAAM,aAAa,GAAG,UAAU,MAAM;AACtC,UAAM,cAAc,GAAG,UAAU,OAAO;AACxC,UAAM,KAAK,KAAK,IAAI,GAAG,UAAU,IAAI,WAAW;AAAA,EACpD;AAZS;;;ACXT,WAAS,cAAc,OAAO;AAAA,IAAE;AAAA,IAAO;AAAA,IAAO;AAAA,IAAW;AAAA,IAAY,cAAc;AAAA,IAAG,aAAa;AAAA;AAAA,IAEnG,GAAG;AAAA,EAAO,GAAGC,WAAU,mBAAmB,WAAW;AACjD,oBAAgB,OAAO,QAAQ,iBAAiB;AAKhD,QAAIA,WAAU;AACV,UAAI,MAAM,MAAM,SAAS;AACrB,cAAM,MAAM,UAAU,MAAM,MAAM;AAAA,MACtC;AACA;AAAA,IACJ;AACA,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,EAAE,OAAO,MAAM,IAAI;AAKzB,QAAI,MAAM,WAAW;AACjB,YAAM,YAAY,MAAM;AACxB,aAAO,MAAM;AAAA,IACjB;AACA,QAAI,MAAM,aAAa,MAAM,iBAAiB;AAC1C,YAAM,kBAAkB,MAAM,mBAAmB;AACjD,aAAO,MAAM;AAAA,IACjB;AACA,QAAI,MAAM,WAAW;AAKjB,YAAM,eAAe,WAAW,gBAAgB;AAChD,aAAO,MAAM;AAAA,IACjB;AAEA,QAAI,UAAU;AACV,YAAM,IAAI;AACd,QAAI,UAAU;AACV,YAAM,IAAI;AACd,QAAI,cAAc;AACd,YAAM,QAAQ;AAElB,QAAI,eAAe,QAAW;AAC1B,mBAAa,OAAO,YAAY,aAAa,YAAY,KAAK;AAAA,IAClE;AAAA,EACJ;AAhDS;;;ACHT,MAAM,sBAAsB,oBAAI,IAAI;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;;;AC3BD,MAAM,WAAW,wBAAC,QAAQ,OAAO,QAAQ,YAAY,IAAI,YAAY,MAAM,OAA1D;;;ACIjB,WAAS,UAAU,SAAS,aAAa,YAAY,YAAY;AAC7D,eAAW,SAAS,aAAa,QAAW,UAAU;AACtD,eAAW,OAAO,YAAY,OAAO;AACjC,cAAQ,aAAa,CAAC,oBAAoB,IAAI,GAAG,IAAI,YAAY,GAAG,IAAI,KAAK,YAAY,MAAM,GAAG,CAAC;AAAA,IACvG;AAAA,EACJ;AALS;;;ACDT,WAASC,6BAA4B,OAAO,WAAW,eAAe;AAClE,UAAM,YAAY,4BAA8B,OAAO,WAAW,aAAa;AAC/E,eAAW,OAAO,OAAO;AACrB,UAAI,cAAc,MAAM,GAAG,CAAC,KACxB,cAAc,UAAU,GAAG,CAAC,GAAG;AAC/B,cAAM,YAAY,mBAAmB,QAAQ,GAAG,MAAM,KAChD,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC,IACtD;AACN,kBAAU,SAAS,IAAI,MAAM,GAAG;AAAA,MACpC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAZS,SAAAA,8BAAA;;;ACOT,MAAM,mBAAN,cAA+B,iBAAiB;AAAA,IAVhD,OAUgD;AAAA;AAAA;AAAA,IAC5C,cAAc;AACV,YAAM,GAAG,SAAS;AAClB,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,6BAA6B;AAAA,IACtC;AAAA,IACA,uBAAuB,OAAO,KAAK;AAC/B,aAAO,MAAM,GAAG;AAAA,IACpB;AAAA,IACA,sBAAsB,UAAU,KAAK;AACjC,UAAI,eAAe,IAAI,GAAG,GAAG;AACzB,cAAM,cAAc,oBAAoB,GAAG;AAC3C,eAAO,cAAc,YAAY,WAAW,IAAI;AAAA,MACpD;AACA,YAAM,CAAC,oBAAoB,IAAI,GAAG,IAAI,YAAY,GAAG,IAAI;AACzD,aAAO,SAAS,aAAa,GAAG;AAAA,IACpC;AAAA,IACA,4BAA4B,OAAO,WAAW,eAAe;AACzD,aAAOC,6BAA4B,OAAO,WAAW,aAAa;AAAA,IACtE;AAAA,IACA,MAAM,aAAa,cAAc,OAAO;AACpC,oBAAc,aAAa,cAAc,KAAK,UAAU,MAAM,mBAAmB,MAAM,KAAK;AAAA,IAChG;AAAA,IACA,eAAe,UAAU,aAAa,WAAW,YAAY;AACzD,gBAAU,UAAU,aAAa,WAAW,UAAU;AAAA,IAC1D;AAAA,IACA,MAAM,UAAU;AACZ,WAAK,WAAW,SAAS,SAAS,OAAO;AACzC,YAAM,MAAM,QAAQ;AAAA,IACxB;AAAA,EACJ;;;ACnCA,WAAS,uBAAuB,SAAS;AACrC,UAAM,UAAU;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO,CAAC;AAAA,MACR,aAAa;AAAA,QACT,aAAa;AAAA,UACT,WAAW,CAAC;AAAA,UACZ,iBAAiB,CAAC;AAAA,UAClB,OAAO,CAAC;AAAA,UACR,MAAM,CAAC;AAAA,UACP,OAAO,CAAC;AAAA,QACZ;AAAA,QACA,cAAc,CAAC;AAAA,MACnB;AAAA,IACJ;AACA,UAAM,OAAO,aAAa,OAAO,KAAK,CAAC,gBAAgB,OAAO,IACxD,IAAI,iBAAiB,OAAO,IAC5B,IAAI,kBAAkB,OAAO;AACnC,SAAK,MAAM,OAAO;AAClB,uBAAmB,IAAI,SAAS,IAAI;AAAA,EACxC;AApBS;AAqBT,WAAS,0BAA0B,SAAS;AACxC,UAAM,UAAU;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO,CAAC;AAAA,MACR,aAAa;AAAA,QACT,aAAa;AAAA,UACT,QAAQ,CAAC;AAAA,QACb;AAAA,QACA,cAAc,CAAC;AAAA,MACnB;AAAA,IACJ;AACA,UAAM,OAAO,IAAI,oBAAoB,OAAO;AAC5C,SAAK,MAAM,OAAO;AAClB,uBAAmB,IAAI,SAAS,IAAI;AAAA,EACxC;AAdS;;;ACxBT,WAAS,mBAAmB,OAAOC,YAAW,SAAS;AACnD,UAAM,gBAAgB,cAAc,KAAK,IAAI,QAAQ,YAAY,KAAK;AACtE,kBAAc,MAAM,mBAAmB,IAAI,eAAeA,YAAW,OAAO,CAAC;AAC7E,WAAO,cAAc;AAAA,EACzB;AAJS;;;ACMT,WAAS,cAAc,SAASC,YAAW;AACvC,WAAQ,cAAc,OAAO,KACzB,OAAO,YAAY,YAClB,OAAO,YAAY,YAAY,CAAC,eAAeA,UAAS;AAAA,EACjE;AAJS;AAQT,WAAS,eAAe,SAASA,YAAW,SAAS,OAAO;AACxD,UAAM,aAAa,CAAC;AACpB,QAAI,cAAc,SAASA,UAAS,GAAG;AACnC,iBAAW,KAAK,mBAAmB,SAAS,eAAeA,UAAS,IAC9DA,WAAU,WAAWA,aACrBA,YAAW,UAAU,QAAQ,WAAW,UAAU,OAAO,CAAC;AAAA,IACpE,OACK;AACD,YAAM,WAAW,gBAAgB,SAASA,YAAW,KAAK;AAC1D,YAAM,cAAc,SAAS;AAC7B,gBAAU,QAAQ,WAAW,GAAG,+BAA+B,mBAAmB;AAClF,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,cAAM,cAAc,SAAS,CAAC;AAC9B,kBAAU,gBAAgB,MAAM,wIAAwI,cAAc;AACtL,cAAM,sBAAsB,uBAAuB,UAC7C,yBACA;AACN,YAAI,CAAC,mBAAmB,IAAI,WAAW,GAAG;AACtC,8BAAoB,WAAW;AAAA,QACnC;AACA,cAAM,gBAAgB,mBAAmB,IAAI,WAAW;AACxD,cAAM,aAAa,EAAE,GAAG,QAAQ;AAIhC,YAAI,WAAW,cACX,OAAO,WAAW,UAAU,YAAY;AACxC,qBAAW,QAAQ,WAAW,MAAM,GAAG,WAAW;AAAA,QACtD;AACA,mBAAW,KAAK,GAAG,cAAc,eAAe,EAAE,GAAGA,YAAW,WAAW,GAAG,CAAC,CAAC,CAAC;AAAA,MACrF;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAjCS;;;ACbT,WAAS,gBAAgB,UAAU,SAAS,OAAO;AAC/C,UAAM,aAAa,CAAC;AACpB,UAAM,uBAAuB,6BAA6B,UAAU,SAAS,OAAO,EAAE,OAAO,CAAC;AAC9F,yBAAqB,QAAQ,CAAC,EAAE,WAAAC,YAAW,WAAW,GAAG,YAAY;AACjE,iBAAW,KAAK,GAAG,eAAe,SAASA,YAAW,UAAU,CAAC;AAAA,IACrE,CAAC;AACD,WAAO;AAAA,EACX;AAPS;;;ACCT,WAAS,WAAW,OAAO;AACvB,WAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,OAAO;AAAA,EAC3D;AAFS;AAOT,WAAS,oBAAoB,OAAO;AAIhC,aAAS,cAAc,mBAAmB,oBAAoB,SAAS;AACnE,UAAI,aAAa,CAAC;AAClB,UAAI,WAAW,iBAAiB,GAAG;AAC/B,qBAAa,gBAAgB,mBAAmB,oBAAoB,KAAK;AAAA,MAC7E,OACK;AACD,qBAAa,eAAe,mBAAmB,oBAAoB,SAAS,KAAK;AAAA,MACrF;AACA,YAAM,YAAY,IAAI,uBAAuB,UAAU;AACvD,UAAI,OAAO;AACP,cAAM,WAAW,KAAK,SAAS;AAC/B,kBAAU,SAAS,KAAK,MAAM;AAC1B,qBAAW,MAAM,YAAY,SAAS;AAAA,QAC1C,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AAhBS;AAiBT,WAAO;AAAA,EACX;AAtBS;AAuBT,MAAM,UAAU,oBAAoB;;;AC3B7B,MAAM,+BAAN,MAAmC;AAAA,IAR1C,OAQ0C;AAAA;AAAA;AAAA,IACxC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,YAAY,CAAC;AAClB,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AACxB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AACtB,WAAK,iBAAiB,oBAAI,IAAI;AAAA,IAChC;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAED,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,mBAAmB;AACjB,WAAK,iBAAiB,SAAS,cAAc,kBAAkB;AAC/D,WAAK,mBAAmB,SAAS,cAAc,qBAAqB;AAEpE,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,kBAAkB;AAClD;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,4BAA4B;AACjC,WAAK,4BAA4B;AACjC,WAAK,uBAAuB;AAG5B,WAAK,YAAY;AACjB,WAAK,0BAA0B;AAG/B,WAAK,uBAAuB;AAE5B,UAAI,KAAK,eAAe;AAEtB,YAAI,OAAO,WAAW,aAAa;AACjC,iBAAO,qBAAqB;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iBAAiB;AACf,YAAM,OAAO,KAAK,eAAe,iBAAiB,uBAAuB;AAEzE,WAAK,QAAQ,CAAC,QAAQ;AACpB,cAAM,WAAW,IAAI,aAAa,gBAAgB;AAClD,cAAM,UAAU,IAAI,aAAa,eAAe;AAEhD,YAAI,YAAY,SAAS;AACvB,eAAK,UAAU,KAAK;AAAA,YAClB,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA,eAAe,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AAAA,YACjF,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,gBAAgB,KAAK;AACnB,aAAO,IAAI,YAAY,EAAE,KAAK;AAAA,IAChC;AAAA,IAEA,8BAA8B;AAE5B,eAAS,iBAAiB,0BAA0B,CAAC,UAAU;AAC7D,aAAK,iBAAiB,MAAM,MAAM;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,IAEA,yBAAyB;AAEvB,YAAM,gBAAgB,SAAS;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,eAAe;AACjB,cAAM,WAAW,cAAc,aAAa,WAAW;AACvD,cAAM,cAAc,SAAS,QAAQ;AACrC,aAAK,mBAAmB,WAAW;AAAA,MACrC,OAAO;AAEL,aAAK,mBAAmB,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,yBAAyB;AAEvB,YAAM,WAAW,SAAS,cAAc,iBAAiB;AACzD,YAAM,WAAW,SAAS,cAAc,iBAAiB;AAEzD,UAAI,YAAY,UAAU;AAExB,cAAM,WAAW,IAAI;AAAA,UACnB,CAAC,YAAY;AACX,oBAAQ,QAAQ,CAAC,UAAU;AACzB,kBAAI,MAAM,gBAAgB;AACxB,sBAAM,OAAO,MAAM,OAAO,aAAa,WAAW;AAClD,oBAAI,SAAS,OAAO,SAAS,KAAK;AAChC,uBAAK,oBAAoB;AAAA,gBAC3B;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,YAAY;AAAA,UACd;AAAA,QACF;AAEA,YAAI,SAAU,UAAS,QAAQ,QAAQ;AACvC,YAAI,SAAU,UAAS,QAAQ,QAAQ;AAAA,MACzC;AAAA,IACF;AAAA,IAEA,iBAAiB,QAAQ;AACvB,YAAM,EAAE,YAAY,IAAI;AACxB,WAAK,cAAc;AAGnB,YAAM,kBAAkB,KAAK;AAC7B,WAAK,iBAAiB,gBAAgB,KAAK,gBAAgB;AAG3D,WAAK,mBAAmB,WAAW;AAGnC,UAAI,oBAAoB,KAAK,gBAAgB;AAC3C,aAAK,0BAA0B;AAC/B,aAAK,qBAAqB;AAG1B,YAAI,KAAK,kBAAkB,KAAK,eAAe,OAAO,GAAG;AACvD,eAAK,0BAA0B;AAAA,QACjC;AAAA,MACF,WAAW,KAAK,gBAAgB;AAG9B,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,IAEA,sBAAsB;AAAA,IAGtB;AAAA,IAEA,mBAAmB,aAAa;AAC9B,UAAI,CAAC,KAAK,iBAAkB;AAI5B,UAAI,gBAAgB,GAAG;AACrB,aAAK,iBAAiB,UAAU,IAAI,WAAW;AAAA,MACjD,OAAO;AACL,aAAK,iBAAiB,UAAU,OAAO,WAAW;AAAA,MACpD;AAAA,IACF;AAAA,IAEA,4BAA4B;AAC1B,UAAI,CAAC,KAAK,eAAgB;AAE1B,UAAI,KAAK,gBAAgB;AAEvB,aAAK,eAAe,MAAM,UAAU;AACpC,aAAK,eAAe,MAAM,UAAU;AAAA,MACtC,OAAO;AAEL,aAAK,eAAe,MAAM,UAAU;AACpC,aAAK,eAAe,MAAM,UAAU;AAAA,MACtC;AAAA,IACF;AAAA,IAEA,uBAAuB;AACrB,UAAI,CAAC,KAAK,gBAAgB;AAExB,aAAK,qBAAqB;AAC1B;AAAA,MACF;AAGA,WAAK,0BAA0B;AAAA,IACjC;AAAA,IAEA,8BAA8B;AAE5B,eAAS,iBAAiB,yBAAyB,CAAC,UAAU;AAC5D,aAAK,2BAA2B,MAAM,MAAM;AAAA,MAC9C,CAAC;AAGD,eAAS,iBAAiB,sBAAsB,CAAC,UAAU;AACzD,aAAK,2BAA2B,MAAM,MAAM;AAAA,MAC9C,CAAC;AAGD,eAAS,iBAAiB,6BAA6B,MAAM;AAE3D,YAAI,KAAK,kBAAkB,KAAK,eAAe,OAAO,GAAG;AACvD,eAAK,0BAA0B;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,2BAA2B,QAAQ;AACjC,YAAM,iBAAiB,OAAO,kBAAkB,CAAC;AACjD,WAAK,iBAAiB,IAAI,IAAI,cAAc;AAG5C,UAAI,KAAK,gBAAgB;AACvB,aAAK,0BAA0B;AAAA,MACjC;AAAA,IAGF;AAAA,IAEA,4BAA4B;AAE1B,YAAM,aAAa,CAAC;AACpB,YAAM,aAAa,CAAC;AAEpB,WAAK,UAAU,QAAQ,CAAC,QAAQ;AAC9B,cAAM,kBAAkB,KAAK,eAAe,IAAI,IAAI,aAAa;AAEjE,YAAI,mBAAmB,CAAC,IAAI,WAAW;AACrC,qBAAW,KAAK,GAAG;AACnB,cAAI,YAAY;AAAA,QAClB,WAAW,CAAC,mBAAmB,IAAI,WAAW;AAC5C,qBAAW,KAAK,GAAG;AACnB,cAAI,YAAY;AAAA,QAClB;AAAA,MACF,CAAC;AAGD,UAAI,WAAW,SAAS,GAAG;AACzB,aAAK,cAAc,UAAU;AAAA,MAC/B;AAEA,UAAI,WAAW,SAAS,GAAG;AACzB,aAAK,eAAe,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,IAEA,cAAc;AACZ,WAAK,UAAU,QAAQ,CAAC,QAAQ;AAC9B,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,QAAQ,MAAM,YAAY;AAC9B,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,YAAY;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IAEA,uBAAuB;AAErB,WAAK,UAAU,QAAQ,CAAC,QAAQ;AAC9B,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,QAAQ,MAAM,YAAY;AAC9B,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,YAAY;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IAEA,cAAc,MAAM;AAElB,WAAK,QAAQ,CAAC,QAAQ;AACpB,YAAI,QAAQ,MAAM,UAAU;AAAA,MAC9B,CAAC;AAGD,YAAM,WAAW,KAAK,IAAI,CAAC,QAAQ,IAAI,OAAO;AAG9C,eAAS,QAAQ,CAAC,YAAY;AAC5B,gBAAQ,MAAM,UAAU;AACxB,gBAAQ,MAAM,YAAY;AAAA,MAC5B,CAAC;AAGD;AAAA,QACE;AAAA,QACA;AAAA,UACE,SAAS,CAAC,GAAG,CAAC;AAAA,UACd,WAAW,CAAC,oBAAoB,iBAAiB;AAAA,QACnD;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO,QAAQ,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,IAEA,eAAe,MAAM;AACnB,YAAM,WAAW,KAAK,IAAI,CAAC,QAAQ,IAAI,OAAO;AAG9C;AAAA,QACE,SAAS,QAAQ;AAAA,QACjB;AAAA,UACE,SAAS,CAAC,GAAG,CAAC;AAAA,UACd,WAAW,CAAC,mBAAmB,mBAAmB;AAAA,QACpD;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO,QAAQ,IAAI;AAAA,QACrB;AAAA,MACF,EAAE,KAAK,MAAM;AAEX,aAAK,QAAQ,CAAC,QAAQ;AACpB,cAAI,QAAQ,MAAM,UAAU;AAAA,QAC9B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,iBAAiB;AACf,UAAI,KAAK,gBAAgB;AACvB,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA;AAAA;AAAA,IAIA,iCAAiC;AAC/B,UAAI,KAAK,eAAe,OAAO,GAAG;AAEhC,YAAI,KAAK,gBAAgB;AACvB,eAAK,0BAA0B;AAAA,QACjC;AAAA,MAGF;AAAA,IACF;AAAA;AAAA,IAGA,QAAQ,UAAU,SAAS;AACzB,YAAM,gBAAgB,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AACxF,YAAM,MAAM,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,kBAAkB,aAAa;AAExE,UAAI,OAAO,CAAC,IAAI,WAAW;AACzB,aAAK,cAAc,CAAC,GAAG,CAAC;AAAA,MAC1B;AAAA,IACF;AAAA,IAEA,QAAQ,UAAU,SAAS;AACzB,YAAM,gBAAgB,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AACxF,YAAM,MAAM,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,kBAAkB,aAAa;AAExE,UAAI,OAAO,IAAI,WAAW;AACxB,aAAK,eAAe,CAAC,GAAG,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,aAAO,KAAK,UAAU,OAAO,CAAC,QAAQ,IAAI,SAAS;AAAA,IACrD;AAAA;AAAA,IAGA,iBAAiB;AACf,cAAQ,IAAI,iDAAqC;AACjD,cAAQ,IAAI,IAAI,OAAO,EAAE,CAAC;AAC1B,cAAQ,IAAI,iCAA0B,KAAK,aAAa,EAAE;AAC1D,cAAQ,IAAI,2BAAoB,KAAK,WAAW,EAAE;AAClD,cAAQ,IAAI,gCAAyB,KAAK,cAAc,EAAE;AAC1D,cAAQ,IAAI,oCAA6B,CAAC,CAAC,KAAK,cAAc,EAAE;AAChE,cAAQ,IAAI,uCAAgC,CAAC,CAAC,KAAK,gBAAgB,EAAE;AACrE,cAAQ,IAAI,+BAAwB,KAAK,UAAU,MAAM,EAAE;AAC3D,cAAQ,IAAI,8BAAuB,KAAK,eAAe,IAAI,EAAE;AAE7D,UAAI,KAAK,eAAe,OAAO,GAAG;AAChC,gBAAQ,IAAI,4BAAqB;AACjC,cAAM,KAAK,KAAK,cAAc,EAAE,QAAQ,CAAC,UAAU;AACjD,kBAAQ,IAAI,YAAO,KAAK,EAAE;AAAA,QAC5B,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,gBAAQ,IAAI,+BAAmB;AAC/B,aAAK,UAAU,QAAQ,CAAC,KAAK,UAAU;AACrC,gBAAM,kBAAkB,KAAK,eAAe,IAAI,IAAI,aAAa;AACjE,kBAAQ,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAI,QAAQ,MAAM,IAAI,OAAO,EAAE;AAC9D,kBAAQ,IAAI,cAAc,IAAI,aAAa,GAAG;AAC9C,kBAAQ,IAAI,2BAA2B,eAAe,EAAE;AACxD,kBAAQ,IAAI,oBAAoB,IAAI,SAAS,EAAE;AAC/C,kBAAQ,IAAI,yBAAyB,IAAI,QAAQ,MAAM,WAAW,SAAS,EAAE;AAC7E,kBAAQ,IAAI,yBAAyB,IAAI,QAAQ,MAAM,WAAW,SAAS,EAAE;AAAA,QAC/E,CAAC;AAAA,MACH;AAEA,cAAQ,IAAI,gCAAyB;AACrC,UAAI,CAAC,KAAK,kBAAkB,KAAK,eAAe,OAAO,GAAG;AACxD,gBAAQ,IAAI,8CAAyC;AAAA,MACvD;AACA,UAAI,KAAK,kBAAkB,KAAK,eAAe,OAAO,GAAG;AACvD,gBAAQ,IAAI,0EAAqE;AAAA,MACnF;AACA,UAAI,KAAK,eAAe,SAAS,GAAG;AAClC,gBAAQ,IAAI,mCAA8B;AAAA,MAC5C;AAAA,IACF;AAAA,IAEA,cAAc,UAAU,SAAS;AAC/B,YAAM,gBAAgB,GAAG,KAAK,gBAAgB,QAAQ,CAAC,IAAI,KAAK,gBAAgB,OAAO,CAAC;AACxF,aAAO,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,kBAAkB,aAAa;AAAA,IACrE;AAAA,EACF;", "names": ["ease", "frameData", "steps", "process", "alpha", "alpha", "progress", "color", "mixNumber", "scale", "progress", "undampedFreq", "progress", "keyframes", "ease", "ease", "keyframes", "percent", "motionValue", "keyframes", "progress", "now", "keyframes", "transform", "transform", "motionValue", "keyframes", "ease", "keyframes", "motionValue", "keyframes", "motionValue", "keyframes", "motionValue", "sync", "time", "number", "getAnimatableNone", "getAnimatableNone", "motionValue", "ease", "distance", "keyframes", "keyframes", "keyframes", "keyframes", "ease", "getValueTransition", "isNotNull", "getFinalKeyframe", "keyframes", "keyframes", "getFinalKeyframe", "scale", "scaleX", "scaleY", "scale", "distance", "transform", "scroll", "now", "getAnimatableNone", "transform", "hasTransform", "getComputedStyle", "isSVGTag", "scrapeMotionValuesFromProps", "scrapeMotionValuesFromProps", "keyframes", "keyframes", "keyframes"]}