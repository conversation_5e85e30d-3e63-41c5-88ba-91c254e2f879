{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/event-coordinator.js", "../../src/modules/currency-formatting.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * EventCoordinator - Sistema centralizado CORRIGIDO para gerenciar eventos do input principal\r\n * Evita conflitos entre múltiplos módulos e memory leaks\r\n */\r\n\r\nexport class EventCoordinator {\r\n  constructor() {\r\n    this.input = null;\r\n    this.listeners = new Map();\r\n    this.isProcessing = false;\r\n    this.eventQueue = [];\r\n    this.boundHandlers = new Map(); // Para rastrear handlers bound\r\n    this.isDestroyed = false;\r\n\r\n    this.init();\r\n  }\r\n\r\n  init() {\r\n    // Aguarda o DOM estar pronto\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => this.findAndSetupInput());\r\n    } else {\r\n      this.findAndSetupInput();\r\n    }\r\n  }\r\n\r\n  findAndSetupInput() {\r\n    this.input = document.querySelector('[is-main=\"true\"]');\r\n    if (this.input && !this.isDestroyed) {\r\n      this.setupMainListeners();\r\n    }\r\n  }\r\n\r\n  setupMainListeners() {\r\n    if (!this.input || this.boundHandlers.has('main')) return;\r\n\r\n    // Cria handlers bound uma única vez\r\n    const inputHandler = (e) => this.handleInputEvent(e);\r\n    const focusHandler = (e) => this.processFocusEvent(e);\r\n    const blurHandler = (e) => this.processBlurEvent(e);\r\n    const changeHandler = (e) => this.processChangeEvent(e);\r\n\r\n    // Armazena referências para cleanup posterior\r\n    this.boundHandlers.set('main', {\r\n      input: inputHandler,\r\n      focus: focusHandler,\r\n      blur: blurHandler,\r\n      change: changeHandler,\r\n    });\r\n\r\n    // Adiciona listeners apenas uma vez\r\n    this.input.addEventListener('input', inputHandler, { passive: true });\r\n    this.input.addEventListener('focus', focusHandler, { passive: true });\r\n    this.input.addEventListener('blur', blurHandler, { passive: true });\r\n    this.input.addEventListener('change', changeHandler, { passive: true });\r\n  }\r\n\r\n  handleInputEvent(e) {\r\n    if (this.isProcessing || this.isDestroyed) {\r\n      return;\r\n    }\r\n\r\n    this.isProcessing = true;\r\n\r\n    // Usa requestAnimationFrame para melhor performance\r\n    requestAnimationFrame(() => {\r\n      this.processInputEvent(e);\r\n      this.isProcessing = false;\r\n\r\n      // Processa queue se houver\r\n      if (this.eventQueue.length > 0) {\r\n        const nextEvent = this.eventQueue.shift();\r\n        requestAnimationFrame(() => this.handleInputEvent(nextEvent));\r\n      }\r\n    });\r\n  }\r\n\r\n  // Registra um listener para um módulo específico\r\n  registerListener(moduleId, eventType, callback) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    // Remove listener anterior se existir (evita duplicação)\r\n    this.unregisterListener(moduleId, eventType);\r\n\r\n    if (!this.listeners.has(key)) {\r\n      this.listeners.set(key, []);\r\n    }\r\n\r\n    this.listeners.get(key).push(callback);\r\n  }\r\n\r\n  // Remove listener de um módulo\r\n  unregisterListener(moduleId, eventType, specificCallback = null) {\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    if (this.listeners.has(key)) {\r\n      if (specificCallback) {\r\n        const callbacks = this.listeners.get(key);\r\n        const index = callbacks.indexOf(specificCallback);\r\n        if (index > -1) {\r\n          callbacks.splice(index, 1);\r\n        }\r\n      } else {\r\n        // Remove todos os callbacks do módulo para este evento\r\n        this.listeners.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove todos os listeners de um módulo\r\n  unregisterModule(moduleId) {\r\n    const keysToRemove = [];\r\n    for (const key of this.listeners.keys()) {\r\n      if (key.startsWith(`${moduleId}_`)) {\r\n        keysToRemove.push(key);\r\n      }\r\n    }\r\n\r\n    keysToRemove.forEach((key) => this.listeners.delete(key));\r\n  }\r\n\r\n  processInputEvent(e) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const inputCallbacks = this.getCallbacksForEvent('input');\r\n\r\n    // Executa callbacks em ordem de prioridade\r\n    const priorityOrder = ['currency-formatting', 'motion-animation', 'patrimony-sync'];\r\n\r\n    for (const moduleId of priorityOrder) {\r\n      const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);\r\n      for (const callbackInfo of moduleCallbacks) {\r\n        try {\r\n          callbackInfo.callback(e);\r\n        } catch (error) {\r\n          console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  processFocusEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('focus', e);\r\n  }\r\n\r\n  processBlurEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('blur', e);\r\n  }\r\n\r\n  processChangeEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('change', e);\r\n  }\r\n\r\n  executeCallbacksForEvent(eventType, e) {\r\n    const callbacks = this.getCallbacksForEvent(eventType);\r\n    callbacks.forEach(({ callback, moduleId }) => {\r\n      try {\r\n        callback(e);\r\n      } catch (error) {\r\n        console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);\r\n      }\r\n    });\r\n  }\r\n\r\n  getCallbacksForEvent(eventType) {\r\n    const callbacks = [];\r\n    for (const [key, callbackList] of this.listeners.entries()) {\r\n      if (key.endsWith(`_${eventType}`)) {\r\n        const moduleId = key.replace(`_${eventType}`, '');\r\n        callbackList.forEach((callback) => {\r\n          callbacks.push({ moduleId, callback });\r\n        });\r\n      }\r\n    }\r\n    return callbacks;\r\n  }\r\n\r\n  // Método para disparar eventos programaticamente\r\n  dispatchInputEvent(sourceModule = 'unknown') {\r\n    if (this.isProcessing || this.isDestroyed || !this.input) {\r\n      return;\r\n    }\r\n\r\n    const event = new Event('input', { bubbles: true });\r\n    event.sourceModule = sourceModule;\r\n    this.input.dispatchEvent(event);\r\n  }\r\n\r\n  // Método para atualizar valor sem disparar eventos\r\n  setSilentValue(value) {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.isProcessing = true;\r\n    this.input.value = value;\r\n\r\n    // Usa requestAnimationFrame para reset mais confiável\r\n    requestAnimationFrame(() => {\r\n      this.isProcessing = false;\r\n    });\r\n  }\r\n\r\n  // Getter para o valor atual\r\n  getValue() {\r\n    return this.input ? this.input.value : '';\r\n  }\r\n\r\n  // Setter que dispara eventos controlados\r\n  setValue(value, sourceModule = 'unknown') {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.input.value = value;\r\n    this.dispatchInputEvent(sourceModule);\r\n  }\r\n\r\n  // Método de cleanup para prevenir memory leaks\r\n  destroy() {\r\n    this.isDestroyed = true;\r\n\r\n    // Remove todos os event listeners\r\n    if (this.input && this.boundHandlers.has('main')) {\r\n      const handlers = this.boundHandlers.get('main');\r\n      this.input.removeEventListener('input', handlers.input);\r\n      this.input.removeEventListener('focus', handlers.focus);\r\n      this.input.removeEventListener('blur', handlers.blur);\r\n      this.input.removeEventListener('change', handlers.change);\r\n    }\r\n\r\n    // Limpa todas as referências\r\n    this.listeners.clear();\r\n    this.boundHandlers.clear();\r\n    this.eventQueue.length = 0;\r\n    this.input = null;\r\n    this.isProcessing = false;\r\n  }\r\n\r\n  // Método para reinicializar se necessário\r\n  reinitialize() {\r\n    this.destroy();\r\n    this.isDestroyed = false;\r\n    this.init();\r\n  }\r\n}\r\n\r\n// Instância singleton\r\nexport const eventCoordinator = new EventCoordinator();\r\n\r\n// Cleanup automático quando a página é descarregada\r\nwindow.addEventListener('beforeunload', () => {\r\n  eventCoordinator.destroy();\r\n});\r\n", "import { eventCoordinator } from './event-coordinator.js';\r\n\r\n/**\r\n * Currency Formatting System - VERSÃO CORRIGIDA\r\n * Handles currency input formatting and validation\r\n * Uses EventCoordinator for main input event coordination\r\n * Implementa cleanup adequado para prevenir memory leaks\r\n */\r\nexport class CurrencyFormattingSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.domObserver = null;\r\n    this.boundHandlers = new Map();\r\n    this.isDestroyed = false;\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized || this.isDestroyed) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.initializeCurrencySystem();\r\n    });\r\n\r\n    // Fallback caso o Webflow não esteja disponível\r\n    setTimeout(() => this.initializeCurrencySystem(), 100);\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  initializeCurrencySystem() {\r\n    if (this.isDestroyed) return;\r\n\r\n    // Aguarda o Webflow carregar completamente\r\n    if (window.Webflow) {\r\n      window.Webflow.push(() => {\r\n        this.setupCurrencyFormatting();\r\n      });\r\n    } else {\r\n      this.setupCurrencyFormatting();\r\n    }\r\n  }\r\n\r\n  setupCurrencyFormatting() {\r\n    if (this.isDestroyed) return;\r\n\r\n    // Configuração para Real Brasileiro\r\n    const formatBRL = (value) => {\r\n      return new Intl.NumberFormat('pt-BR', {\r\n        style: 'currency',\r\n        currency: 'BRL',\r\n      }).format(value);\r\n    };\r\n\r\n    // Função para formatar input enquanto digita\r\n    const formatCurrencyInput = (input) => {\r\n      let value = input.value.replace(/\\D/g, '');\r\n      if (value === '') {\r\n        input.value = '';\r\n        return 0;\r\n      }\r\n\r\n      // Converte centavos para reais\r\n      const numericValue = parseInt(value) / 100;\r\n\r\n      // Formata usando Intl.NumberFormat\r\n      const formatted = new Intl.NumberFormat('pt-BR', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2,\r\n      }).format(numericValue);\r\n\r\n      input.value = formatted;\r\n      return numericValue;\r\n    };\r\n\r\n    // Função para obter valor numérico limpo\r\n    const getCurrencyValue = (input) => {\r\n      const cleanValue = input.value.replace(/[^\\d,]/g, '').replace(',', '.');\r\n      return parseFloat(cleanValue) || 0;\r\n    };\r\n\r\n    // Inicializa inputs de moeda - separa main input dos outros\r\n    const currencyInputs = document.querySelectorAll('[data-currency=\"true\"]');\r\n    const mainInput = document.querySelector('[is-main=\"true\"]');\r\n\r\n    // Para inputs normais de moeda\r\n    currencyInputs.forEach((input) => {\r\n      if (!input || input.hasAttribute('is-main') || this.isDestroyed) return;\r\n\r\n      // Cria handlers bound únicos para cada input\r\n      const inputId = input.id || `currency-${Math.random().toString(36).substr(2, 9)}`;\r\n\r\n      if (!this.boundHandlers.has(inputId)) {\r\n        const handlers = {\r\n          input: (e) => this.handleCurrencyInput(e, formatCurrencyInput),\r\n          focus: (e) => this.handleCurrencyFocus(e, getCurrencyValue),\r\n          blur: (e) => this.handleCurrencyBlur(e, formatCurrencyInput),\r\n        };\r\n\r\n        this.boundHandlers.set(inputId, { input, handlers });\r\n\r\n        // Remove listeners existentes se houver\r\n        input.removeEventListener('input', handlers.input);\r\n        input.removeEventListener('focus', handlers.focus);\r\n        input.removeEventListener('blur', handlers.blur);\r\n\r\n        // Adiciona novos listeners\r\n        input.addEventListener('input', handlers.input, { passive: true });\r\n        input.addEventListener('focus', handlers.focus, { passive: true });\r\n        input.addEventListener('blur', handlers.blur, { passive: true });\r\n\r\n        // Formatação inicial se já houver valor\r\n        if (input.value && input.value !== input.placeholder) {\r\n          formatCurrencyInput(input);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Para o input principal, usa EventCoordinator\r\n    if (mainInput && !this.isDestroyed) {\r\n      // Remove listeners anteriores se existirem\r\n      eventCoordinator.unregisterModule('currency-formatting');\r\n\r\n      // Registra novos listeners\r\n      eventCoordinator.registerListener('currency-formatting', 'input', (e) =>\r\n        this.handleCurrencyInput(e, formatCurrencyInput)\r\n      );\r\n      eventCoordinator.registerListener('currency-formatting', 'focus', (e) =>\r\n        this.handleCurrencyFocus(e, getCurrencyValue)\r\n      );\r\n      eventCoordinator.registerListener('currency-formatting', 'blur', (e) =>\r\n        this.handleCurrencyBlur(e, formatCurrencyInput)\r\n      );\r\n\r\n      // Formatação inicial se já houver valor\r\n      if (mainInput.value && mainInput.value !== mainInput.placeholder) {\r\n        formatCurrencyInput(mainInput);\r\n      }\r\n    }\r\n\r\n    // Função para calcular com precisão usando Currency.js\r\n    window.calculateCurrency = (value1, value2, operation = 'add') => {\r\n      const curr1 = window.currency(value1);\r\n      const curr2 = window.currency(value2);\r\n\r\n      switch (operation) {\r\n        case 'add':\r\n          return curr1.add(curr2);\r\n        case 'subtract':\r\n          return curr1.subtract(curr2);\r\n        case 'multiply':\r\n          return curr1.multiply(curr2);\r\n        case 'divide':\r\n          return curr1.divide(curr2);\r\n        default:\r\n          return curr1;\r\n      }\r\n    };\r\n\r\n    // Função para formatar qualquer valor\r\n    window.formatCurrency = formatBRL;\r\n\r\n    // Setup para inputs individuais de alocação\r\n    this.setupAllocationInputs(getCurrencyValue);\r\n\r\n    // Observa mudanças no DOM para reinicializar inputs dinâmicos\r\n    this.setupDOMObserver();\r\n  }\r\n\r\n  handleCurrencyInput(event, formatCurrencyInput) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const numericValue = formatCurrencyInput(event.target);\r\n\r\n    // Dispara evento customizado para outros scripts\r\n    event.target.dispatchEvent(\r\n      new CustomEvent('currencyChange', {\r\n        detail: {\r\n          value: numericValue,\r\n          currencyValue: window.currency ? window.currency(numericValue) : numericValue,\r\n          formatted: window.formatCurrency ? window.formatCurrency(numericValue) : numericValue,\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  handleCurrencyFocus(event, getCurrencyValue) {\r\n    if (this.isDestroyed) return;\r\n\r\n    // Remove formatação para edição mais fácil\r\n    const value = getCurrencyValue(event.target);\r\n    if (value > 0) {\r\n      event.target.value = value.toFixed(2).replace('.', ',');\r\n    }\r\n  }\r\n\r\n  handleCurrencyBlur(event, formatCurrencyInput) {\r\n    if (this.isDestroyed) return;\r\n\r\n    // Reaplica formatação completa\r\n    formatCurrencyInput(event.target);\r\n  }\r\n\r\n  setupAllocationInputs(getCurrencyValue) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const individualInputs = document.querySelectorAll(\r\n      '.currency-input.individual, [input-settings=\"receive\"]'\r\n    );\r\n\r\n    individualInputs.forEach((input) => {\r\n      if (!input || this.isDestroyed) return;\r\n\r\n      // Remove listener anterior se existir\r\n      const existingHandler = input._currencyChangeHandler;\r\n      if (existingHandler) {\r\n        input.removeEventListener('currencyChange', existingHandler);\r\n      }\r\n\r\n      // Cria novo handler\r\n      const handler = () => this.updateTotalAllocation(getCurrencyValue);\r\n      input._currencyChangeHandler = handler;\r\n\r\n      input.addEventListener('currencyChange', handler, { passive: true });\r\n    });\r\n  }\r\n\r\n  updateTotalAllocation(getCurrencyValue) {\r\n    if (this.isDestroyed || !window.currency) return;\r\n\r\n    let total = window.currency(0);\r\n    document\r\n      .querySelectorAll('.currency-input.individual, [input-settings=\"receive\"]')\r\n      .forEach((input) => {\r\n        if (input && input.value && !this.isDestroyed) {\r\n          const value = getCurrencyValue(input);\r\n          total = total.add(value);\r\n        }\r\n      });\r\n\r\n    // Dispara evento para outros componentes\r\n    if (!this.isDestroyed) {\r\n      document.dispatchEvent(\r\n        new CustomEvent('totalAllocationChange', {\r\n          detail: {\r\n            total: total.value,\r\n            formatted: window.formatCurrency ? window.formatCurrency(total.value) : total.value,\r\n          },\r\n        })\r\n      );\r\n    }\r\n  }\r\n\r\n  setupDOMObserver() {\r\n    if (this.isDestroyed || this.domObserver) return;\r\n\r\n    // Cria observer com throttling para evitar muitas reinicializações\r\n    let observerTimeout;\r\n    const throttledReinit = () => {\r\n      if (observerTimeout) clearTimeout(observerTimeout);\r\n      observerTimeout = setTimeout(() => {\r\n        if (!this.isDestroyed) {\r\n          this.initializeCurrencySystem();\r\n        }\r\n      }, 100);\r\n    };\r\n\r\n    if (window.Webflow) {\r\n      window.Webflow.push(() => {\r\n        if (this.isDestroyed) return;\r\n\r\n        this.domObserver = new MutationObserver((mutations) => {\r\n          if (this.isDestroyed) return;\r\n\r\n          let shouldReinit = false;\r\n          mutations.forEach((mutation) => {\r\n            if (mutation.addedNodes.length) {\r\n              // Verifica se algum dos nós adicionados é relevante\r\n              for (const node of mutation.addedNodes) {\r\n                if (node.nodeType === Node.ELEMENT_NODE) {\r\n                  if (\r\n                    node.matches('[data-currency=\"true\"], [is-main=\"true\"]') ||\r\n                    node.querySelector('[data-currency=\"true\"], [is-main=\"true\"]')\r\n                  ) {\r\n                    shouldReinit = true;\r\n                    break;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          });\r\n\r\n          if (shouldReinit) {\r\n            throttledReinit();\r\n          }\r\n        });\r\n\r\n        this.domObserver.observe(document.body, {\r\n          childList: true,\r\n          subtree: true,\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  // Método de cleanup para prevenir memory leaks\r\n  cleanup() {\r\n    this.isDestroyed = true;\r\n\r\n    // Desconecta DOM observer\r\n    if (this.domObserver) {\r\n      this.domObserver.disconnect();\r\n      this.domObserver = null;\r\n    }\r\n\r\n    // Remove todos os event listeners dos inputs normais\r\n    for (const [inputId, { input, handlers }] of this.boundHandlers.entries()) {\r\n      if (input) {\r\n        input.removeEventListener('input', handlers.input);\r\n        input.removeEventListener('focus', handlers.focus);\r\n        input.removeEventListener('blur', handlers.blur);\r\n\r\n        // Remove handler customizado se existir\r\n        if (input._currencyChangeHandler) {\r\n          input.removeEventListener('currencyChange', input._currencyChangeHandler);\r\n          delete input._currencyChangeHandler;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Limpa referências\r\n    this.boundHandlers.clear();\r\n\r\n    // Remove listeners do EventCoordinator\r\n    if (eventCoordinator && !eventCoordinator.isDestroyed) {\r\n      eventCoordinator.unregisterModule('currency-formatting');\r\n    }\r\n\r\n    // Remove funções globais se foram criadas por este módulo\r\n    if (window.calculateCurrency) {\r\n      delete window.calculateCurrency;\r\n    }\r\n    if (window.formatCurrency) {\r\n      delete window.formatCurrency;\r\n    }\r\n\r\n    this.isInitialized = false;\r\n  }\r\n\r\n  // Método para reinicializar se necessário\r\n  reinitialize() {\r\n    this.cleanup();\r\n    this.isDestroyed = false;\r\n    this.init();\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACKtF,MAAM,mBAAN,MAAuB;AAAA,IAL9B,OAK8B;AAAA;AAAA;AAAA,IAC5B,cAAc;AACZ,WAAK,QAAQ;AACb,WAAK,YAAY,oBAAI,IAAI;AACzB,WAAK,eAAe;AACpB,WAAK,aAAa,CAAC;AACnB,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,cAAc;AAEnB,WAAK,KAAK;AAAA,IACZ;AAAA,IAEA,OAAO;AAEL,UAAI,SAAS,eAAe,WAAW;AACrC,iBAAS,iBAAiB,oBAAoB,MAAM,KAAK,kBAAkB,CAAC;AAAA,MAC9E,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,WAAK,QAAQ,SAAS,cAAc,kBAAkB;AACtD,UAAI,KAAK,SAAS,CAAC,KAAK,aAAa;AACnC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,IAEA,qBAAqB;AACnB,UAAI,CAAC,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,EAAG;AAGnD,YAAM,eAAe,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACrB,YAAM,eAAe,wBAAC,MAAM,KAAK,kBAAkB,CAAC,GAA/B;AACrB,YAAM,cAAc,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACpB,YAAM,gBAAgB,wBAAC,MAAM,KAAK,mBAAmB,CAAC,GAAhC;AAGtB,WAAK,cAAc,IAAI,QAAQ;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAGD,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,QAAQ,aAAa,EAAE,SAAS,KAAK,CAAC;AAClE,WAAK,MAAM,iBAAiB,UAAU,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,IACxE;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,gBAAgB,KAAK,aAAa;AACzC;AAAA,MACF;AAEA,WAAK,eAAe;AAGpB,4BAAsB,MAAM;AAC1B,aAAK,kBAAkB,CAAC;AACxB,aAAK,eAAe;AAGpB,YAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,gBAAM,YAAY,KAAK,WAAW,MAAM;AACxC,gCAAsB,MAAM,KAAK,iBAAiB,SAAS,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,iBAAiB,UAAU,WAAW,UAAU;AAC9C,UAAI,KAAK,YAAa;AAEtB,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAGpC,WAAK,mBAAmB,UAAU,SAAS;AAE3C,UAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,aAAK,UAAU,IAAI,KAAK,CAAC,CAAC;AAAA,MAC5B;AAEA,WAAK,UAAU,IAAI,GAAG,EAAE,KAAK,QAAQ;AAAA,IACvC;AAAA;AAAA,IAGA,mBAAmB,UAAU,WAAW,mBAAmB,MAAM;AAC/D,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAEpC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,YAAI,kBAAkB;AACpB,gBAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,gBAAM,QAAQ,UAAU,QAAQ,gBAAgB;AAChD,cAAI,QAAQ,IAAI;AACd,sBAAU,OAAO,OAAO,CAAC;AAAA,UAC3B;AAAA,QACF,OAAO;AAEL,eAAK,UAAU,OAAO,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,iBAAiB,UAAU;AACzB,YAAM,eAAe,CAAC;AACtB,iBAAW,OAAO,KAAK,UAAU,KAAK,GAAG;AACvC,YAAI,IAAI,WAAW,GAAG,QAAQ,GAAG,GAAG;AAClC,uBAAa,KAAK,GAAG;AAAA,QACvB;AAAA,MACF;AAEA,mBAAa,QAAQ,CAAC,QAAQ,KAAK,UAAU,OAAO,GAAG,CAAC;AAAA,IAC1D;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AAEtB,YAAM,iBAAiB,KAAK,qBAAqB,OAAO;AAGxD,YAAM,gBAAgB,CAAC,uBAAuB,oBAAoB,gBAAgB;AAElF,iBAAW,YAAY,eAAe;AACpC,cAAM,kBAAkB,eAAe,OAAO,CAAC,OAAO,GAAG,aAAa,QAAQ;AAC9E,mBAAW,gBAAgB,iBAAiB;AAC1C,cAAI;AACF,yBAAa,SAAS,CAAC;AAAA,UACzB,SAAS,OAAO;AACd,oBAAQ,MAAM,8BAA8B,QAAQ,cAAc,KAAK;AAAA,UACzE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,SAAS,CAAC;AAAA,IAC1C;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,QAAQ,CAAC;AAAA,IACzC;AAAA,IAEA,mBAAmB,GAAG;AACpB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,UAAU,CAAC;AAAA,IAC3C;AAAA,IAEA,yBAAyB,WAAW,GAAG;AACrC,YAAM,YAAY,KAAK,qBAAqB,SAAS;AACrD,gBAAU,QAAQ,CAAC,EAAE,UAAU,SAAS,MAAM;AAC5C,YAAI;AACF,mBAAS,CAAC;AAAA,QACZ,SAAS,OAAO;AACd,kBAAQ,MAAM,8BAA8B,QAAQ,IAAI,SAAS,cAAc,KAAK;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,qBAAqB,WAAW;AAC9B,YAAM,YAAY,CAAC;AACnB,iBAAW,CAAC,KAAK,YAAY,KAAK,KAAK,UAAU,QAAQ,GAAG;AAC1D,YAAI,IAAI,SAAS,IAAI,SAAS,EAAE,GAAG;AACjC,gBAAM,WAAW,IAAI,QAAQ,IAAI,SAAS,IAAI,EAAE;AAChD,uBAAa,QAAQ,CAAC,aAAa;AACjC,sBAAU,KAAK,EAAE,UAAU,SAAS,CAAC;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,mBAAmB,eAAe,WAAW;AAC3C,UAAI,KAAK,gBAAgB,KAAK,eAAe,CAAC,KAAK,OAAO;AACxD;AAAA,MACF;AAEA,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC;AAClD,YAAM,eAAe;AACrB,WAAK,MAAM,cAAc,KAAK;AAAA,IAChC;AAAA;AAAA,IAGA,eAAe,OAAO;AACpB,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,eAAe;AACpB,WAAK,MAAM,QAAQ;AAGnB,4BAAsB,MAAM;AAC1B,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,WAAW;AACT,aAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ;AAAA,IACzC;AAAA;AAAA,IAGA,SAAS,OAAO,eAAe,WAAW;AACxC,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,YAAY;AAAA,IACtC;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,cAAc;AAGnB,UAAI,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,GAAG;AAChD,cAAM,WAAW,KAAK,cAAc,IAAI,MAAM;AAC9C,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,QAAQ,SAAS,IAAI;AACpD,aAAK,MAAM,oBAAoB,UAAU,SAAS,MAAM;AAAA,MAC1D;AAGA,WAAK,UAAU,MAAM;AACrB,WAAK,cAAc,MAAM;AACzB,WAAK,WAAW,SAAS;AACzB,WAAK,QAAQ;AACb,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,eAAe;AACb,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAGO,MAAM,mBAAmB,IAAI,iBAAiB;AAGrD,SAAO,iBAAiB,gBAAgB,MAAM;AAC5C,qBAAiB,QAAQ;AAAA,EAC3B,CAAC;;;ACtPM,MAAM,2BAAN,MAA+B;AAAA,IARtC,OAQsC;AAAA;AAAA;AAAA,IACpC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,cAAc;AACnB,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,cAAc;AAAA,IACrB;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,iBAAiB,KAAK,aAAa;AAC1C;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,yBAAyB;AAAA,MAChC,CAAC;AAGD,iBAAW,MAAM,KAAK,yBAAyB,GAAG,GAAG;AAErD,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,2BAA2B;AACzB,UAAI,KAAK,YAAa;AAGtB,UAAI,OAAO,SAAS;AAClB,eAAO,QAAQ,KAAK,MAAM;AACxB,eAAK,wBAAwB;AAAA,QAC/B,CAAC;AAAA,MACH,OAAO;AACL,aAAK,wBAAwB;AAAA,MAC/B;AAAA,IACF;AAAA,IAEA,0BAA0B;AACxB,UAAI,KAAK,YAAa;AAGtB,YAAM,YAAY,wBAAC,UAAU;AAC3B,eAAO,IAAI,KAAK,aAAa,SAAS;AAAA,UACpC,OAAO;AAAA,UACP,UAAU;AAAA,QACZ,CAAC,EAAE,OAAO,KAAK;AAAA,MACjB,GALkB;AAQlB,YAAM,sBAAsB,wBAAC,UAAU;AACrC,YAAI,QAAQ,MAAM,MAAM,QAAQ,OAAO,EAAE;AACzC,YAAI,UAAU,IAAI;AAChB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AAGA,cAAM,eAAe,SAAS,KAAK,IAAI;AAGvC,cAAM,YAAY,IAAI,KAAK,aAAa,SAAS;AAAA,UAC/C,uBAAuB;AAAA,UACvB,uBAAuB;AAAA,QACzB,CAAC,EAAE,OAAO,YAAY;AAEtB,cAAM,QAAQ;AACd,eAAO;AAAA,MACT,GAlB4B;AAqB5B,YAAM,mBAAmB,wBAAC,UAAU;AAClC,cAAM,aAAa,MAAM,MAAM,QAAQ,WAAW,EAAE,EAAE,QAAQ,KAAK,GAAG;AACtE,eAAO,WAAW,UAAU,KAAK;AAAA,MACnC,GAHyB;AAMzB,YAAM,iBAAiB,SAAS,iBAAiB,wBAAwB;AACzE,YAAM,YAAY,SAAS,cAAc,kBAAkB;AAG3D,qBAAe,QAAQ,CAAC,UAAU;AAChC,YAAI,CAAC,SAAS,MAAM,aAAa,SAAS,KAAK,KAAK,YAAa;AAGjE,cAAM,UAAU,MAAM,MAAM,YAAY,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAE/E,YAAI,CAAC,KAAK,cAAc,IAAI,OAAO,GAAG;AACpC,gBAAM,WAAW;AAAA,YACf,OAAO,wBAAC,MAAM,KAAK,oBAAoB,GAAG,mBAAmB,GAAtD;AAAA,YACP,OAAO,wBAAC,MAAM,KAAK,oBAAoB,GAAG,gBAAgB,GAAnD;AAAA,YACP,MAAM,wBAAC,MAAM,KAAK,mBAAmB,GAAG,mBAAmB,GAArD;AAAA,UACR;AAEA,eAAK,cAAc,IAAI,SAAS,EAAE,OAAO,SAAS,CAAC;AAGnD,gBAAM,oBAAoB,SAAS,SAAS,KAAK;AACjD,gBAAM,oBAAoB,SAAS,SAAS,KAAK;AACjD,gBAAM,oBAAoB,QAAQ,SAAS,IAAI;AAG/C,gBAAM,iBAAiB,SAAS,SAAS,OAAO,EAAE,SAAS,KAAK,CAAC;AACjE,gBAAM,iBAAiB,SAAS,SAAS,OAAO,EAAE,SAAS,KAAK,CAAC;AACjE,gBAAM,iBAAiB,QAAQ,SAAS,MAAM,EAAE,SAAS,KAAK,CAAC;AAG/D,cAAI,MAAM,SAAS,MAAM,UAAU,MAAM,aAAa;AACpD,gCAAoB,KAAK;AAAA,UAC3B;AAAA,QACF;AAAA,MACF,CAAC;AAGD,UAAI,aAAa,CAAC,KAAK,aAAa;AAElC,yBAAiB,iBAAiB,qBAAqB;AAGvD,yBAAiB;AAAA,UAAiB;AAAA,UAAuB;AAAA,UAAS,CAAC,MACjE,KAAK,oBAAoB,GAAG,mBAAmB;AAAA,QACjD;AACA,yBAAiB;AAAA,UAAiB;AAAA,UAAuB;AAAA,UAAS,CAAC,MACjE,KAAK,oBAAoB,GAAG,gBAAgB;AAAA,QAC9C;AACA,yBAAiB;AAAA,UAAiB;AAAA,UAAuB;AAAA,UAAQ,CAAC,MAChE,KAAK,mBAAmB,GAAG,mBAAmB;AAAA,QAChD;AAGA,YAAI,UAAU,SAAS,UAAU,UAAU,UAAU,aAAa;AAChE,8BAAoB,SAAS;AAAA,QAC/B;AAAA,MACF;AAGA,aAAO,oBAAoB,CAAC,QAAQ,QAAQ,YAAY,UAAU;AAChE,cAAM,QAAQ,OAAO,SAAS,MAAM;AACpC,cAAM,QAAQ,OAAO,SAAS,MAAM;AAEpC,gBAAQ,WAAW;AAAA,UACjB,KAAK;AACH,mBAAO,MAAM,IAAI,KAAK;AAAA,UACxB,KAAK;AACH,mBAAO,MAAM,SAAS,KAAK;AAAA,UAC7B,KAAK;AACH,mBAAO,MAAM,SAAS,KAAK;AAAA,UAC7B,KAAK;AACH,mBAAO,MAAM,OAAO,KAAK;AAAA,UAC3B;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,aAAO,iBAAiB;AAGxB,WAAK,sBAAsB,gBAAgB;AAG3C,WAAK,iBAAiB;AAAA,IACxB;AAAA,IAEA,oBAAoB,OAAO,qBAAqB;AAC9C,UAAI,KAAK,YAAa;AAEtB,YAAM,eAAe,oBAAoB,MAAM,MAAM;AAGrD,YAAM,OAAO;AAAA,QACX,IAAI,YAAY,kBAAkB;AAAA,UAChC,QAAQ;AAAA,YACN,OAAO;AAAA,YACP,eAAe,OAAO,WAAW,OAAO,SAAS,YAAY,IAAI;AAAA,YACjE,WAAW,OAAO,iBAAiB,OAAO,eAAe,YAAY,IAAI;AAAA,UAC3E;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,oBAAoB,OAAO,kBAAkB;AAC3C,UAAI,KAAK,YAAa;AAGtB,YAAM,QAAQ,iBAAiB,MAAM,MAAM;AAC3C,UAAI,QAAQ,GAAG;AACb,cAAM,OAAO,QAAQ,MAAM,QAAQ,CAAC,EAAE,QAAQ,KAAK,GAAG;AAAA,MACxD;AAAA,IACF;AAAA,IAEA,mBAAmB,OAAO,qBAAqB;AAC7C,UAAI,KAAK,YAAa;AAGtB,0BAAoB,MAAM,MAAM;AAAA,IAClC;AAAA,IAEA,sBAAsB,kBAAkB;AACtC,UAAI,KAAK,YAAa;AAEtB,YAAM,mBAAmB,SAAS;AAAA,QAChC;AAAA,MACF;AAEA,uBAAiB,QAAQ,CAAC,UAAU;AAClC,YAAI,CAAC,SAAS,KAAK,YAAa;AAGhC,cAAM,kBAAkB,MAAM;AAC9B,YAAI,iBAAiB;AACnB,gBAAM,oBAAoB,kBAAkB,eAAe;AAAA,QAC7D;AAGA,cAAM,UAAU,6BAAM,KAAK,sBAAsB,gBAAgB,GAAjD;AAChB,cAAM,yBAAyB;AAE/B,cAAM,iBAAiB,kBAAkB,SAAS,EAAE,SAAS,KAAK,CAAC;AAAA,MACrE,CAAC;AAAA,IACH;AAAA,IAEA,sBAAsB,kBAAkB;AACtC,UAAI,KAAK,eAAe,CAAC,OAAO,SAAU;AAE1C,UAAI,QAAQ,OAAO,SAAS,CAAC;AAC7B,eACG,iBAAiB,wDAAwD,EACzE,QAAQ,CAAC,UAAU;AAClB,YAAI,SAAS,MAAM,SAAS,CAAC,KAAK,aAAa;AAC7C,gBAAM,QAAQ,iBAAiB,KAAK;AACpC,kBAAQ,MAAM,IAAI,KAAK;AAAA,QACzB;AAAA,MACF,CAAC;AAGH,UAAI,CAAC,KAAK,aAAa;AACrB,iBAAS;AAAA,UACP,IAAI,YAAY,yBAAyB;AAAA,YACvC,QAAQ;AAAA,cACN,OAAO,MAAM;AAAA,cACb,WAAW,OAAO,iBAAiB,OAAO,eAAe,MAAM,KAAK,IAAI,MAAM;AAAA,YAChF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,IAEA,mBAAmB;AACjB,UAAI,KAAK,eAAe,KAAK,YAAa;AAG1C,UAAI;AACJ,YAAM,kBAAkB,6BAAM;AAC5B,YAAI,gBAAiB,cAAa,eAAe;AACjD,0BAAkB,WAAW,MAAM;AACjC,cAAI,CAAC,KAAK,aAAa;AACrB,iBAAK,yBAAyB;AAAA,UAChC;AAAA,QACF,GAAG,GAAG;AAAA,MACR,GAPwB;AASxB,UAAI,OAAO,SAAS;AAClB,eAAO,QAAQ,KAAK,MAAM;AACxB,cAAI,KAAK,YAAa;AAEtB,eAAK,cAAc,IAAI,iBAAiB,CAAC,cAAc;AACrD,gBAAI,KAAK,YAAa;AAEtB,gBAAI,eAAe;AACnB,sBAAU,QAAQ,CAAC,aAAa;AAC9B,kBAAI,SAAS,WAAW,QAAQ;AAE9B,2BAAW,QAAQ,SAAS,YAAY;AACtC,sBAAI,KAAK,aAAa,KAAK,cAAc;AACvC,wBACE,KAAK,QAAQ,0CAA0C,KACvD,KAAK,cAAc,0CAA0C,GAC7D;AACA,qCAAe;AACf;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAC;AAED,gBAAI,cAAc;AAChB,8BAAgB;AAAA,YAClB;AAAA,UACF,CAAC;AAED,eAAK,YAAY,QAAQ,SAAS,MAAM;AAAA,YACtC,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,cAAc;AAGnB,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,WAAW;AAC5B,aAAK,cAAc;AAAA,MACrB;AAGA,iBAAW,CAAC,SAAS,EAAE,OAAO,SAAS,CAAC,KAAK,KAAK,cAAc,QAAQ,GAAG;AACzE,YAAI,OAAO;AACT,gBAAM,oBAAoB,SAAS,SAAS,KAAK;AACjD,gBAAM,oBAAoB,SAAS,SAAS,KAAK;AACjD,gBAAM,oBAAoB,QAAQ,SAAS,IAAI;AAG/C,cAAI,MAAM,wBAAwB;AAChC,kBAAM,oBAAoB,kBAAkB,MAAM,sBAAsB;AACxE,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAGA,WAAK,cAAc,MAAM;AAGzB,UAAI,oBAAoB,CAAC,iBAAiB,aAAa;AACrD,yBAAiB,iBAAiB,qBAAqB;AAAA,MACzD;AAGA,UAAI,OAAO,mBAAmB;AAC5B,eAAO,OAAO;AAAA,MAChB;AACA,UAAI,OAAO,gBAAgB;AACzB,eAAO,OAAO;AAAA,MAChB;AAEA,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA,IAGA,eAAe;AACb,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;", "names": []}