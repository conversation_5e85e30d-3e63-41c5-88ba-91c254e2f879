{"version": 3, "sources": ["../../src/styles/dynamic-grid.css"], "sourcesContent": ["/**\r\n * Dynamic Grid CSS\r\n * Handles grid layout for dynamically visible patrimony items\r\n * Works with DynamicGridManager for proper border and corner styling\r\n */\r\n\r\n/* Base grid container */\r\n.patrimonio_interactive_content-wrapper {\r\n  border-radius: 30px;\r\n  container-type: inline-size;\r\n  overflow: hidden;\r\n  background-color: transparent;\r\n}\r\n\r\n/* Base grid item styling */\r\n.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item {\r\n  border: none;\r\n  border-radius: 0;\r\n  position: relative;\r\n  background-color: inherit;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* Default borders using pseudo-elements */\r\n.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before,\r\n.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {\r\n  content: '';\r\n  position: absolute;\r\n  background-color: rgba(0, 0, 0, 0.24);\r\n  z-index: 1;\r\n  pointer-events: none;\r\n}\r\n\r\n/* Right border */\r\n.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before {\r\n  top: 0;\r\n  right: 0;\r\n  width: 1px;\r\n  height: 100%;\r\n}\r\n\r\n/* Bottom border */\r\n.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 1px;\r\n}\r\n\r\n/* Grid layouts for different breakpoints */\r\n/* Desktop (991px+) - 3 columns */\r\n@media screen and (min-width: 991px) {\r\n  .patrimonio_interactive_content-wrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 0;\r\n  }\r\n}\r\n\r\n/* Tablet (768px - 990px) - 2 columns */\r\n@media screen and (max-width: 990px) and (min-width: 768px) {\r\n  .patrimonio_interactive_content-wrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 0;\r\n  }\r\n}\r\n\r\n/* Tablet Portrait (521px - 767px) - 2 columns */\r\n@media screen and (max-width: 767px) and (min-width: 521px) {\r\n  .patrimonio_interactive_content-wrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 0;\r\n  }\r\n}\r\n\r\n/* Mobile (up to 520px) - 1 column */\r\n@media screen and (max-width: 520px) {\r\n  .patrimonio_interactive_content-wrapper {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 0;\r\n  }\r\n}\r\n\r\n/* Dynamic position-based styling */\r\n/* Remove right border for last item in row */\r\n.patrimonio_interactive_item.grid-last-in-row::before {\r\n  display: none !important;\r\n}\r\n\r\n/* Remove bottom border for items in last row */\r\n.patrimonio_interactive_item.grid-last-row::after {\r\n  display: none !important;\r\n}\r\n\r\n/* Corner rounding for dynamic positions */\r\n.patrimonio_interactive_item.grid-top-left {\r\n  border-top-left-radius: 30px !important;\r\n}\r\n\r\n.patrimonio_interactive_item.grid-top-right {\r\n  border-top-right-radius: 30px !important;\r\n}\r\n\r\n.patrimonio_interactive_item.grid-bottom-left {\r\n  border-bottom-left-radius: 30px !important;\r\n}\r\n\r\n.patrimonio_interactive_item.grid-bottom-right {\r\n  border-bottom-right-radius: 30px !important;\r\n}\r\n\r\n/* Special case: single item */\r\n.patrimonio_interactive_item.grid-only-item {\r\n  border-radius: 30px !important;\r\n}\r\n\r\n.patrimonio_interactive_item.grid-only-item::before,\r\n.patrimonio_interactive_item.grid-only-item::after {\r\n  display: none !important;\r\n}\r\n\r\n/* Visibility transitions */\r\n.patrimonio_interactive_item {\r\n  opacity: 1;\r\n  transform: scale(1);\r\n  transition: opacity 0.3s ease, transform 0.3s ease, border-radius 0.3s ease;\r\n}\r\n\r\n/* Hidden state */\r\n.patrimonio_interactive_item.hidden {\r\n  display: none !important;\r\n}\r\n\r\n/* Animating state (used during hide/show transitions) */\r\n.patrimonio_interactive_item.animating-out {\r\n  opacity: 0;\r\n  transform: scale(0.95);\r\n}\r\n\r\n.patrimonio_interactive_item.animating-in {\r\n  opacity: 0;\r\n  transform: scale(0.95);\r\n}\r\n\r\n/* Hover effects */\r\n.patrimonio_interactive_item:hover {\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n/* Focus styles for accessibility */\r\n.patrimonio_interactive_item:focus-within {\r\n  outline: 2px solid rgba(59, 130, 246, 0.5);\r\n  outline-offset: -2px;\r\n  z-index: 2;\r\n}\r\n\r\n/* Ensure proper stacking during animations */\r\n.patrimonio_interactive_content-wrapper {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* Smooth scrollbar for the wrapper */\r\n.patrimonio_interactive_content-wrapper {\r\n  scrollbar-width: thin;\r\n  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;\r\n}\r\n\r\n.patrimonio_interactive_content-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  background: transparent;\r\n}\r\n\r\n.patrimonio_interactive_content-wrapper::-webkit-scrollbar-track {\r\n  background: rgba(0, 0, 0, 0.05);\r\n  border-radius: 10px;\r\n}\r\n\r\n.patrimonio_interactive_content-wrapper::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0.2) 100%);\r\n  border-radius: 10px;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.patrimonio_interactive_content-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.3) 100%);\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before,\r\n  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {\r\n    background-color: rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  .patrimonio_interactive_item.hidden {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n/* High contrast mode support */\r\n@media (prefers-contrast: high) {\r\n  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before,\r\n  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {\r\n    background-color: currentColor;\r\n    opacity: 0.3;\r\n  }\r\n}\r\n\r\n/* Reduced motion support */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .patrimonio_interactive_item,\r\n  .patrimonio_interactive_content-wrapper {\r\n    transition: none !important;\r\n  }\r\n}\r\n"], "mappings": ";AAOA,CAAC;AACC,iBAAe;AACf,kBAAgB;AAChB,YAAU;AACV,oBAAkB;AACpB;AAGA,CARC,uCAQuC,EAAE,CAAC;AACzC,UAAQ;AACR,iBAAe;AACf,YAAU;AACV,oBAAkB;AAClB,cAAY,IAAI,KAAK;AACvB;AAGA,CAjBC,uCAiBuC,EAAE,CATC,2BAS2B;AACtE,CAlBC,uCAkBuC,EAAE,CAVC,2BAU2B;AACpE,WAAS;AACT,YAAU;AACV,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,WAAS;AACT,kBAAgB;AAClB;AAGA,CA3BC,uCA2BuC,EAAE,CAnBC,2BAmB2B;AACpE,OAAK;AACL,SAAO;AACP,SAAO;AACP,UAAQ;AACV;AAGA,CAnCC,uCAmCuC,EAAE,CA3BC,2BA2B2B;AACpE,UAAQ;AACR,QAAM;AACN,SAAO;AACP,UAAQ;AACV;AAIA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GA7CD;AA8CG,aAAS;AACT,2BAAuB,OAAO,CAAC,EAAE;AACjC,SAAK;AACP;AACF;AAGA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,EAAE;AACnD,GAtDD;AAuDG,aAAS;AACT,2BAAuB,OAAO,CAAC,EAAE;AACjC,SAAK;AACP;AACF;AAGA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,EAAE;AACnD,GA/DD;AAgEG,aAAS;AACT,2BAAuB,OAAO,CAAC,EAAE;AACjC,SAAK;AACP;AACF;AAGA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GAxED;AAyEG,aAAS;AACT,2BAAuB;AACvB,SAAK;AACP;AACF;AAIA,CAzE2C,2BAyEf,CAAC,gBAAgB;AAC3C,WAAS;AACX;AAGA,CA9E2C,2BA8Ef,CAAC,aAAa;AACxC,WAAS;AACX;AAGA,CAnF2C,2BAmFf,CAAC;AAC3B,0BAAwB;AAC1B;AAEA,CAvF2C,2BAuFf,CAAC;AAC3B,2BAAyB;AAC3B;AAEA,CA3F2C,2BA2Ff,CAAC;AAC3B,6BAA2B;AAC7B;AAEA,CA/F2C,2BA+Ff,CAAC;AAC3B,8BAA4B;AAC9B;AAGA,CApG2C,2BAoGf,CAAC;AAC3B,iBAAe;AACjB;AAEA,CAxG2C,2BAwGf,CAJC,cAIc;AAC3C,CAzG2C,2BAyGf,CALC,cAKc;AACzC,WAAS;AACX;AAGA,CA9G2C;AA+GzC,WAAS;AACT,aAAW,MAAM;AACjB;AAAA,IAAY,QAAQ,KAAK,IAAI;AAAA,IAAE,UAAU,KAAK,IAAI;AAAA,IAAE,cAAc,KAAK;AACzE;AAGA,CArH2C,2BAqHf,CAAC;AAC3B,WAAS;AACX;AAGA,CA1H2C,2BA0Hf,CAAC;AAC3B,WAAS;AACT,aAAW,MAAM;AACnB;AAEA,CA/H2C,2BA+Hf,CAAC;AAC3B,WAAS;AACT,aAAW,MAAM;AACnB;AAGA,CArI2C,2BAqIf;AAC1B,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAClC;AAGA,CA1I2C,2BA0If;AAC1B,WAAS,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,kBAAgB;AAChB,WAAS;AACX;AAGA,CAzJC;AA0JC,YAAU;AACV,WAAS;AACX;AAGA,CA/JC;AAgKC,mBAAiB;AACjB,mBAAiB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;AACtC;AAEA,CApKC,sCAoKsC;AACrC,SAAO;AACP,cAAY;AACd;AAEA,CAzKC,sCAyKsC;AACrC,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,iBAAe;AACjB;AAEA,CA9KC,sCA8KsC;AACrC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAA5C;AAAA,MAAgD,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;AAC/E,iBAAe;AACf,cAAY,IAAI,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACjD;AAEA,CApLC,sCAoLsC,yBAAyB;AAC9D;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAA5C;AAAA,MAAgD,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;AACjF;AAGA,OAAO;AACL,GA1LD,uCA0LyC,EAAE,CAlLD,2BAkL6B;AAAA,EACtE,CA3LD,uCA2LyC,EAAE,CAnLD,2BAmL6B;AACpE,sBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAClC;AAEA,GAvLyC,2BAuLb,CAlED;AAmEzB,aAAS;AACX;AACF;AAGA,OAAO,CAAC,gBAAgB,EAAE;AACxB,GAtMD,uCAsMyC,EAAE,CA9LD,2BA8L6B;AAAA,EACtE,CAvMD,uCAuMyC,EAAE,CA/LD,2BA+L6B;AACpE,sBAAkB;AAClB,aAAS;AACX;AACF;AAGA,OAAO,CAAC,sBAAsB,EAAE;AAC9B,GAvMyC;AAAA,EAwMzC,CAhND;AAiNG,gBAAY;AACd;AACF;", "names": []}