{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/event-coordinator.js", "../../src/modules/patrimony-sync.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * EventCoordinator - Sistema centralizado CORRIGIDO para gerenciar eventos do input principal\r\n * Evita conflitos entre múltiplos módulos e memory leaks\r\n */\r\n\r\nexport class EventCoordinator {\r\n  constructor() {\r\n    this.input = null;\r\n    this.listeners = new Map();\r\n    this.isProcessing = false;\r\n    this.eventQueue = [];\r\n    this.boundHandlers = new Map(); // Para rastrear handlers bound\r\n    this.isDestroyed = false;\r\n\r\n    this.init();\r\n  }\r\n\r\n  init() {\r\n    // Aguarda o DOM estar pronto\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => this.findAndSetupInput());\r\n    } else {\r\n      this.findAndSetupInput();\r\n    }\r\n  }\r\n\r\n  findAndSetupInput() {\r\n    this.input = document.querySelector('[is-main=\"true\"]');\r\n    if (this.input && !this.isDestroyed) {\r\n      this.setupMainListeners();\r\n    }\r\n  }\r\n\r\n  setupMainListeners() {\r\n    if (!this.input || this.boundHandlers.has('main')) return;\r\n\r\n    // Cria handlers bound uma única vez\r\n    const inputHandler = (e) => this.handleInputEvent(e);\r\n    const focusHandler = (e) => this.processFocusEvent(e);\r\n    const blurHandler = (e) => this.processBlurEvent(e);\r\n    const changeHandler = (e) => this.processChangeEvent(e);\r\n\r\n    // Armazena referências para cleanup posterior\r\n    this.boundHandlers.set('main', {\r\n      input: inputHandler,\r\n      focus: focusHandler,\r\n      blur: blurHandler,\r\n      change: changeHandler,\r\n    });\r\n\r\n    // Adiciona listeners apenas uma vez\r\n    this.input.addEventListener('input', inputHandler, { passive: true });\r\n    this.input.addEventListener('focus', focusHandler, { passive: true });\r\n    this.input.addEventListener('blur', blurHandler, { passive: true });\r\n    this.input.addEventListener('change', changeHandler, { passive: true });\r\n  }\r\n\r\n  handleInputEvent(e) {\r\n    if (this.isProcessing || this.isDestroyed) {\r\n      return;\r\n    }\r\n\r\n    this.isProcessing = true;\r\n\r\n    // Usa requestAnimationFrame para melhor performance\r\n    requestAnimationFrame(() => {\r\n      this.processInputEvent(e);\r\n      this.isProcessing = false;\r\n\r\n      // Processa queue se houver\r\n      if (this.eventQueue.length > 0) {\r\n        const nextEvent = this.eventQueue.shift();\r\n        requestAnimationFrame(() => this.handleInputEvent(nextEvent));\r\n      }\r\n    });\r\n  }\r\n\r\n  // Registra um listener para um módulo específico\r\n  registerListener(moduleId, eventType, callback) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    // Remove listener anterior se existir (evita duplicação)\r\n    this.unregisterListener(moduleId, eventType);\r\n\r\n    if (!this.listeners.has(key)) {\r\n      this.listeners.set(key, []);\r\n    }\r\n\r\n    this.listeners.get(key).push(callback);\r\n  }\r\n\r\n  // Remove listener de um módulo\r\n  unregisterListener(moduleId, eventType, specificCallback = null) {\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    if (this.listeners.has(key)) {\r\n      if (specificCallback) {\r\n        const callbacks = this.listeners.get(key);\r\n        const index = callbacks.indexOf(specificCallback);\r\n        if (index > -1) {\r\n          callbacks.splice(index, 1);\r\n        }\r\n      } else {\r\n        // Remove todos os callbacks do módulo para este evento\r\n        this.listeners.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove todos os listeners de um módulo\r\n  unregisterModule(moduleId) {\r\n    const keysToRemove = [];\r\n    for (const key of this.listeners.keys()) {\r\n      if (key.startsWith(`${moduleId}_`)) {\r\n        keysToRemove.push(key);\r\n      }\r\n    }\r\n\r\n    keysToRemove.forEach((key) => this.listeners.delete(key));\r\n  }\r\n\r\n  processInputEvent(e) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const inputCallbacks = this.getCallbacksForEvent('input');\r\n\r\n    // Executa callbacks em ordem de prioridade\r\n    const priorityOrder = ['currency-formatting', 'motion-animation', 'patrimony-sync'];\r\n\r\n    for (const moduleId of priorityOrder) {\r\n      const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);\r\n      for (const callbackInfo of moduleCallbacks) {\r\n        try {\r\n          callbackInfo.callback(e);\r\n        } catch (error) {\r\n          console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  processFocusEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('focus', e);\r\n  }\r\n\r\n  processBlurEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('blur', e);\r\n  }\r\n\r\n  processChangeEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('change', e);\r\n  }\r\n\r\n  executeCallbacksForEvent(eventType, e) {\r\n    const callbacks = this.getCallbacksForEvent(eventType);\r\n    callbacks.forEach(({ callback, moduleId }) => {\r\n      try {\r\n        callback(e);\r\n      } catch (error) {\r\n        console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);\r\n      }\r\n    });\r\n  }\r\n\r\n  getCallbacksForEvent(eventType) {\r\n    const callbacks = [];\r\n    for (const [key, callbackList] of this.listeners.entries()) {\r\n      if (key.endsWith(`_${eventType}`)) {\r\n        const moduleId = key.replace(`_${eventType}`, '');\r\n        callbackList.forEach((callback) => {\r\n          callbacks.push({ moduleId, callback });\r\n        });\r\n      }\r\n    }\r\n    return callbacks;\r\n  }\r\n\r\n  // Método para disparar eventos programaticamente\r\n  dispatchInputEvent(sourceModule = 'unknown') {\r\n    if (this.isProcessing || this.isDestroyed || !this.input) {\r\n      return;\r\n    }\r\n\r\n    const event = new Event('input', { bubbles: true });\r\n    event.sourceModule = sourceModule;\r\n    this.input.dispatchEvent(event);\r\n  }\r\n\r\n  // Método para atualizar valor sem disparar eventos\r\n  setSilentValue(value) {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.isProcessing = true;\r\n    this.input.value = value;\r\n\r\n    // Usa requestAnimationFrame para reset mais confiável\r\n    requestAnimationFrame(() => {\r\n      this.isProcessing = false;\r\n    });\r\n  }\r\n\r\n  // Getter para o valor atual\r\n  getValue() {\r\n    return this.input ? this.input.value : '';\r\n  }\r\n\r\n  // Setter que dispara eventos controlados\r\n  setValue(value, sourceModule = 'unknown') {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.input.value = value;\r\n    this.dispatchInputEvent(sourceModule);\r\n  }\r\n\r\n  // Método de cleanup para prevenir memory leaks\r\n  destroy() {\r\n    this.isDestroyed = true;\r\n\r\n    // Remove todos os event listeners\r\n    if (this.input && this.boundHandlers.has('main')) {\r\n      const handlers = this.boundHandlers.get('main');\r\n      this.input.removeEventListener('input', handlers.input);\r\n      this.input.removeEventListener('focus', handlers.focus);\r\n      this.input.removeEventListener('blur', handlers.blur);\r\n      this.input.removeEventListener('change', handlers.change);\r\n    }\r\n\r\n    // Limpa todas as referências\r\n    this.listeners.clear();\r\n    this.boundHandlers.clear();\r\n    this.eventQueue.length = 0;\r\n    this.input = null;\r\n    this.isProcessing = false;\r\n  }\r\n\r\n  // Método para reinicializar se necessário\r\n  reinitialize() {\r\n    this.destroy();\r\n    this.isDestroyed = false;\r\n    this.init();\r\n  }\r\n}\r\n\r\n// Instância singleton\r\nexport const eventCoordinator = new EventCoordinator();\r\n\r\n// Cleanup automático quando a página é descarregada\r\nwindow.addEventListener('beforeunload', () => {\r\n  eventCoordinator.destroy();\r\n});\r\n", "import { eventCoordinator } from './event-coordinator.js';\r\n\r\n/**\r\n * Patrimony Sync System\r\n * Código original funcionando da pasta Modelo - Webflow\r\n * Usa EventCoordinator para evitar loops infinitos\r\n */\r\n\r\n// Cache Manager (from original)\r\nconst CacheManager = {\r\n  set(key, value) {\r\n    try {\r\n      window.localStorage.setItem(key, JSON.stringify(value));\r\n    } catch {\r\n      // Silent fail\r\n    }\r\n  },\r\n  get(key) {\r\n    try {\r\n      const value = window.localStorage.getItem(key);\r\n      return value ? JSON.parse(value) : null;\r\n    } catch {\r\n      return null;\r\n    }\r\n  },\r\n  remove(key) {\r\n    try {\r\n      window.localStorage.removeItem(key);\r\n    } catch {\r\n      // Silent fail\r\n    }\r\n  },\r\n};\r\n\r\n// Utils (from original)\r\nconst Utils = {\r\n  formatCurrency(value) {\r\n    return new Intl.NumberFormat('pt-BR', {\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2,\r\n    }).format(value);\r\n  },\r\n\r\n  parseCurrencyValue(value) {\r\n    if (!value || typeof value !== 'string') return 0;\r\n    const cleanValue = value.replace(/[^\\d,]/g, '').replace(',', '.');\r\n    return parseFloat(cleanValue) || 0;\r\n  },\r\n\r\n  calculatePercentage(value, total) {\r\n    if (!total || total === 0) return 0;\r\n    return (value / total) * 100;\r\n  },\r\n\r\n  formatPercentage(value) {\r\n    return `${value.toFixed(1)}%`;\r\n  },\r\n\r\n  debounce(func, wait) {\r\n    let timeout;\r\n    return function executedFunction(...args) {\r\n      const later = () => {\r\n        clearTimeout(timeout);\r\n        func(...args);\r\n      };\r\n      clearTimeout(timeout);\r\n      timeout = setTimeout(later, wait);\r\n    };\r\n  },\r\n};\r\n\r\n// PatrimonySync namespace (from original)\r\nconst PatrimonySync = {\r\n  mainValue: 0,\r\n  cacheKey: 'patrimony_main_value',\r\n  allocationsCacheKey: 'patrimony_allocations',\r\n  isInitialized: false,\r\n};\r\n\r\n// Main input synchronization (from original)\r\nconst MainInputSync = {\r\n  input: null,\r\n\r\n  init() {\r\n    this.input = document.querySelector('[is-main=\"true\"]');\r\n    if (!this.input) {\r\n      console.warn('Main input not found');\r\n      return;\r\n    }\r\n\r\n    // Load cached value\r\n    const cachedValue = CacheManager.get(PatrimonySync.cacheKey);\r\n    if (cachedValue !== null && cachedValue > 0) {\r\n      PatrimonySync.mainValue = cachedValue;\r\n      this.input.value = Utils.formatCurrency(cachedValue);\r\n    }\r\n\r\n    // Setup event listeners\r\n    this.setupListeners();\r\n  },\r\n\r\n  setupListeners() {\r\n    // Registra listeners no EventCoordinator em vez de adicionar diretamente\r\n    eventCoordinator.registerListener(\r\n      'patrimony-sync',\r\n      'input',\r\n      Utils.debounce((e) => {\r\n        const value = Utils.parseCurrencyValue(e.target.value);\r\n        this.handleValueChange(value);\r\n      }, 300)\r\n    );\r\n\r\n    eventCoordinator.registerListener('patrimony-sync', 'change', (e) => {\r\n      const value = Utils.parseCurrencyValue(e.target.value);\r\n      this.handleValueChange(value);\r\n    });\r\n\r\n    // Listen for currency change events (from existing currency system)\r\n    this.input.addEventListener('currencyChange', (e) => {\r\n      this.handleValueChange(e.detail.value);\r\n    });\r\n  },\r\n\r\n  handleValueChange(value) {\r\n    PatrimonySync.mainValue = value;\r\n    CacheManager.set(PatrimonySync.cacheKey, value);\r\n\r\n    // Dispatch custom event for other components\r\n    document.dispatchEvent(\r\n      new CustomEvent('patrimonyMainValueChanged', {\r\n        detail: {\r\n          value,\r\n          formatted: Utils.formatCurrency(value),\r\n        },\r\n      })\r\n    );\r\n\r\n    // Dispatch event for total patrimony changes (used by AtivosGraficoSync)\r\n    document.dispatchEvent(\r\n      new CustomEvent('totalPatrimonyChanged', {\r\n        detail: {\r\n          value,\r\n          formatted: Utils.formatCurrency(value),\r\n        },\r\n      })\r\n    );\r\n\r\n    // Update all section 3 allocations and validate\r\n    AllocationSync.updateAllAllocations();\r\n    AllocationSync.validateAllAllocations();\r\n  },\r\n\r\n  getValue() {\r\n    return PatrimonySync.mainValue;\r\n  },\r\n\r\n  setValue(value) {\r\n    PatrimonySync.mainValue = value;\r\n    if (this.input) {\r\n      this.input.value = Utils.formatCurrency(value);\r\n      this.input.dispatchEvent(new Event('input', { bubbles: true }));\r\n    }\r\n  },\r\n};\r\n\r\n// Section 3 allocation synchronization (from original)\r\nconst AllocationSync = {\r\n  items: [],\r\n\r\n  init() {\r\n    // Find all patrimonio interactive items\r\n    const containers = document.querySelectorAll('.patrimonio_interactive_item');\r\n\r\n    containers.forEach((container, index) => {\r\n      // Find elements within the correct structure\r\n      const activeItem = container.querySelector('.active-produto-item');\r\n      const disabledItem = container.querySelector('.disabled-produto-item');\r\n\r\n      if (!activeItem || !disabledItem) return;\r\n\r\n      const input = activeItem.querySelector('[input-settings=\"receive\"]');\r\n      const slider = activeItem.querySelector('range-slider');\r\n      const percentageDisplay = activeItem.querySelector('.porcentagem-calculadora');\r\n\r\n      // Find elements in disabled state\r\n      const valorProduto = disabledItem.querySelector('.valor-produto');\r\n      const percentageDisabled = disabledItem.querySelector('.porcentagem-calculadora-disabled');\r\n      const backgroundItemAcao = disabledItem.querySelector('.background-item-acao');\r\n\r\n      if (input && slider) {\r\n        const item = {\r\n          container,\r\n          activeItem,\r\n          disabledItem,\r\n          input,\r\n          slider,\r\n          percentageDisplay,\r\n          valorProduto,\r\n          percentageDisabled,\r\n          backgroundItemAcao,\r\n          index,\r\n          value: 0,\r\n          percentage: 0,\r\n          maxAllowed: 0,\r\n        };\r\n\r\n        this.items.push(item);\r\n        this.setupItemListeners(item);\r\n      }\r\n    });\r\n\r\n    // Load cached allocations\r\n    this.loadCachedAllocations();\r\n  },\r\n\r\n  setupItemListeners(item) {\r\n    // Input change listener with validation\r\n    item.input.addEventListener('currencyChange', (e) => {\r\n      this.handleInputChange(item, e.detail.value);\r\n    });\r\n\r\n    item.input.addEventListener(\r\n      'input',\r\n      Utils.debounce((e) => {\r\n        const value = Utils.parseCurrencyValue(e.target.value);\r\n        this.handleInputChange(item, value);\r\n      }, 300)\r\n    );\r\n\r\n    // Slider change listener with validation\r\n    item.slider.addEventListener('input', (e) => {\r\n      this.handleSliderChange(item, parseFloat(e.target.value));\r\n    });\r\n\r\n    // Focus/blur for better UX\r\n    item.input.addEventListener('focus', () => {\r\n      item.container.classList.add('input-focused');\r\n      this.updateMaxAllowed(item);\r\n    });\r\n\r\n    item.input.addEventListener('blur', () => {\r\n      item.container.classList.remove('input-focused');\r\n      // Final validation on blur\r\n      this.validateAllocation(item);\r\n    });\r\n  },\r\n\r\n  handleInputChange(item, value) {\r\n    const mainValue = MainInputSync.getValue();\r\n\r\n    // Calculate max allowed for this item\r\n    const otherAllocations = this.getTotalAllocatedExcept(item);\r\n    const maxAllowed = Math.max(0, mainValue - otherAllocations);\r\n\r\n    // Validate and cap the value\r\n    if (value > maxAllowed) {\r\n      value = maxAllowed;\r\n      item.input.value = Utils.formatCurrency(value);\r\n      VisualFeedback.showAllocationWarning(\r\n        item.container,\r\n        `Valor máximo disponível: R$ ${Utils.formatCurrency(maxAllowed)}`\r\n      );\r\n    }\r\n\r\n    item.value = value;\r\n    item.percentage = Utils.calculatePercentage(value, mainValue);\r\n    item.maxAllowed = maxAllowed;\r\n\r\n    // Update all displays\r\n    this.updateSlider(item);\r\n    this.updatePercentageDisplay(item);\r\n    this.updateValorProduto(item);\r\n    this.updateBackgroundItemAcao(item);\r\n\r\n    // Save to cache\r\n    this.saveAllocations();\r\n\r\n    // Dispatch event\r\n    this.dispatchAllocationChange(item);\r\n\r\n    // Check total allocation status\r\n    this.checkTotalAllocationStatus();\r\n  },\r\n\r\n  handleSliderChange(item, sliderValue) {\r\n    const mainValue = MainInputSync.getValue();\r\n    let value = mainValue * sliderValue;\r\n\r\n    // Validate against max allowed\r\n    const otherAllocations = this.getTotalAllocatedExcept(item);\r\n    const maxAllowed = Math.max(0, mainValue - otherAllocations);\r\n\r\n    if (value > maxAllowed) {\r\n      value = maxAllowed;\r\n      // Update slider to reflect the capped value\r\n      const cappedSliderValue = mainValue > 0 ? value / mainValue : 0;\r\n      item.slider.value = cappedSliderValue;\r\n      VisualFeedback.showAllocationWarning(\r\n        item.container,\r\n        `Valor máximo disponível: R$ ${Utils.formatCurrency(maxAllowed)}`\r\n      );\r\n    }\r\n\r\n    item.value = value;\r\n    item.percentage = value > 0 && mainValue > 0 ? (value / mainValue) * 100 : 0;\r\n    item.maxAllowed = maxAllowed;\r\n\r\n    // Update displays\r\n    item.input.value = Utils.formatCurrency(value);\r\n    this.updatePercentageDisplay(item);\r\n    this.updateValorProduto(item);\r\n    this.updateBackgroundItemAcao(item);\r\n\r\n    // Save to cache\r\n    this.saveAllocations();\r\n\r\n    // Dispatch event\r\n    this.dispatchAllocationChange(item);\r\n\r\n    // Check total allocation status\r\n    this.checkTotalAllocationStatus();\r\n  },\r\n\r\n  updateSlider(item) {\r\n    const mainValue = MainInputSync.getValue();\r\n    if (mainValue > 0) {\r\n      const sliderValue = item.value / mainValue;\r\n      item.slider.value = Math.min(1, Math.max(0, sliderValue));\r\n    } else {\r\n      item.slider.value = 0;\r\n    }\r\n  },\r\n\r\n  updatePercentageDisplay(item) {\r\n    const formattedPercentage = Utils.formatPercentage(item.percentage);\r\n\r\n    // Update active percentage display\r\n    if (item.percentageDisplay) {\r\n      item.percentageDisplay.textContent = formattedPercentage;\r\n    }\r\n\r\n    // Update disabled percentage display\r\n    if (item.percentageDisabled) {\r\n      item.percentageDisabled.textContent = formattedPercentage;\r\n    }\r\n  },\r\n\r\n  updateValorProduto(item) {\r\n    if (item.valorProduto) {\r\n      item.valorProduto.textContent = Utils.formatCurrency(item.value);\r\n    }\r\n  },\r\n\r\n  updateBackgroundItemAcao(item) {\r\n    if (item.backgroundItemAcao && window.Motion) {\r\n      const { animate } = window.Motion;\r\n      const widthPercentage = Math.max(0, Math.min(100, item.percentage));\r\n\r\n      animate(\r\n        item.backgroundItemAcao,\r\n        {\r\n          width: `${widthPercentage}%`,\r\n        },\r\n        {\r\n          duration: 0.5,\r\n          easing: 'ease-out',\r\n        }\r\n      );\r\n    }\r\n  },\r\n\r\n  updateMaxAllowed(item) {\r\n    const mainValue = MainInputSync.getValue();\r\n    const otherAllocations = this.getTotalAllocatedExcept(item);\r\n    item.maxAllowed = Math.max(0, mainValue - otherAllocations);\r\n  },\r\n\r\n  validateAllocation(item) {\r\n    const mainValue = MainInputSync.getValue();\r\n    const otherAllocations = this.getTotalAllocatedExcept(item);\r\n    const maxAllowed = Math.max(0, mainValue - otherAllocations);\r\n\r\n    if (item.value > maxAllowed) {\r\n      item.value = maxAllowed;\r\n      item.input.value = Utils.formatCurrency(maxAllowed);\r\n      this.updateSlider(item);\r\n      this.updatePercentageDisplay(item);\r\n      this.updateValorProduto(item);\r\n      this.updateBackgroundItemAcao(item);\r\n      this.saveAllocations();\r\n    }\r\n  },\r\n\r\n  validateAllAllocations() {\r\n    const mainValue = MainInputSync.getValue();\r\n    const total = this.getTotalAllocated();\r\n\r\n    if (total > mainValue) {\r\n      // Proportionally reduce all allocations\r\n      const ratio = mainValue / total;\r\n      this.items.forEach((item) => {\r\n        const newValue = item.value * ratio;\r\n        item.value = newValue;\r\n        item.percentage = Utils.calculatePercentage(newValue, mainValue);\r\n        item.input.value = Utils.formatCurrency(newValue);\r\n        this.updateSlider(item);\r\n        this.updatePercentageDisplay(item);\r\n        this.updateValorProduto(item);\r\n        this.updateBackgroundItemAcao(item);\r\n      });\r\n      this.saveAllocations();\r\n    }\r\n  },\r\n\r\n  updateAllAllocations() {\r\n    const mainValue = MainInputSync.getValue();\r\n\r\n    this.items.forEach((item) => {\r\n      // Update max allowed for each item\r\n      this.updateMaxAllowed(item);\r\n\r\n      // Recalculate percentage based on current value\r\n      if (mainValue > 0) {\r\n        item.percentage = Utils.calculatePercentage(item.value, mainValue);\r\n        this.updateSlider(item);\r\n        this.updatePercentageDisplay(item);\r\n        this.updateValorProduto(item);\r\n        this.updateBackgroundItemAcao(item);\r\n      } else {\r\n        // Reset if main value is 0\r\n        item.value = 0;\r\n        item.percentage = 0;\r\n        item.input.value = Utils.formatCurrency(0);\r\n        item.slider.value = 0;\r\n        this.updatePercentageDisplay(item);\r\n        this.updateValorProduto(item);\r\n        this.updateBackgroundItemAcao(item);\r\n      }\r\n    });\r\n  },\r\n\r\n  checkTotalAllocationStatus() {\r\n    const mainValue = MainInputSync.getValue();\r\n    const total = this.getTotalAllocated();\r\n    const remaining = mainValue - total;\r\n\r\n    document.dispatchEvent(\r\n      new CustomEvent('allocationStatusChanged', {\r\n        detail: {\r\n          mainValue,\r\n          totalAllocated: total,\r\n          remaining,\r\n          isFullyAllocated: remaining === 0,\r\n          isOverAllocated: remaining < 0,\r\n          percentageAllocated: mainValue > 0 ? (total / mainValue) * 100 : 0,\r\n          remainingPercentage: mainValue > 0 ? (remaining / mainValue) * 100 : 0,\r\n        },\r\n      })\r\n    );\r\n  },\r\n\r\n  getTotalAllocated() {\r\n    return this.items.reduce((sum, item) => sum + item.value, 0);\r\n  },\r\n\r\n  getTotalAllocatedExcept(excludeItem) {\r\n    return this.items.reduce((sum, item) => {\r\n      return item === excludeItem ? sum : sum + item.value;\r\n    }, 0);\r\n  },\r\n\r\n  getRemainingValue() {\r\n    const mainValue = MainInputSync.getValue();\r\n    const totalAllocated = this.getTotalAllocated();\r\n    return Math.max(0, mainValue - totalAllocated);\r\n  },\r\n\r\n  saveAllocations() {\r\n    const allocations = this.items.map((item) => ({\r\n      index: item.index,\r\n      value: item.value,\r\n      percentage: item.percentage,\r\n    }));\r\n\r\n    CacheManager.set(PatrimonySync.allocationsCacheKey, allocations);\r\n  },\r\n\r\n  loadCachedAllocations() {\r\n    const cached = CacheManager.get(PatrimonySync.allocationsCacheKey);\r\n    if (!cached || !Array.isArray(cached)) return;\r\n\r\n    cached.forEach((cachedItem) => {\r\n      const item = this.items.find((i) => i.index === cachedItem.index);\r\n      if (item) {\r\n        item.value = cachedItem.value;\r\n        item.percentage = cachedItem.percentage;\r\n        item.input.value = Utils.formatCurrency(item.value);\r\n        this.updateSlider(item);\r\n        this.updatePercentageDisplay(item);\r\n        this.updateValorProduto(item);\r\n        this.updateBackgroundItemAcao(item);\r\n\r\n        // Dispatch allocation change event for visual sync systems\r\n        this.dispatchAllocationChange(item);\r\n      }\r\n    });\r\n\r\n    // Validate after loading\r\n    this.validateAllAllocations();\r\n\r\n    // Dispatch event to notify other systems that cache loading is complete\r\n    document.dispatchEvent(\r\n      new CustomEvent('patrimonySystemReady', {\r\n        detail: {\r\n          cacheLoaded: true,\r\n          itemsCount: cached.length,\r\n        },\r\n      })\r\n    );\r\n\r\n    // console.log(`📦 Cache loaded: ${cached.length} allocations restored`);\r\n  },\r\n\r\n  dispatchAllocationChange(item) {\r\n    // Extract category and product from container attributes\r\n    const category = item.container.getAttribute('ativo-category') || '';\r\n    const product = item.container.getAttribute('ativo-product') || '';\r\n\r\n    document.dispatchEvent(\r\n      new CustomEvent('allocationChanged', {\r\n        detail: {\r\n          index: item.index,\r\n          category: category,\r\n          product: product,\r\n          value: item.value,\r\n          percentage: item.percentage,\r\n          formatted: Utils.formatCurrency(item.value),\r\n          remaining: this.getRemainingValue(),\r\n        },\r\n      })\r\n    );\r\n\r\n    // Also dispatch patrimonyValueChanged for resultado-sync\r\n    document.dispatchEvent(\r\n      new CustomEvent('patrimonyValueChanged', {\r\n        detail: {\r\n          key: `${category}-${product}`,\r\n          category: category,\r\n          product: product,\r\n          value: item.value,\r\n          percentage: item.percentage,\r\n        },\r\n      })\r\n    );\r\n  },\r\n};\r\n\r\n// Visual feedback system (from original)\r\nconst VisualFeedback = {\r\n  showAllocationWarning(container, message) {\r\n    let warning = container.querySelector('.allocation-warning');\r\n\r\n    if (!warning) {\r\n      warning = document.createElement('div');\r\n      warning.className = 'allocation-warning';\r\n      warning.style.cssText = `\r\n        color: #ef4444;\r\n        font-size: 0.875rem;\r\n        margin-top: 0.5rem;\r\n        opacity: 0;\r\n        transition: opacity 0.3s ease;\r\n        position: absolute;\r\n        background: white;\r\n        padding: 0.5rem;\r\n        border-radius: 0.25rem;\r\n        box-shadow: 0 2px 10px rgba(239, 68, 68, 0.2);\r\n        z-index: 10;\r\n      `;\r\n      container.style.position = 'relative';\r\n      container.appendChild(warning);\r\n    }\r\n\r\n    warning.textContent = message;\r\n    warning.style.opacity = '1';\r\n\r\n    // Position the warning\r\n    const input = container.querySelector('input');\r\n    if (input) {\r\n      const rect = input.getBoundingClientRect();\r\n      warning.style.top = `${input.offsetTop + rect.height + 5}px`;\r\n      warning.style.left = `${input.offsetLeft}px`;\r\n    }\r\n\r\n    // Auto hide\r\n    setTimeout(() => {\r\n      warning.style.opacity = '0';\r\n    }, 3000);\r\n\r\n    // Add limit reached styling to input\r\n    if (input) {\r\n      input.style.borderColor = '#ef4444';\r\n      setTimeout(() => {\r\n        input.style.borderColor = '';\r\n      }, 3000);\r\n    }\r\n  },\r\n};\r\n\r\n// Main PatrimonySyncSystem class for module export\r\nexport class PatrimonySyncSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => {\r\n        this.initialize();\r\n      });\r\n    } else {\r\n      this.initialize();\r\n    }\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  initialize() {\r\n    // Check for dependencies\r\n    if (!window.currency) {\r\n      console.error('Currency.js is required for PatrimonySync');\r\n      return;\r\n    }\r\n\r\n    // Wait for Motion.js to be available\r\n    const waitForMotion = () => {\r\n      if (window.Motion) {\r\n        this.initializeComponents();\r\n      } else {\r\n        setTimeout(waitForMotion, 50);\r\n      }\r\n    };\r\n    waitForMotion();\r\n  }\r\n\r\n  initializeComponents() {\r\n    // Initialize main input\r\n    MainInputSync.init();\r\n\r\n    // Wait a bit for dynamic content to load\r\n    setTimeout(() => {\r\n      AllocationSync.init();\r\n      PatrimonySync.isInitialized = true;\r\n\r\n      // Initial status check\r\n      AllocationSync.checkTotalAllocationStatus();\r\n\r\n      // Dispatch ready event\r\n      document.dispatchEvent(\r\n        new CustomEvent('patrimonySyncReady', {\r\n          detail: {\r\n            mainValue: this.getMainValue(),\r\n            totalAllocated: this.getTotalAllocated(),\r\n            remaining: this.getRemainingValue(),\r\n          },\r\n        })\r\n      );\r\n\r\n      // Garante que AllocationSync esteja exposto globalmente após inicialização\r\n      if (typeof window !== 'undefined') {\r\n        window.getAllocationSync = () => AllocationSync;\r\n        console.warn('✅ AllocationSync exposto globalmente');\r\n      }\r\n\r\n      // Atualiza os valores do Webflow imediatamente após a inicialização\r\n      this.updateWebflowPatrimonyDisplay();\r\n    }, 100);\r\n\r\n    // Atualiza valores do Webflow na inicialização\r\n    this.updateWebflowPatrimonyDisplay();\r\n\r\n    // Atualiza valores do Webflow sempre que o status mudar\r\n    document.addEventListener('patrimonyMainValueChanged', () => {\r\n      this.updateWebflowPatrimonyDisplay();\r\n    });\r\n    document.addEventListener('allocationChanged', () => {\r\n      this.updateWebflowPatrimonyDisplay();\r\n    });\r\n    document.addEventListener('allocationStatusChanged', () => {\r\n      this.updateWebflowPatrimonyDisplay();\r\n    });\r\n  }\r\n\r\n  // Atualiza os valores do Webflow (patrimonio_money_wrapper e header-and-patrimonio)\r\n  updateWebflowPatrimonyDisplay() {\r\n    const mainValue = this.getMainValue();\r\n    const formattedValue = Utils.formatCurrency(mainValue);\r\n\r\n    // Atualiza o valor restante\r\n    const restanteEl = document.querySelector('.patrimonio_money_wrapper .patrimonio-restante');\r\n    if (restanteEl) {\r\n      restanteEl.textContent = Utils.formatCurrency(this.getRemainingValue());\r\n    }\r\n\r\n    // Atualiza o valor total na seção principal\r\n    const totalEl = document.querySelector('.patrimonio_money_wrapper .patrimonio-total-value');\r\n    if (totalEl) {\r\n      totalEl.textContent = formattedValue;\r\n    }\r\n\r\n    // Atualiza o valor total no header da seção de resultados (header-and-patrimonio)\r\n    const headerTotalEl = document.querySelector('[data-patrimonio-total=\"true\"]');\r\n    if (headerTotalEl) {\r\n      headerTotalEl.textContent = formattedValue;\r\n    }\r\n\r\n    // Atualiza todos os elementos com classe patrimonio-total-value (fallback)\r\n    const allTotalElements = document.querySelectorAll('.patrimonio-total-value');\r\n    allTotalElements.forEach((el) => {\r\n      el.textContent = formattedValue;\r\n    });\r\n\r\n    // Atualiza a porcentagem do valor restante - busca em todo o documento\r\n    const porcentagemRestanteElements = document.querySelectorAll('.porcentagem-restante');\r\n    if (porcentagemRestanteElements.length > 0) {\r\n      const mainValue = this.getMainValue();\r\n      const restante = this.getRemainingValue();\r\n      const percent = mainValue > 0 ? (restante / mainValue) * 100 : 0;\r\n      const formattedPercent = Utils.formatPercentage(percent);\r\n\r\n      // Atualiza todos os elementos com a classe porcentagem-restante\r\n      porcentagemRestanteElements.forEach((el) => {\r\n        el.textContent = formattedPercent;\r\n      });\r\n    }\r\n  }\r\n\r\n  // Public API methods\r\n  getMainValue() {\r\n    return MainInputSync.getValue();\r\n  }\r\n\r\n  setMainValue(value) {\r\n    MainInputSync.setValue(value);\r\n  }\r\n\r\n  getTotalAllocated() {\r\n    return AllocationSync.getTotalAllocated();\r\n  }\r\n\r\n  getRemainingValue() {\r\n    return AllocationSync.getRemainingValue();\r\n  }\r\n\r\n  getAllocations() {\r\n    return AllocationSync.items.map((item) => ({\r\n      index: item.index,\r\n      value: item.value,\r\n      percentage: item.percentage,\r\n      formatted: Utils.formatCurrency(item.value),\r\n      maxAllowed: item.maxAllowed,\r\n    }));\r\n  }\r\n\r\n  reset() {\r\n    CacheManager.remove(PatrimonySync.cacheKey);\r\n    CacheManager.remove(PatrimonySync.allocationsCacheKey);\r\n\r\n    // Also clear asset selection cache for complete reset\r\n    CacheManager.remove('asset_selection_filter');\r\n\r\n    // Clear resultado sync cache\r\n    CacheManager.remove('resultado_sync_data');\r\n\r\n    MainInputSync.setValue(0);\r\n    AllocationSync.items.forEach((item) => {\r\n      item.value = 0;\r\n      item.percentage = 0;\r\n      item.maxAllowed = 0;\r\n      item.input.value = Utils.formatCurrency(0);\r\n      item.slider.value = 0;\r\n      AllocationSync.updatePercentageDisplay(item);\r\n      AllocationSync.updateValorProduto(item);\r\n      AllocationSync.updateBackgroundItemAcao(item);\r\n    });\r\n\r\n    AllocationSync.checkTotalAllocationStatus();\r\n\r\n    // Dispatch event to notify asset selection system about the reset\r\n    document.dispatchEvent(\r\n      new CustomEvent('patrimonySyncReset', {\r\n        detail: {\r\n          timestamp: Date.now(),\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  destroy() {\r\n    this.isInitialized = false;\r\n    AllocationSync.items = [];\r\n    MainInputSync.input = null;\r\n  }\r\n\r\n  // Expõe AllocationSync para integração externa\r\n  getAllocationSync() {\r\n    return AllocationSync;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACKtF,MAAM,mBAAN,MAAuB;AAAA,IAL9B,OAK8B;AAAA;AAAA;AAAA,IAC5B,cAAc;AACZ,WAAK,QAAQ;AACb,WAAK,YAAY,oBAAI,IAAI;AACzB,WAAK,eAAe;AACpB,WAAK,aAAa,CAAC;AACnB,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,cAAc;AAEnB,WAAK,KAAK;AAAA,IACZ;AAAA,IAEA,OAAO;AAEL,UAAI,SAAS,eAAe,WAAW;AACrC,iBAAS,iBAAiB,oBAAoB,MAAM,KAAK,kBAAkB,CAAC;AAAA,MAC9E,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,WAAK,QAAQ,SAAS,cAAc,kBAAkB;AACtD,UAAI,KAAK,SAAS,CAAC,KAAK,aAAa;AACnC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,IAEA,qBAAqB;AACnB,UAAI,CAAC,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,EAAG;AAGnD,YAAM,eAAe,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACrB,YAAM,eAAe,wBAAC,MAAM,KAAK,kBAAkB,CAAC,GAA/B;AACrB,YAAM,cAAc,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACpB,YAAM,gBAAgB,wBAAC,MAAM,KAAK,mBAAmB,CAAC,GAAhC;AAGtB,WAAK,cAAc,IAAI,QAAQ;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAGD,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,QAAQ,aAAa,EAAE,SAAS,KAAK,CAAC;AAClE,WAAK,MAAM,iBAAiB,UAAU,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,IACxE;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,gBAAgB,KAAK,aAAa;AACzC;AAAA,MACF;AAEA,WAAK,eAAe;AAGpB,4BAAsB,MAAM;AAC1B,aAAK,kBAAkB,CAAC;AACxB,aAAK,eAAe;AAGpB,YAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,gBAAM,YAAY,KAAK,WAAW,MAAM;AACxC,gCAAsB,MAAM,KAAK,iBAAiB,SAAS,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,iBAAiB,UAAU,WAAW,UAAU;AAC9C,UAAI,KAAK,YAAa;AAEtB,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAGpC,WAAK,mBAAmB,UAAU,SAAS;AAE3C,UAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,aAAK,UAAU,IAAI,KAAK,CAAC,CAAC;AAAA,MAC5B;AAEA,WAAK,UAAU,IAAI,GAAG,EAAE,KAAK,QAAQ;AAAA,IACvC;AAAA;AAAA,IAGA,mBAAmB,UAAU,WAAW,mBAAmB,MAAM;AAC/D,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAEpC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,YAAI,kBAAkB;AACpB,gBAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,gBAAM,QAAQ,UAAU,QAAQ,gBAAgB;AAChD,cAAI,QAAQ,IAAI;AACd,sBAAU,OAAO,OAAO,CAAC;AAAA,UAC3B;AAAA,QACF,OAAO;AAEL,eAAK,UAAU,OAAO,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,iBAAiB,UAAU;AACzB,YAAM,eAAe,CAAC;AACtB,iBAAW,OAAO,KAAK,UAAU,KAAK,GAAG;AACvC,YAAI,IAAI,WAAW,GAAG,QAAQ,GAAG,GAAG;AAClC,uBAAa,KAAK,GAAG;AAAA,QACvB;AAAA,MACF;AAEA,mBAAa,QAAQ,CAAC,QAAQ,KAAK,UAAU,OAAO,GAAG,CAAC;AAAA,IAC1D;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AAEtB,YAAM,iBAAiB,KAAK,qBAAqB,OAAO;AAGxD,YAAM,gBAAgB,CAAC,uBAAuB,oBAAoB,gBAAgB;AAElF,iBAAW,YAAY,eAAe;AACpC,cAAM,kBAAkB,eAAe,OAAO,CAAC,OAAO,GAAG,aAAa,QAAQ;AAC9E,mBAAW,gBAAgB,iBAAiB;AAC1C,cAAI;AACF,yBAAa,SAAS,CAAC;AAAA,UACzB,SAAS,OAAO;AACd,oBAAQ,MAAM,8BAA8B,QAAQ,cAAc,KAAK;AAAA,UACzE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,SAAS,CAAC;AAAA,IAC1C;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,QAAQ,CAAC;AAAA,IACzC;AAAA,IAEA,mBAAmB,GAAG;AACpB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,UAAU,CAAC;AAAA,IAC3C;AAAA,IAEA,yBAAyB,WAAW,GAAG;AACrC,YAAM,YAAY,KAAK,qBAAqB,SAAS;AACrD,gBAAU,QAAQ,CAAC,EAAE,UAAU,SAAS,MAAM;AAC5C,YAAI;AACF,mBAAS,CAAC;AAAA,QACZ,SAAS,OAAO;AACd,kBAAQ,MAAM,8BAA8B,QAAQ,IAAI,SAAS,cAAc,KAAK;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,qBAAqB,WAAW;AAC9B,YAAM,YAAY,CAAC;AACnB,iBAAW,CAAC,KAAK,YAAY,KAAK,KAAK,UAAU,QAAQ,GAAG;AAC1D,YAAI,IAAI,SAAS,IAAI,SAAS,EAAE,GAAG;AACjC,gBAAM,WAAW,IAAI,QAAQ,IAAI,SAAS,IAAI,EAAE;AAChD,uBAAa,QAAQ,CAAC,aAAa;AACjC,sBAAU,KAAK,EAAE,UAAU,SAAS,CAAC;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,mBAAmB,eAAe,WAAW;AAC3C,UAAI,KAAK,gBAAgB,KAAK,eAAe,CAAC,KAAK,OAAO;AACxD;AAAA,MACF;AAEA,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC;AAClD,YAAM,eAAe;AACrB,WAAK,MAAM,cAAc,KAAK;AAAA,IAChC;AAAA;AAAA,IAGA,eAAe,OAAO;AACpB,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,eAAe;AACpB,WAAK,MAAM,QAAQ;AAGnB,4BAAsB,MAAM;AAC1B,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,WAAW;AACT,aAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ;AAAA,IACzC;AAAA;AAAA,IAGA,SAAS,OAAO,eAAe,WAAW;AACxC,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,YAAY;AAAA,IACtC;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,cAAc;AAGnB,UAAI,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,GAAG;AAChD,cAAM,WAAW,KAAK,cAAc,IAAI,MAAM;AAC9C,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,QAAQ,SAAS,IAAI;AACpD,aAAK,MAAM,oBAAoB,UAAU,SAAS,MAAM;AAAA,MAC1D;AAGA,WAAK,UAAU,MAAM;AACrB,WAAK,cAAc,MAAM;AACzB,WAAK,WAAW,SAAS;AACzB,WAAK,QAAQ;AACb,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,eAAe;AACb,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAGO,MAAM,mBAAmB,IAAI,iBAAiB;AAGrD,SAAO,iBAAiB,gBAAgB,MAAM;AAC5C,qBAAiB,QAAQ;AAAA,EAC3B,CAAC;;;ACrPD,MAAM,eAAe;AAAA,IACnB,IAAI,KAAK,OAAO;AACd,UAAI;AACF,eAAO,aAAa,QAAQ,KAAK,KAAK,UAAU,KAAK,CAAC;AAAA,MACxD,QAAQ;AAAA,MAER;AAAA,IACF;AAAA,IACA,IAAI,KAAK;AACP,UAAI;AACF,cAAM,QAAQ,OAAO,aAAa,QAAQ,GAAG;AAC7C,eAAO,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,MACrC,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,OAAO,KAAK;AACV,UAAI;AACF,eAAO,aAAa,WAAW,GAAG;AAAA,MACpC,QAAQ;AAAA,MAER;AAAA,IACF;AAAA,EACF;AAGA,MAAM,QAAQ;AAAA,IACZ,eAAe,OAAO;AACpB,aAAO,IAAI,KAAK,aAAa,SAAS;AAAA,QACpC,uBAAuB;AAAA,QACvB,uBAAuB;AAAA,MACzB,CAAC,EAAE,OAAO,KAAK;AAAA,IACjB;AAAA,IAEA,mBAAmB,OAAO;AACxB,UAAI,CAAC,SAAS,OAAO,UAAU,SAAU,QAAO;AAChD,YAAM,aAAa,MAAM,QAAQ,WAAW,EAAE,EAAE,QAAQ,KAAK,GAAG;AAChE,aAAO,WAAW,UAAU,KAAK;AAAA,IACnC;AAAA,IAEA,oBAAoB,OAAO,OAAO;AAChC,UAAI,CAAC,SAAS,UAAU,EAAG,QAAO;AAClC,aAAQ,QAAQ,QAAS;AAAA,IAC3B;AAAA,IAEA,iBAAiB,OAAO;AACtB,aAAO,GAAG,MAAM,QAAQ,CAAC,CAAC;AAAA,IAC5B;AAAA,IAEA,SAAS,MAAM,MAAM;AACnB,UAAI;AACJ,aAAO,gCAAS,oBAAoB,MAAM;AACxC,cAAM,QAAQ,6BAAM;AAClB,uBAAa,OAAO;AACpB,eAAK,GAAG,IAAI;AAAA,QACd,GAHc;AAId,qBAAa,OAAO;AACpB,kBAAU,WAAW,OAAO,IAAI;AAAA,MAClC,GAPO;AAAA,IAQT;AAAA,EACF;AAGA,MAAM,gBAAgB;AAAA,IACpB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,eAAe;AAAA,EACjB;AAGA,MAAM,gBAAgB;AAAA,IACpB,OAAO;AAAA,IAEP,OAAO;AACL,WAAK,QAAQ,SAAS,cAAc,kBAAkB;AACtD,UAAI,CAAC,KAAK,OAAO;AACf,gBAAQ,KAAK,sBAAsB;AACnC;AAAA,MACF;AAGA,YAAM,cAAc,aAAa,IAAI,cAAc,QAAQ;AAC3D,UAAI,gBAAgB,QAAQ,cAAc,GAAG;AAC3C,sBAAc,YAAY;AAC1B,aAAK,MAAM,QAAQ,MAAM,eAAe,WAAW;AAAA,MACrD;AAGA,WAAK,eAAe;AAAA,IACtB;AAAA,IAEA,iBAAiB;AAEf,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA,MAAM,SAAS,CAAC,MAAM;AACpB,gBAAM,QAAQ,MAAM,mBAAmB,EAAE,OAAO,KAAK;AACrD,eAAK,kBAAkB,KAAK;AAAA,QAC9B,GAAG,GAAG;AAAA,MACR;AAEA,uBAAiB,iBAAiB,kBAAkB,UAAU,CAAC,MAAM;AACnE,cAAM,QAAQ,MAAM,mBAAmB,EAAE,OAAO,KAAK;AACrD,aAAK,kBAAkB,KAAK;AAAA,MAC9B,CAAC;AAGD,WAAK,MAAM,iBAAiB,kBAAkB,CAAC,MAAM;AACnD,aAAK,kBAAkB,EAAE,OAAO,KAAK;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,IAEA,kBAAkB,OAAO;AACvB,oBAAc,YAAY;AAC1B,mBAAa,IAAI,cAAc,UAAU,KAAK;AAG9C,eAAS;AAAA,QACP,IAAI,YAAY,6BAA6B;AAAA,UAC3C,QAAQ;AAAA,YACN;AAAA,YACA,WAAW,MAAM,eAAe,KAAK;AAAA,UACvC;AAAA,QACF,CAAC;AAAA,MACH;AAGA,eAAS;AAAA,QACP,IAAI,YAAY,yBAAyB;AAAA,UACvC,QAAQ;AAAA,YACN;AAAA,YACA,WAAW,MAAM,eAAe,KAAK;AAAA,UACvC;AAAA,QACF,CAAC;AAAA,MACH;AAGA,qBAAe,qBAAqB;AACpC,qBAAe,uBAAuB;AAAA,IACxC;AAAA,IAEA,WAAW;AACT,aAAO,cAAc;AAAA,IACvB;AAAA,IAEA,SAAS,OAAO;AACd,oBAAc,YAAY;AAC1B,UAAI,KAAK,OAAO;AACd,aAAK,MAAM,QAAQ,MAAM,eAAe,KAAK;AAC7C,aAAK,MAAM,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAGA,MAAM,iBAAiB;AAAA,IACrB,OAAO,CAAC;AAAA,IAER,OAAO;AAEL,YAAM,aAAa,SAAS,iBAAiB,8BAA8B;AAE3E,iBAAW,QAAQ,CAAC,WAAW,UAAU;AAEvC,cAAM,aAAa,UAAU,cAAc,sBAAsB;AACjE,cAAM,eAAe,UAAU,cAAc,wBAAwB;AAErE,YAAI,CAAC,cAAc,CAAC,aAAc;AAElC,cAAM,QAAQ,WAAW,cAAc,4BAA4B;AACnE,cAAM,SAAS,WAAW,cAAc,cAAc;AACtD,cAAM,oBAAoB,WAAW,cAAc,0BAA0B;AAG7E,cAAM,eAAe,aAAa,cAAc,gBAAgB;AAChE,cAAM,qBAAqB,aAAa,cAAc,mCAAmC;AACzF,cAAM,qBAAqB,aAAa,cAAc,uBAAuB;AAE7E,YAAI,SAAS,QAAQ;AACnB,gBAAM,OAAO;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,YAAY;AAAA,UACd;AAEA,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,mBAAmB,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC;AAGD,WAAK,sBAAsB;AAAA,IAC7B;AAAA,IAEA,mBAAmB,MAAM;AAEvB,WAAK,MAAM,iBAAiB,kBAAkB,CAAC,MAAM;AACnD,aAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK;AAAA,MAC7C,CAAC;AAED,WAAK,MAAM;AAAA,QACT;AAAA,QACA,MAAM,SAAS,CAAC,MAAM;AACpB,gBAAM,QAAQ,MAAM,mBAAmB,EAAE,OAAO,KAAK;AACrD,eAAK,kBAAkB,MAAM,KAAK;AAAA,QACpC,GAAG,GAAG;AAAA,MACR;AAGA,WAAK,OAAO,iBAAiB,SAAS,CAAC,MAAM;AAC3C,aAAK,mBAAmB,MAAM,WAAW,EAAE,OAAO,KAAK,CAAC;AAAA,MAC1D,CAAC;AAGD,WAAK,MAAM,iBAAiB,SAAS,MAAM;AACzC,aAAK,UAAU,UAAU,IAAI,eAAe;AAC5C,aAAK,iBAAiB,IAAI;AAAA,MAC5B,CAAC;AAED,WAAK,MAAM,iBAAiB,QAAQ,MAAM;AACxC,aAAK,UAAU,UAAU,OAAO,eAAe;AAE/C,aAAK,mBAAmB,IAAI;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,IAEA,kBAAkB,MAAM,OAAO;AAC7B,YAAM,YAAY,cAAc,SAAS;AAGzC,YAAM,mBAAmB,KAAK,wBAAwB,IAAI;AAC1D,YAAM,aAAa,KAAK,IAAI,GAAG,YAAY,gBAAgB;AAG3D,UAAI,QAAQ,YAAY;AACtB,gBAAQ;AACR,aAAK,MAAM,QAAQ,MAAM,eAAe,KAAK;AAC7C,uBAAe;AAAA,UACb,KAAK;AAAA,UACL,qCAA+B,MAAM,eAAe,UAAU,CAAC;AAAA,QACjE;AAAA,MACF;AAEA,WAAK,QAAQ;AACb,WAAK,aAAa,MAAM,oBAAoB,OAAO,SAAS;AAC5D,WAAK,aAAa;AAGlB,WAAK,aAAa,IAAI;AACtB,WAAK,wBAAwB,IAAI;AACjC,WAAK,mBAAmB,IAAI;AAC5B,WAAK,yBAAyB,IAAI;AAGlC,WAAK,gBAAgB;AAGrB,WAAK,yBAAyB,IAAI;AAGlC,WAAK,2BAA2B;AAAA,IAClC;AAAA,IAEA,mBAAmB,MAAM,aAAa;AACpC,YAAM,YAAY,cAAc,SAAS;AACzC,UAAI,QAAQ,YAAY;AAGxB,YAAM,mBAAmB,KAAK,wBAAwB,IAAI;AAC1D,YAAM,aAAa,KAAK,IAAI,GAAG,YAAY,gBAAgB;AAE3D,UAAI,QAAQ,YAAY;AACtB,gBAAQ;AAER,cAAM,oBAAoB,YAAY,IAAI,QAAQ,YAAY;AAC9D,aAAK,OAAO,QAAQ;AACpB,uBAAe;AAAA,UACb,KAAK;AAAA,UACL,qCAA+B,MAAM,eAAe,UAAU,CAAC;AAAA,QACjE;AAAA,MACF;AAEA,WAAK,QAAQ;AACb,WAAK,aAAa,QAAQ,KAAK,YAAY,IAAK,QAAQ,YAAa,MAAM;AAC3E,WAAK,aAAa;AAGlB,WAAK,MAAM,QAAQ,MAAM,eAAe,KAAK;AAC7C,WAAK,wBAAwB,IAAI;AACjC,WAAK,mBAAmB,IAAI;AAC5B,WAAK,yBAAyB,IAAI;AAGlC,WAAK,gBAAgB;AAGrB,WAAK,yBAAyB,IAAI;AAGlC,WAAK,2BAA2B;AAAA,IAClC;AAAA,IAEA,aAAa,MAAM;AACjB,YAAM,YAAY,cAAc,SAAS;AACzC,UAAI,YAAY,GAAG;AACjB,cAAM,cAAc,KAAK,QAAQ;AACjC,aAAK,OAAO,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,WAAW,CAAC;AAAA,MAC1D,OAAO;AACL,aAAK,OAAO,QAAQ;AAAA,MACtB;AAAA,IACF;AAAA,IAEA,wBAAwB,MAAM;AAC5B,YAAM,sBAAsB,MAAM,iBAAiB,KAAK,UAAU;AAGlE,UAAI,KAAK,mBAAmB;AAC1B,aAAK,kBAAkB,cAAc;AAAA,MACvC;AAGA,UAAI,KAAK,oBAAoB;AAC3B,aAAK,mBAAmB,cAAc;AAAA,MACxC;AAAA,IACF;AAAA,IAEA,mBAAmB,MAAM;AACvB,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa,cAAc,MAAM,eAAe,KAAK,KAAK;AAAA,MACjE;AAAA,IACF;AAAA,IAEA,yBAAyB,MAAM;AAC7B,UAAI,KAAK,sBAAsB,OAAO,QAAQ;AAC5C,cAAM,EAAE,QAAQ,IAAI,OAAO;AAC3B,cAAM,kBAAkB,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,UAAU,CAAC;AAElE;AAAA,UACE,KAAK;AAAA,UACL;AAAA,YACE,OAAO,GAAG,eAAe;AAAA,UAC3B;AAAA,UACA;AAAA,YACE,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iBAAiB,MAAM;AACrB,YAAM,YAAY,cAAc,SAAS;AACzC,YAAM,mBAAmB,KAAK,wBAAwB,IAAI;AAC1D,WAAK,aAAa,KAAK,IAAI,GAAG,YAAY,gBAAgB;AAAA,IAC5D;AAAA,IAEA,mBAAmB,MAAM;AACvB,YAAM,YAAY,cAAc,SAAS;AACzC,YAAM,mBAAmB,KAAK,wBAAwB,IAAI;AAC1D,YAAM,aAAa,KAAK,IAAI,GAAG,YAAY,gBAAgB;AAE3D,UAAI,KAAK,QAAQ,YAAY;AAC3B,aAAK,QAAQ;AACb,aAAK,MAAM,QAAQ,MAAM,eAAe,UAAU;AAClD,aAAK,aAAa,IAAI;AACtB,aAAK,wBAAwB,IAAI;AACjC,aAAK,mBAAmB,IAAI;AAC5B,aAAK,yBAAyB,IAAI;AAClC,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,IAEA,yBAAyB;AACvB,YAAM,YAAY,cAAc,SAAS;AACzC,YAAM,QAAQ,KAAK,kBAAkB;AAErC,UAAI,QAAQ,WAAW;AAErB,cAAM,QAAQ,YAAY;AAC1B,aAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,gBAAM,WAAW,KAAK,QAAQ;AAC9B,eAAK,QAAQ;AACb,eAAK,aAAa,MAAM,oBAAoB,UAAU,SAAS;AAC/D,eAAK,MAAM,QAAQ,MAAM,eAAe,QAAQ;AAChD,eAAK,aAAa,IAAI;AACtB,eAAK,wBAAwB,IAAI;AACjC,eAAK,mBAAmB,IAAI;AAC5B,eAAK,yBAAyB,IAAI;AAAA,QACpC,CAAC;AACD,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,IAEA,uBAAuB;AACrB,YAAM,YAAY,cAAc,SAAS;AAEzC,WAAK,MAAM,QAAQ,CAAC,SAAS;AAE3B,aAAK,iBAAiB,IAAI;AAG1B,YAAI,YAAY,GAAG;AACjB,eAAK,aAAa,MAAM,oBAAoB,KAAK,OAAO,SAAS;AACjE,eAAK,aAAa,IAAI;AACtB,eAAK,wBAAwB,IAAI;AACjC,eAAK,mBAAmB,IAAI;AAC5B,eAAK,yBAAyB,IAAI;AAAA,QACpC,OAAO;AAEL,eAAK,QAAQ;AACb,eAAK,aAAa;AAClB,eAAK,MAAM,QAAQ,MAAM,eAAe,CAAC;AACzC,eAAK,OAAO,QAAQ;AACpB,eAAK,wBAAwB,IAAI;AACjC,eAAK,mBAAmB,IAAI;AAC5B,eAAK,yBAAyB,IAAI;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,6BAA6B;AAC3B,YAAM,YAAY,cAAc,SAAS;AACzC,YAAM,QAAQ,KAAK,kBAAkB;AACrC,YAAM,YAAY,YAAY;AAE9B,eAAS;AAAA,QACP,IAAI,YAAY,2BAA2B;AAAA,UACzC,QAAQ;AAAA,YACN;AAAA,YACA,gBAAgB;AAAA,YAChB;AAAA,YACA,kBAAkB,cAAc;AAAA,YAChC,iBAAiB,YAAY;AAAA,YAC7B,qBAAqB,YAAY,IAAK,QAAQ,YAAa,MAAM;AAAA,YACjE,qBAAqB,YAAY,IAAK,YAAY,YAAa,MAAM;AAAA,UACvE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,aAAO,KAAK,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,OAAO,CAAC;AAAA,IAC7D;AAAA,IAEA,wBAAwB,aAAa;AACnC,aAAO,KAAK,MAAM,OAAO,CAAC,KAAK,SAAS;AACtC,eAAO,SAAS,cAAc,MAAM,MAAM,KAAK;AAAA,MACjD,GAAG,CAAC;AAAA,IACN;AAAA,IAEA,oBAAoB;AAClB,YAAM,YAAY,cAAc,SAAS;AACzC,YAAM,iBAAiB,KAAK,kBAAkB;AAC9C,aAAO,KAAK,IAAI,GAAG,YAAY,cAAc;AAAA,IAC/C;AAAA,IAEA,kBAAkB;AAChB,YAAM,cAAc,KAAK,MAAM,IAAI,CAAC,UAAU;AAAA,QAC5C,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK;AAAA,QACZ,YAAY,KAAK;AAAA,MACnB,EAAE;AAEF,mBAAa,IAAI,cAAc,qBAAqB,WAAW;AAAA,IACjE;AAAA,IAEA,wBAAwB;AACtB,YAAM,SAAS,aAAa,IAAI,cAAc,mBAAmB;AACjE,UAAI,CAAC,UAAU,CAAC,MAAM,QAAQ,MAAM,EAAG;AAEvC,aAAO,QAAQ,CAAC,eAAe;AAC7B,cAAM,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,UAAU,WAAW,KAAK;AAChE,YAAI,MAAM;AACR,eAAK,QAAQ,WAAW;AACxB,eAAK,aAAa,WAAW;AAC7B,eAAK,MAAM,QAAQ,MAAM,eAAe,KAAK,KAAK;AAClD,eAAK,aAAa,IAAI;AACtB,eAAK,wBAAwB,IAAI;AACjC,eAAK,mBAAmB,IAAI;AAC5B,eAAK,yBAAyB,IAAI;AAGlC,eAAK,yBAAyB,IAAI;AAAA,QACpC;AAAA,MACF,CAAC;AAGD,WAAK,uBAAuB;AAG5B,eAAS;AAAA,QACP,IAAI,YAAY,wBAAwB;AAAA,UACtC,QAAQ;AAAA,YACN,aAAa;AAAA,YACb,YAAY,OAAO;AAAA,UACrB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IAGF;AAAA,IAEA,yBAAyB,MAAM;AAE7B,YAAM,WAAW,KAAK,UAAU,aAAa,gBAAgB,KAAK;AAClE,YAAM,UAAU,KAAK,UAAU,aAAa,eAAe,KAAK;AAEhE,eAAS;AAAA,QACP,IAAI,YAAY,qBAAqB;AAAA,UACnC,QAAQ;AAAA,YACN,OAAO,KAAK;AAAA,YACZ;AAAA,YACA;AAAA,YACA,OAAO,KAAK;AAAA,YACZ,YAAY,KAAK;AAAA,YACjB,WAAW,MAAM,eAAe,KAAK,KAAK;AAAA,YAC1C,WAAW,KAAK,kBAAkB;AAAA,UACpC;AAAA,QACF,CAAC;AAAA,MACH;AAGA,eAAS;AAAA,QACP,IAAI,YAAY,yBAAyB;AAAA,UACvC,QAAQ;AAAA,YACN,KAAK,GAAG,QAAQ,IAAI,OAAO;AAAA,YAC3B;AAAA,YACA;AAAA,YACA,OAAO,KAAK;AAAA,YACZ,YAAY,KAAK;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAGA,MAAM,iBAAiB;AAAA,IACrB,sBAAsB,WAAW,SAAS;AACxC,UAAI,UAAU,UAAU,cAAc,qBAAqB;AAE3D,UAAI,CAAC,SAAS;AACZ,kBAAU,SAAS,cAAc,KAAK;AACtC,gBAAQ,YAAY;AACpB,gBAAQ,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaxB,kBAAU,MAAM,WAAW;AAC3B,kBAAU,YAAY,OAAO;AAAA,MAC/B;AAEA,cAAQ,cAAc;AACtB,cAAQ,MAAM,UAAU;AAGxB,YAAM,QAAQ,UAAU,cAAc,OAAO;AAC7C,UAAI,OAAO;AACT,cAAM,OAAO,MAAM,sBAAsB;AACzC,gBAAQ,MAAM,MAAM,GAAG,MAAM,YAAY,KAAK,SAAS,CAAC;AACxD,gBAAQ,MAAM,OAAO,GAAG,MAAM,UAAU;AAAA,MAC1C;AAGA,iBAAW,MAAM;AACf,gBAAQ,MAAM,UAAU;AAAA,MAC1B,GAAG,GAAI;AAGP,UAAI,OAAO;AACT,cAAM,MAAM,cAAc;AAC1B,mBAAW,MAAM;AACf,gBAAM,MAAM,cAAc;AAAA,QAC5B,GAAG,GAAI;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAGO,MAAM,sBAAN,MAA0B;AAAA,IAjmBjC,OAimBiC;AAAA;AAAA;AAAA,IAC/B,cAAc;AACZ,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,UAAI,SAAS,eAAe,WAAW;AACrC,iBAAS,iBAAiB,oBAAoB,MAAM;AAClD,eAAK,WAAW;AAAA,QAClB,CAAC;AAAA,MACH,OAAO;AACL,aAAK,WAAW;AAAA,MAClB;AAEA,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,aAAa;AAEX,UAAI,CAAC,OAAO,UAAU;AACpB,gBAAQ,MAAM,2CAA2C;AACzD;AAAA,MACF;AAGA,YAAM,gBAAgB,6BAAM;AAC1B,YAAI,OAAO,QAAQ;AACjB,eAAK,qBAAqB;AAAA,QAC5B,OAAO;AACL,qBAAW,eAAe,EAAE;AAAA,QAC9B;AAAA,MACF,GANsB;AAOtB,oBAAc;AAAA,IAChB;AAAA,IAEA,uBAAuB;AAErB,oBAAc,KAAK;AAGnB,iBAAW,MAAM;AACf,uBAAe,KAAK;AACpB,sBAAc,gBAAgB;AAG9B,uBAAe,2BAA2B;AAG1C,iBAAS;AAAA,UACP,IAAI,YAAY,sBAAsB;AAAA,YACpC,QAAQ;AAAA,cACN,WAAW,KAAK,aAAa;AAAA,cAC7B,gBAAgB,KAAK,kBAAkB;AAAA,cACvC,WAAW,KAAK,kBAAkB;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAGA,YAAI,OAAO,WAAW,aAAa;AACjC,iBAAO,oBAAoB,MAAM;AACjC,kBAAQ,KAAK,2CAAsC;AAAA,QACrD;AAGA,aAAK,8BAA8B;AAAA,MACrC,GAAG,GAAG;AAGN,WAAK,8BAA8B;AAGnC,eAAS,iBAAiB,6BAA6B,MAAM;AAC3D,aAAK,8BAA8B;AAAA,MACrC,CAAC;AACD,eAAS,iBAAiB,qBAAqB,MAAM;AACnD,aAAK,8BAA8B;AAAA,MACrC,CAAC;AACD,eAAS,iBAAiB,2BAA2B,MAAM;AACzD,aAAK,8BAA8B;AAAA,MACrC,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,gCAAgC;AAC9B,YAAM,YAAY,KAAK,aAAa;AACpC,YAAM,iBAAiB,MAAM,eAAe,SAAS;AAGrD,YAAM,aAAa,SAAS,cAAc,gDAAgD;AAC1F,UAAI,YAAY;AACd,mBAAW,cAAc,MAAM,eAAe,KAAK,kBAAkB,CAAC;AAAA,MACxE;AAGA,YAAM,UAAU,SAAS,cAAc,mDAAmD;AAC1F,UAAI,SAAS;AACX,gBAAQ,cAAc;AAAA,MACxB;AAGA,YAAM,gBAAgB,SAAS,cAAc,gCAAgC;AAC7E,UAAI,eAAe;AACjB,sBAAc,cAAc;AAAA,MAC9B;AAGA,YAAM,mBAAmB,SAAS,iBAAiB,yBAAyB;AAC5E,uBAAiB,QAAQ,CAAC,OAAO;AAC/B,WAAG,cAAc;AAAA,MACnB,CAAC;AAGD,YAAM,8BAA8B,SAAS,iBAAiB,uBAAuB;AACrF,UAAI,4BAA4B,SAAS,GAAG;AAC1C,cAAMA,aAAY,KAAK,aAAa;AACpC,cAAM,WAAW,KAAK,kBAAkB;AACxC,cAAM,UAAUA,aAAY,IAAK,WAAWA,aAAa,MAAM;AAC/D,cAAM,mBAAmB,MAAM,iBAAiB,OAAO;AAGvD,oCAA4B,QAAQ,CAAC,OAAO;AAC1C,aAAG,cAAc;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGA,eAAe;AACb,aAAO,cAAc,SAAS;AAAA,IAChC;AAAA,IAEA,aAAa,OAAO;AAClB,oBAAc,SAAS,KAAK;AAAA,IAC9B;AAAA,IAEA,oBAAoB;AAClB,aAAO,eAAe,kBAAkB;AAAA,IAC1C;AAAA,IAEA,oBAAoB;AAClB,aAAO,eAAe,kBAAkB;AAAA,IAC1C;AAAA,IAEA,iBAAiB;AACf,aAAO,eAAe,MAAM,IAAI,CAAC,UAAU;AAAA,QACzC,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK;AAAA,QACZ,YAAY,KAAK;AAAA,QACjB,WAAW,MAAM,eAAe,KAAK,KAAK;AAAA,QAC1C,YAAY,KAAK;AAAA,MACnB,EAAE;AAAA,IACJ;AAAA,IAEA,QAAQ;AACN,mBAAa,OAAO,cAAc,QAAQ;AAC1C,mBAAa,OAAO,cAAc,mBAAmB;AAGrD,mBAAa,OAAO,wBAAwB;AAG5C,mBAAa,OAAO,qBAAqB;AAEzC,oBAAc,SAAS,CAAC;AACxB,qBAAe,MAAM,QAAQ,CAAC,SAAS;AACrC,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,aAAa;AAClB,aAAK,MAAM,QAAQ,MAAM,eAAe,CAAC;AACzC,aAAK,OAAO,QAAQ;AACpB,uBAAe,wBAAwB,IAAI;AAC3C,uBAAe,mBAAmB,IAAI;AACtC,uBAAe,yBAAyB,IAAI;AAAA,MAC9C,CAAC;AAED,qBAAe,2BAA2B;AAG1C,eAAS;AAAA,QACP,IAAI,YAAY,sBAAsB;AAAA,UACpC,QAAQ;AAAA,YACN,WAAW,KAAK,IAAI;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,UAAU;AACR,WAAK,gBAAgB;AACrB,qBAAe,QAAQ,CAAC;AACxB,oBAAc,QAAQ;AAAA,IACxB;AAAA;AAAA,IAGA,oBAAoB;AAClB,aAAO;AAAA,IACT;AAAA,EACF;", "names": ["mainValue"]}