/* src/styles/dynamic-grid.css */
.patrimonio_interactive_content-wrapper {
  border-radius: 30px;
  container-type: inline-size;
  overflow: hidden;
  background-color: transparent;
}
.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item {
  border: none;
  border-radius: 0;
  position: relative;
  background-color: inherit;
  transition: all 0.3s ease;
}
.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before,
.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {
  content: "";
  position: absolute;
  background-color: rgba(0, 0, 0, 0.24);
  z-index: 1;
  pointer-events: none;
}
.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before {
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
}
.patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
}
@media screen and (min-width: 991px) {
  .patrimonio_interactive_content-wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0;
  }
}
@media screen and (max-width: 990px) and (min-width: 768px) {
  .patrimonio_interactive_content-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0;
  }
}
@media screen and (max-width: 767px) and (min-width: 521px) {
  .patrimonio_interactive_content-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0;
  }
}
@media screen and (max-width: 520px) {
  .patrimonio_interactive_content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0;
  }
}
.patrimonio_interactive_item.grid-last-in-row::before {
  display: none !important;
}
.patrimonio_interactive_item.grid-last-row::after {
  display: none !important;
}
.patrimonio_interactive_item.grid-top-left {
  border-top-left-radius: 30px !important;
}
.patrimonio_interactive_item.grid-top-right {
  border-top-right-radius: 30px !important;
}
.patrimonio_interactive_item.grid-bottom-left {
  border-bottom-left-radius: 30px !important;
}
.patrimonio_interactive_item.grid-bottom-right {
  border-bottom-right-radius: 30px !important;
}
.patrimonio_interactive_item.grid-only-item {
  border-radius: 30px !important;
}
.patrimonio_interactive_item.grid-only-item::before,
.patrimonio_interactive_item.grid-only-item::after {
  display: none !important;
}
.patrimonio_interactive_item {
  opacity: 1;
  transform: scale(1);
  transition:
    opacity 0.3s ease,
    transform 0.3s ease,
    border-radius 0.3s ease;
}
.patrimonio_interactive_item.hidden {
  display: none !important;
}
.patrimonio_interactive_item.animating-out {
  opacity: 0;
  transform: scale(0.95);
}
.patrimonio_interactive_item.animating-in {
  opacity: 0;
  transform: scale(0.95);
}
.patrimonio_interactive_item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.patrimonio_interactive_item:focus-within {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: -2px;
  z-index: 2;
}
.patrimonio_interactive_content-wrapper {
  position: relative;
  z-index: 1;
}
.patrimonio_interactive_content-wrapper {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
.patrimonio_interactive_content-wrapper::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}
.patrimonio_interactive_content-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}
.patrimonio_interactive_content-wrapper::-webkit-scrollbar-thumb {
  background:
    linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.15) 0%,
      rgba(0, 0, 0, 0.2) 100%);
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.patrimonio_interactive_content-wrapper::-webkit-scrollbar-thumb:hover {
  background:
    linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.25) 0%,
      rgba(0, 0, 0, 0.3) 100%);
}
@media print {
  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before,
  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {
    background-color: rgba(0, 0, 0, 0.12);
  }
  .patrimonio_interactive_item.hidden {
    display: none !important;
  }
}
@media (prefers-contrast: high) {
  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::before,
  .patrimonio_interactive_content-wrapper > .patrimonio_interactive_item::after {
    background-color: currentColor;
    opacity: 0.3;
  }
}
@media (prefers-reduced-motion: reduce) {
  .patrimonio_interactive_item,
  .patrimonio_interactive_content-wrapper {
    transition: none !important;
  }
}
/*# sourceMappingURL=dynamic-grid.css.map */
