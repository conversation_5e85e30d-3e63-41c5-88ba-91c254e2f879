{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/config/typebot.js", "../../src/modules/typebot-integration.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Typebot Integration Configuration\r\n * Configure your Typebot credentials and settings here\r\n */\r\n\r\n// Typebot Configuration\r\nconst TYPEBOT_CONFIG = {\r\n  // Your Typebot public ID (get this from your Typebot URL)\r\n  PUBLIC_ID: 'relatorio-reino', // ✅ Atualizado com ID correto usado no HTML\r\n\r\n  // API Base URL (use your self-hosted instance or typebot.io)\r\n  API_BASE_URL: 'https://typebot.co/api/v1', // ✅ Atualizado para typebot.co\r\n\r\n  // Authorization token (optional - needed for private bots)\r\n  AUTH_TOKEN: 'Bearer Dza0r0rRB4rzjSeGiKlawQK8', // ✅ Configurado\r\n\r\n  // Webhook configuration from your Typebot\r\n  WEBHOOK_CONFIG: {\r\n    blockId: 'o8gkxhaloh3c3w5ldx94zlwx',\r\n    webhookUrl:\r\n      'https://typebot.co/api/v1/typebots/mp0wrmwrxpqnycbxlcimcz5n/blocks/o8gkxhaloh3c3w5ldx94zlwx/results/{resultId}/executeWebhook',\r\n  },\r\n\r\n  // Typebot Embed Configuration\r\n  EMBED_CONFIG: {\r\n    // Container where the typebot will be embedded\r\n    containerId: 'typebot-container',\r\n\r\n    // Use official Typebot popup\r\n    usePopup: true,\r\n\r\n    // CDN URL for Typebot JS library\r\n    cdnUrl: 'https://cdn.jsdelivr.net/npm/@typebot.io/js@0/dist/web.js',\r\n\r\n    // Typebot theme customization\r\n    theme: {\r\n      button: {\r\n        backgroundColor: '#0042DA',\r\n        iconColor: '#FFFFFF',\r\n        customIconSrc: null,\r\n        size: 'medium',\r\n        dragAndDrop: false,\r\n        position: 'right',\r\n      },\r\n      chatWindow: {\r\n        backgroundColor: '#FFFFFF',\r\n        maxWidth: '400px',\r\n        maxHeight: '600px',\r\n      },\r\n    },\r\n\r\n    // Auto-open configuration\r\n    autoOpenDelay: 0, // Set to 0 to not auto-open, or milliseconds to delay\r\n  },\r\n\r\n  // Webhook endpoint for completion callbacks\r\n  COMPLETION_WEBHOOK: '/api/typebot-completion', // Your endpoint to handle completion\r\n\r\n  // Debug mode\r\n  DEBUG: window.location.hostname === 'localhost' || window.location.search.includes('debug=true'),\r\n};\r\n\r\n// Variable mapping - maps your form data to Typebot variables\r\nconst VARIABLE_MAPPING = {\r\n  // Your form field name -> Typebot variable name\r\n  patrimonio: 'patrimonio', // Ajustado para minúsculo\r\n  ativosEscolhidos: 'ativos', // Nome do campo coletado no form\r\n  email: 'email', // Será coletado pelo Typebot\r\n  nome: 'nome', // Será coletado pelo Typebot\r\n  alocacao: 'alocacao', // Dados de alocação\r\n  totalAlocado: 'totalAlocado', // Total alocado calculado\r\n  patrimonioRestante: 'patrimonioRestante', // Patrimônio restante\r\n  sessionId: 'sessionId',\r\n};\r\n\r\n// Export configuration\r\nexport { TYPEBOT_CONFIG, VARIABLE_MAPPING };\r\n\r\n/**\r\n * Typebot Client for API interactions\r\n */\r\nexport class TypebotClient {\r\n  constructor(config = TYPEBOT_CONFIG) {\r\n    this.config = config;\r\n    this.sessionId = null;\r\n    this.resultId = null;\r\n    this.isCompleted = false;\r\n  }\r\n\r\n  /**\r\n   * Start a new Typebot chat session\r\n   * @param {Object} prefilledData - Data to prefill in the typebot\r\n   * @returns {Promise<Object>} - Session data\r\n   */\r\n  async startChat(prefilledData = {}) {\r\n    try {\r\n      // Check if fetch is available\r\n      if (typeof fetch === 'undefined') {\r\n        throw new Error('Fetch is not available in this environment');\r\n      }\r\n\r\n      const url = `${this.config.API_BASE_URL}/typebots/${this.config.PUBLIC_ID}/startChat`;\r\n\r\n      // Map form data to typebot variables\r\n      const prefilledVariables = this.mapFormDataToVariables(prefilledData);\r\n\r\n      const requestBody = {\r\n        prefilledVariables,\r\n        isStreamEnabled: false,\r\n        textBubbleContentFormat: 'richText',\r\n      };\r\n\r\n      const headers = {\r\n        'Content-Type': 'application/json',\r\n      };\r\n\r\n      // Add authorization if needed\r\n      if (this.config.AUTH_TOKEN) {\r\n        headers['Authorization'] = this.config.AUTH_TOKEN;\r\n      }\r\n\r\n      const response = await window.fetch(url, {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(requestBody),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Typebot API error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Store session information\r\n      this.sessionId = data.sessionId;\r\n      this.resultId = data.resultId;\r\n\r\n      if (this.config.DEBUG) {\r\n        // console.log('🤖 Typebot chat started:', {\r\n        //   sessionId: this.sessionId,\r\n        //   resultId: this.resultId,\r\n        //   prefilledVariables,\r\n        // });\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      console.error('❌ Failed to start Typebot chat:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Continue the chat with a message\r\n   * @param {string} message - Message to send\r\n   * @returns {Promise<Object>} - Response data\r\n   */\r\n  async continueChat(message) {\r\n    if (!this.sessionId) {\r\n      throw new Error('No active session. Start chat first.');\r\n    }\r\n\r\n    try {\r\n      const url = `${this.config.API_BASE_URL}/sessions/${this.sessionId}/continueChat`;\r\n\r\n      const requestBody = {\r\n        message: {\r\n          type: 'text',\r\n          text: message,\r\n        },\r\n        textBubbleContentFormat: 'richText',\r\n      };\r\n\r\n      const headers = {\r\n        'Content-Type': 'application/json',\r\n      };\r\n\r\n      if (this.config.AUTH_TOKEN) {\r\n        headers['Authorization'] = this.config.AUTH_TOKEN;\r\n      }\r\n\r\n      const response = await window.fetch(url, {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(requestBody),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Typebot API error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (this.config.DEBUG) {\r\n        // console.log('🤖 Typebot chat continued:', data);\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      console.error('❌ Failed to continue Typebot chat:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Map form data to typebot variables\r\n   * @param {Object} formData - Form data from your application\r\n   * @returns {Object} - Mapped variables for typebot\r\n   */\r\n  mapFormDataToVariables(formData) {\r\n    const mappedVariables = {};\r\n\r\n    // Calculate derived values\r\n    const totalAlocado = formData.alocacao\r\n      ? Object.values(formData.alocacao).reduce((sum, item) => sum + (item.value || 0), 0)\r\n      : 0;\r\n    const patrimonioRestante = (formData.patrimonio || 0) - totalAlocado;\r\n\r\n    // Add calculated values to formData for mapping\r\n    const enhancedFormData = {\r\n      ...formData,\r\n      totalAlocado,\r\n      patrimonioRestante,\r\n    };\r\n\r\n    for (const [formField, typebotVar] of Object.entries(VARIABLE_MAPPING)) {\r\n      if (enhancedFormData[formField] !== undefined) {\r\n        // Handle special formatting for certain fields\r\n        if (formField === 'patrimonio' && typeof enhancedFormData[formField] === 'number') {\r\n          mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString('pt-BR')}`;\r\n        } else if (\r\n          formField === 'totalAlocado' &&\r\n          typeof enhancedFormData[formField] === 'number'\r\n        ) {\r\n          mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString('pt-BR')}`;\r\n        } else if (\r\n          formField === 'patrimonioRestante' &&\r\n          typeof enhancedFormData[formField] === 'number'\r\n        ) {\r\n          mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString('pt-BR')}`;\r\n        } else if (formField === 'ativosEscolhidos' && Array.isArray(enhancedFormData[formField])) {\r\n          // Format selected assets as readable text\r\n          const ativosList = enhancedFormData[formField]\r\n            .map((ativo) => `${ativo.product} (${ativo.category})`)\r\n            .join(', ');\r\n          mappedVariables[typebotVar] = ativosList;\r\n        } else if (formField === 'alocacao' && typeof enhancedFormData[formField] === 'object') {\r\n          // Format allocation data as readable text, only showing non-zero values\r\n          const alocacaoList = Object.entries(enhancedFormData[formField])\r\n            .filter(([, data]) => (data.value || 0) > 0)\r\n            .map(\r\n              ([, data]) =>\r\n                `${data.product}: R$ ${(data.value || 0).toLocaleString('pt-BR')} (${Math.round((data.percentage || 0) * 100) / 100}%)`\r\n            )\r\n            .join('\\n');\r\n          mappedVariables[typebotVar] = alocacaoList || 'Nenhuma alocação realizada';\r\n        } else {\r\n          mappedVariables[typebotVar] = String(enhancedFormData[formField]);\r\n        }\r\n      }\r\n    }\r\n\r\n    return mappedVariables;\r\n  }\r\n\r\n  /**\r\n   * Check if the chat is completed\r\n   * @returns {boolean}\r\n   */\r\n  isSessionCompleted() {\r\n    return this.isCompleted;\r\n  }\r\n\r\n  /**\r\n   * Mark session as completed\r\n   */\r\n  markCompleted() {\r\n    this.isCompleted = true;\r\n\r\n    if (this.config.DEBUG) {\r\n      // console.log('🤖 Typebot session marked as completed');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset the client for a new session\r\n   */\r\n  reset() {\r\n    this.sessionId = null;\r\n    this.resultId = null;\r\n    this.isCompleted = false;\r\n  }\r\n}\r\n", "/**\r\n * Typebot Integration System\r\n * Integrates Typebot chat with the existing webflow button system\r\n * Handles the flow: Form Data → Typebot → Completion → Supabase\r\n */\r\n\r\nimport { TYPEBOT_CONFIG, TypebotClient } from '../config/typebot.js';\r\n\r\nexport class TypebotIntegrationSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.typebotClient = new TypebotClient(TYPEBOT_CONFIG);\r\n    this.completionCallbacks = [];\r\n    this.currentFormData = null;\r\n    this.isTypebotActive = false;\r\n    this.typebotLibrary = null; // Will hold the Typebot library reference\r\n\r\n    // Bind methods\r\n    this.handleTypebotCompletion = this.handleTypebotCompletion.bind(this);\r\n  }\r\n\r\n  async init() {\r\n    if (this.isInitialized) return;\r\n\r\n    try {\r\n      // Load Typebot library\r\n      await this.loadTypebotLibrary();\r\n\r\n      // Initialize Typebot popup\r\n      await this.initializeTypebotPopup();\r\n\r\n      // Setup completion webhook listener\r\n      this.setupCompletionListener();\r\n\r\n      // Setup embed container if needed\r\n      this.setupEmbedContainer();\r\n\r\n      // Setup global API for external integration\r\n      this.setupGlobalAPI();\r\n\r\n      this.isInitialized = true;\r\n    } catch (error) {\r\n      console.error('❌ TypebotIntegrationSystem initialization failed:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Load Typebot library from CDN\r\n   */\r\n  async loadTypebotLibrary() {\r\n    try {\r\n      // Check if already loaded\r\n      if (window.Typebot) {\r\n        this.typebotLibrary = window.Typebot;\r\n        return;\r\n      }\r\n\r\n      // Dynamically import the Typebot library\r\n      const { default: Typebot } = await import(TYPEBOT_CONFIG.EMBED_CONFIG.cdnUrl);\r\n      this.typebotLibrary = Typebot;\r\n      window.Typebot = Typebot;\r\n\r\n      if (TYPEBOT_CONFIG.DEBUG) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('🤖 Typebot library loaded successfully');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Failed to load Typebot library:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize Typebot popup\r\n   */\r\n  async initializeTypebotPopup() {\r\n    if (!this.typebotLibrary) {\r\n      throw new Error('Typebot library not loaded');\r\n    }\r\n\r\n    try {\r\n      // Initialize the popup with your typebot ID\r\n      await this.typebotLibrary.initPopup({\r\n        typebot: TYPEBOT_CONFIG.PUBLIC_ID,\r\n        prefilledVariables: {}, // Will be set when opening\r\n        onMessage: (message) => {\r\n          // Handle messages from Typebot\r\n          this.handleTypebotMessage(message);\r\n        },\r\n        onEnd: () => {\r\n          // Handle when typebot conversation ends\r\n          this.handleTypebotEnd();\r\n        },\r\n      });\r\n\r\n      if (TYPEBOT_CONFIG.DEBUG) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('🤖 Typebot popup initialized');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Failed to initialize Typebot popup:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle messages from Typebot\r\n   */\r\n  handleTypebotMessage(message) {\r\n    if (TYPEBOT_CONFIG.DEBUG) {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🤖 Typebot message:', message);\r\n    }\r\n\r\n    // Check if message contains completion data\r\n    if (message.type === 'completion' || message.data?.completed) {\r\n      this.handleTypebotCompletion(message.data);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle when Typebot ends\r\n   */\r\n  handleTypebotEnd() {\r\n    // eslint-disable-next-line no-console\r\n    console.log('🏁 [TypebotIntegration] Typebot conversation ended (onEnd triggered)');\r\n\r\n    this.isTypebotActive = false;\r\n\r\n    // If we have current form data, trigger completion\r\n    if (this.currentFormData) {\r\n      // eslint-disable-next-line no-console\r\n      console.log('📋 [TypebotIntegration] Auto-triggering completion with form data');\r\n      this.handleTypebotCompletion({ completed: true, autoTriggered: true });\r\n    } else {\r\n      console.warn('⚠️ [TypebotIntegration] No form data available for auto-completion');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start Typebot with form data instead of sending directly to Supabase\r\n   * @param {Object} formData - Collected form data\r\n   * @param {Function} onCompletion - Callback when typebot is completed\r\n   * @returns {Promise<void>}\r\n   */\r\n  async startTypebotFlow(formData, onCompletion = null) {\r\n    try {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🚀 [TypebotIntegration] startTypebotFlow called with:', formData);\r\n\r\n      if (!this.typebotLibrary) {\r\n        throw new Error('Typebot library not loaded');\r\n      }\r\n\r\n      // Store form data and completion callback\r\n      this.currentFormData = formData;\r\n      if (onCompletion) {\r\n        this.completionCallbacks.push(onCompletion);\r\n        // eslint-disable-next-line no-console\r\n        console.log('📝 [TypebotIntegration] Completion callback registered');\r\n      }\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log(`📞 [TypebotIntegration] Total callbacks: ${this.completionCallbacks.length}`);\r\n\r\n      // Map form data to typebot variables\r\n      const prefilledVariables = this.typebotClient.mapFormDataToVariables(formData);\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log('🔄 [TypebotIntegration] Prefilled variables:', prefilledVariables);\r\n\r\n      // Set prefilled variables and open popup\r\n      if (this.typebotLibrary.setPrefilledVariables) {\r\n        this.typebotLibrary.setPrefilledVariables(prefilledVariables);\r\n      }\r\n\r\n      // Open the Typebot popup\r\n      // eslint-disable-next-line no-console\r\n      console.log('🎯 [TypebotIntegration] Opening Typebot popup...');\r\n      this.typebotLibrary.open();\r\n\r\n      this.isTypebotActive = true;\r\n\r\n      // Dispatch event for other systems\r\n      document.dispatchEvent(\r\n        new CustomEvent('typebotStarted', {\r\n          detail: {\r\n            formData: formData,\r\n            prefilledVariables: prefilledVariables,\r\n          },\r\n        })\r\n      );\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log('✅ [TypebotIntegration] Typebot popup opened successfully');\r\n\r\n      return { success: true, prefilledVariables };\r\n    } catch (error) {\r\n      console.error('❌ [TypebotIntegration] Failed to start Typebot flow:', error);\r\n\r\n      // Fallback to direct Supabase submission if Typebot fails\r\n      this.handleTypebotError(error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle Typebot completion and trigger Supabase submission\r\n   */\r\n  async handleTypebotCompletion(typebotData = {}) {\r\n    try {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🤖 [TypebotIntegration] handleTypebotCompletion called with data:', typebotData);\r\n\r\n      // DEBUG: Log all properties of typebotData to understand the structure\r\n      // eslint-disable-next-line no-console\r\n      console.log(\r\n        '🔍 [TypebotIntegration] Complete typebotData structure:',\r\n        JSON.stringify(typebotData, null, 2)\r\n      );\r\n\r\n      if (!this.currentFormData) {\r\n        console.error('❌ [TypebotIntegration] No form data available for Supabase submission');\r\n        return;\r\n      }\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log('📋 [TypebotIntegration] Current form data:', this.currentFormData);\r\n\r\n      // Mark typebot as completed\r\n      this.typebotClient.markCompleted();\r\n      this.isTypebotActive = false;\r\n\r\n      // Extract nome and email from typebot data\r\n      // Try multiple extraction methods due to Typebot's data structure\r\n      let nome = null;\r\n      let email = null;\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log(\r\n        '🔍 [TypebotIntegration] Complete typebotData structure:',\r\n        JSON.stringify(typebotData, null, 2)\r\n      );\r\n\r\n      // Method 1: Direct properties (check if they're real values, not IDs)\r\n      if (typebotData.nome && !this.isEncryptedValue(typebotData.nome)) {\r\n        nome = typebotData.nome;\r\n      } else if (typebotData.nome && this.isEncryptedValue(typebotData.nome)) {\r\n        // Use encrypted value as fallback for now - we know it came from Typebot\r\n        console.warn(\r\n          '⚠️ [TypebotIntegration] Nome is encrypted, using fallback:',\r\n          typebotData.nome\r\n        );\r\n        nome = 'Nome capturado via Typebot (ID: ' + typebotData.nome.substring(0, 8) + '...)';\r\n      }\r\n\r\n      if (typebotData.email && !this.isEncryptedValue(typebotData.email)) {\r\n        email = typebotData.email;\r\n      } else if (typebotData.email && this.isEncryptedValue(typebotData.email)) {\r\n        // Use encrypted value as fallback for now - we know it came from Typebot\r\n        console.warn(\r\n          '⚠️ [TypebotIntegration] Email is encrypted, using fallback:',\r\n          typebotData.email\r\n        );\r\n        email = 'email-typebot-' + typebotData.email.substring(0, 8) + '@capturado.com';\r\n      }\r\n\r\n      // Method 2: Check for alternative property names\r\n      if (!nome) {\r\n        nome = typebotData.name || typebotData.nome_usuario || typebotData.userName || null;\r\n        if (nome && this.isEncryptedValue(nome)) nome = null;\r\n      }\r\n      if (!email) {\r\n        email = typebotData.e_mail || typebotData.userEmail || typebotData.email_usuario || null;\r\n        if (email && this.isEncryptedValue(email)) email = null;\r\n      }\r\n\r\n      // Method 3: Check typebot session data for real values\r\n      if (typebotData.sessionData) {\r\n        const sessionVars = typebotData.sessionData.variables || {};\r\n        if (!nome && sessionVars.nome && !this.isEncryptedValue(sessionVars.nome)) {\r\n          nome = sessionVars.nome;\r\n        }\r\n        if (!email && sessionVars.email && !this.isEncryptedValue(sessionVars.email)) {\r\n          email = sessionVars.email;\r\n        }\r\n      }\r\n\r\n      // Method 4: Look in result data\r\n      if (typebotData.resultData) {\r\n        const resultVars = typebotData.resultData.variables || {};\r\n        if (!nome && resultVars.nome && !this.isEncryptedValue(resultVars.nome)) {\r\n          nome = resultVars.nome;\r\n        }\r\n        if (!email && resultVars.email && !this.isEncryptedValue(resultVars.email)) {\r\n          email = resultVars.email;\r\n        }\r\n      }\r\n\r\n      // Method 5: Check if data has a variables or answers property\r\n      if (typebotData.variables) {\r\n        if (\r\n          !nome &&\r\n          typebotData.variables.nome &&\r\n          !this.isEncryptedValue(typebotData.variables.nome)\r\n        ) {\r\n          nome = typebotData.variables.nome;\r\n        }\r\n        if (\r\n          !email &&\r\n          typebotData.variables.email &&\r\n          !this.isEncryptedValue(typebotData.variables.email)\r\n        ) {\r\n          email = typebotData.variables.email;\r\n        }\r\n      }\r\n\r\n      // Method 6: Parse answers array more thoroughly\r\n      if (typebotData.answers && Array.isArray(typebotData.answers)) {\r\n        for (const answer of typebotData.answers) {\r\n          if (answer.type === 'text' && answer.value && !this.isEncryptedValue(answer.value)) {\r\n            // Check if this looks like a name\r\n            if (\r\n              !nome &&\r\n              (answer.question?.toLowerCase().includes('nome') ||\r\n                answer.question?.toLowerCase().includes('name') ||\r\n                (answer.value.length > 2 &&\r\n                  answer.value.length < 50 &&\r\n                  !answer.value.includes('@')))\r\n            ) {\r\n              nome = answer.value;\r\n            }\r\n            // Check if this looks like an email\r\n            if (!email && answer.value.includes('@') && answer.value.includes('.')) {\r\n              email = answer.value;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Log what we extracted\r\n      // eslint-disable-next-line no-console\r\n      console.log('📝 [TypebotIntegration] Extracted user info from Typebot:', { nome, email });\r\n\r\n      // If still encrypted/placeholder values, use fallback values\r\n      if (nome && this.isEncryptedValue(nome)) {\r\n        console.warn('⚠️ [TypebotIntegration] Nome appears to be encrypted/placeholder:', nome);\r\n        nome = 'Nome não capturado do Typebot';\r\n      }\r\n      if (email && this.isEncryptedValue(email)) {\r\n        console.warn('⚠️ [TypebotIntegration] Email appears to be encrypted/placeholder:', email);\r\n        email = '<EMAIL>';\r\n      }\r\n\r\n      // Merge typebot data with original form data\r\n      const enhancedFormData = {\r\n        ...this.currentFormData,\r\n        // Add user contact information from Typebot\r\n        nome: nome,\r\n        email: email,\r\n        // Typebot metadata\r\n        typebotSessionId: this.typebotClient.sessionId,\r\n        typebotResultId: this.typebotClient.resultId,\r\n        typebotData: typebotData,\r\n        completedAt: new Date().toISOString(),\r\n      };\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log('🔄 [TypebotIntegration] Enhanced form data prepared:', enhancedFormData);\r\n\r\n      // Validate that we have user info\r\n      if (!nome || !email) {\r\n        console.warn(\r\n          '⚠️ [TypebotIntegration] Missing user info from Typebot. Nome:',\r\n          nome,\r\n          'Email:',\r\n          email\r\n        );\r\n      }\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log(\r\n        `📞 [TypebotIntegration] Executing ${this.completionCallbacks.length} completion callbacks...`\r\n      );\r\n\r\n      // Execute completion callbacks\r\n      for (const callback of this.completionCallbacks) {\r\n        try {\r\n          // eslint-disable-next-line no-console\r\n          console.log('🎯 [TypebotIntegration] Calling completion callback...');\r\n          await callback(enhancedFormData);\r\n          // eslint-disable-next-line no-console\r\n          console.log('✅ [TypebotIntegration] Completion callback executed successfully');\r\n        } catch (callbackError) {\r\n          console.error('❌ [TypebotIntegration] Completion callback error:', callbackError);\r\n        }\r\n      }\r\n\r\n      // Clear callbacks and form data\r\n      this.completionCallbacks = [];\r\n      this.currentFormData = null;\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log('🎉 [TypebotIntegration] Dispatching typebotCompleted event...');\r\n\r\n      // Dispatch completion event\r\n      document.dispatchEvent(\r\n        new CustomEvent('typebotCompleted', {\r\n          detail: {\r\n            formData: enhancedFormData,\r\n            typebotData: typebotData,\r\n            userInfo: { nome, email },\r\n          },\r\n        })\r\n      );\r\n\r\n      // eslint-disable-next-line no-console\r\n      console.log('✅ [TypebotIntegration] handleTypebotCompletion completed successfully');\r\n    } catch (error) {\r\n      console.error('❌ [TypebotIntegration] Failed to handle Typebot completion:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup completion listener for webhooks or client-side actions\r\n   */\r\n  setupCompletionListener() {\r\n    // eslint-disable-next-line no-console\r\n    console.log('🔧 [TypebotIntegration] Setting up completion listeners...');\r\n\r\n    // Listen for custom completion events\r\n    document.addEventListener('typebotFlowCompleted', (event) => {\r\n      // eslint-disable-next-line no-console\r\n      console.log('📨 [TypebotIntegration] typebotFlowCompleted event received:', event.detail);\r\n      this.handleTypebotCompletion(event.detail);\r\n    });\r\n\r\n    // Listen for postMessage from embedded typebot\r\n    window.addEventListener('message', (event) => {\r\n      // eslint-disable-next-line no-console\r\n      console.log('📬 [TypebotIntegration] postMessage received:', event.data);\r\n\r\n      if (event.data && event.data.type === 'typebot-completion') {\r\n        // eslint-disable-next-line no-console\r\n        console.log('🎯 [TypebotIntegration] typebot-completion message detected');\r\n        this.handleTypebotCompletion(event.data.data);\r\n      } else if (event.data && event.data.type === 'typebot-close-request') {\r\n        // eslint-disable-next-line no-console\r\n        console.log('🔒 [TypebotIntegration] typebot-close-request received');\r\n        this.closeTypebot(event.data.data);\r\n      }\r\n    });\r\n\r\n    // Setup webhook endpoint listener (if using server-side webhooks)\r\n    this.setupWebhookListener();\r\n\r\n    // Setup fallback navigation listener\r\n    this.setupFallbackNavigationListener();\r\n\r\n    // eslint-disable-next-line no-console\r\n    console.log('✅ [TypebotIntegration] Completion listeners setup complete');\r\n  }\r\n\r\n  /**\r\n   * Setup webhook listener for server-side completion callbacks\r\n   */\r\n  setupWebhookListener() {\r\n    // This would typically be handled by your server\r\n    // For client-side detection, we'll check for completion periodically\r\n    if (TYPEBOT_CONFIG.COMPLETION_WEBHOOK) {\r\n      // Implement webhook polling or server-sent events here\r\n      // For now, we'll use a simple polling mechanism\r\n      this.startCompletionPolling();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start polling for completion (fallback method)\r\n   */\r\n  startCompletionPolling() {\r\n    const pollInterval = setInterval(() => {\r\n      if (!this.isTypebotActive || this.typebotClient.isSessionCompleted()) {\r\n        clearInterval(pollInterval);\r\n        return;\r\n      }\r\n\r\n      // Check if typebot session is completed\r\n      // This is a simplified approach - in production, you'd use webhooks\r\n      this.checkTypebotCompletion();\r\n    }, 5000); // Check every 5 seconds\r\n  }\r\n\r\n  /**\r\n   * Check if typebot is completed (simplified implementation)\r\n   */\r\n  async checkTypebotCompletion() {\r\n    // In a real implementation, you would check the typebot API\r\n    // or listen for specific events that indicate completion\r\n    // For now, this is a placeholder\r\n  }\r\n\r\n  /**\r\n   * Setup embed container for typebot\r\n   */\r\n  setupEmbedContainer() {\r\n    const { containerId } = TYPEBOT_CONFIG.EMBED_CONFIG;\r\n    let container = document.getElementById(containerId);\r\n\r\n    if (!container) {\r\n      // Create container if it doesn't exist\r\n      container = document.createElement('div');\r\n      container.id = containerId;\r\n      container.style.cssText = `\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        z-index: 9999;\r\n        max-width: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.chatWindow.maxWidth};\r\n        max-height: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.chatWindow.maxHeight};\r\n      `;\r\n      document.body.appendChild(container);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize embedded typebot\r\n   */\r\n  initializeEmbed() {\r\n    // This would integrate with Typebot's embed library\r\n    // For now, we'll create a placeholder\r\n    const container = document.getElementById(TYPEBOT_CONFIG.EMBED_CONFIG.containerId);\r\n    if (container) {\r\n      container.innerHTML = `\r\n        <div style=\"\r\n          background: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.chatWindow.backgroundColor};\r\n          border-radius: 10px;\r\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n          padding: 20px;\r\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n        \">\r\n          <h3 style=\"margin: 0 0 15px 0; color: #333;\">Reino Capital</h3>\r\n          <p style=\"margin: 0 0 15px 0; color: #666; font-size: 14px;\">\r\n            Obrigado por preencher o formulário! Continue a conversa conosco:\r\n          </p>\r\n          <button onclick=\"window.openTypebot('${this.typebotClient.sessionId}')\" style=\"\r\n            background: ${TYPEBOT_CONFIG.EMBED_CONFIG.theme.button.backgroundColor};\r\n            color: white;\r\n            border: none;\r\n            padding: 12px 24px;\r\n            border-radius: 6px;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n          \">\r\n            Continuar Conversa →\r\n          </button>\r\n        </div>\r\n      `;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine if embed mode should be used\r\n   */\r\n  shouldUseEmbed() {\r\n    return (\r\n      TYPEBOT_CONFIG.EMBED_CONFIG &&\r\n      TYPEBOT_CONFIG.EMBED_CONFIG.containerId &&\r\n      TYPEBOT_CONFIG.PUBLIC_ID\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Handle typebot errors with fallback\r\n   */\r\n  async handleTypebotError(error) {\r\n    console.error('🤖 Typebot error, falling back to direct submission:', error);\r\n\r\n    // Fallback to direct Supabase submission\r\n    if (this.currentFormData && this.completionCallbacks.length > 0) {\r\n      const fallbackData = {\r\n        ...this.currentFormData,\r\n        typebotError: error.message,\r\n        fallbackSubmission: true,\r\n        submittedAt: new Date().toISOString(),\r\n      };\r\n\r\n      // Execute completion callbacks with fallback data\r\n      for (const callback of this.completionCallbacks) {\r\n        try {\r\n          await callback(fallbackData);\r\n        } catch (callbackError) {\r\n          console.error('❌ Fallback callback error:', callbackError);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup global API for external integration\r\n   */\r\n  setupGlobalAPI() {\r\n    // Add to global ReinoCalculator API\r\n    if (window.ReinoCalculator) {\r\n      window.ReinoCalculator.typebot = {\r\n        start: (formData, onCompletion) => this.startTypebotFlow(formData, onCompletion),\r\n        complete: (data) => this.handleTypebotCompletion(data),\r\n        isActive: () => this.isTypebotActive,\r\n        client: this.typebotClient,\r\n        config: TYPEBOT_CONFIG,\r\n      };\r\n    }\r\n\r\n    // Global function for embed buttons\r\n    window.openTypebot = (sessionId) => {\r\n      // Open typebot in new window or modal\r\n      const typebotUrl = `https://typebot.io/${TYPEBOT_CONFIG.PUBLIC_ID}?sessionId=${sessionId}`;\r\n      window.open(typebotUrl, 'typebot', 'width=400,height=600,scrollbars=yes,resizable=yes');\r\n    };\r\n\r\n    // Global function to trigger completion (for webhook/external use)\r\n    window.triggerTypebotCompletion = (data) => {\r\n      document.dispatchEvent(\r\n        new CustomEvent('typebotFlowCompleted', {\r\n          detail: data,\r\n        })\r\n      );\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Add completion callback\r\n   */\r\n  onCompletion(callback) {\r\n    this.completionCallbacks.push(callback);\r\n  }\r\n\r\n  /**\r\n   * Remove completion callback\r\n   */\r\n  removeCompletion(callback) {\r\n    const index = this.completionCallbacks.indexOf(callback);\r\n    if (index > -1) {\r\n      this.completionCallbacks.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current status\r\n   */\r\n  getStatus() {\r\n    return {\r\n      isInitialized: this.isInitialized,\r\n      isTypebotActive: this.isTypebotActive,\r\n      hasFormData: !!this.currentFormData,\r\n      sessionId: this.typebotClient.sessionId,\r\n      isCompleted: this.typebotClient.isSessionCompleted(),\r\n      callbackCount: this.completionCallbacks.length,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Helper function to detect encrypted/placeholder values from Typebot\r\n   */\r\n  isEncryptedValue(value) {\r\n    if (!value || typeof value !== 'string') return false;\r\n\r\n    // Check for typical encrypted patterns\r\n    const encryptedPatterns = [\r\n      'XBCHzvp1qAbdX',\r\n      'VFChNVSCXQ2rXv4DrJ8Ah',\r\n      'giiLFGw5xXBCHzvp1qAbdX',\r\n      'v3VFChNVSCXQ2rXv4DrJ8Ah',\r\n    ];\r\n\r\n    // Check if value matches any known encrypted patterns\r\n    if (encryptedPatterns.some((pattern) => value.includes(pattern))) {\r\n      return true;\r\n    }\r\n\r\n    // Check for suspicious characteristics of encrypted values\r\n    if (value.length > 15 && !/\\s/.test(value) && !/[@.]/.test(value)) {\r\n      // Long string without spaces, dots, or @ signs - likely encrypted\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Close Typebot popup\r\n   */\r\n  closeTypebot(data = {}) {\r\n    try {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🔒 [TypebotIntegration] Closing Typebot popup...', data);\r\n\r\n      // Try to close using the Typebot library\r\n      if (this.typebotLibrary && typeof this.typebotLibrary.close === 'function') {\r\n        this.typebotLibrary.close();\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ [TypebotIntegration] Typebot closed via library method');\r\n      } else if (this.typebotLibrary && typeof this.typebotLibrary.toggle === 'function') {\r\n        // Some versions use toggle to close\r\n        this.typebotLibrary.toggle();\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ [TypebotIntegration] Typebot toggled (closed) via library method');\r\n      } else {\r\n        // Fallback: Try to find and hide the typebot container\r\n        const typebotContainer =\r\n          document.querySelector('#typebot-bubble') ||\r\n          document.querySelector('[data-testid=\"typebot-bubble\"]') ||\r\n          document.querySelector('.typebot-container');\r\n\r\n        if (typebotContainer) {\r\n          typebotContainer.style.display = 'none';\r\n          // eslint-disable-next-line no-console\r\n          console.log('✅ [TypebotIntegration] Typebot hidden via DOM manipulation');\r\n        } else {\r\n          console.warn('⚠️ [TypebotIntegration] Could not find Typebot container to close');\r\n        }\r\n      }\r\n\r\n      // Mark as inactive\r\n      this.isTypebotActive = false;\r\n    } catch (error) {\r\n      console.error('❌ [TypebotIntegration] Error closing Typebot:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup fallback navigation listener for direct navigation requests\r\n   */\r\n  setupFallbackNavigationListener() {\r\n    // eslint-disable-next-line no-console\r\n    console.log('🔧 [TypebotIntegration] Setting up fallback navigation listener...');\r\n\r\n    document.addEventListener('forceNavigateToResults', (event) => {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🎯 [TypebotIntegration] Fallback navigation event received:', event.detail);\r\n\r\n      try {\r\n        // Try to access the step navigation system through global API\r\n        if (\r\n          window.ReinoCalculator &&\r\n          window.ReinoCalculator.navigation &&\r\n          window.ReinoCalculator.navigation.stepNavigation\r\n        ) {\r\n          // eslint-disable-next-line no-console\r\n          console.log('✅ [TypebotIntegration] Executing fallback navigation to step 4...');\r\n          window.ReinoCalculator.navigation.stepNavigation.showStep(4);\r\n        } else {\r\n          console.warn(\r\n            '⚠️ [TypebotIntegration] ReinoCalculator navigation not available for fallback'\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ [TypebotIntegration] Fallback navigation failed:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Cleanup\r\n   */\r\n  cleanup() {\r\n    this.completionCallbacks = [];\r\n    this.currentFormData = null;\r\n    this.isTypebotActive = false;\r\n    this.typebotClient.reset();\r\n\r\n    // Remove embed container\r\n    const container = document.getElementById(TYPEBOT_CONFIG.EMBED_CONFIG.containerId);\r\n    if (container) {\r\n      container.remove();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACM7F,MAAM,iBAAiB;AAAA;AAAA,IAErB,WAAW;AAAA;AAAA;AAAA,IAGX,cAAc;AAAA;AAAA;AAAA,IAGd,YAAY;AAAA;AAAA;AAAA,IAGZ,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,YACE;AAAA,IACJ;AAAA;AAAA,IAGA,cAAc;AAAA;AAAA,MAEZ,aAAa;AAAA;AAAA,MAGb,UAAU;AAAA;AAAA,MAGV,QAAQ;AAAA;AAAA,MAGR,OAAO;AAAA,QACL,QAAQ;AAAA,UACN,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,eAAe;AAAA,UACf,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,QACA,YAAY;AAAA,UACV,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,WAAW;AAAA,QACb;AAAA,MACF;AAAA;AAAA,MAGA,eAAe;AAAA;AAAA,IACjB;AAAA;AAAA,IAGA,oBAAoB;AAAA;AAAA;AAAA,IAGpB,OAAO,OAAO,SAAS,aAAa,eAAe,OAAO,SAAS,OAAO,SAAS,YAAY;AAAA,EACjG;AAGA,MAAM,mBAAmB;AAAA;AAAA,IAEvB,YAAY;AAAA;AAAA,IACZ,kBAAkB;AAAA;AAAA,IAClB,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,cAAc;AAAA;AAAA,IACd,oBAAoB;AAAA;AAAA,IACpB,WAAW;AAAA,EACb;AAQO,MAAM,gBAAN,MAAoB;AAAA,IAjF3B,OAiF2B;AAAA;AAAA;AAAA,IACzB,YAAY,SAAS,gBAAgB;AACnC,WAAK,SAAS;AACd,WAAK,YAAY;AACjB,WAAK,WAAW;AAChB,WAAK,cAAc;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,UAAU,gBAAgB,CAAC,GAAG;AAClC,UAAI;AAEF,YAAI,OAAO,UAAU,aAAa;AAChC,gBAAM,IAAI,MAAM,4CAA4C;AAAA,QAC9D;AAEA,cAAM,MAAM,GAAG,KAAK,OAAO,YAAY,aAAa,KAAK,OAAO,SAAS;AAGzE,cAAM,qBAAqB,KAAK,uBAAuB,aAAa;AAEpE,cAAM,cAAc;AAAA,UAClB;AAAA,UACA,iBAAiB;AAAA,UACjB,yBAAyB;AAAA,QAC3B;AAEA,cAAM,UAAU;AAAA,UACd,gBAAgB;AAAA,QAClB;AAGA,YAAI,KAAK,OAAO,YAAY;AAC1B,kBAAQ,eAAe,IAAI,KAAK,OAAO;AAAA,QACzC;AAEA,cAAM,WAAW,MAAM,OAAO,MAAM,KAAK;AAAA,UACvC,QAAQ;AAAA,UACR;AAAA,UACA,MAAM,KAAK,UAAU,WAAW;AAAA,QAClC,CAAC;AAED,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,MAAM,sBAAsB,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;AAAA,QAChF;AAEA,cAAM,OAAO,MAAM,SAAS,KAAK;AAGjC,aAAK,YAAY,KAAK;AACtB,aAAK,WAAW,KAAK;AAErB,YAAI,KAAK,OAAO,OAAO;AAAA,QAMvB;AAEA,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,wCAAmC,KAAK;AACtD,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,aAAa,SAAS;AAC1B,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AAEA,UAAI;AACF,cAAM,MAAM,GAAG,KAAK,OAAO,YAAY,aAAa,KAAK,SAAS;AAElE,cAAM,cAAc;AAAA,UAClB,SAAS;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,UACR;AAAA,UACA,yBAAyB;AAAA,QAC3B;AAEA,cAAM,UAAU;AAAA,UACd,gBAAgB;AAAA,QAClB;AAEA,YAAI,KAAK,OAAO,YAAY;AAC1B,kBAAQ,eAAe,IAAI,KAAK,OAAO;AAAA,QACzC;AAEA,cAAM,WAAW,MAAM,OAAO,MAAM,KAAK;AAAA,UACvC,QAAQ;AAAA,UACR;AAAA,UACA,MAAM,KAAK,UAAU,WAAW;AAAA,QAClC,CAAC;AAED,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,MAAM,sBAAsB,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;AAAA,QAChF;AAEA,cAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,YAAI,KAAK,OAAO,OAAO;AAAA,QAEvB;AAEA,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,2CAAsC,KAAK;AACzD,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,uBAAuB,UAAU;AAC/B,YAAM,kBAAkB,CAAC;AAGzB,YAAM,eAAe,SAAS,WAC1B,OAAO,OAAO,SAAS,QAAQ,EAAE,OAAO,CAAC,KAAK,SAAS,OAAO,KAAK,SAAS,IAAI,CAAC,IACjF;AACJ,YAAM,sBAAsB,SAAS,cAAc,KAAK;AAGxD,YAAM,mBAAmB;AAAA,QACvB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAEA,iBAAW,CAAC,WAAW,UAAU,KAAK,OAAO,QAAQ,gBAAgB,GAAG;AACtE,YAAI,iBAAiB,SAAS,MAAM,QAAW;AAE7C,cAAI,cAAc,gBAAgB,OAAO,iBAAiB,SAAS,MAAM,UAAU;AACjF,4BAAgB,UAAU,IAAI,MAAM,iBAAiB,SAAS,EAAE,eAAe,OAAO,CAAC;AAAA,UACzF,WACE,cAAc,kBACd,OAAO,iBAAiB,SAAS,MAAM,UACvC;AACA,4BAAgB,UAAU,IAAI,MAAM,iBAAiB,SAAS,EAAE,eAAe,OAAO,CAAC;AAAA,UACzF,WACE,cAAc,wBACd,OAAO,iBAAiB,SAAS,MAAM,UACvC;AACA,4BAAgB,UAAU,IAAI,MAAM,iBAAiB,SAAS,EAAE,eAAe,OAAO,CAAC;AAAA,UACzF,WAAW,cAAc,sBAAsB,MAAM,QAAQ,iBAAiB,SAAS,CAAC,GAAG;AAEzF,kBAAM,aAAa,iBAAiB,SAAS,EAC1C,IAAI,CAAC,UAAU,GAAG,MAAM,OAAO,KAAK,MAAM,QAAQ,GAAG,EACrD,KAAK,IAAI;AACZ,4BAAgB,UAAU,IAAI;AAAA,UAChC,WAAW,cAAc,cAAc,OAAO,iBAAiB,SAAS,MAAM,UAAU;AAEtF,kBAAM,eAAe,OAAO,QAAQ,iBAAiB,SAAS,CAAC,EAC5D,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,KAAK,SAAS,KAAK,CAAC,EAC1C;AAAA,cACC,CAAC,CAAC,EAAE,IAAI,MACN,GAAG,KAAK,OAAO,SAAS,KAAK,SAAS,GAAG,eAAe,OAAO,CAAC,KAAK,KAAK,OAAO,KAAK,cAAc,KAAK,GAAG,IAAI,GAAG;AAAA,YACvH,EACC,KAAK,IAAI;AACZ,4BAAgB,UAAU,IAAI,gBAAgB;AAAA,UAChD,OAAO;AACL,4BAAgB,UAAU,IAAI,OAAO,iBAAiB,SAAS,CAAC;AAAA,UAClE;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,qBAAqB;AACnB,aAAO,KAAK;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAKA,gBAAgB;AACd,WAAK,cAAc;AAEnB,UAAI,KAAK,OAAO,OAAO;AAAA,MAEvB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,QAAQ;AACN,WAAK,YAAY;AACjB,WAAK,WAAW;AAChB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;;;AC5RO,MAAM,2BAAN,MAA+B;AAAA,IARtC,OAQsC;AAAA;AAAA;AAAA,IACpC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,gBAAgB,IAAI,cAAc,cAAc;AACrD,WAAK,sBAAsB,CAAC;AAC5B,WAAK,kBAAkB;AACvB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AAGtB,WAAK,0BAA0B,KAAK,wBAAwB,KAAK,IAAI;AAAA,IACvE;AAAA,IAEA,MAAM,OAAO;AACX,UAAI,KAAK,cAAe;AAExB,UAAI;AAEF,cAAM,KAAK,mBAAmB;AAG9B,cAAM,KAAK,uBAAuB;AAGlC,aAAK,wBAAwB;AAG7B,aAAK,oBAAoB;AAGzB,aAAK,eAAe;AAEpB,aAAK,gBAAgB;AAAA,MACvB,SAAS,OAAO;AACd,gBAAQ,MAAM,0DAAqD,KAAK;AAAA,MAC1E;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,qBAAqB;AACzB,UAAI;AAEF,YAAI,OAAO,SAAS;AAClB,eAAK,iBAAiB,OAAO;AAC7B;AAAA,QACF;AAGA,cAAM,EAAE,SAAS,QAAQ,IAAI,MAAM,OAAO,eAAe,aAAa;AACtE,aAAK,iBAAiB;AACtB,eAAO,UAAU;AAEjB,YAAI,eAAe,OAAO;AAExB,kBAAQ,IAAI,+CAAwC;AAAA,QACtD;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,0CAAqC,KAAK;AACxD,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,yBAAyB;AAC7B,UAAI,CAAC,KAAK,gBAAgB;AACxB,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAC9C;AAEA,UAAI;AAEF,cAAM,KAAK,eAAe,UAAU;AAAA,UAClC,SAAS,eAAe;AAAA,UACxB,oBAAoB,CAAC;AAAA;AAAA,UACrB,WAAW,wBAAC,YAAY;AAEtB,iBAAK,qBAAqB,OAAO;AAAA,UACnC,GAHW;AAAA,UAIX,OAAO,6BAAM;AAEX,iBAAK,iBAAiB;AAAA,UACxB,GAHO;AAAA,QAIT,CAAC;AAED,YAAI,eAAe,OAAO;AAExB,kBAAQ,IAAI,qCAA8B;AAAA,QAC5C;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,8CAAyC,KAAK;AAC5D,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,qBAAqB,SAAS;AAC5B,UAAI,eAAe,OAAO;AAExB,gBAAQ,IAAI,8BAAuB,OAAO;AAAA,MAC5C;AAGA,UAAI,QAAQ,SAAS,gBAAgB,QAAQ,MAAM,WAAW;AAC5D,aAAK,wBAAwB,QAAQ,IAAI;AAAA,MAC3C;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB;AAEjB,cAAQ,IAAI,6EAAsE;AAElF,WAAK,kBAAkB;AAGvB,UAAI,KAAK,iBAAiB;AAExB,gBAAQ,IAAI,0EAAmE;AAC/E,aAAK,wBAAwB,EAAE,WAAW,MAAM,eAAe,KAAK,CAAC;AAAA,MACvE,OAAO;AACL,gBAAQ,KAAK,8EAAoE;AAAA,MACnF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,MAAM,iBAAiB,UAAU,eAAe,MAAM;AACpD,UAAI;AAEF,gBAAQ,IAAI,gEAAyD,QAAQ;AAE7E,YAAI,CAAC,KAAK,gBAAgB;AACxB,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAGA,aAAK,kBAAkB;AACvB,YAAI,cAAc;AAChB,eAAK,oBAAoB,KAAK,YAAY;AAE1C,kBAAQ,IAAI,+DAAwD;AAAA,QACtE;AAGA,gBAAQ,IAAI,mDAA4C,KAAK,oBAAoB,MAAM,EAAE;AAGzF,cAAM,qBAAqB,KAAK,cAAc,uBAAuB,QAAQ;AAG7E,gBAAQ,IAAI,uDAAgD,kBAAkB;AAG9E,YAAI,KAAK,eAAe,uBAAuB;AAC7C,eAAK,eAAe,sBAAsB,kBAAkB;AAAA,QAC9D;AAIA,gBAAQ,IAAI,yDAAkD;AAC9D,aAAK,eAAe,KAAK;AAEzB,aAAK,kBAAkB;AAGvB,iBAAS;AAAA,UACP,IAAI,YAAY,kBAAkB;AAAA,YAChC,QAAQ;AAAA,cACN;AAAA,cACA;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAGA,gBAAQ,IAAI,+DAA0D;AAEtE,eAAO,EAAE,SAAS,MAAM,mBAAmB;AAAA,MAC7C,SAAS,OAAO;AACd,gBAAQ,MAAM,6DAAwD,KAAK;AAG3E,aAAK,mBAAmB,KAAK;AAC7B,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,wBAAwB,cAAc,CAAC,GAAG;AAC9C,UAAI;AAEF,gBAAQ,IAAI,4EAAqE,WAAW;AAI5F,gBAAQ;AAAA,UACN;AAAA,UACA,KAAK,UAAU,aAAa,MAAM,CAAC;AAAA,QACrC;AAEA,YAAI,CAAC,KAAK,iBAAiB;AACzB,kBAAQ,MAAM,4EAAuE;AACrF;AAAA,QACF;AAGA,gBAAQ,IAAI,qDAA8C,KAAK,eAAe;AAG9E,aAAK,cAAc,cAAc;AACjC,aAAK,kBAAkB;AAIvB,YAAI,OAAO;AACX,YAAI,QAAQ;AAGZ,gBAAQ;AAAA,UACN;AAAA,UACA,KAAK,UAAU,aAAa,MAAM,CAAC;AAAA,QACrC;AAGA,YAAI,YAAY,QAAQ,CAAC,KAAK,iBAAiB,YAAY,IAAI,GAAG;AAChE,iBAAO,YAAY;AAAA,QACrB,WAAW,YAAY,QAAQ,KAAK,iBAAiB,YAAY,IAAI,GAAG;AAEtE,kBAAQ;AAAA,YACN;AAAA,YACA,YAAY;AAAA,UACd;AACA,iBAAO,qCAAqC,YAAY,KAAK,UAAU,GAAG,CAAC,IAAI;AAAA,QACjF;AAEA,YAAI,YAAY,SAAS,CAAC,KAAK,iBAAiB,YAAY,KAAK,GAAG;AAClE,kBAAQ,YAAY;AAAA,QACtB,WAAW,YAAY,SAAS,KAAK,iBAAiB,YAAY,KAAK,GAAG;AAExE,kBAAQ;AAAA,YACN;AAAA,YACA,YAAY;AAAA,UACd;AACA,kBAAQ,mBAAmB,YAAY,MAAM,UAAU,GAAG,CAAC,IAAI;AAAA,QACjE;AAGA,YAAI,CAAC,MAAM;AACT,iBAAO,YAAY,QAAQ,YAAY,gBAAgB,YAAY,YAAY;AAC/E,cAAI,QAAQ,KAAK,iBAAiB,IAAI,EAAG,QAAO;AAAA,QAClD;AACA,YAAI,CAAC,OAAO;AACV,kBAAQ,YAAY,UAAU,YAAY,aAAa,YAAY,iBAAiB;AACpF,cAAI,SAAS,KAAK,iBAAiB,KAAK,EAAG,SAAQ;AAAA,QACrD;AAGA,YAAI,YAAY,aAAa;AAC3B,gBAAM,cAAc,YAAY,YAAY,aAAa,CAAC;AAC1D,cAAI,CAAC,QAAQ,YAAY,QAAQ,CAAC,KAAK,iBAAiB,YAAY,IAAI,GAAG;AACzE,mBAAO,YAAY;AAAA,UACrB;AACA,cAAI,CAAC,SAAS,YAAY,SAAS,CAAC,KAAK,iBAAiB,YAAY,KAAK,GAAG;AAC5E,oBAAQ,YAAY;AAAA,UACtB;AAAA,QACF;AAGA,YAAI,YAAY,YAAY;AAC1B,gBAAM,aAAa,YAAY,WAAW,aAAa,CAAC;AACxD,cAAI,CAAC,QAAQ,WAAW,QAAQ,CAAC,KAAK,iBAAiB,WAAW,IAAI,GAAG;AACvE,mBAAO,WAAW;AAAA,UACpB;AACA,cAAI,CAAC,SAAS,WAAW,SAAS,CAAC,KAAK,iBAAiB,WAAW,KAAK,GAAG;AAC1E,oBAAQ,WAAW;AAAA,UACrB;AAAA,QACF;AAGA,YAAI,YAAY,WAAW;AACzB,cACE,CAAC,QACD,YAAY,UAAU,QACtB,CAAC,KAAK,iBAAiB,YAAY,UAAU,IAAI,GACjD;AACA,mBAAO,YAAY,UAAU;AAAA,UAC/B;AACA,cACE,CAAC,SACD,YAAY,UAAU,SACtB,CAAC,KAAK,iBAAiB,YAAY,UAAU,KAAK,GAClD;AACA,oBAAQ,YAAY,UAAU;AAAA,UAChC;AAAA,QACF;AAGA,YAAI,YAAY,WAAW,MAAM,QAAQ,YAAY,OAAO,GAAG;AAC7D,qBAAW,UAAU,YAAY,SAAS;AACxC,gBAAI,OAAO,SAAS,UAAU,OAAO,SAAS,CAAC,KAAK,iBAAiB,OAAO,KAAK,GAAG;AAElF,kBACE,CAAC,SACA,OAAO,UAAU,YAAY,EAAE,SAAS,MAAM,KAC7C,OAAO,UAAU,YAAY,EAAE,SAAS,MAAM,KAC7C,OAAO,MAAM,SAAS,KACrB,OAAO,MAAM,SAAS,MACtB,CAAC,OAAO,MAAM,SAAS,GAAG,IAC9B;AACA,uBAAO,OAAO;AAAA,cAChB;AAEA,kBAAI,CAAC,SAAS,OAAO,MAAM,SAAS,GAAG,KAAK,OAAO,MAAM,SAAS,GAAG,GAAG;AACtE,wBAAQ,OAAO;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAIA,gBAAQ,IAAI,oEAA6D,EAAE,MAAM,MAAM,CAAC;AAGxF,YAAI,QAAQ,KAAK,iBAAiB,IAAI,GAAG;AACvC,kBAAQ,KAAK,+EAAqE,IAAI;AACtF,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,KAAK,iBAAiB,KAAK,GAAG;AACzC,kBAAQ,KAAK,gFAAsE,KAAK;AACxF,kBAAQ;AAAA,QACV;AAGA,cAAM,mBAAmB;AAAA,UACvB,GAAG,KAAK;AAAA;AAAA,UAER;AAAA,UACA;AAAA;AAAA,UAEA,kBAAkB,KAAK,cAAc;AAAA,UACrC,iBAAiB,KAAK,cAAc;AAAA,UACpC;AAAA,UACA,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,QACtC;AAGA,gBAAQ,IAAI,+DAAwD,gBAAgB;AAGpF,YAAI,CAAC,QAAQ,CAAC,OAAO;AACnB,kBAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAGA,gBAAQ;AAAA,UACN,4CAAqC,KAAK,oBAAoB,MAAM;AAAA,QACtE;AAGA,mBAAW,YAAY,KAAK,qBAAqB;AAC/C,cAAI;AAEF,oBAAQ,IAAI,+DAAwD;AACpE,kBAAM,SAAS,gBAAgB;AAE/B,oBAAQ,IAAI,uEAAkE;AAAA,UAChF,SAAS,eAAe;AACtB,oBAAQ,MAAM,0DAAqD,aAAa;AAAA,UAClF;AAAA,QACF;AAGA,aAAK,sBAAsB,CAAC;AAC5B,aAAK,kBAAkB;AAGvB,gBAAQ,IAAI,sEAA+D;AAG3E,iBAAS;AAAA,UACP,IAAI,YAAY,oBAAoB;AAAA,YAClC,QAAQ;AAAA,cACN,UAAU;AAAA,cACV;AAAA,cACA,UAAU,EAAE,MAAM,MAAM;AAAA,YAC1B;AAAA,UACF,CAAC;AAAA,QACH;AAGA,gBAAQ,IAAI,4EAAuE;AAAA,MACrF,SAAS,OAAO;AACd,gBAAQ,MAAM,oEAA+D,KAAK;AAAA,MACpF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,0BAA0B;AAExB,cAAQ,IAAI,mEAA4D;AAGxE,eAAS,iBAAiB,wBAAwB,CAAC,UAAU;AAE3D,gBAAQ,IAAI,uEAAgE,MAAM,MAAM;AACxF,aAAK,wBAAwB,MAAM,MAAM;AAAA,MAC3C,CAAC;AAGD,aAAO,iBAAiB,WAAW,CAAC,UAAU;AAE5C,gBAAQ,IAAI,wDAAiD,MAAM,IAAI;AAEvE,YAAI,MAAM,QAAQ,MAAM,KAAK,SAAS,sBAAsB;AAE1D,kBAAQ,IAAI,oEAA6D;AACzE,eAAK,wBAAwB,MAAM,KAAK,IAAI;AAAA,QAC9C,WAAW,MAAM,QAAQ,MAAM,KAAK,SAAS,yBAAyB;AAEpE,kBAAQ,IAAI,+DAAwD;AACpE,eAAK,aAAa,MAAM,KAAK,IAAI;AAAA,QACnC;AAAA,MACF,CAAC;AAGD,WAAK,qBAAqB;AAG1B,WAAK,gCAAgC;AAGrC,cAAQ,IAAI,iEAA4D;AAAA,IAC1E;AAAA;AAAA;AAAA;AAAA,IAKA,uBAAuB;AAGrB,UAAI,eAAe,oBAAoB;AAGrC,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,yBAAyB;AACvB,YAAM,eAAe,YAAY,MAAM;AACrC,YAAI,CAAC,KAAK,mBAAmB,KAAK,cAAc,mBAAmB,GAAG;AACpE,wBAAc,YAAY;AAC1B;AAAA,QACF;AAIA,aAAK,uBAAuB;AAAA,MAC9B,GAAG,GAAI;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,yBAAyB;AAAA,IAI/B;AAAA;AAAA;AAAA;AAAA,IAKA,sBAAsB;AACpB,YAAM,EAAE,YAAY,IAAI,eAAe;AACvC,UAAI,YAAY,SAAS,eAAe,WAAW;AAEnD,UAAI,CAAC,WAAW;AAEd,oBAAY,SAAS,cAAc,KAAK;AACxC,kBAAU,KAAK;AACf,kBAAU,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,qBAKX,eAAe,aAAa,MAAM,WAAW,QAAQ;AAAA,sBACpD,eAAe,aAAa,MAAM,WAAW,SAAS;AAAA;AAEtE,iBAAS,KAAK,YAAY,SAAS;AAAA,MACrC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,kBAAkB;AAGhB,YAAM,YAAY,SAAS,eAAe,eAAe,aAAa,WAAW;AACjF,UAAI,WAAW;AACb,kBAAU,YAAY;AAAA;AAAA,wBAEJ,eAAe,aAAa,MAAM,WAAW,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDAUnC,KAAK,cAAc,SAAS;AAAA,0BACnD,eAAe,aAAa,MAAM,OAAO,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAa9E;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB;AACf,aACE,eAAe,gBACf,eAAe,aAAa,eAC5B,eAAe;AAAA,IAEnB;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,mBAAmB,OAAO;AAC9B,cAAQ,MAAM,+DAAwD,KAAK;AAG3E,UAAI,KAAK,mBAAmB,KAAK,oBAAoB,SAAS,GAAG;AAC/D,cAAM,eAAe;AAAA,UACnB,GAAG,KAAK;AAAA,UACR,cAAc,MAAM;AAAA,UACpB,oBAAoB;AAAA,UACpB,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,QACtC;AAGA,mBAAW,YAAY,KAAK,qBAAqB;AAC/C,cAAI;AACF,kBAAM,SAAS,YAAY;AAAA,UAC7B,SAAS,eAAe;AACtB,oBAAQ,MAAM,mCAA8B,aAAa;AAAA,UAC3D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB;AAEf,UAAI,OAAO,iBAAiB;AAC1B,eAAO,gBAAgB,UAAU;AAAA,UAC/B,OAAO,wBAAC,UAAU,iBAAiB,KAAK,iBAAiB,UAAU,YAAY,GAAxE;AAAA,UACP,UAAU,wBAAC,SAAS,KAAK,wBAAwB,IAAI,GAA3C;AAAA,UACV,UAAU,6BAAM,KAAK,iBAAX;AAAA,UACV,QAAQ,KAAK;AAAA,UACb,QAAQ;AAAA,QACV;AAAA,MACF;AAGA,aAAO,cAAc,CAAC,cAAc;AAElC,cAAM,aAAa,sBAAsB,eAAe,SAAS,cAAc,SAAS;AACxF,eAAO,KAAK,YAAY,WAAW,mDAAmD;AAAA,MACxF;AAGA,aAAO,2BAA2B,CAAC,SAAS;AAC1C,iBAAS;AAAA,UACP,IAAI,YAAY,wBAAwB;AAAA,YACtC,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa,UAAU;AACrB,WAAK,oBAAoB,KAAK,QAAQ;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB,UAAU;AACzB,YAAM,QAAQ,KAAK,oBAAoB,QAAQ,QAAQ;AACvD,UAAI,QAAQ,IAAI;AACd,aAAK,oBAAoB,OAAO,OAAO,CAAC;AAAA,MAC1C;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AACV,aAAO;AAAA,QACL,eAAe,KAAK;AAAA,QACpB,iBAAiB,KAAK;AAAA,QACtB,aAAa,CAAC,CAAC,KAAK;AAAA,QACpB,WAAW,KAAK,cAAc;AAAA,QAC9B,aAAa,KAAK,cAAc,mBAAmB;AAAA,QACnD,eAAe,KAAK,oBAAoB;AAAA,MAC1C;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB,OAAO;AACtB,UAAI,CAAC,SAAS,OAAO,UAAU,SAAU,QAAO;AAGhD,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,UAAI,kBAAkB,KAAK,CAAC,YAAY,MAAM,SAAS,OAAO,CAAC,GAAG;AAChE,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,SAAS,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,GAAG;AAEjE,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa,OAAO,CAAC,GAAG;AACtB,UAAI;AAEF,gBAAQ,IAAI,2DAAoD,IAAI;AAGpE,YAAI,KAAK,kBAAkB,OAAO,KAAK,eAAe,UAAU,YAAY;AAC1E,eAAK,eAAe,MAAM;AAE1B,kBAAQ,IAAI,+DAA0D;AAAA,QACxE,WAAW,KAAK,kBAAkB,OAAO,KAAK,eAAe,WAAW,YAAY;AAElF,eAAK,eAAe,OAAO;AAE3B,kBAAQ,IAAI,yEAAoE;AAAA,QAClF,OAAO;AAEL,gBAAM,mBACJ,SAAS,cAAc,iBAAiB,KACxC,SAAS,cAAc,gCAAgC,KACvD,SAAS,cAAc,oBAAoB;AAE7C,cAAI,kBAAkB;AACpB,6BAAiB,MAAM,UAAU;AAEjC,oBAAQ,IAAI,iEAA4D;AAAA,UAC1E,OAAO;AACL,oBAAQ,KAAK,6EAAmE;AAAA,UAClF;AAAA,QACF;AAGA,aAAK,kBAAkB;AAAA,MACzB,SAAS,OAAO;AACd,gBAAQ,MAAM,sDAAiD,KAAK;AAAA,MACtE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,kCAAkC;AAEhC,cAAQ,IAAI,2EAAoE;AAEhF,eAAS,iBAAiB,0BAA0B,CAAC,UAAU;AAE7D,gBAAQ,IAAI,sEAA+D,MAAM,MAAM;AAEvF,YAAI;AAEF,cACE,OAAO,mBACP,OAAO,gBAAgB,cACvB,OAAO,gBAAgB,WAAW,gBAClC;AAEA,oBAAQ,IAAI,wEAAmE;AAC/E,mBAAO,gBAAgB,WAAW,eAAe,SAAS,CAAC;AAAA,UAC7D,OAAO;AACL,oBAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,QACF,SAAS,OAAO;AACd,kBAAQ,MAAM,2DAAsD,KAAK;AAAA,QAC3E;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA,IAKA,UAAU;AACR,WAAK,sBAAsB,CAAC;AAC5B,WAAK,kBAAkB;AACvB,WAAK,kBAAkB;AACvB,WAAK,cAAc,MAAM;AAGzB,YAAM,YAAY,SAAS,eAAe,eAAe,aAAa,WAAW;AACjF,UAAI,WAAW;AACb,kBAAU,OAAO;AAAA,MACnB;AAAA,IACF;AAAA,EACF;", "names": []}