{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/attribute-fixer.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Attribute Fixer System\r\n * Runtime correction of attribute inconsistencies in Webflow elements\r\n * This system fixes critical pairing issues without modifying the source Webflow files\r\n */\r\n\r\nexport class AttributeFixerSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.fixesApplied = [];\r\n    this.originalAttributes = new Map(); // Store original values for debugging\r\n  }\r\n\r\n  async init() {\r\n    try {\r\n      // console.log('🔧 AttributeFixerSystem: Starting runtime corrections...');\r\n\r\n      // Store original attributes before making changes\r\n      this.backupOriginalAttributes();\r\n\r\n      // Apply all fixes\r\n      this.fixCapitalizationIssues();\r\n      this.fixTypos();\r\n      this.fixCategoryAssignments();\r\n      this.fixAttributeOrder();\r\n      this.fixDuplicates();\r\n\r\n      this.isInitialized = true;\r\n\r\n      // console.log(`✅ AttributeFixerSystem: Applied ${this.fixesApplied.length} fixes`);\r\n      // this.logFixesSummary();\r\n    } catch (error) {\r\n      console.error('❌ AttributeFixerSystem initialization failed:', error);\r\n    }\r\n  }\r\n\r\n  backupOriginalAttributes() {\r\n    // Backup patrimonio elements\r\n    const patrimonioElements = document.querySelectorAll(\r\n      '.patrimonio_interactive_item[ativo-category][ativo-product]'\r\n    );\r\n    patrimonioElements.forEach((el, index) => {\r\n      this.originalAttributes.set(`patrimonio-${index}`, {\r\n        category: el.getAttribute('ativo-category'),\r\n        product: el.getAttribute('ativo-product'),\r\n        element: el,\r\n      });\r\n    });\r\n\r\n    // Backup ativos elements\r\n    const ativosElements = document.querySelectorAll(\r\n      '.ativos-grafico-item[ativo-category][ativo-product]'\r\n    );\r\n    ativosElements.forEach((el, index) => {\r\n      this.originalAttributes.set(`ativos-${index}`, {\r\n        category: el.getAttribute('ativo-category'),\r\n        product: el.getAttribute('ativo-product'),\r\n        element: el,\r\n      });\r\n    });\r\n\r\n    // console.log(`📋 Backed up attributes for ${this.originalAttributes.size} elements`);\r\n  }\r\n\r\n  fixCapitalizationIssues() {\r\n    // console.log('🔧 Fixing capitalization issues...');\r\n\r\n    // Fix \"Fundo de Investimento\" → \"Fundo de investimento\"\r\n    this.applyAttributeFix(\r\n      '.patrimonio_interactive_item[ativo-category=\"Fundo de Investimento\"]',\r\n      'ativo-category',\r\n      'Fundo de investimento',\r\n      'Capitalization: Fundo de Investimento → Fundo de investimento'\r\n    );\r\n\r\n    // Fix \"Renda Variável\" → \"Renda variável\"\r\n    this.applyAttributeFix(\r\n      '.patrimonio_interactive_item[ativo-category=\"Renda Variável\"]',\r\n      'ativo-category',\r\n      'Renda variável',\r\n      'Capitalization: Renda Variável → Renda variável'\r\n    );\r\n  }\r\n\r\n  fixTypos() {\r\n    // console.log('🔧 Fixing typos...');\r\n\r\n    // Fix \"Popupança\" → \"Poupança\"\r\n    this.applyAttributeFix(\r\n      '.patrimonio_interactive_item[ativo-product=\"Popupança\"]',\r\n      'ativo-product',\r\n      'Poupança',\r\n      'Typo fix: Popupança → Poupança'\r\n    );\r\n  }\r\n\r\n  fixCategoryAssignments() {\r\n    // console.log('🔧 Fixing category assignments...');\r\n\r\n    // Fix \"Operação compromissada\" category for \"Criptoativos\" → should be \"Outros\"\r\n    this.applyAttributeFix(\r\n      '.patrimonio_interactive_item[ativo-category=\"Operação compromissada\"][ativo-product=\"Criptoativos\"]',\r\n      'ativo-category',\r\n      'Outros',\r\n      'Category fix: Criptoativos moved from \"Operação compromissada\" to \"Outros\"'\r\n    );\r\n  }\r\n\r\n  fixAttributeOrder() {\r\n    // console.log('🔧 Standardizing attribute order...');\r\n\r\n    // Find elements with ativo-product before ativo-category and reorder them\r\n    const elementsToReorder = document.querySelectorAll('[ativo-product][ativo-category]');\r\n\r\n    elementsToReorder.forEach((el) => {\r\n      const category = el.getAttribute('ativo-category');\r\n      const product = el.getAttribute('ativo-product');\r\n\r\n      if (category && product) {\r\n        // Remove both attributes\r\n        el.removeAttribute('ativo-category');\r\n        el.removeAttribute('ativo-product');\r\n\r\n        // Add them back in correct order (category first)\r\n        el.setAttribute('ativo-category', category);\r\n        el.setAttribute('ativo-product', product);\r\n\r\n        this.fixesApplied.push(`Attribute order: Standardized order for ${category} + ${product}`);\r\n      }\r\n    });\r\n  }\r\n\r\n  fixDuplicates() {\r\n    // console.log('🔧 Handling duplicate elements...');\r\n\r\n    // Find duplicate ativos elements\r\n    const ativosElements = document.querySelectorAll(\r\n      '.ativos-grafico-item[ativo-category][ativo-product]'\r\n    );\r\n    const seen = new Set();\r\n    const duplicates = [];\r\n\r\n    ativosElements.forEach((el) => {\r\n      const category = el.getAttribute('ativo-category');\r\n      const product = el.getAttribute('ativo-product');\r\n      const key = `${category}|${product}`;\r\n\r\n      if (seen.has(key)) {\r\n        duplicates.push({ element: el, category, product });\r\n      } else {\r\n        seen.add(key);\r\n      }\r\n    });\r\n\r\n    // Handle the specific duplicate: \"Fundo de investimento\" + \"Ações\"\r\n    duplicates.forEach((duplicate) => {\r\n      if (duplicate.category === 'Fundo de investimento' && duplicate.product === 'Ações') {\r\n        // Change the duplicate to \"Renda variável\" + \"Ações\" to match the patrimonio element\r\n        duplicate.element.setAttribute('ativo-category', 'Renda variável');\r\n        this.fixesApplied.push(\r\n          'Duplicate fix: Changed duplicate \"Fundo de investimento + Ações\" to \"Renda variável + Ações\"'\r\n        );\r\n      }\r\n    });\r\n  }\r\n\r\n  applyAttributeFix(selector, attribute, newValue, description) {\r\n    const elements = document.querySelectorAll(selector);\r\n\r\n    elements.forEach((el) => {\r\n      el.setAttribute(attribute, newValue);\r\n      this.fixesApplied.push(`${description} (${selector})`);\r\n    });\r\n\r\n    if (elements.length === 0) {\r\n      console.warn(`  ⚠️ No elements found for selector: ${selector}`);\r\n    }\r\n  }\r\n\r\n  logFixesSummary() {\r\n    // Logging disabled for production\r\n    // console.log('\\n📋 ATTRIBUTE FIXES SUMMARY:');\r\n    // console.log('='.repeat(50));\r\n    //\r\n    // this.fixesApplied.forEach((fix, index) => {\r\n    //   console.log(`${index + 1}. ${fix}`);\r\n    // });\r\n    //\r\n    // console.log('\\n🎯 EXPECTED IMPROVEMENTS:');\r\n    // console.log('✅ Capitalization mismatches resolved');\r\n    // console.log('✅ Typos corrected');\r\n    // console.log('✅ Category assignments fixed');\r\n    // console.log('✅ Duplicate elements handled');\r\n    // console.log('✅ Attribute order standardized');\r\n    //\r\n    // console.log('\\n🔍 To verify fixes worked:');\r\n    // console.log('  • Run: ReinoCalculator.debugSync()');\r\n    // console.log('  • Check: Should see 15/15 elements paired (100% success rate)');\r\n  }\r\n\r\n  // Validation method to check if fixes worked\r\n  validateFixes() {\r\n    // console.log('🔍 Validating applied fixes...');\r\n\r\n    const patrimonioElements = document.querySelectorAll(\r\n      '.patrimonio_interactive_item[ativo-category][ativo-product]'\r\n    );\r\n    const ativosElements = document.querySelectorAll(\r\n      '.ativos-grafico-item[ativo-category][ativo-product]'\r\n    );\r\n\r\n    // console.log(\r\n    //   `📊 Elements found: ${patrimonioElements.length} patrimonio, ${ativosElements.length} ativos`\r\n    // );\r\n\r\n    // Check for exact matches\r\n    let successfulPairs = 0;\r\n    const patrimonioAttribs = Array.from(patrimonioElements).map((el) => ({\r\n      category: el.getAttribute('ativo-category'),\r\n      product: el.getAttribute('ativo-product'),\r\n    }));\r\n\r\n    const ativosAttribs = Array.from(ativosElements).map((el) => ({\r\n      category: el.getAttribute('ativo-category'),\r\n      product: el.getAttribute('ativo-product'),\r\n    }));\r\n\r\n    patrimonioAttribs.forEach((p) => {\r\n      const hasMatch = ativosAttribs.some(\r\n        (a) => a.category === p.category && a.product === p.product\r\n      );\r\n      if (hasMatch) {\r\n        successfulPairs += 1;\r\n        // console.log(`✅ PAIRED: \"${p.category}\" + \"${p.product}\"`);\r\n      } else {\r\n        console.warn(`❌ UNPAIRED: \"${p.category}\" + \"${p.product}\"`);\r\n      }\r\n    });\r\n\r\n    const successRate = (successfulPairs / patrimonioElements.length) * 100;\r\n    // console.log(\r\n    //   `\\n📈 SUCCESS RATE: ${successfulPairs}/${patrimonioElements.length} (${successRate.toFixed(1)}%)`\r\n    // );\r\n\r\n    if (successRate === 100) {\r\n      // console.log('🎉 ALL ELEMENTS CAN NOW BE PAIRED!');\r\n    } else {\r\n      console.warn('⚠️ Some elements still cannot be paired. Check logs above.');\r\n    }\r\n\r\n    return { successfulPairs, total: patrimonioElements.length, successRate };\r\n  }\r\n\r\n  // Restore original attributes (for debugging/testing)\r\n  restoreOriginalAttributes() {\r\n    // console.log('🔄 Restoring original attributes...');\r\n\r\n    this.originalAttributes.forEach((backup) => {\r\n      const el = backup.element;\r\n      if (el && el.parentNode) {\r\n        el.setAttribute('ativo-category', backup.category);\r\n        el.setAttribute('ativo-product', backup.product);\r\n      }\r\n    });\r\n\r\n    this.fixesApplied = [];\r\n    // console.log('✅ Original attributes restored');\r\n  }\r\n\r\n  // Get detailed status\r\n  getStatus() {\r\n    return {\r\n      initialized: this.isInitialized,\r\n      fixesApplied: this.fixesApplied.length,\r\n      hasBackup: this.originalAttributes.size > 0,\r\n      fixes: this.fixesApplied,\r\n    };\r\n  }\r\n\r\n  // Cleanup method\r\n  cleanup() {\r\n    this.fixesApplied = [];\r\n    this.originalAttributes.clear();\r\n    this.isInitialized = false;\r\n    // console.log('🧹 AttributeFixerSystem cleaned up');\r\n  }\r\n}\r\n\r\n// Export for use in main app\r\nexport default AttributeFixerSystem;\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACMtF,MAAM,uBAAN,MAA2B;AAAA,IANlC,OAMkC;AAAA;AAAA;AAAA,IAChC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,eAAe,CAAC;AACrB,WAAK,qBAAqB,oBAAI,IAAI;AAAA,IACpC;AAAA,IAEA,MAAM,OAAO;AACX,UAAI;AAIF,aAAK,yBAAyB;AAG9B,aAAK,wBAAwB;AAC7B,aAAK,SAAS;AACd,aAAK,uBAAuB;AAC5B,aAAK,kBAAkB;AACvB,aAAK,cAAc;AAEnB,aAAK,gBAAgB;AAAA,MAIvB,SAAS,OAAO;AACd,gBAAQ,MAAM,sDAAiD,KAAK;AAAA,MACtE;AAAA,IACF;AAAA,IAEA,2BAA2B;AAEzB,YAAM,qBAAqB,SAAS;AAAA,QAClC;AAAA,MACF;AACA,yBAAmB,QAAQ,CAAC,IAAI,UAAU;AACxC,aAAK,mBAAmB,IAAI,cAAc,KAAK,IAAI;AAAA,UACjD,UAAU,GAAG,aAAa,gBAAgB;AAAA,UAC1C,SAAS,GAAG,aAAa,eAAe;AAAA,UACxC,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAGD,YAAM,iBAAiB,SAAS;AAAA,QAC9B;AAAA,MACF;AACA,qBAAe,QAAQ,CAAC,IAAI,UAAU;AACpC,aAAK,mBAAmB,IAAI,UAAU,KAAK,IAAI;AAAA,UAC7C,UAAU,GAAG,aAAa,gBAAgB;AAAA,UAC1C,SAAS,GAAG,aAAa,eAAe;AAAA,UACxC,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAAA,IAGH;AAAA,IAEA,0BAA0B;AAIxB,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,WAAW;AAIT,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,yBAAyB;AAIvB,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,oBAAoB;AAIlB,YAAM,oBAAoB,SAAS,iBAAiB,iCAAiC;AAErF,wBAAkB,QAAQ,CAAC,OAAO;AAChC,cAAM,WAAW,GAAG,aAAa,gBAAgB;AACjD,cAAM,UAAU,GAAG,aAAa,eAAe;AAE/C,YAAI,YAAY,SAAS;AAEvB,aAAG,gBAAgB,gBAAgB;AACnC,aAAG,gBAAgB,eAAe;AAGlC,aAAG,aAAa,kBAAkB,QAAQ;AAC1C,aAAG,aAAa,iBAAiB,OAAO;AAExC,eAAK,aAAa,KAAK,2CAA2C,QAAQ,MAAM,OAAO,EAAE;AAAA,QAC3F;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,gBAAgB;AAId,YAAM,iBAAiB,SAAS;AAAA,QAC9B;AAAA,MACF;AACA,YAAM,OAAO,oBAAI,IAAI;AACrB,YAAM,aAAa,CAAC;AAEpB,qBAAe,QAAQ,CAAC,OAAO;AAC7B,cAAM,WAAW,GAAG,aAAa,gBAAgB;AACjD,cAAM,UAAU,GAAG,aAAa,eAAe;AAC/C,cAAM,MAAM,GAAG,QAAQ,IAAI,OAAO;AAElC,YAAI,KAAK,IAAI,GAAG,GAAG;AACjB,qBAAW,KAAK,EAAE,SAAS,IAAI,UAAU,QAAQ,CAAC;AAAA,QACpD,OAAO;AACL,eAAK,IAAI,GAAG;AAAA,QACd;AAAA,MACF,CAAC;AAGD,iBAAW,QAAQ,CAAC,cAAc;AAChC,YAAI,UAAU,aAAa,2BAA2B,UAAU,YAAY,eAAS;AAEnF,oBAAU,QAAQ,aAAa,kBAAkB,mBAAgB;AACjE,eAAK,aAAa;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,kBAAkB,UAAU,WAAW,UAAU,aAAa;AAC5D,YAAM,WAAW,SAAS,iBAAiB,QAAQ;AAEnD,eAAS,QAAQ,CAAC,OAAO;AACvB,WAAG,aAAa,WAAW,QAAQ;AACnC,aAAK,aAAa,KAAK,GAAG,WAAW,KAAK,QAAQ,GAAG;AAAA,MACvD,CAAC;AAED,UAAI,SAAS,WAAW,GAAG;AACzB,gBAAQ,KAAK,kDAAwC,QAAQ,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,IAEA,kBAAkB;AAAA,IAmBlB;AAAA;AAAA,IAGA,gBAAgB;AAGd,YAAM,qBAAqB,SAAS;AAAA,QAClC;AAAA,MACF;AACA,YAAM,iBAAiB,SAAS;AAAA,QAC9B;AAAA,MACF;AAOA,UAAI,kBAAkB;AACtB,YAAM,oBAAoB,MAAM,KAAK,kBAAkB,EAAE,IAAI,CAAC,QAAQ;AAAA,QACpE,UAAU,GAAG,aAAa,gBAAgB;AAAA,QAC1C,SAAS,GAAG,aAAa,eAAe;AAAA,MAC1C,EAAE;AAEF,YAAM,gBAAgB,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,QAAQ;AAAA,QAC5D,UAAU,GAAG,aAAa,gBAAgB;AAAA,QAC1C,SAAS,GAAG,aAAa,eAAe;AAAA,MAC1C,EAAE;AAEF,wBAAkB,QAAQ,CAAC,MAAM;AAC/B,cAAM,WAAW,cAAc;AAAA,UAC7B,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE;AAAA,QACtD;AACA,YAAI,UAAU;AACZ,6BAAmB;AAAA,QAErB,OAAO;AACL,kBAAQ,KAAK,qBAAgB,EAAE,QAAQ,QAAQ,EAAE,OAAO,GAAG;AAAA,QAC7D;AAAA,MACF,CAAC;AAED,YAAM,cAAe,kBAAkB,mBAAmB,SAAU;AAKpE,UAAI,gBAAgB,KAAK;AAAA,MAEzB,OAAO;AACL,gBAAQ,KAAK,sEAA4D;AAAA,MAC3E;AAEA,aAAO,EAAE,iBAAiB,OAAO,mBAAmB,QAAQ,YAAY;AAAA,IAC1E;AAAA;AAAA,IAGA,4BAA4B;AAG1B,WAAK,mBAAmB,QAAQ,CAAC,WAAW;AAC1C,cAAM,KAAK,OAAO;AAClB,YAAI,MAAM,GAAG,YAAY;AACvB,aAAG,aAAa,kBAAkB,OAAO,QAAQ;AACjD,aAAG,aAAa,iBAAiB,OAAO,OAAO;AAAA,QACjD;AAAA,MACF,CAAC;AAED,WAAK,eAAe,CAAC;AAAA,IAEvB;AAAA;AAAA,IAGA,YAAY;AACV,aAAO;AAAA,QACL,aAAa,KAAK;AAAA,QAClB,cAAc,KAAK,aAAa;AAAA,QAChC,WAAW,KAAK,mBAAmB,OAAO;AAAA,QAC1C,OAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,eAAe,CAAC;AACrB,WAAK,mBAAmB,MAAM;AAC9B,WAAK,gBAAgB;AAAA,IAEvB;AAAA,EACF;AAGA,MAAO,0BAAQ;", "names": []}