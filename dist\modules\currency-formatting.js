"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/event-coordinator.js
  var EventCoordinator = class {
    static {
      __name(this, "EventCoordinator");
    }
    constructor() {
      this.input = null;
      this.listeners = /* @__PURE__ */ new Map();
      this.isProcessing = false;
      this.eventQueue = [];
      this.boundHandlers = /* @__PURE__ */ new Map();
      this.isDestroyed = false;
      this.init();
    }
    init() {
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.findAndSetupInput());
      } else {
        this.findAndSetupInput();
      }
    }
    findAndSetupInput() {
      this.input = document.querySelector('[is-main="true"]');
      if (this.input && !this.isDestroyed) {
        this.setupMainListeners();
      }
    }
    setupMainListeners() {
      if (!this.input || this.boundHandlers.has("main")) return;
      const inputHandler = /* @__PURE__ */ __name((e) => this.handleInputEvent(e), "inputHandler");
      const focusHandler = /* @__PURE__ */ __name((e) => this.processFocusEvent(e), "focusHandler");
      const blurHandler = /* @__PURE__ */ __name((e) => this.processBlurEvent(e), "blurHandler");
      const changeHandler = /* @__PURE__ */ __name((e) => this.processChangeEvent(e), "changeHandler");
      this.boundHandlers.set("main", {
        input: inputHandler,
        focus: focusHandler,
        blur: blurHandler,
        change: changeHandler
      });
      this.input.addEventListener("input", inputHandler, { passive: true });
      this.input.addEventListener("focus", focusHandler, { passive: true });
      this.input.addEventListener("blur", blurHandler, { passive: true });
      this.input.addEventListener("change", changeHandler, { passive: true });
    }
    handleInputEvent(e) {
      if (this.isProcessing || this.isDestroyed) {
        return;
      }
      this.isProcessing = true;
      requestAnimationFrame(() => {
        this.processInputEvent(e);
        this.isProcessing = false;
        if (this.eventQueue.length > 0) {
          const nextEvent = this.eventQueue.shift();
          requestAnimationFrame(() => this.handleInputEvent(nextEvent));
        }
      });
    }
    // Registra um listener para um módulo específico
    registerListener(moduleId, eventType, callback) {
      if (this.isDestroyed) return;
      const key = `${moduleId}_${eventType}`;
      this.unregisterListener(moduleId, eventType);
      if (!this.listeners.has(key)) {
        this.listeners.set(key, []);
      }
      this.listeners.get(key).push(callback);
    }
    // Remove listener de um módulo
    unregisterListener(moduleId, eventType, specificCallback = null) {
      const key = `${moduleId}_${eventType}`;
      if (this.listeners.has(key)) {
        if (specificCallback) {
          const callbacks = this.listeners.get(key);
          const index = callbacks.indexOf(specificCallback);
          if (index > -1) {
            callbacks.splice(index, 1);
          }
        } else {
          this.listeners.delete(key);
        }
      }
    }
    // Remove todos os listeners de um módulo
    unregisterModule(moduleId) {
      const keysToRemove = [];
      for (const key of this.listeners.keys()) {
        if (key.startsWith(`${moduleId}_`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => this.listeners.delete(key));
    }
    processInputEvent(e) {
      if (this.isDestroyed) return;
      const inputCallbacks = this.getCallbacksForEvent("input");
      const priorityOrder = ["currency-formatting", "motion-animation", "patrimony-sync"];
      for (const moduleId of priorityOrder) {
        const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);
        for (const callbackInfo of moduleCallbacks) {
          try {
            callbackInfo.callback(e);
          } catch (error) {
            console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);
          }
        }
      }
    }
    processFocusEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("focus", e);
    }
    processBlurEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("blur", e);
    }
    processChangeEvent(e) {
      if (this.isDestroyed) return;
      this.executeCallbacksForEvent("change", e);
    }
    executeCallbacksForEvent(eventType, e) {
      const callbacks = this.getCallbacksForEvent(eventType);
      callbacks.forEach(({ callback, moduleId }) => {
        try {
          callback(e);
        } catch (error) {
          console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);
        }
      });
    }
    getCallbacksForEvent(eventType) {
      const callbacks = [];
      for (const [key, callbackList] of this.listeners.entries()) {
        if (key.endsWith(`_${eventType}`)) {
          const moduleId = key.replace(`_${eventType}`, "");
          callbackList.forEach((callback) => {
            callbacks.push({ moduleId, callback });
          });
        }
      }
      return callbacks;
    }
    // Método para disparar eventos programaticamente
    dispatchInputEvent(sourceModule = "unknown") {
      if (this.isProcessing || this.isDestroyed || !this.input) {
        return;
      }
      const event = new Event("input", { bubbles: true });
      event.sourceModule = sourceModule;
      this.input.dispatchEvent(event);
    }
    // Método para atualizar valor sem disparar eventos
    setSilentValue(value) {
      if (this.isDestroyed || !this.input) return;
      this.isProcessing = true;
      this.input.value = value;
      requestAnimationFrame(() => {
        this.isProcessing = false;
      });
    }
    // Getter para o valor atual
    getValue() {
      return this.input ? this.input.value : "";
    }
    // Setter que dispara eventos controlados
    setValue(value, sourceModule = "unknown") {
      if (this.isDestroyed || !this.input) return;
      this.input.value = value;
      this.dispatchInputEvent(sourceModule);
    }
    // Método de cleanup para prevenir memory leaks
    destroy() {
      this.isDestroyed = true;
      if (this.input && this.boundHandlers.has("main")) {
        const handlers = this.boundHandlers.get("main");
        this.input.removeEventListener("input", handlers.input);
        this.input.removeEventListener("focus", handlers.focus);
        this.input.removeEventListener("blur", handlers.blur);
        this.input.removeEventListener("change", handlers.change);
      }
      this.listeners.clear();
      this.boundHandlers.clear();
      this.eventQueue.length = 0;
      this.input = null;
      this.isProcessing = false;
    }
    // Método para reinicializar se necessário
    reinitialize() {
      this.destroy();
      this.isDestroyed = false;
      this.init();
    }
  };
  var eventCoordinator = new EventCoordinator();
  window.addEventListener("beforeunload", () => {
    eventCoordinator.destroy();
  });

  // src/modules/currency-formatting.js
  var CurrencyFormattingSystem = class {
    static {
      __name(this, "CurrencyFormattingSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.domObserver = null;
      this.boundHandlers = /* @__PURE__ */ new Map();
      this.isDestroyed = false;
    }
    init() {
      if (this.isInitialized || this.isDestroyed) {
        return;
      }
      document.addEventListener("DOMContentLoaded", () => {
        this.initializeCurrencySystem();
      });
      setTimeout(() => this.initializeCurrencySystem(), 100);
      this.isInitialized = true;
    }
    initializeCurrencySystem() {
      if (this.isDestroyed) return;
      if (window.Webflow) {
        window.Webflow.push(() => {
          this.setupCurrencyFormatting();
        });
      } else {
        this.setupCurrencyFormatting();
      }
    }
    setupCurrencyFormatting() {
      if (this.isDestroyed) return;
      const formatBRL = /* @__PURE__ */ __name((value) => {
        return new Intl.NumberFormat("pt-BR", {
          style: "currency",
          currency: "BRL"
        }).format(value);
      }, "formatBRL");
      const formatCurrencyInput = /* @__PURE__ */ __name((input) => {
        let value = input.value.replace(/\D/g, "");
        if (value === "") {
          input.value = "";
          return 0;
        }
        const numericValue = parseInt(value) / 100;
        const formatted = new Intl.NumberFormat("pt-BR", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(numericValue);
        input.value = formatted;
        return numericValue;
      }, "formatCurrencyInput");
      const getCurrencyValue = /* @__PURE__ */ __name((input) => {
        const cleanValue = input.value.replace(/[^\d,]/g, "").replace(",", ".");
        return parseFloat(cleanValue) || 0;
      }, "getCurrencyValue");
      const currencyInputs = document.querySelectorAll('[data-currency="true"]');
      const mainInput = document.querySelector('[is-main="true"]');
      currencyInputs.forEach((input) => {
        if (!input || input.hasAttribute("is-main") || this.isDestroyed) return;
        const inputId = input.id || `currency-${Math.random().toString(36).substr(2, 9)}`;
        if (!this.boundHandlers.has(inputId)) {
          const handlers = {
            input: /* @__PURE__ */ __name((e) => this.handleCurrencyInput(e, formatCurrencyInput), "input"),
            focus: /* @__PURE__ */ __name((e) => this.handleCurrencyFocus(e, getCurrencyValue), "focus"),
            blur: /* @__PURE__ */ __name((e) => this.handleCurrencyBlur(e, formatCurrencyInput), "blur")
          };
          this.boundHandlers.set(inputId, { input, handlers });
          input.removeEventListener("input", handlers.input);
          input.removeEventListener("focus", handlers.focus);
          input.removeEventListener("blur", handlers.blur);
          input.addEventListener("input", handlers.input, { passive: true });
          input.addEventListener("focus", handlers.focus, { passive: true });
          input.addEventListener("blur", handlers.blur, { passive: true });
          if (input.value && input.value !== input.placeholder) {
            formatCurrencyInput(input);
          }
        }
      });
      if (mainInput && !this.isDestroyed) {
        eventCoordinator.unregisterModule("currency-formatting");
        eventCoordinator.registerListener(
          "currency-formatting",
          "input",
          (e) => this.handleCurrencyInput(e, formatCurrencyInput)
        );
        eventCoordinator.registerListener(
          "currency-formatting",
          "focus",
          (e) => this.handleCurrencyFocus(e, getCurrencyValue)
        );
        eventCoordinator.registerListener(
          "currency-formatting",
          "blur",
          (e) => this.handleCurrencyBlur(e, formatCurrencyInput)
        );
        if (mainInput.value && mainInput.value !== mainInput.placeholder) {
          formatCurrencyInput(mainInput);
        }
      }
      window.calculateCurrency = (value1, value2, operation = "add") => {
        const curr1 = window.currency(value1);
        const curr2 = window.currency(value2);
        switch (operation) {
          case "add":
            return curr1.add(curr2);
          case "subtract":
            return curr1.subtract(curr2);
          case "multiply":
            return curr1.multiply(curr2);
          case "divide":
            return curr1.divide(curr2);
          default:
            return curr1;
        }
      };
      window.formatCurrency = formatBRL;
      this.setupAllocationInputs(getCurrencyValue);
      this.setupDOMObserver();
    }
    handleCurrencyInput(event, formatCurrencyInput) {
      if (this.isDestroyed) return;
      const numericValue = formatCurrencyInput(event.target);
      event.target.dispatchEvent(
        new CustomEvent("currencyChange", {
          detail: {
            value: numericValue,
            currencyValue: window.currency ? window.currency(numericValue) : numericValue,
            formatted: window.formatCurrency ? window.formatCurrency(numericValue) : numericValue
          }
        })
      );
    }
    handleCurrencyFocus(event, getCurrencyValue) {
      if (this.isDestroyed) return;
      const value = getCurrencyValue(event.target);
      if (value > 0) {
        event.target.value = value.toFixed(2).replace(".", ",");
      }
    }
    handleCurrencyBlur(event, formatCurrencyInput) {
      if (this.isDestroyed) return;
      formatCurrencyInput(event.target);
    }
    setupAllocationInputs(getCurrencyValue) {
      if (this.isDestroyed) return;
      const individualInputs = document.querySelectorAll(
        '.currency-input.individual, [input-settings="receive"]'
      );
      individualInputs.forEach((input) => {
        if (!input || this.isDestroyed) return;
        const existingHandler = input._currencyChangeHandler;
        if (existingHandler) {
          input.removeEventListener("currencyChange", existingHandler);
        }
        const handler = /* @__PURE__ */ __name(() => this.updateTotalAllocation(getCurrencyValue), "handler");
        input._currencyChangeHandler = handler;
        input.addEventListener("currencyChange", handler, { passive: true });
      });
    }
    updateTotalAllocation(getCurrencyValue) {
      if (this.isDestroyed || !window.currency) return;
      let total = window.currency(0);
      document.querySelectorAll('.currency-input.individual, [input-settings="receive"]').forEach((input) => {
        if (input && input.value && !this.isDestroyed) {
          const value = getCurrencyValue(input);
          total = total.add(value);
        }
      });
      if (!this.isDestroyed) {
        document.dispatchEvent(
          new CustomEvent("totalAllocationChange", {
            detail: {
              total: total.value,
              formatted: window.formatCurrency ? window.formatCurrency(total.value) : total.value
            }
          })
        );
      }
    }
    setupDOMObserver() {
      if (this.isDestroyed || this.domObserver) return;
      let observerTimeout;
      const throttledReinit = /* @__PURE__ */ __name(() => {
        if (observerTimeout) clearTimeout(observerTimeout);
        observerTimeout = setTimeout(() => {
          if (!this.isDestroyed) {
            this.initializeCurrencySystem();
          }
        }, 100);
      }, "throttledReinit");
      if (window.Webflow) {
        window.Webflow.push(() => {
          if (this.isDestroyed) return;
          this.domObserver = new MutationObserver((mutations) => {
            if (this.isDestroyed) return;
            let shouldReinit = false;
            mutations.forEach((mutation) => {
              if (mutation.addedNodes.length) {
                for (const node of mutation.addedNodes) {
                  if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node.matches('[data-currency="true"], [is-main="true"]') || node.querySelector('[data-currency="true"], [is-main="true"]')) {
                      shouldReinit = true;
                      break;
                    }
                  }
                }
              }
            });
            if (shouldReinit) {
              throttledReinit();
            }
          });
          this.domObserver.observe(document.body, {
            childList: true,
            subtree: true
          });
        });
      }
    }
    // Método de cleanup para prevenir memory leaks
    cleanup() {
      this.isDestroyed = true;
      if (this.domObserver) {
        this.domObserver.disconnect();
        this.domObserver = null;
      }
      for (const [inputId, { input, handlers }] of this.boundHandlers.entries()) {
        if (input) {
          input.removeEventListener("input", handlers.input);
          input.removeEventListener("focus", handlers.focus);
          input.removeEventListener("blur", handlers.blur);
          if (input._currencyChangeHandler) {
            input.removeEventListener("currencyChange", input._currencyChangeHandler);
            delete input._currencyChangeHandler;
          }
        }
      }
      this.boundHandlers.clear();
      if (eventCoordinator && !eventCoordinator.isDestroyed) {
        eventCoordinator.unregisterModule("currency-formatting");
      }
      if (window.calculateCurrency) {
        delete window.calculateCurrency;
      }
      if (window.formatCurrency) {
        delete window.formatCurrency;
      }
      this.isInitialized = false;
    }
    // Método para reinicializar se necessário
    reinitialize() {
      this.cleanup();
      this.isDestroyed = false;
      this.init();
    }
  };
})();
//# sourceMappingURL=currency-formatting.js.map
