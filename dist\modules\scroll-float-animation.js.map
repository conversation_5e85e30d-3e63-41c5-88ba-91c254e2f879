{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/event-coordinator.js", "../../src/modules/scroll-float-animation.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * EventCoordinator - Sistema centralizado CORRIGIDO para gerenciar eventos do input principal\r\n * Evita conflitos entre múltiplos módulos e memory leaks\r\n */\r\n\r\nexport class EventCoordinator {\r\n  constructor() {\r\n    this.input = null;\r\n    this.listeners = new Map();\r\n    this.isProcessing = false;\r\n    this.eventQueue = [];\r\n    this.boundHandlers = new Map(); // Para rastrear handlers bound\r\n    this.isDestroyed = false;\r\n\r\n    this.init();\r\n  }\r\n\r\n  init() {\r\n    // Aguarda o DOM estar pronto\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => this.findAndSetupInput());\r\n    } else {\r\n      this.findAndSetupInput();\r\n    }\r\n  }\r\n\r\n  findAndSetupInput() {\r\n    this.input = document.querySelector('[is-main=\"true\"]');\r\n    if (this.input && !this.isDestroyed) {\r\n      this.setupMainListeners();\r\n    }\r\n  }\r\n\r\n  setupMainListeners() {\r\n    if (!this.input || this.boundHandlers.has('main')) return;\r\n\r\n    // Cria handlers bound uma única vez\r\n    const inputHandler = (e) => this.handleInputEvent(e);\r\n    const focusHandler = (e) => this.processFocusEvent(e);\r\n    const blurHandler = (e) => this.processBlurEvent(e);\r\n    const changeHandler = (e) => this.processChangeEvent(e);\r\n\r\n    // Armazena referências para cleanup posterior\r\n    this.boundHandlers.set('main', {\r\n      input: inputHandler,\r\n      focus: focusHandler,\r\n      blur: blurHandler,\r\n      change: changeHandler,\r\n    });\r\n\r\n    // Adiciona listeners apenas uma vez\r\n    this.input.addEventListener('input', inputHandler, { passive: true });\r\n    this.input.addEventListener('focus', focusHandler, { passive: true });\r\n    this.input.addEventListener('blur', blurHandler, { passive: true });\r\n    this.input.addEventListener('change', changeHandler, { passive: true });\r\n  }\r\n\r\n  handleInputEvent(e) {\r\n    if (this.isProcessing || this.isDestroyed) {\r\n      return;\r\n    }\r\n\r\n    this.isProcessing = true;\r\n\r\n    // Usa requestAnimationFrame para melhor performance\r\n    requestAnimationFrame(() => {\r\n      this.processInputEvent(e);\r\n      this.isProcessing = false;\r\n\r\n      // Processa queue se houver\r\n      if (this.eventQueue.length > 0) {\r\n        const nextEvent = this.eventQueue.shift();\r\n        requestAnimationFrame(() => this.handleInputEvent(nextEvent));\r\n      }\r\n    });\r\n  }\r\n\r\n  // Registra um listener para um módulo específico\r\n  registerListener(moduleId, eventType, callback) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    // Remove listener anterior se existir (evita duplicação)\r\n    this.unregisterListener(moduleId, eventType);\r\n\r\n    if (!this.listeners.has(key)) {\r\n      this.listeners.set(key, []);\r\n    }\r\n\r\n    this.listeners.get(key).push(callback);\r\n  }\r\n\r\n  // Remove listener de um módulo\r\n  unregisterListener(moduleId, eventType, specificCallback = null) {\r\n    const key = `${moduleId}_${eventType}`;\r\n\r\n    if (this.listeners.has(key)) {\r\n      if (specificCallback) {\r\n        const callbacks = this.listeners.get(key);\r\n        const index = callbacks.indexOf(specificCallback);\r\n        if (index > -1) {\r\n          callbacks.splice(index, 1);\r\n        }\r\n      } else {\r\n        // Remove todos os callbacks do módulo para este evento\r\n        this.listeners.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove todos os listeners de um módulo\r\n  unregisterModule(moduleId) {\r\n    const keysToRemove = [];\r\n    for (const key of this.listeners.keys()) {\r\n      if (key.startsWith(`${moduleId}_`)) {\r\n        keysToRemove.push(key);\r\n      }\r\n    }\r\n\r\n    keysToRemove.forEach((key) => this.listeners.delete(key));\r\n  }\r\n\r\n  processInputEvent(e) {\r\n    if (this.isDestroyed) return;\r\n\r\n    const inputCallbacks = this.getCallbacksForEvent('input');\r\n\r\n    // Executa callbacks em ordem de prioridade\r\n    const priorityOrder = ['currency-formatting', 'motion-animation', 'patrimony-sync'];\r\n\r\n    for (const moduleId of priorityOrder) {\r\n      const moduleCallbacks = inputCallbacks.filter((cb) => cb.moduleId === moduleId);\r\n      for (const callbackInfo of moduleCallbacks) {\r\n        try {\r\n          callbackInfo.callback(e);\r\n        } catch (error) {\r\n          console.error(`EventCoordinator: Error in ${moduleId} listener:`, error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  processFocusEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('focus', e);\r\n  }\r\n\r\n  processBlurEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('blur', e);\r\n  }\r\n\r\n  processChangeEvent(e) {\r\n    if (this.isDestroyed) return;\r\n    this.executeCallbacksForEvent('change', e);\r\n  }\r\n\r\n  executeCallbacksForEvent(eventType, e) {\r\n    const callbacks = this.getCallbacksForEvent(eventType);\r\n    callbacks.forEach(({ callback, moduleId }) => {\r\n      try {\r\n        callback(e);\r\n      } catch (error) {\r\n        console.error(`EventCoordinator: Error in ${moduleId} ${eventType} listener:`, error);\r\n      }\r\n    });\r\n  }\r\n\r\n  getCallbacksForEvent(eventType) {\r\n    const callbacks = [];\r\n    for (const [key, callbackList] of this.listeners.entries()) {\r\n      if (key.endsWith(`_${eventType}`)) {\r\n        const moduleId = key.replace(`_${eventType}`, '');\r\n        callbackList.forEach((callback) => {\r\n          callbacks.push({ moduleId, callback });\r\n        });\r\n      }\r\n    }\r\n    return callbacks;\r\n  }\r\n\r\n  // Método para disparar eventos programaticamente\r\n  dispatchInputEvent(sourceModule = 'unknown') {\r\n    if (this.isProcessing || this.isDestroyed || !this.input) {\r\n      return;\r\n    }\r\n\r\n    const event = new Event('input', { bubbles: true });\r\n    event.sourceModule = sourceModule;\r\n    this.input.dispatchEvent(event);\r\n  }\r\n\r\n  // Método para atualizar valor sem disparar eventos\r\n  setSilentValue(value) {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.isProcessing = true;\r\n    this.input.value = value;\r\n\r\n    // Usa requestAnimationFrame para reset mais confiável\r\n    requestAnimationFrame(() => {\r\n      this.isProcessing = false;\r\n    });\r\n  }\r\n\r\n  // Getter para o valor atual\r\n  getValue() {\r\n    return this.input ? this.input.value : '';\r\n  }\r\n\r\n  // Setter que dispara eventos controlados\r\n  setValue(value, sourceModule = 'unknown') {\r\n    if (this.isDestroyed || !this.input) return;\r\n\r\n    this.input.value = value;\r\n    this.dispatchInputEvent(sourceModule);\r\n  }\r\n\r\n  // Método de cleanup para prevenir memory leaks\r\n  destroy() {\r\n    this.isDestroyed = true;\r\n\r\n    // Remove todos os event listeners\r\n    if (this.input && this.boundHandlers.has('main')) {\r\n      const handlers = this.boundHandlers.get('main');\r\n      this.input.removeEventListener('input', handlers.input);\r\n      this.input.removeEventListener('focus', handlers.focus);\r\n      this.input.removeEventListener('blur', handlers.blur);\r\n      this.input.removeEventListener('change', handlers.change);\r\n    }\r\n\r\n    // Limpa todas as referências\r\n    this.listeners.clear();\r\n    this.boundHandlers.clear();\r\n    this.eventQueue.length = 0;\r\n    this.input = null;\r\n    this.isProcessing = false;\r\n  }\r\n\r\n  // Método para reinicializar se necessário\r\n  reinitialize() {\r\n    this.destroy();\r\n    this.isDestroyed = false;\r\n    this.init();\r\n  }\r\n}\r\n\r\n// Instância singleton\r\nexport const eventCoordinator = new EventCoordinator();\r\n\r\n// Cleanup automático quando a página é descarregada\r\nwindow.addEventListener('beforeunload', () => {\r\n  eventCoordinator.destroy();\r\n});\r\n", "import { eventCoordinator } from './event-coordinator.js';\r\n\r\n/**\r\n * Scroll Float Animation System\r\n * Handles scroll-triggered animations for the componente-alocao-float component\r\n * Uses Motion.js inView to detect when _3-section-patrimonio-alocation reaches viewport center\r\n */\r\nexport class ScrollFloatAnimationSystem {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.Motion = null;\r\n    this.floatComponent = null;\r\n    this.targetSection = null;\r\n    this.inViewCleanup = null;\r\n    this.isAnimated = false;\r\n  }\r\n\r\n  init() {\r\n    if (this.isInitialized) {\r\n      return;\r\n    }\r\n\r\n    document.addEventListener('DOMContentLoaded', () => {\r\n      this.waitForMotion();\r\n    });\r\n\r\n    this.isInitialized = true;\r\n  }\r\n\r\n  waitForMotion() {\r\n    if (window.Motion && window.Motion.inView) {\r\n      this.Motion = window.Motion;\r\n      this.initScrollAnimation();\r\n    } else {\r\n      setTimeout(() => this.waitForMotion(), 50);\r\n    }\r\n  }\r\n\r\n  initScrollAnimation() {\r\n    const { animate, inView } = this.Motion;\r\n\r\n    // Find the target elements\r\n    this.floatComponent = document.querySelector('.componente-alocao-float');\r\n    this.targetSection = document.querySelector('._3-section-patrimonio-alocation');\r\n\r\n    if (!this.floatComponent || !this.targetSection) {\r\n      console.warn('ScrollFloatAnimation: Required elements not found');\r\n      return;\r\n    }\r\n\r\n    // Set initial state - hidden and scaled down\r\n    this.setInitialState();\r\n\r\n    // Setup scroll-triggered animation using inView\r\n    this.setupScrollTrigger(animate, inView);\r\n  }\r\n\r\n  setInitialState() {\r\n    // Set initial hidden state - positioned below\r\n    this.floatComponent.style.transform = 'translate(-50%, 30px)';\r\n    this.floatComponent.style.opacity = '0';\r\n  }\r\n\r\n  setupScrollTrigger(animate, inView) {\r\n    // Use inView to detect when the section enters and leaves viewport\r\n    this.inViewCleanup = inView(\r\n      this.targetSection,\r\n      (element, enterInfo) => {\r\n        // Element enters viewport - show animation\r\n        if (!this.isAnimated) {\r\n          this.animateFloatIn(animate);\r\n          this.isAnimated = true;\r\n        }\r\n\r\n        // Return function that fires when element leaves viewport\r\n        return (leaveInfo) => {\r\n          this.animateFloatOut(animate);\r\n          this.isAnimated = false;\r\n        };\r\n      },\r\n      {\r\n        // Trigger when 50% of the section is visible (middle of viewport)\r\n        amount: 0.5,\r\n        // Add margin to trigger earlier for better UX\r\n        margin: '-10% 0px -10% 0px',\r\n      }\r\n    );\r\n  }\r\n\r\n  animateFloatIn(animate) {\r\n    // Animate sliding up from bottom to final position\r\n    animate(\r\n      this.floatComponent,\r\n      {\r\n        transform: 'translate(-50%, 0px)',\r\n        opacity: 1,\r\n      },\r\n      {\r\n        duration: 0.6,\r\n        ease: 'backOut',\r\n      }\r\n    );\r\n\r\n    // Register animation completion event\r\n    document.dispatchEvent(\r\n      new CustomEvent('scrollFloatAnimated', {\r\n        detail: {\r\n          component: this.floatComponent,\r\n          section: this.targetSection,\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  addFloatingEffect(animate) {\r\n    // Floating effect removed to prevent interference with CSS centering\r\n    return;\r\n  }\r\n\r\n  animateFloatOut(animate) {\r\n    // Animate sliding down and fade out\r\n    animate(\r\n      this.floatComponent,\r\n      {\r\n        transform: 'translate(-50%, 30px)',\r\n        opacity: 0,\r\n      },\r\n      {\r\n        duration: 0.4,\r\n        ease: 'circIn',\r\n      }\r\n    );\r\n\r\n    // Dispatch exit event\r\n    document.dispatchEvent(\r\n      new CustomEvent('scrollFloatExited', {\r\n        detail: {\r\n          component: this.floatComponent,\r\n          section: this.targetSection,\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Reset the animation state (useful for testing or re-triggering)\r\n   */\r\n  reset() {\r\n    if (!this.Motion || !this.floatComponent) return;\r\n\r\n    const { animate, inView } = this.Motion;\r\n\r\n    this.isAnimated = false;\r\n    this.setInitialState();\r\n\r\n    // Clean up existing inView instance\r\n    if (this.inViewCleanup && typeof this.inViewCleanup === 'function') {\r\n      this.inViewCleanup();\r\n    }\r\n\r\n    // Re-initialize scroll trigger\r\n    this.setupScrollTrigger(animate, inView);\r\n  }\r\n\r\n  /**\r\n   * Force show the component (bypass scroll trigger)\r\n   */\r\n  forceShow() {\r\n    if (!this.Motion || !this.floatComponent) return;\r\n\r\n    const { animate } = this.Motion;\r\n\r\n    animate(\r\n      this.floatComponent,\r\n      {\r\n        transform: 'translate(-50%, 0px)',\r\n        opacity: 1,\r\n      },\r\n      {\r\n        duration: 0.5,\r\n        ease: 'backOut',\r\n      }\r\n    );\r\n\r\n    this.isAnimated = true;\r\n  }\r\n\r\n  /**\r\n   * Force hide the component\r\n   */\r\n  forceHide() {\r\n    if (!this.Motion || !this.floatComponent) return;\r\n\r\n    const { animate } = this.Motion;\r\n\r\n    animate(\r\n      this.floatComponent,\r\n      {\r\n        transform: 'translate(-50%, 30px)',\r\n        opacity: 0,\r\n      },\r\n      {\r\n        duration: 0.4,\r\n        ease: 'circIn',\r\n      }\r\n    );\r\n\r\n    this.isAnimated = false;\r\n  }\r\n\r\n  /**\r\n   * Cleanup method for proper disposal\r\n   */\r\n  cleanup() {\r\n    if (this.inViewCleanup && typeof this.inViewCleanup === 'function') {\r\n      this.inViewCleanup();\r\n    }\r\n\r\n    this.Motion = null;\r\n    this.floatComponent = null;\r\n    this.targetSection = null;\r\n    this.inViewCleanup = null;\r\n    this.isAnimated = false;\r\n    this.isInitialized = false;\r\n  }\r\n\r\n  /**\r\n   * Get current animation state for debugging\r\n   */\r\n  getStatus() {\r\n    return {\r\n      isInitialized: this.isInitialized,\r\n      hasMotion: !!this.Motion,\r\n      hasInView: !!(this.Motion && this.Motion.inView),\r\n      hasFloatComponent: !!this.floatComponent,\r\n      hasTargetSection: !!this.targetSection,\r\n      isAnimated: this.isAnimated,\r\n      hasInViewCleanup: !!this.inViewCleanup,\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACKtF,MAAM,mBAAN,MAAuB;AAAA,IAL9B,OAK8B;AAAA;AAAA;AAAA,IAC5B,cAAc;AACZ,WAAK,QAAQ;AACb,WAAK,YAAY,oBAAI,IAAI;AACzB,WAAK,eAAe;AACpB,WAAK,aAAa,CAAC;AACnB,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,cAAc;AAEnB,WAAK,KAAK;AAAA,IACZ;AAAA,IAEA,OAAO;AAEL,UAAI,SAAS,eAAe,WAAW;AACrC,iBAAS,iBAAiB,oBAAoB,MAAM,KAAK,kBAAkB,CAAC;AAAA,MAC9E,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,WAAK,QAAQ,SAAS,cAAc,kBAAkB;AACtD,UAAI,KAAK,SAAS,CAAC,KAAK,aAAa;AACnC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,IAEA,qBAAqB;AACnB,UAAI,CAAC,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,EAAG;AAGnD,YAAM,eAAe,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACrB,YAAM,eAAe,wBAAC,MAAM,KAAK,kBAAkB,CAAC,GAA/B;AACrB,YAAM,cAAc,wBAAC,MAAM,KAAK,iBAAiB,CAAC,GAA9B;AACpB,YAAM,gBAAgB,wBAAC,MAAM,KAAK,mBAAmB,CAAC,GAAhC;AAGtB,WAAK,cAAc,IAAI,QAAQ;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAGD,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,SAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AACpE,WAAK,MAAM,iBAAiB,QAAQ,aAAa,EAAE,SAAS,KAAK,CAAC;AAClE,WAAK,MAAM,iBAAiB,UAAU,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,IACxE;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,gBAAgB,KAAK,aAAa;AACzC;AAAA,MACF;AAEA,WAAK,eAAe;AAGpB,4BAAsB,MAAM;AAC1B,aAAK,kBAAkB,CAAC;AACxB,aAAK,eAAe;AAGpB,YAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,gBAAM,YAAY,KAAK,WAAW,MAAM;AACxC,gCAAsB,MAAM,KAAK,iBAAiB,SAAS,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,iBAAiB,UAAU,WAAW,UAAU;AAC9C,UAAI,KAAK,YAAa;AAEtB,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAGpC,WAAK,mBAAmB,UAAU,SAAS;AAE3C,UAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,aAAK,UAAU,IAAI,KAAK,CAAC,CAAC;AAAA,MAC5B;AAEA,WAAK,UAAU,IAAI,GAAG,EAAE,KAAK,QAAQ;AAAA,IACvC;AAAA;AAAA,IAGA,mBAAmB,UAAU,WAAW,mBAAmB,MAAM;AAC/D,YAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AAEpC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,YAAI,kBAAkB;AACpB,gBAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,gBAAM,QAAQ,UAAU,QAAQ,gBAAgB;AAChD,cAAI,QAAQ,IAAI;AACd,sBAAU,OAAO,OAAO,CAAC;AAAA,UAC3B;AAAA,QACF,OAAO;AAEL,eAAK,UAAU,OAAO,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,iBAAiB,UAAU;AACzB,YAAM,eAAe,CAAC;AACtB,iBAAW,OAAO,KAAK,UAAU,KAAK,GAAG;AACvC,YAAI,IAAI,WAAW,GAAG,QAAQ,GAAG,GAAG;AAClC,uBAAa,KAAK,GAAG;AAAA,QACvB;AAAA,MACF;AAEA,mBAAa,QAAQ,CAAC,QAAQ,KAAK,UAAU,OAAO,GAAG,CAAC;AAAA,IAC1D;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AAEtB,YAAM,iBAAiB,KAAK,qBAAqB,OAAO;AAGxD,YAAM,gBAAgB,CAAC,uBAAuB,oBAAoB,gBAAgB;AAElF,iBAAW,YAAY,eAAe;AACpC,cAAM,kBAAkB,eAAe,OAAO,CAAC,OAAO,GAAG,aAAa,QAAQ;AAC9E,mBAAW,gBAAgB,iBAAiB;AAC1C,cAAI;AACF,yBAAa,SAAS,CAAC;AAAA,UACzB,SAAS,OAAO;AACd,oBAAQ,MAAM,8BAA8B,QAAQ,cAAc,KAAK;AAAA,UACzE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,kBAAkB,GAAG;AACnB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,SAAS,CAAC;AAAA,IAC1C;AAAA,IAEA,iBAAiB,GAAG;AAClB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,QAAQ,CAAC;AAAA,IACzC;AAAA,IAEA,mBAAmB,GAAG;AACpB,UAAI,KAAK,YAAa;AACtB,WAAK,yBAAyB,UAAU,CAAC;AAAA,IAC3C;AAAA,IAEA,yBAAyB,WAAW,GAAG;AACrC,YAAM,YAAY,KAAK,qBAAqB,SAAS;AACrD,gBAAU,QAAQ,CAAC,EAAE,UAAU,SAAS,MAAM;AAC5C,YAAI;AACF,mBAAS,CAAC;AAAA,QACZ,SAAS,OAAO;AACd,kBAAQ,MAAM,8BAA8B,QAAQ,IAAI,SAAS,cAAc,KAAK;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,qBAAqB,WAAW;AAC9B,YAAM,YAAY,CAAC;AACnB,iBAAW,CAAC,KAAK,YAAY,KAAK,KAAK,UAAU,QAAQ,GAAG;AAC1D,YAAI,IAAI,SAAS,IAAI,SAAS,EAAE,GAAG;AACjC,gBAAM,WAAW,IAAI,QAAQ,IAAI,SAAS,IAAI,EAAE;AAChD,uBAAa,QAAQ,CAAC,aAAa;AACjC,sBAAU,KAAK,EAAE,UAAU,SAAS,CAAC;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,mBAAmB,eAAe,WAAW;AAC3C,UAAI,KAAK,gBAAgB,KAAK,eAAe,CAAC,KAAK,OAAO;AACxD;AAAA,MACF;AAEA,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC;AAClD,YAAM,eAAe;AACrB,WAAK,MAAM,cAAc,KAAK;AAAA,IAChC;AAAA;AAAA,IAGA,eAAe,OAAO;AACpB,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,eAAe;AACpB,WAAK,MAAM,QAAQ;AAGnB,4BAAsB,MAAM;AAC1B,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,WAAW;AACT,aAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ;AAAA,IACzC;AAAA;AAAA,IAGA,SAAS,OAAO,eAAe,WAAW;AACxC,UAAI,KAAK,eAAe,CAAC,KAAK,MAAO;AAErC,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,YAAY;AAAA,IACtC;AAAA;AAAA,IAGA,UAAU;AACR,WAAK,cAAc;AAGnB,UAAI,KAAK,SAAS,KAAK,cAAc,IAAI,MAAM,GAAG;AAChD,cAAM,WAAW,KAAK,cAAc,IAAI,MAAM;AAC9C,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,SAAS,SAAS,KAAK;AACtD,aAAK,MAAM,oBAAoB,QAAQ,SAAS,IAAI;AACpD,aAAK,MAAM,oBAAoB,UAAU,SAAS,MAAM;AAAA,MAC1D;AAGA,WAAK,UAAU,MAAM;AACrB,WAAK,cAAc,MAAM;AACzB,WAAK,WAAW,SAAS;AACzB,WAAK,QAAQ;AACb,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,eAAe;AACb,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAGO,MAAM,mBAAmB,IAAI,iBAAiB;AAGrD,SAAO,iBAAiB,gBAAgB,MAAM;AAC5C,qBAAiB,QAAQ;AAAA,EAC3B,CAAC;;;ACvPM,MAAM,6BAAN,MAAiC;AAAA,IAPxC,OAOwC;AAAA;AAAA;AAAA,IACtC,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,SAAS;AACd,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA,IACpB;AAAA,IAEA,OAAO;AACL,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,oBAAoB,MAAM;AAClD,aAAK,cAAc;AAAA,MACrB,CAAC;AAED,WAAK,gBAAgB;AAAA,IACvB;AAAA,IAEA,gBAAgB;AACd,UAAI,OAAO,UAAU,OAAO,OAAO,QAAQ;AACzC,aAAK,SAAS,OAAO;AACrB,aAAK,oBAAoB;AAAA,MAC3B,OAAO;AACL,mBAAW,MAAM,KAAK,cAAc,GAAG,EAAE;AAAA,MAC3C;AAAA,IACF;AAAA,IAEA,sBAAsB;AACpB,YAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AAGjC,WAAK,iBAAiB,SAAS,cAAc,0BAA0B;AACvE,WAAK,gBAAgB,SAAS,cAAc,kCAAkC;AAE9E,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe;AAC/C,gBAAQ,KAAK,mDAAmD;AAChE;AAAA,MACF;AAGA,WAAK,gBAAgB;AAGrB,WAAK,mBAAmB,SAAS,MAAM;AAAA,IACzC;AAAA,IAEA,kBAAkB;AAEhB,WAAK,eAAe,MAAM,YAAY;AACtC,WAAK,eAAe,MAAM,UAAU;AAAA,IACtC;AAAA,IAEA,mBAAmB,SAAS,QAAQ;AAElC,WAAK,gBAAgB;AAAA,QACnB,KAAK;AAAA,QACL,CAAC,SAAS,cAAc;AAEtB,cAAI,CAAC,KAAK,YAAY;AACpB,iBAAK,eAAe,OAAO;AAC3B,iBAAK,aAAa;AAAA,UACpB;AAGA,iBAAO,CAAC,cAAc;AACpB,iBAAK,gBAAgB,OAAO;AAC5B,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AAAA,QACA;AAAA;AAAA,UAEE,QAAQ;AAAA;AAAA,UAER,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IAEA,eAAe,SAAS;AAEtB;AAAA,QACE,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,QACR;AAAA,MACF;AAGA,eAAS;AAAA,QACP,IAAI,YAAY,uBAAuB;AAAA,UACrC,QAAQ;AAAA,YACN,WAAW,KAAK;AAAA,YAChB,SAAS,KAAK;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,kBAAkB,SAAS;AAEzB;AAAA,IACF;AAAA,IAEA,gBAAgB,SAAS;AAEvB;AAAA,QACE,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,QACR;AAAA,MACF;AAGA,eAAS;AAAA,QACP,IAAI,YAAY,qBAAqB;AAAA,UACnC,QAAQ;AAAA,YACN,WAAW,KAAK;AAAA,YAChB,SAAS,KAAK;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,QAAQ;AACN,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,eAAgB;AAE1C,YAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AAEjC,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAGrB,UAAI,KAAK,iBAAiB,OAAO,KAAK,kBAAkB,YAAY;AAClE,aAAK,cAAc;AAAA,MACrB;AAGA,WAAK,mBAAmB,SAAS,MAAM;AAAA,IACzC;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AACV,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,eAAgB;AAE1C,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB;AAAA,QACE,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,QACR;AAAA,MACF;AAEA,WAAK,aAAa;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AACV,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,eAAgB;AAE1C,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB;AAAA,QACE,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,QACR;AAAA,MACF;AAEA,WAAK,aAAa;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA,IAKA,UAAU;AACR,UAAI,KAAK,iBAAiB,OAAO,KAAK,kBAAkB,YAAY;AAClE,aAAK,cAAc;AAAA,MACrB;AAEA,WAAK,SAAS;AACd,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AACV,aAAO;AAAA,QACL,eAAe,KAAK;AAAA,QACpB,WAAW,CAAC,CAAC,KAAK;AAAA,QAClB,WAAW,CAAC,EAAE,KAAK,UAAU,KAAK,OAAO;AAAA,QACzC,mBAAmB,CAAC,CAAC,KAAK;AAAA,QAC1B,kBAAkB,CAAC,CAAC,KAAK;AAAA,QACzB,YAAY,KAAK;AAAA,QACjB,kBAAkB,CAAC,CAAC,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;", "names": []}