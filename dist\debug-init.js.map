{"version": 3, "sources": ["../bin/live-reload.js", "../src/debug-init.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * Debug Initialization Script\r\n * Helps identify issues during page load and initialization\r\n */\r\n\r\n// Global debug object\r\nwindow.ReinoDebug = {\r\n  startTime: Date.now(),\r\n  phases: [],\r\n  errors: [],\r\n  warnings: [],\r\n  systemStatus: {},\r\n\r\n  // Add a phase marker\r\n  markPhase(phase, details = {}) {\r\n    const timestamp = Date.now() - this.startTime;\r\n    this.phases.push({\r\n      phase,\r\n      timestamp,\r\n      details,\r\n      time: new Date().toISOString()\r\n    });\r\n    console.log(`[DEBUG ${timestamp}ms] ${phase}`, details);\r\n  },\r\n\r\n  // Log an error\r\n  logError(error, context = '') {\r\n    this.errors.push({\r\n      error: error.message || error,\r\n      stack: error.stack,\r\n      context,\r\n      timestamp: Date.now() - this.startTime\r\n    });\r\n    console.error(`[DEBUG ERROR] ${context}:`, error);\r\n  },\r\n\r\n  // Log a warning\r\n  logWarning(message, details = {}) {\r\n    this.warnings.push({\r\n      message,\r\n      details,\r\n      timestamp: Date.now() - this.startTime\r\n    });\r\n    console.warn(`[DEBUG WARNING] ${message}`, details);\r\n  },\r\n\r\n  // Check system status\r\n  checkSystemStatus() {\r\n    this.systemStatus = {\r\n      // Check if critical libraries are loaded\r\n      sortableJS: !!window.Sortable,\r\n      webflow: !!window.Webflow,\r\n      motion: !!window.Motion,\r\n      reinoCalculator: !!window.ReinoCalculator,\r\n\r\n      // Check DOM elements\r\n      domElements: {\r\n        dropArea: !!document.querySelector('.ativos_main_drop_area'),\r\n        interactiveWrapper: !!document.querySelector('.patrimonio_interactive_content-wrapper'),\r\n        mainList: !!document.querySelector('.ativos_main-list'),\r\n        section2: !!document.querySelector('._2-section-calc-ativos'),\r\n        section3: !!document.querySelector('._3-section-patrimonio-alocation')\r\n      },\r\n\r\n      // Check initialization status\r\n      initialized: window.ReinoCalculator?.app?.isInitialized || false,\r\n\r\n      // Memory usage\r\n      memory: performance.memory ? {\r\n        used: Math.round(performance.memory.usedJSHeapSize / 1048576) + 'MB',\r\n        total: Math.round(performance.memory.totalJSHeapSize / 1048576) + 'MB'\r\n      } : 'Not available'\r\n    };\r\n\r\n    return this.systemStatus;\r\n  },\r\n\r\n  // Generate report\r\n  generateReport() {\r\n    const report = {\r\n      totalTime: Date.now() - this.startTime,\r\n      phases: this.phases,\r\n      errors: this.errors,\r\n      warnings: this.warnings,\r\n      systemStatus: this.checkSystemStatus(),\r\n      performanceMetrics: this.getPerformanceMetrics()\r\n    };\r\n\r\n    console.group('🔍 Reino Debug Report');\r\n    console.log('Total initialization time:', report.totalTime + 'ms');\r\n    console.log('Phases:', report.phases);\r\n    console.log('Errors:', report.errors);\r\n    console.log('Warnings:', report.warnings);\r\n    console.log('System Status:', report.systemStatus);\r\n    console.log('Performance:', report.performanceMetrics);\r\n    console.groupEnd();\r\n\r\n    return report;\r\n  },\r\n\r\n  // Get performance metrics\r\n  getPerformanceMetrics() {\r\n    const navigation = performance.getEntriesByType('navigation')[0];\r\n    return {\r\n      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,\r\n      loadComplete: navigation?.loadEventEnd - navigation?.loadEventStart,\r\n      resourceCount: performance.getEntriesByType('resource').length\r\n    };\r\n  },\r\n\r\n  // Monitor for infinite loops\r\n  startLoopMonitor() {\r\n    let lastCheck = Date.now();\r\n    let loopCount = 0;\r\n    const maxLoops = 1000;\r\n\r\n    const monitor = setInterval(() => {\r\n      const now = Date.now();\r\n      const timeDiff = now - lastCheck;\r\n\r\n      if (timeDiff < 10) {\r\n        loopCount++;\r\n        if (loopCount > maxLoops) {\r\n          this.logError(new Error('Possible infinite loop detected'), 'Loop Monitor');\r\n          clearInterval(monitor);\r\n        }\r\n      } else {\r\n        loopCount = 0;\r\n      }\r\n\r\n      lastCheck = now;\r\n    }, 10);\r\n\r\n    // Stop monitoring after 30 seconds\r\n    setTimeout(() => clearInterval(monitor), 30000);\r\n  },\r\n\r\n  // Track initialization timeout\r\n  startTimeoutMonitor(timeout = 15000) {\r\n    setTimeout(() => {\r\n      if (!window.ReinoCalculator?.app?.isInitialized) {\r\n        this.logError(new Error('Initialization timeout'), `No completion after ${timeout}ms`);\r\n        this.generateReport();\r\n\r\n        // Try to identify stuck point\r\n        const systems = window.ReinoCalculator?.systems || {};\r\n        const stuckSystems = [];\r\n\r\n        for (const [name, system] of Object.entries(systems)) {\r\n          if (!system.isInitialized) {\r\n            stuckSystems.push(name);\r\n          }\r\n        }\r\n\r\n        if (stuckSystems.length > 0) {\r\n          console.error('Systems not initialized:', stuckSystems);\r\n        }\r\n      }\r\n    }, timeout);\r\n  }\r\n};\r\n\r\n// Start monitoring\r\nReinoDebug.markPhase('Debug script loaded');\r\nReinoDebug.startLoopMonitor();\r\nReinoDebug.startTimeoutMonitor();\r\n\r\n// Monitor DOM ready\r\nif (document.readyState === 'loading') {\r\n  ReinoDebug.markPhase('DOM still loading');\r\n  document.addEventListener('DOMContentLoaded', () => {\r\n    ReinoDebug.markPhase('DOM Content Loaded');\r\n    ReinoDebug.checkSystemStatus();\r\n  });\r\n} else {\r\n  ReinoDebug.markPhase('DOM already loaded');\r\n}\r\n\r\n// Monitor window load\r\nwindow.addEventListener('load', () => {\r\n  ReinoDebug.markPhase('Window loaded');\r\n  ReinoDebug.checkSystemStatus();\r\n});\r\n\r\n// Monitor Reino Calculator initialization\r\ndocument.addEventListener('reinoCalculatorReady', (event) => {\r\n  ReinoDebug.markPhase('Reino Calculator Ready', {\r\n    systems: Object.keys(event.detail.systems || {})\r\n  });\r\n  ReinoDebug.generateReport();\r\n});\r\n\r\n// Monitor specific system events\r\nconst eventsToMonitor = [\r\n  'dragDropItemAdded',\r\n  'dragDropItemRemoved',\r\n  'dragDropStateChanged',\r\n  'assetsSyncCompleted'\r\n];\r\n\r\neventsToMonitor.forEach(eventName => {\r\n  document.addEventListener(eventName, (event) => {\r\n    ReinoDebug.markPhase(`Event: ${eventName}`, event.detail);\r\n  });\r\n});\r\n\r\n// Monitor errors\r\nwindow.addEventListener('error', (event) => {\r\n  ReinoDebug.logError(event.error || event, 'Window error event');\r\n});\r\n\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  ReinoDebug.logError(event.reason, 'Unhandled promise rejection');\r\n});\r\n\r\n// Expose debug commands\r\nwindow.debugReino = {\r\n  report: () => ReinoDebug.generateReport(),\r\n  status: () => ReinoDebug.checkSystemStatus(),\r\n  phases: () => ReinoDebug.phases,\r\n  errors: () => ReinoDebug.errors,\r\n\r\n  // Force initialization\r\n  forceInit: async () => {\r\n    console.log('Forcing initialization...');\r\n    if (window.ReinoCalculator?.app) {\r\n      await window.ReinoCalculator.app.init();\r\n    } else {\r\n      console.error('ReinoCalculator app not found');\r\n    }\r\n  },\r\n\r\n  // Check specific systems\r\n  checkSystem: (systemName) => {\r\n    const system = window.ReinoCalculator?.systems?.[systemName];\r\n    if (system) {\r\n      console.log(`System ${systemName}:`, {\r\n        initialized: system.isInitialized,\r\n        state: system.getState?.() || 'No state method'\r\n      });\r\n    } else {\r\n      console.error(`System ${systemName} not found`);\r\n    }\r\n  },\r\n\r\n  // Test SortableJS loading\r\n  testSortable: () => {\r\n    if (window.Sortable) {\r\n      console.log('✅ SortableJS is loaded');\r\n      const testEl = document.createElement('div');\r\n      try {\r\n        new window.Sortable(testEl);\r\n        console.log('✅ SortableJS can create instances');\r\n      } catch (error) {\r\n        console.error('❌ SortableJS instance creation failed:', error);\r\n      }\r\n    } else {\r\n      console.error('❌ SortableJS not loaded');\r\n    }\r\n  }\r\n};\r\n\r\nconsole.log(`\r\n🔍 Reino Debug Mode Active\r\n========================\r\nUse these commands in console:\r\n\r\n• debugReino.report() - Generate full report\r\n• debugReino.status() - Check system status\r\n• debugReino.phases - View initialization phases\r\n• debugReino.errors - View errors\r\n• debugReino.forceInit() - Force initialization\r\n• debugReino.checkSystem('systemName') - Check specific system\r\n• debugReino.testSortable() - Test SortableJS\r\n\r\nReport will auto-generate on timeout or completion.\r\n`);\r\n"], "mappings": ";;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACM7F,SAAO,aAAa;AAAA,IAClB,WAAW,KAAK,IAAI;AAAA,IACpB,QAAQ,CAAC;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,IACX,cAAc,CAAC;AAAA;AAAA,IAGf,UAAU,OAAO,UAAU,CAAC,GAAG;AAC7B,YAAM,YAAY,KAAK,IAAI,IAAI,KAAK;AACpC,WAAK,OAAO,KAAK;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAM,oBAAI,KAAK,GAAE,YAAY;AAAA,MAC/B,CAAC;AACD,cAAQ,IAAI,UAAU,SAAS,OAAO,KAAK,IAAI,OAAO;AAAA,IACxD;AAAA;AAAA,IAGA,SAAS,OAAO,UAAU,IAAI;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,OAAO,MAAM,WAAW;AAAA,QACxB,OAAO,MAAM;AAAA,QACb;AAAA,QACA,WAAW,KAAK,IAAI,IAAI,KAAK;AAAA,MAC/B,CAAC;AACD,cAAQ,MAAM,iBAAiB,OAAO,KAAK,KAAK;AAAA,IAClD;AAAA;AAAA,IAGA,WAAW,SAAS,UAAU,CAAC,GAAG;AAChC,WAAK,SAAS,KAAK;AAAA,QACjB;AAAA,QACA;AAAA,QACA,WAAW,KAAK,IAAI,IAAI,KAAK;AAAA,MAC/B,CAAC;AACD,cAAQ,KAAK,mBAAmB,OAAO,IAAI,OAAO;AAAA,IACpD;AAAA;AAAA,IAGA,oBAAoB;AAClB,WAAK,eAAe;AAAA;AAAA,QAElB,YAAY,CAAC,CAAC,OAAO;AAAA,QACrB,SAAS,CAAC,CAAC,OAAO;AAAA,QAClB,QAAQ,CAAC,CAAC,OAAO;AAAA,QACjB,iBAAiB,CAAC,CAAC,OAAO;AAAA;AAAA,QAG1B,aAAa;AAAA,UACX,UAAU,CAAC,CAAC,SAAS,cAAc,wBAAwB;AAAA,UAC3D,oBAAoB,CAAC,CAAC,SAAS,cAAc,yCAAyC;AAAA,UACtF,UAAU,CAAC,CAAC,SAAS,cAAc,mBAAmB;AAAA,UACtD,UAAU,CAAC,CAAC,SAAS,cAAc,yBAAyB;AAAA,UAC5D,UAAU,CAAC,CAAC,SAAS,cAAc,kCAAkC;AAAA,QACvE;AAAA;AAAA,QAGA,aAAa,OAAO,iBAAiB,KAAK,iBAAiB;AAAA;AAAA,QAG3D,QAAQ,YAAY,SAAS;AAAA,UAC3B,MAAM,KAAK,MAAM,YAAY,OAAO,iBAAiB,OAAO,IAAI;AAAA,UAChE,OAAO,KAAK,MAAM,YAAY,OAAO,kBAAkB,OAAO,IAAI;AAAA,QACpE,IAAI;AAAA,MACN;AAEA,aAAO,KAAK;AAAA,IACd;AAAA;AAAA,IAGA,iBAAiB;AACf,YAAM,SAAS;AAAA,QACb,WAAW,KAAK,IAAI,IAAI,KAAK;AAAA,QAC7B,QAAQ,KAAK;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,UAAU,KAAK;AAAA,QACf,cAAc,KAAK,kBAAkB;AAAA,QACrC,oBAAoB,KAAK,sBAAsB;AAAA,MACjD;AAEA,cAAQ,MAAM,8BAAuB;AACrC,cAAQ,IAAI,8BAA8B,OAAO,YAAY,IAAI;AACjE,cAAQ,IAAI,WAAW,OAAO,MAAM;AACpC,cAAQ,IAAI,WAAW,OAAO,MAAM;AACpC,cAAQ,IAAI,aAAa,OAAO,QAAQ;AACxC,cAAQ,IAAI,kBAAkB,OAAO,YAAY;AACjD,cAAQ,IAAI,gBAAgB,OAAO,kBAAkB;AACrD,cAAQ,SAAS;AAEjB,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,wBAAwB;AACtB,YAAM,aAAa,YAAY,iBAAiB,YAAY,EAAE,CAAC;AAC/D,aAAO;AAAA,QACL,kBAAkB,YAAY,2BAA2B,YAAY;AAAA,QACrE,cAAc,YAAY,eAAe,YAAY;AAAA,QACrD,eAAe,YAAY,iBAAiB,UAAU,EAAE;AAAA,MAC1D;AAAA,IACF;AAAA;AAAA,IAGA,mBAAmB;AACjB,UAAI,YAAY,KAAK,IAAI;AACzB,UAAI,YAAY;AAChB,YAAM,WAAW;AAEjB,YAAM,UAAU,YAAY,MAAM;AAChC,cAAM,MAAM,KAAK,IAAI;AACrB,cAAM,WAAW,MAAM;AAEvB,YAAI,WAAW,IAAI;AACjB;AACA,cAAI,YAAY,UAAU;AACxB,iBAAK,SAAS,IAAI,MAAM,iCAAiC,GAAG,cAAc;AAC1E,0BAAc,OAAO;AAAA,UACvB;AAAA,QACF,OAAO;AACL,sBAAY;AAAA,QACd;AAEA,oBAAY;AAAA,MACd,GAAG,EAAE;AAGL,iBAAW,MAAM,cAAc,OAAO,GAAG,GAAK;AAAA,IAChD;AAAA;AAAA,IAGA,oBAAoB,UAAU,MAAO;AACnC,iBAAW,MAAM;AACf,YAAI,CAAC,OAAO,iBAAiB,KAAK,eAAe;AAC/C,eAAK,SAAS,IAAI,MAAM,wBAAwB,GAAG,uBAAuB,OAAO,IAAI;AACrF,eAAK,eAAe;AAGpB,gBAAM,UAAU,OAAO,iBAAiB,WAAW,CAAC;AACpD,gBAAM,eAAe,CAAC;AAEtB,qBAAW,CAAC,MAAM,MAAM,KAAK,OAAO,QAAQ,OAAO,GAAG;AACpD,gBAAI,CAAC,OAAO,eAAe;AACzB,2BAAa,KAAK,IAAI;AAAA,YACxB;AAAA,UACF;AAEA,cAAI,aAAa,SAAS,GAAG;AAC3B,oBAAQ,MAAM,4BAA4B,YAAY;AAAA,UACxD;AAAA,QACF;AAAA,MACF,GAAG,OAAO;AAAA,IACZ;AAAA,EACF;AAGA,aAAW,UAAU,qBAAqB;AAC1C,aAAW,iBAAiB;AAC5B,aAAW,oBAAoB;AAG/B,MAAI,SAAS,eAAe,WAAW;AACrC,eAAW,UAAU,mBAAmB;AACxC,aAAS,iBAAiB,oBAAoB,MAAM;AAClD,iBAAW,UAAU,oBAAoB;AACzC,iBAAW,kBAAkB;AAAA,IAC/B,CAAC;AAAA,EACH,OAAO;AACL,eAAW,UAAU,oBAAoB;AAAA,EAC3C;AAGA,SAAO,iBAAiB,QAAQ,MAAM;AACpC,eAAW,UAAU,eAAe;AACpC,eAAW,kBAAkB;AAAA,EAC/B,CAAC;AAGD,WAAS,iBAAiB,wBAAwB,CAAC,UAAU;AAC3D,eAAW,UAAU,0BAA0B;AAAA,MAC7C,SAAS,OAAO,KAAK,MAAM,OAAO,WAAW,CAAC,CAAC;AAAA,IACjD,CAAC;AACD,eAAW,eAAe;AAAA,EAC5B,CAAC;AAGD,MAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,kBAAgB,QAAQ,eAAa;AACnC,aAAS,iBAAiB,WAAW,CAAC,UAAU;AAC9C,iBAAW,UAAU,UAAU,SAAS,IAAI,MAAM,MAAM;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC;AAGD,SAAO,iBAAiB,SAAS,CAAC,UAAU;AAC1C,eAAW,SAAS,MAAM,SAAS,OAAO,oBAAoB;AAAA,EAChE,CAAC;AAED,SAAO,iBAAiB,sBAAsB,CAAC,UAAU;AACvD,eAAW,SAAS,MAAM,QAAQ,6BAA6B;AAAA,EACjE,CAAC;AAGD,SAAO,aAAa;AAAA,IAClB,QAAQ,MAAM,WAAW,eAAe;AAAA,IACxC,QAAQ,MAAM,WAAW,kBAAkB;AAAA,IAC3C,QAAQ,MAAM,WAAW;AAAA,IACzB,QAAQ,MAAM,WAAW;AAAA;AAAA,IAGzB,WAAW,YAAY;AACrB,cAAQ,IAAI,2BAA2B;AACvC,UAAI,OAAO,iBAAiB,KAAK;AAC/B,cAAM,OAAO,gBAAgB,IAAI,KAAK;AAAA,MACxC,OAAO;AACL,gBAAQ,MAAM,+BAA+B;AAAA,MAC/C;AAAA,IACF;AAAA;AAAA,IAGA,aAAa,CAAC,eAAe;AAC3B,YAAM,SAAS,OAAO,iBAAiB,UAAU,UAAU;AAC3D,UAAI,QAAQ;AACV,gBAAQ,IAAI,UAAU,UAAU,KAAK;AAAA,UACnC,aAAa,OAAO;AAAA,UACpB,OAAO,OAAO,WAAW,KAAK;AAAA,QAChC,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,MAAM,UAAU,UAAU,YAAY;AAAA,MAChD;AAAA,IACF;AAAA;AAAA,IAGA,cAAc,MAAM;AAClB,UAAI,OAAO,UAAU;AACnB,gBAAQ,IAAI,6BAAwB;AACpC,cAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,YAAI;AACF,cAAI,OAAO,SAAS,MAAM;AAC1B,kBAAQ,IAAI,wCAAmC;AAAA,QACjD,SAAS,OAAO;AACd,kBAAQ,MAAM,+CAA0C,KAAK;AAAA,QAC/D;AAAA,MACF,OAAO;AACL,gBAAQ,MAAM,8BAAyB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAcX;", "names": []}