"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/asset-selection-filter.js
  var AssetSelectionFilterSystem = class {
    static {
      __name(this, "AssetSelectionFilterSystem");
    }
    constructor() {
      this.isInitialized = false;
      this.selectedAssets = /* @__PURE__ */ new Set();
      this.section2Assets = [];
      this.section3Assets = [];
      this.section2 = null;
      this.section3 = null;
      this.counterElement = null;
      this.cacheKey = "asset_selection_filter";
    }
    // Cache Manager - similar to patrimony-sync.js
    get CacheManager() {
      return {
        set: /* @__PURE__ */ __name((key, value) => {
          try {
            window.localStorage.setItem(key, JSON.stringify(value));
          } catch {
          }
        }, "set"),
        get: /* @__PURE__ */ __name((key) => {
          try {
            const value = window.localStorage.getItem(key);
            return value ? JSON.parse(value) : null;
          } catch {
            return null;
          }
        }, "get"),
        remove: /* @__PURE__ */ __name((key) => {
          try {
            window.localStorage.removeItem(key);
          } catch {
          }
        }, "remove")
      };
    }
    init() {
      if (this.isInitialized) {
        return;
      }
      document.addEventListener("DOMContentLoaded", () => {
        this.initializeSystem();
      });
      this.isInitialized = true;
    }
    initializeSystem() {
      this.section2 = document.querySelector("._2-section-calc-ativos");
      this.section3 = document.querySelector("._3-section-patrimonio-alocation");
      if (!this.section2 || !this.section3) {
        console.warn("Se\xE7\xF5es 2 ou 3 n\xE3o encontradas");
        return;
      }
      this.setupAssetSelection();
      this.setupCounter();
      this.setupClearButton();
      this.initialFilterSetup();
      this.setupSystemListeners();
      this.loadCachedSelections();
      setTimeout(() => {
        document.dispatchEvent(
          new CustomEvent("assetSelectionSystemReady", {
            detail: {
              selectedCount: this.selectedAssets.size,
              selectedAssets: Array.from(this.selectedAssets),
              cacheLoaded: false
            }
          })
        );
      }, 200);
    }
    setupAssetSelection() {
      const dropdownAssets = this.section2.querySelectorAll(".ativo-item-subcategory");
      dropdownAssets.forEach((asset) => this.makeAssetSelectable(asset, "dropdown"));
      const individualAssets = this.section2.querySelectorAll(".ativos_item:not(.dropdown)");
      individualAssets.forEach((asset) => this.makeAssetSelectable(asset, "individual"));
    }
    makeAssetSelectable(assetElement, type) {
      const category = assetElement.getAttribute("ativo-category");
      const product = assetElement.getAttribute("ativo-product");
      if (!category || !product) {
        return;
      }
      const normalizedCategory = this.normalizeString(category);
      const normalizedProduct = this.normalizeString(product);
      const checkboxContainer = document.createElement("div");
      checkboxContainer.className = "asset-checkbox-container";
      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.className = "asset-checkbox";
      checkbox.id = `asset-${normalizedCategory}-${normalizedProduct}`.replace(/\s+/g, "-").toLowerCase();
      const label = document.createElement("label");
      label.htmlFor = checkbox.id;
      label.className = "asset-checkbox-label";
      checkboxContainer.appendChild(checkbox);
      checkboxContainer.appendChild(label);
      if (type === "dropdown") {
        assetElement.insertBefore(checkboxContainer, assetElement.firstChild);
      } else {
        const iconElement = assetElement.querySelector(".icon-dragabble");
        if (iconElement) {
          iconElement.parentNode.insertBefore(checkboxContainer, iconElement.nextSibling);
        } else {
          assetElement.insertBefore(checkboxContainer, assetElement.firstChild);
        }
      }
      checkbox.addEventListener("change", (e) => {
        this.handleAssetSelection(e.target.checked, category, product, assetElement);
      });
      assetElement.addEventListener("click", (e) => {
        if (!e.target.matches(".asset-checkbox, .asset-checkbox-label")) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event("change"));
        }
      });
      this.section2Assets.push({
        element: assetElement,
        checkbox,
        category,
        product,
        normalizedKey: `${normalizedCategory}|${normalizedProduct}`,
        key: `${category}|${product}`
        // manter key original para compatibilidade
      });
    }
    // Método para normalizar strings para comparação consistente
    normalizeString(str) {
      return str.toLowerCase().trim();
    }
    handleAssetSelection(isSelected, category, product, assetElement) {
      const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;
      if (isSelected) {
        this.selectedAssets.add(normalizedKey);
        assetElement.classList.add("selected-asset");
        this.resetAssetValues(category, product);
      } else {
        this.selectedAssets.delete(normalizedKey);
        assetElement.classList.remove("selected-asset");
        this.resetAssetValues(category, product);
      }
      this.updateCounter();
      this.filterSection3();
      this.saveSelectionsToCache();
      document.dispatchEvent(
        new CustomEvent("assetSelectionChanged", {
          detail: {
            selectedCount: this.selectedAssets.size,
            selectedAssets: Array.from(this.selectedAssets)
          }
        })
      );
    }
    setupSystemListeners() {
      document.addEventListener("patrimonySyncReset", () => {
        this.selectedAssets.clear();
        this.section2Assets.forEach((asset) => {
          asset.checkbox.checked = false;
          asset.element.classList.remove("selected-asset");
        });
        this.updateCounter();
        this.filterSection3();
      });
    }
    setupCounter() {
      this.counterElement = this.section2.querySelector(".counter_ativos");
      if (this.counterElement) {
        this.updateCounter();
      }
    }
    updateCounter() {
      if (this.counterElement) {
        this.counterElement.textContent = `(${this.selectedAssets.size})`;
      }
    }
    setupClearButton() {
      const clearButton = this.section2.querySelector(".ativos_clean-button");
      if (clearButton) {
        clearButton.addEventListener("click", (e) => {
          e.preventDefault();
          this.clearAllSelections();
        });
      }
    }
    /**
     * Reset all input values for a specific asset to zero
     * This ensures deselected assets don't affect calculations
     */
    resetAssetValues(category, product) {
      try {
        const patrimonioItem = this.section3.querySelector(
          `.patrimonio_interactive_item[ativo-category="${category}"][ativo-product="${product}"]`
        );
        if (patrimonioItem) {
          const input = patrimonioItem.querySelector('[input-settings="receive"]');
          if (input) {
            input.value = "R$ 0,00";
            input.dispatchEvent(
              new CustomEvent("currencyChange", {
                detail: { value: 0 },
                bubbles: true
              })
            );
            input.dispatchEvent(new Event("input", { bubbles: true }));
          }
          const slider = patrimonioItem.querySelector("range-slider");
          if (slider) {
            slider.value = 0;
            slider.dispatchEvent(new Event("input", { bubbles: true }));
          }
          const percentageDisplay = patrimonioItem.querySelector(".porcentagem-calculadora");
          if (percentageDisplay) {
            percentageDisplay.textContent = "0%";
          }
        }
      } catch (error) {
        console.warn(`Error resetting values for ${category} - ${product}:`, error);
      }
    }
    /**
     * Save current asset selections to cache
     */
    saveSelectionsToCache() {
      try {
        const selectionsData = {
          selectedAssets: Array.from(this.selectedAssets),
          timestamp: Date.now()
        };
        this.CacheManager.set(this.cacheKey, selectionsData);
      } catch (error) {
        console.warn("Error saving asset selections to cache:", error);
      }
    }
    /**
     * Load asset selections from cache and restore UI state
     */
    loadCachedSelections() {
      try {
        const cachedData = this.CacheManager.get(this.cacheKey);
        if (!cachedData || !Array.isArray(cachedData.selectedAssets)) {
          return;
        }
        this.selectedAssets = new Set(cachedData.selectedAssets);
        cachedData.selectedAssets.forEach((normalizedKey) => {
          const asset = this.section2Assets.find((a) => a.normalizedKey === normalizedKey);
          if (asset) {
            asset.checkbox.checked = true;
            asset.element.classList.add("selected-asset");
          }
        });
        this.updateCounter();
        this.filterSection3();
        document.dispatchEvent(
          new CustomEvent("assetSelectionChanged", {
            detail: {
              selectedCount: this.selectedAssets.size,
              selectedAssets: Array.from(this.selectedAssets),
              fromCache: true
            }
          })
        );
        setTimeout(() => {
          document.dispatchEvent(
            new CustomEvent("assetSelectionSystemReady", {
              detail: {
                selectedCount: this.selectedAssets.size,
                selectedAssets: Array.from(this.selectedAssets),
                cacheLoaded: true
              }
            })
          );
        }, 100);
      } catch (error) {
        console.warn("Error loading asset selections from cache:", error);
      }
    }
    /**
     * Clear asset selections cache
     */
    clearSelectionsCache() {
      try {
        this.CacheManager.remove(this.cacheKey);
      } catch (error) {
        console.warn("Error clearing asset selections cache:", error);
      }
    }
    clearAllSelections() {
      this.selectedAssets.clear();
      this.section2Assets.forEach((asset) => {
        asset.checkbox.checked = false;
        asset.element.classList.remove("selected-asset");
        this.resetAssetValues(asset.category, asset.product);
      });
      this.updateCounter();
      this.filterSection3();
      this.clearSelectionsCache();
    }
    initialFilterSetup() {
      const section3Assets = this.section3.querySelectorAll(
        ".ativos-grafico-item, .patrimonio_interactive_item"
      );
      section3Assets.forEach((asset) => {
        const category = asset.getAttribute("ativo-category");
        const product = asset.getAttribute("ativo-product");
        if (category && product) {
          this.section3Assets.push({
            element: asset,
            category,
            product,
            normalizedKey: `${this.normalizeString(category)}|${this.normalizeString(product)}`,
            key: `${category}|${product}`
            // manter key original para compatibilidade
          });
        }
      });
      this.filterSection3();
    }
    filterSection3() {
      this.section3Assets.forEach((asset) => {
        const isSelected = this.selectedAssets.has(asset.normalizedKey);
        if (isSelected) {
          asset.element.style.display = "";
          asset.element.classList.remove("asset-filtered-out");
          asset.element.classList.add("asset-filtered-in");
        } else {
          asset.element.style.display = "none";
          asset.element.classList.add("asset-filtered-out");
          asset.element.classList.remove("asset-filtered-in");
        }
      });
      document.dispatchEvent(
        new CustomEvent("assetFilterChanged", {
          detail: {
            selectedAssets: Array.from(this.selectedAssets),
            selectedCount: this.selectedAssets.size
          }
        })
      );
    }
    // Métodos públicos para integração
    getSelectedAssets() {
      return Array.from(this.selectedAssets);
    }
    isAssetSelected(category, product) {
      const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;
      return this.selectedAssets.has(normalizedKey);
    }
    selectAsset(category, product) {
      const asset = this.section2Assets.find((a) => a.category === category && a.product === product);
      if (asset && !asset.checkbox.checked) {
        asset.checkbox.checked = true;
        asset.checkbox.dispatchEvent(new Event("change"));
      }
    }
    deselectAsset(category, product) {
      const asset = this.section2Assets.find((a) => a.category === category && a.product === product);
      if (asset && asset.checkbox.checked) {
        asset.checkbox.checked = false;
        asset.checkbox.dispatchEvent(new Event("change"));
      }
    }
    /**
     * Reset the entire asset selection system and clear all caches
     * This method can be called by other systems for complete reset
     */
    resetSystem() {
      try {
        this.clearAllSelections();
        document.dispatchEvent(
          new CustomEvent("assetSelectionSystemReset", {
            detail: {
              timestamp: Date.now()
            }
          })
        );
      } catch (error) {
        console.warn("Error resetting asset selection system:", error);
      }
    }
    /**
     * Get cache information for debugging
     */
    getCacheInfo() {
      try {
        const cachedData = this.CacheManager.get(this.cacheKey);
        return {
          hasCachedData: !!cachedData,
          cachedSelections: cachedData?.selectedAssets?.length || 0,
          timestamp: cachedData?.timestamp || null,
          currentSelections: this.selectedAssets.size
        };
      } catch (error) {
        console.warn("Error getting cache info:", error);
        return null;
      }
    }
  };
})();
//# sourceMappingURL=asset-selection-filter.js.map
