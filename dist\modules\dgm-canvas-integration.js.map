{"version": 3, "sources": ["../../bin/live-reload.js", "../../src/modules/dgm-canvas-integration.js"], "sourcesContent": ["new EventSource(`${SERVE_ORIGIN}/esbuild`).addEventListener('change', () => location.reload());\r\n", "/**\r\n * DGM Canvas Integration System\r\n * Handles sending processed form data to DGM Canvas\r\n *\r\n * This module is responsible for:\r\n * - Formatting data for DGM Canvas\r\n * - Sending HTTP requests to DGM Canvas API\r\n * - Handling success/error responses\r\n * - Providing configuration options\r\n */\r\n\r\n/* global navigator, fetch, AbortController */\r\n\r\nexport class DGMCanvasIntegration {\r\n  constructor(config = {}) {\r\n    this.isInitialized = false;\r\n    this.debugMode = false;\r\n\r\n    // Configuration\r\n    this.config = {\r\n      endpoint: config.endpoint || this.getDefaultEndpoint(),\r\n      timeout: config.timeout || 30000, // 30 seconds\r\n      retryAttempts: config.retryAttempts || 3,\r\n      retryDelay: config.retryDelay || 1000, // 1 second\r\n      ...config,\r\n    };\r\n\r\n    // Headers\r\n    this.defaultHeaders = {\r\n      'Content-Type': 'application/json',\r\n      'X-Source': 'app-calc-reino',\r\n      'X-Version': '1.0.0',\r\n      ...config.headers,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get default endpoint based on environment\r\n   * @returns {string} Default endpoint URL\r\n   */\r\n  getDefaultEndpoint() {\r\n    // Check if running in development or production\r\n    if (typeof window !== 'undefined') {\r\n      const isDevelopment =\r\n        window.location.hostname === 'localhost' ||\r\n        window.location.hostname === '127.0.0.1' ||\r\n        window.location.hostname.includes('webflow.io') || // Webflow preview\r\n        window.location.search.includes('debug=true');\r\n\r\n      if (isDevelopment) {\r\n        return 'http://localhost:5173/api/data'; // DGM Canvas dev server\r\n      }\r\n    }\r\n\r\n    // Production endpoint - você pode configurar conforme necessário\r\n    return 'https://your-dgm-canvas-production-url.com/api/data';\r\n  }\r\n\r\n  async init() {\r\n    if (this.isInitialized) return;\r\n\r\n    try {\r\n      // Setup debug mode\r\n      this.debugMode =\r\n        window.location.hostname === 'localhost' ||\r\n        window.location.hostname === '127.0.0.1' ||\r\n        window.location.search.includes('debug=true');\r\n\r\n      // Test connection if in debug mode\r\n      if (this.debugMode) {\r\n        await this.testConnection();\r\n      }\r\n\r\n      this.isInitialized = true;\r\n\r\n      if (this.debugMode) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ [DGMCanvas] Integration initialized');\r\n        // eslint-disable-next-line no-console\r\n        console.log('📋 [DGMCanvas] Config:', this.config);\r\n        // eslint-disable-next-line no-console\r\n        console.log('🌐 [DGMCanvas] Endpoint:', this.config.endpoint);\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ [DGMCanvas] Initialization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Test connection to DGM Canvas\r\n   */\r\n  async testConnection() {\r\n    try {\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\r\n\r\n      const response = await fetch(this.config.endpoint + '/health', {\r\n        method: 'GET',\r\n        headers: {\r\n          'X-Source': 'app-calc-reino-test',\r\n        },\r\n        signal: controller.signal,\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (response.ok) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ [DGMCanvas] Connection test successful');\r\n      } else {\r\n        console.warn('⚠️ [DGMCanvas] Connection test failed with status:', response.status);\r\n      }\r\n    } catch (error) {\r\n      console.warn('⚠️ [DGMCanvas] Connection test failed:', error.message);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send data to DGM Canvas\r\n   * @param {Object} formData - The collected form data\r\n   * @param {Object} supabaseResult - The result from Supabase insertion\r\n   * @returns {Promise<Object>} Result object with success status\r\n   */\r\n  async sendData(formData, supabaseResult = null) {\r\n    if (!this.isInitialized) {\r\n      await this.init();\r\n    }\r\n\r\n    try {\r\n      if (this.debugMode) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('🎨 [DGMCanvas] Preparing data for transmission...');\r\n        // eslint-disable-next-line no-console\r\n        console.log('🌐 [DGMCanvas] Target endpoint:', this.config.endpoint);\r\n      }\r\n\r\n      // Format data for DGM Canvas\r\n      const dgmData = this.formatDataForDGM(formData, supabaseResult);\r\n\r\n      if (this.debugMode) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('📊 [DGMCanvas] Formatted data summary:');\r\n        // eslint-disable-next-line no-console\r\n        console.log(`💰 Patrimônio: R$ ${dgmData.patrimonio.total.toLocaleString('pt-BR')}`);\r\n        // eslint-disable-next-line no-console\r\n        console.log(`📈 Ativos escolhidos: ${dgmData.ativos.escolhidos.length} itens`);\r\n        dgmData.ativos.escolhidos.forEach((ativo, index) => {\r\n          // eslint-disable-next-line no-console\r\n          console.log(`   ${index + 1}. ${ativo.product} (${ativo.category})`);\r\n        });\r\n        // eslint-disable-next-line no-console\r\n        console.log(\r\n          `💼 Alocações com valor: ${dgmData.ativos.alocacao.filter((a) => a.value > 0).length} itens`\r\n        );\r\n        dgmData.ativos.alocacao\r\n          .filter((a) => a.value > 0)\r\n          .forEach((ativo) => {\r\n            // eslint-disable-next-line no-console\r\n            console.log(\r\n              `   • ${ativo.product}: ${ativo.valueFormatted} (${ativo.percentageFormatted})`\r\n            );\r\n          });\r\n        // eslint-disable-next-line no-console\r\n        console.log(\r\n          `👤 Usuário: ${dgmData.usuario.nome || 'N/A'} (${dgmData.usuario.email || 'N/A'})`\r\n        );\r\n        // eslint-disable-next-line no-console\r\n        console.log('📋 [DGMCanvas] Full data:', JSON.stringify(dgmData, null, 2));\r\n      }\r\n\r\n      // Send with retry logic\r\n      const result = await this.sendWithRetry(dgmData);\r\n\r\n      if (this.debugMode) {\r\n        // eslint-disable-next-line no-console\r\n        console.log('✅ [DGMCanvas] Data sent successfully:', result);\r\n      }\r\n\r\n      // Dispatch success event\r\n      this.dispatchEvent('dgmCanvasDataSent', {\r\n        dgmData,\r\n        result,\r\n        success: true,\r\n      });\r\n\r\n      return { success: true, result };\r\n    } catch (error) {\r\n      console.error('❌ [DGMCanvas] Failed to send data:', error);\r\n\r\n      // Dispatch error event\r\n      this.dispatchEvent('dgmCanvasError', {\r\n        error: error.message,\r\n        formData,\r\n        supabaseResult,\r\n      });\r\n\r\n      // Don't throw - let calling code decide how to handle\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Format form data for DGM Canvas\r\n   * @param {Object} formData - Raw form data\r\n   * @param {Object} supabaseResult - Supabase insertion result\r\n   * @returns {Object} Formatted data for DGM Canvas\r\n   */\r\n  formatDataForDGM(formData, supabaseResult) {\r\n    // Calculate totals\r\n    const totalAlocado = Object.values(formData.alocacao || {}).reduce(\r\n      (sum, item) => sum + (item.value || 0),\r\n      0\r\n    );\r\n\r\n    const patrimonioRestante = (formData.patrimonio || 0) - totalAlocado;\r\n    const percentualAlocado =\r\n      formData.patrimonio > 0 ? (totalAlocado / formData.patrimonio) * 100 : 0;\r\n\r\n    return {\r\n      // Identification\r\n      id: supabaseResult?.id || this.generateTempId(),\r\n      timestamp: formData.timestamp || new Date().toISOString(),\r\n\r\n      // Financial data\r\n      patrimonio: {\r\n        total: formData.patrimonio || 0,\r\n        totalFormatted: this.formatCurrency(formData.patrimonio || 0),\r\n        alocado: totalAlocado,\r\n        alocadoFormatted: this.formatCurrency(totalAlocado),\r\n        restante: patrimonioRestante,\r\n        restanteFormatted: this.formatCurrency(patrimonioRestante),\r\n        percentualAlocado: Math.round(percentualAlocado * 100) / 100,\r\n        percentualAlocadoFormatted: `${Math.round(percentualAlocado * 100) / 100}%`,\r\n      },\r\n\r\n      // Asset allocation\r\n      ativos: {\r\n        escolhidos: formData.ativosEscolhidos || [],\r\n        alocacao: this.formatAllocationForDGM(formData.alocacao || {}),\r\n        resumo: this.generateAllocationSummary(formData.alocacao || {}),\r\n      },\r\n\r\n      // User information (if available from Typebot)\r\n      usuario: {\r\n        nome: formData.nome || null,\r\n        email: formData.email || null,\r\n        hasUserData: !!(formData.nome || formData.email),\r\n      },\r\n\r\n      // Integration metadata\r\n      metadata: {\r\n        source: 'app-calc-reino',\r\n        version: '1.0.0',\r\n        hasTypebot: !!(formData.typebotSessionId || formData.typebotResultId),\r\n        typebotSessionId: formData.typebotSessionId || null,\r\n        typebotResultId: formData.typebotResultId || null,\r\n        userAgent:\r\n          formData.userAgent ||\r\n          (typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown'),\r\n        sessionId: formData.sessionId || this.generateTempId(),\r\n        supabaseId: supabaseResult?.id || null,\r\n        submittedAt: formData.timestamp || new Date().toISOString(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Format allocation data for DGM Canvas\r\n   * @param {Object} allocation - Raw allocation data\r\n   * @returns {Array} Formatted allocation array\r\n   */\r\n  formatAllocationForDGM(allocation) {\r\n    return Object.entries(allocation).map(([key, data]) => ({\r\n      key,\r\n      category: data.category || 'unknown',\r\n      product: data.product || 'unknown',\r\n      value: data.value || 0,\r\n      valueFormatted: this.formatCurrency(data.value || 0),\r\n      percentage: data.percentage || 0,\r\n      percentageFormatted: `${Math.round((data.percentage || 0) * 100) / 100}%`,\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Generate allocation summary\r\n   * @param {Object} allocation - Raw allocation data\r\n   * @returns {Object} Summary statistics\r\n   */\r\n  generateAllocationSummary(allocation) {\r\n    const categories = {};\r\n    let totalValue = 0;\r\n\r\n    Object.values(allocation).forEach((item) => {\r\n      const category = item.category || 'unknown';\r\n      const value = item.value || 0;\r\n\r\n      if (!categories[category]) {\r\n        categories[category] = { value: 0, count: 0 };\r\n      }\r\n\r\n      categories[category].value += value;\r\n      categories[category].count += 1;\r\n      totalValue += value;\r\n    });\r\n\r\n    return {\r\n      totalItems: Object.keys(allocation).length,\r\n      totalValue,\r\n      totalValueFormatted: this.formatCurrency(totalValue),\r\n      categories: Object.entries(categories).map(([name, data]) => ({\r\n        name,\r\n        value: data.value,\r\n        valueFormatted: this.formatCurrency(data.value),\r\n        count: data.count,\r\n        percentage: totalValue > 0 ? Math.round((data.value / totalValue) * 100 * 100) / 100 : 0,\r\n        percentageFormatted:\r\n          totalValue > 0 ? `${Math.round((data.value / totalValue) * 100 * 100) / 100}%` : '0%',\r\n      })),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Send data with retry logic\r\n   * @param {Object} data - Data to send\r\n   * @returns {Promise<Object>} Response data\r\n   */\r\n  async sendWithRetry(data) {\r\n    let lastError;\r\n\r\n    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt += 1) {\r\n      try {\r\n        if (this.debugMode && attempt > 1) {\r\n          // eslint-disable-next-line no-console\r\n          console.log(`🔄 [DGMCanvas] Retry attempt ${attempt}/${this.config.retryAttempts}`);\r\n        }\r\n\r\n        const controller = new AbortController();\r\n        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);\r\n\r\n        const response = await fetch(this.config.endpoint, {\r\n          method: 'POST',\r\n          headers: this.defaultHeaders,\r\n          body: JSON.stringify(data),\r\n          signal: controller.signal,\r\n        });\r\n\r\n        clearTimeout(timeoutId);\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n        }\r\n\r\n        const result = await response.json();\r\n\r\n        if (this.debugMode) {\r\n          // eslint-disable-next-line no-console\r\n          console.log(`✅ [DGMCanvas] Data sent successfully to DGM Canvas on attempt ${attempt}`);\r\n        }\r\n\r\n        return result;\r\n      } catch (error) {\r\n        lastError = error;\r\n\r\n        if (this.debugMode) {\r\n          console.warn(`⚠️ [DGMCanvas] Attempt ${attempt} failed:`, error.message);\r\n        }\r\n\r\n        // Don't retry on certain errors\r\n        if (error.name === 'AbortError' || error.message.includes('400')) {\r\n          break;\r\n        }\r\n\r\n        // Wait before retry (except on last attempt)\r\n        if (attempt < this.config.retryAttempts) {\r\n          await this.delay(this.config.retryDelay * attempt);\r\n        }\r\n      }\r\n    }\r\n\r\n    throw lastError;\r\n  }\r\n\r\n  /**\r\n   * Update configuration\r\n   * @param {Object} newConfig - New configuration options\r\n   */\r\n  updateConfig(newConfig) {\r\n    this.config = { ...this.config, ...newConfig };\r\n\r\n    if (newConfig.headers) {\r\n      this.defaultHeaders = { ...this.defaultHeaders, ...newConfig.headers };\r\n    }\r\n\r\n    if (this.debugMode) {\r\n      // eslint-disable-next-line no-console\r\n      console.log('🔧 [DGMCanvas] Configuration updated:', this.config);\r\n      // eslint-disable-next-line no-console\r\n      console.log('🌐 [DGMCanvas] New endpoint:', this.config.endpoint);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reinitialize with new configuration (useful for testing)\r\n   * @param {Object} newConfig - New configuration options\r\n   */\r\n  async reinitialize(newConfig = {}) {\r\n    this.isInitialized = false;\r\n    this.updateConfig(newConfig);\r\n    await this.init();\r\n  }\r\n\r\n  /**\r\n   * Get current configuration\r\n   * @returns {Object} Current configuration\r\n   */\r\n  getConfig() {\r\n    return { ...this.config };\r\n  }\r\n\r\n  /**\r\n   * Get integration status\r\n   * @returns {Object} Status information\r\n   */\r\n  getStatus() {\r\n    return {\r\n      initialized: this.isInitialized,\r\n      debugMode: this.debugMode,\r\n      endpoint: this.config.endpoint,\r\n      lastError: this.lastError || null,\r\n      version: '1.0.0',\r\n    };\r\n  }\r\n\r\n  // Utility methods\r\n  generateTempId() {\r\n    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\r\n  }\r\n\r\n  formatCurrency(value) {\r\n    return new Intl.NumberFormat('pt-BR', {\r\n      style: 'currency',\r\n      currency: 'BRL',\r\n    }).format(value || 0);\r\n  }\r\n\r\n  delay(ms) {\r\n    return new Promise((resolve) => setTimeout(resolve, ms));\r\n  }\r\n\r\n  dispatchEvent(eventName, detail) {\r\n    if (typeof document !== 'undefined') {\r\n      document.dispatchEvent(new CustomEvent(eventName, { detail }));\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const dgmCanvasIntegration = new DGMCanvasIntegration();\r\n\r\n// Export class for custom instances\r\nexport default DGMCanvasIntegration;\r\n"], "mappings": ";;;;;;AAAA,MAAI,YAAY,GAAG,uBAAY,UAAU,EAAE,iBAAiB,UAAU,MAAM,SAAS,OAAO,CAAC;;;ACatF,MAAM,uBAAN,MAA2B;AAAA,IAblC,OAakC;AAAA;AAAA;AAAA,IAChC,YAAY,SAAS,CAAC,GAAG;AACvB,WAAK,gBAAgB;AACrB,WAAK,YAAY;AAGjB,WAAK,SAAS;AAAA,QACZ,UAAU,OAAO,YAAY,KAAK,mBAAmB;AAAA,QACrD,SAAS,OAAO,WAAW;AAAA;AAAA,QAC3B,eAAe,OAAO,iBAAiB;AAAA,QACvC,YAAY,OAAO,cAAc;AAAA;AAAA,QACjC,GAAG;AAAA,MACL;AAGA,WAAK,iBAAiB;AAAA,QACpB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,qBAAqB;AAEnB,UAAI,OAAO,WAAW,aAAa;AACjC,cAAM,gBACJ,OAAO,SAAS,aAAa,eAC7B,OAAO,SAAS,aAAa,eAC7B,OAAO,SAAS,SAAS,SAAS,YAAY;AAAA,QAC9C,OAAO,SAAS,OAAO,SAAS,YAAY;AAE9C,YAAI,eAAe;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,aAAO;AAAA,IACT;AAAA,IAEA,MAAM,OAAO;AACX,UAAI,KAAK,cAAe;AAExB,UAAI;AAEF,aAAK,YACH,OAAO,SAAS,aAAa,eAC7B,OAAO,SAAS,aAAa,eAC7B,OAAO,SAAS,OAAO,SAAS,YAAY;AAG9C,YAAI,KAAK,WAAW;AAClB,gBAAM,KAAK,eAAe;AAAA,QAC5B;AAEA,aAAK,gBAAgB;AAErB,YAAI,KAAK,WAAW;AAElB,kBAAQ,IAAI,4CAAuC;AAEnD,kBAAQ,IAAI,iCAA0B,KAAK,MAAM;AAEjD,kBAAQ,IAAI,mCAA4B,KAAK,OAAO,QAAQ;AAAA,QAC9D;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,6CAAwC,KAAK;AAC3D,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,iBAAiB;AACrB,UAAI;AACF,cAAM,aAAa,IAAI,gBAAgB;AACvC,cAAM,YAAY,WAAW,MAAM,WAAW,MAAM,GAAG,GAAI;AAE3D,cAAM,WAAW,MAAM,MAAM,KAAK,OAAO,WAAW,WAAW;AAAA,UAC7D,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,YAAY;AAAA,UACd;AAAA,UACA,QAAQ,WAAW;AAAA,QACrB,CAAC;AAED,qBAAa,SAAS;AAEtB,YAAI,SAAS,IAAI;AAEf,kBAAQ,IAAI,+CAA0C;AAAA,QACxD,OAAO;AACL,kBAAQ,KAAK,gEAAsD,SAAS,MAAM;AAAA,QACpF;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,oDAA0C,MAAM,OAAO;AAAA,MACtE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,MAAM,SAAS,UAAU,iBAAiB,MAAM;AAC9C,UAAI,CAAC,KAAK,eAAe;AACvB,cAAM,KAAK,KAAK;AAAA,MAClB;AAEA,UAAI;AACF,YAAI,KAAK,WAAW;AAElB,kBAAQ,IAAI,0DAAmD;AAE/D,kBAAQ,IAAI,0CAAmC,KAAK,OAAO,QAAQ;AAAA,QACrE;AAGA,cAAM,UAAU,KAAK,iBAAiB,UAAU,cAAc;AAE9D,YAAI,KAAK,WAAW;AAElB,kBAAQ,IAAI,+CAAwC;AAEpD,kBAAQ,IAAI,+BAAqB,QAAQ,WAAW,MAAM,eAAe,OAAO,CAAC,EAAE;AAEnF,kBAAQ,IAAI,gCAAyB,QAAQ,OAAO,WAAW,MAAM,QAAQ;AAC7E,kBAAQ,OAAO,WAAW,QAAQ,CAAC,OAAO,UAAU;AAElD,oBAAQ,IAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,GAAG;AAAA,UACrE,CAAC;AAED,kBAAQ;AAAA,YACN,wCAA2B,QAAQ,OAAO,SAAS,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM;AAAA,UACtF;AACA,kBAAQ,OAAO,SACZ,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,EACzB,QAAQ,CAAC,UAAU;AAElB,oBAAQ;AAAA,cACN,aAAQ,MAAM,OAAO,KAAK,MAAM,cAAc,KAAK,MAAM,mBAAmB;AAAA,YAC9E;AAAA,UACF,CAAC;AAEH,kBAAQ;AAAA,YACN,yBAAe,QAAQ,QAAQ,QAAQ,KAAK,KAAK,QAAQ,QAAQ,SAAS,KAAK;AAAA,UACjF;AAEA,kBAAQ,IAAI,oCAA6B,KAAK,UAAU,SAAS,MAAM,CAAC,CAAC;AAAA,QAC3E;AAGA,cAAM,SAAS,MAAM,KAAK,cAAc,OAAO;AAE/C,YAAI,KAAK,WAAW;AAElB,kBAAQ,IAAI,8CAAyC,MAAM;AAAA,QAC7D;AAGA,aAAK,cAAc,qBAAqB;AAAA,UACtC;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AAED,eAAO,EAAE,SAAS,MAAM,OAAO;AAAA,MACjC,SAAS,OAAO;AACd,gBAAQ,MAAM,2CAAsC,KAAK;AAGzD,aAAK,cAAc,kBAAkB;AAAA,UACnC,OAAO,MAAM;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC;AAGD,eAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ;AAAA,MAChD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,iBAAiB,UAAU,gBAAgB;AAEzC,YAAM,eAAe,OAAO,OAAO,SAAS,YAAY,CAAC,CAAC,EAAE;AAAA,QAC1D,CAAC,KAAK,SAAS,OAAO,KAAK,SAAS;AAAA,QACpC;AAAA,MACF;AAEA,YAAM,sBAAsB,SAAS,cAAc,KAAK;AACxD,YAAM,oBACJ,SAAS,aAAa,IAAK,eAAe,SAAS,aAAc,MAAM;AAEzE,aAAO;AAAA;AAAA,QAEL,IAAI,gBAAgB,MAAM,KAAK,eAAe;AAAA,QAC9C,WAAW,SAAS,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA;AAAA,QAGxD,YAAY;AAAA,UACV,OAAO,SAAS,cAAc;AAAA,UAC9B,gBAAgB,KAAK,eAAe,SAAS,cAAc,CAAC;AAAA,UAC5D,SAAS;AAAA,UACT,kBAAkB,KAAK,eAAe,YAAY;AAAA,UAClD,UAAU;AAAA,UACV,mBAAmB,KAAK,eAAe,kBAAkB;AAAA,UACzD,mBAAmB,KAAK,MAAM,oBAAoB,GAAG,IAAI;AAAA,UACzD,4BAA4B,GAAG,KAAK,MAAM,oBAAoB,GAAG,IAAI,GAAG;AAAA,QAC1E;AAAA;AAAA,QAGA,QAAQ;AAAA,UACN,YAAY,SAAS,oBAAoB,CAAC;AAAA,UAC1C,UAAU,KAAK,uBAAuB,SAAS,YAAY,CAAC,CAAC;AAAA,UAC7D,QAAQ,KAAK,0BAA0B,SAAS,YAAY,CAAC,CAAC;AAAA,QAChE;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,MAAM,SAAS,QAAQ;AAAA,UACvB,OAAO,SAAS,SAAS;AAAA,UACzB,aAAa,CAAC,EAAE,SAAS,QAAQ,SAAS;AAAA,QAC5C;AAAA;AAAA,QAGA,UAAU;AAAA,UACR,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,YAAY,CAAC,EAAE,SAAS,oBAAoB,SAAS;AAAA,UACrD,kBAAkB,SAAS,oBAAoB;AAAA,UAC/C,iBAAiB,SAAS,mBAAmB;AAAA,UAC7C,WACE,SAAS,cACR,OAAO,cAAc,cAAc,UAAU,YAAY;AAAA,UAC5D,WAAW,SAAS,aAAa,KAAK,eAAe;AAAA,UACrD,YAAY,gBAAgB,MAAM;AAAA,UAClC,aAAa,SAAS,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,uBAAuB,YAAY;AACjC,aAAO,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,CAAC,KAAK,IAAI,OAAO;AAAA,QACtD;AAAA,QACA,UAAU,KAAK,YAAY;AAAA,QAC3B,SAAS,KAAK,WAAW;AAAA,QACzB,OAAO,KAAK,SAAS;AAAA,QACrB,gBAAgB,KAAK,eAAe,KAAK,SAAS,CAAC;AAAA,QACnD,YAAY,KAAK,cAAc;AAAA,QAC/B,qBAAqB,GAAG,KAAK,OAAO,KAAK,cAAc,KAAK,GAAG,IAAI,GAAG;AAAA,MACxE,EAAE;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,0BAA0B,YAAY;AACpC,YAAM,aAAa,CAAC;AACpB,UAAI,aAAa;AAEjB,aAAO,OAAO,UAAU,EAAE,QAAQ,CAAC,SAAS;AAC1C,cAAM,WAAW,KAAK,YAAY;AAClC,cAAM,QAAQ,KAAK,SAAS;AAE5B,YAAI,CAAC,WAAW,QAAQ,GAAG;AACzB,qBAAW,QAAQ,IAAI,EAAE,OAAO,GAAG,OAAO,EAAE;AAAA,QAC9C;AAEA,mBAAW,QAAQ,EAAE,SAAS;AAC9B,mBAAW,QAAQ,EAAE,SAAS;AAC9B,sBAAc;AAAA,MAChB,CAAC;AAED,aAAO;AAAA,QACL,YAAY,OAAO,KAAK,UAAU,EAAE;AAAA,QACpC;AAAA,QACA,qBAAqB,KAAK,eAAe,UAAU;AAAA,QACnD,YAAY,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO;AAAA,UAC5D;AAAA,UACA,OAAO,KAAK;AAAA,UACZ,gBAAgB,KAAK,eAAe,KAAK,KAAK;AAAA,UAC9C,OAAO,KAAK;AAAA,UACZ,YAAY,aAAa,IAAI,KAAK,MAAO,KAAK,QAAQ,aAAc,MAAM,GAAG,IAAI,MAAM;AAAA,UACvF,qBACE,aAAa,IAAI,GAAG,KAAK,MAAO,KAAK,QAAQ,aAAc,MAAM,GAAG,IAAI,GAAG,MAAM;AAAA,QACrF,EAAE;AAAA,MACJ;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,MAAM,cAAc,MAAM;AACxB,UAAI;AAEJ,eAAS,UAAU,GAAG,WAAW,KAAK,OAAO,eAAe,WAAW,GAAG;AACxE,YAAI;AACF,cAAI,KAAK,aAAa,UAAU,GAAG;AAEjC,oBAAQ,IAAI,uCAAgC,OAAO,IAAI,KAAK,OAAO,aAAa,EAAE;AAAA,UACpF;AAEA,gBAAM,aAAa,IAAI,gBAAgB;AACvC,gBAAM,YAAY,WAAW,MAAM,WAAW,MAAM,GAAG,KAAK,OAAO,OAAO;AAE1E,gBAAM,WAAW,MAAM,MAAM,KAAK,OAAO,UAAU;AAAA,YACjD,QAAQ;AAAA,YACR,SAAS,KAAK;AAAA,YACd,MAAM,KAAK,UAAU,IAAI;AAAA,YACzB,QAAQ,WAAW;AAAA,UACrB,CAAC;AAED,uBAAa,SAAS;AAEtB,cAAI,CAAC,SAAS,IAAI;AAChB,kBAAM,IAAI,MAAM,QAAQ,SAAS,MAAM,KAAK,SAAS,UAAU,EAAE;AAAA,UACnE;AAEA,gBAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,cAAI,KAAK,WAAW;AAElB,oBAAQ,IAAI,sEAAiE,OAAO,EAAE;AAAA,UACxF;AAEA,iBAAO;AAAA,QACT,SAAS,OAAO;AACd,sBAAY;AAEZ,cAAI,KAAK,WAAW;AAClB,oBAAQ,KAAK,oCAA0B,OAAO,YAAY,MAAM,OAAO;AAAA,UACzE;AAGA,cAAI,MAAM,SAAS,gBAAgB,MAAM,QAAQ,SAAS,KAAK,GAAG;AAChE;AAAA,UACF;AAGA,cAAI,UAAU,KAAK,OAAO,eAAe;AACvC,kBAAM,KAAK,MAAM,KAAK,OAAO,aAAa,OAAO;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAEA,YAAM;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa,WAAW;AACtB,WAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,GAAG,UAAU;AAE7C,UAAI,UAAU,SAAS;AACrB,aAAK,iBAAiB,EAAE,GAAG,KAAK,gBAAgB,GAAG,UAAU,QAAQ;AAAA,MACvE;AAEA,UAAI,KAAK,WAAW;AAElB,gBAAQ,IAAI,gDAAyC,KAAK,MAAM;AAEhE,gBAAQ,IAAI,uCAAgC,KAAK,OAAO,QAAQ;AAAA,MAClE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,aAAa,YAAY,CAAC,GAAG;AACjC,WAAK,gBAAgB;AACrB,WAAK,aAAa,SAAS;AAC3B,YAAM,KAAK,KAAK;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,YAAY;AACV,aAAO,EAAE,GAAG,KAAK,OAAO;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,YAAY;AACV,aAAO;AAAA,QACL,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK,OAAO;AAAA,QACtB,WAAW,KAAK,aAAa;AAAA,QAC7B,SAAS;AAAA,MACX;AAAA,IACF;AAAA;AAAA,IAGA,iBAAiB;AACf,aAAO,UAAU,KAAK,IAAI,IAAI,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC;AAAA,IAC5E;AAAA,IAEA,eAAe,OAAO;AACpB,aAAO,IAAI,KAAK,aAAa,SAAS;AAAA,QACpC,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC,EAAE,OAAO,SAAS,CAAC;AAAA,IACtB;AAAA,IAEA,MAAM,IAAI;AACR,aAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,IACzD;AAAA,IAEA,cAAc,WAAW,QAAQ;AAC/B,UAAI,OAAO,aAAa,aAAa;AACnC,iBAAS,cAAc,IAAI,YAAY,WAAW,EAAE,OAAO,CAAC,CAAC;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AAGO,MAAM,uBAAuB,IAAI,qBAAqB;AAG7D,MAAO,iCAAQ;", "names": []}