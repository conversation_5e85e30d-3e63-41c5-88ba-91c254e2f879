"use strict";
(() => {
  var __defProp = Object.defineProperty;
  var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

  // bin/live-reload.js
  new EventSource(`${"http://localhost:3000"}/esbuild`).addEventListener("change", () => location.reload());

  // src/modules/simple-sync.js
  var SimpleSyncSystem = class {
    static {
      __name(this, "SimpleSyncSystem");
    }
    constructor() {
      this.pairs = [];
      this.maxBarHeight = 45;
      this.isInitialized = false;
      this.patrimonySyncSystem = null;
    }
    async init() {
      try {
        this.setupPatrimonySyncReference();
        this.findPairs();
        this.setupListeners();
        this.setupBudgetListeners();
        document.addEventListener("patrimonySystemReady", () => {
          this.syncAllFromCachedValues();
        });
        setTimeout(() => {
          this.syncAllPairs();
        }, 100);
        this.isInitialized = true;
      } catch (error) {
        console.error("\u274C Simple sync initialization failed:", error);
      }
    }
    setupPatrimonySyncReference() {
      if (window.ReinoCalculator?.data?.patrimony) {
        this.patrimonySyncSystem = window.ReinoCalculator.data.patrimony;
      } else {
        document.addEventListener("reinoCalculatorReady", (event) => {
          this.patrimonySyncSystem = event.detail.systems.patrimonySync;
        });
      }
    }
    setupBudgetListeners() {
      document.addEventListener("allocationStatusChanged", (event) => {
        this.handleBudgetStatusChange(event.detail);
      });
      document.addEventListener("allocationChanged", (event) => {
        this.handleAllocationChange(event.detail);
      });
    }
    handleBudgetStatusChange(status) {
      this.budgetStatus = status;
    }
    handleAllocationChange(detail) {
      const { category, product, percentage } = detail;
      if (!category || !product) return;
      const pair = this.pairs.find((p) => p.category === category && p.product === product);
      if (pair) {
        if (pair.patrimonio.slider) {
          pair.patrimonio.slider.value = percentage / 100;
        }
        this.syncFromPatrimonio(pair);
      }
    }
    findPairs() {
      const patrimonioItems = document.querySelectorAll(
        ".patrimonio_interactive_item[ativo-category][ativo-product]"
      );
      patrimonioItems.forEach((patrimonioEl) => {
        const category = patrimonioEl.getAttribute("ativo-category");
        const product = patrimonioEl.getAttribute("ativo-product");
        if (!category || !product) return;
        const ativosEl = document.querySelector(
          `.ativos-grafico-item[ativo-category="${category}"][ativo-product="${product}"]`
        );
        if (ativosEl) {
          const pair = {
            patrimonio: {
              element: patrimonioEl,
              input: patrimonioEl.querySelector('[input-settings="receive"]'),
              slider: patrimonioEl.querySelector("range-slider"),
              percentage: patrimonioEl.querySelector(".porcentagem-calculadora")
            },
            ativos: {
              element: ativosEl,
              bar: ativosEl.querySelector(".barra-porcentagem-item"),
              percentage: ativosEl.querySelector(".porcentagem-float-alocacao")
            },
            category,
            product
          };
          if (pair.patrimonio.input && pair.patrimonio.slider && pair.ativos.bar && pair.ativos.percentage) {
            pair.ativos.bar.style.width = "";
            this.pairs.push(pair);
          } else {
            console.warn(`\u26A0\uFE0F Missing elements for pair: ${category} - ${product}`);
          }
        }
      });
    }
    setupListeners() {
      this.pairs.forEach((pair) => {
        pair.patrimonio.input?.addEventListener("input", () => {
          this.syncFromPatrimonio(pair);
        });
        pair.patrimonio.slider?.addEventListener("input", () => {
          this.syncFromPatrimonio(pair);
        });
        pair.patrimonio.slider?.addEventListener("change", () => {
          this.syncFromPatrimonio(pair);
        });
      });
    }
    syncFromPatrimonio(pair) {
      try {
        const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;
        const percentage = sliderValue * 100;
        const barHeight = percentage / 100 * this.maxBarHeight;
        pair.ativos.bar.style.height = `${barHeight}px`;
        pair.ativos.bar.style.width = "";
        const formattedPercentage = `${percentage.toFixed(1)}%`;
        pair.ativos.percentage.textContent = formattedPercentage;
        if (pair.patrimonio.percentage) {
          pair.patrimonio.percentage.textContent = formattedPercentage;
        }
      } catch (error) {
        console.error(`\u274C Sync error for ${pair.category}-${pair.product}:`, error);
      }
    }
    shouldAllowVisualUpdate(pair) {
      return true;
    }
    // Sync all pairs to current values
    syncAllPairs() {
      this.pairs.forEach((pair) => {
        this.syncFromPatrimonio(pair);
      });
    }
    // Force sync all pairs from their current slider/input values (for cache restoration)
    syncAllFromCachedValues() {
      this.pairs.forEach((pair) => {
        if (pair.patrimonio.slider && pair.patrimonio.input) {
          const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;
          this.syncFromPatrimonio(pair);
        }
      });
    }
    // Force sync all visual elements based on current slider values (fix zeroing issues)
    forceSyncFromSliders() {
      this.pairs.forEach((pair) => {
        if (pair.patrimonio.slider) {
          const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;
          const percentage = sliderValue * 100;
          const barHeight = percentage / 100 * this.maxBarHeight;
          pair.ativos.bar.style.height = `${barHeight}px`;
          const formattedPercentage = `${percentage.toFixed(1)}%`;
          pair.ativos.percentage.textContent = formattedPercentage;
          if (pair.patrimonio.percentage) {
            pair.patrimonio.percentage.textContent = formattedPercentage;
          }
        }
      });
    }
    // Public method to update specific pair
    updatePair(category, product, percentage) {
      const pair = this.pairs.find((p) => p.category === category && p.product === product);
      if (pair) {
        pair.patrimonio.slider.value = percentage / 100;
        pair.patrimonio.slider.dispatchEvent(new Event("input", { bubbles: true }));
        this.syncFromPatrimonio(pair);
        return true;
      }
      console.warn(`\u26A0\uFE0F Pair not found: ${category} - ${product}`);
      return false;
    }
    // Update pair by percentage value
    updatePairByValue(category, product, value, totalValue) {
      if (totalValue > 0) {
        const percentage = value / totalValue * 100;
        return this.updatePair(category, product, percentage);
      }
      return false;
    }
    // Reset all pairs to 0%
    resetAll() {
      this.pairs.forEach((pair) => {
        pair.patrimonio.slider.value = 0;
        this.syncFromPatrimonio(pair);
      });
    }
    // Get current allocations
    getAllocations() {
      return this.pairs.map((pair) => ({
        category: pair.category,
        product: pair.product,
        percentage: parseFloat(pair.patrimonio.slider.value) * 100,
        value: parseFloat(pair.patrimonio.input.value) || 0
      }));
    }
    // Get total allocated percentage
    getTotalAllocatedPercentage() {
      return this.pairs.reduce((total, pair) => {
        return total + (parseFloat(pair.patrimonio.slider.value) * 100 || 0);
      }, 0);
    }
    // Debug method to check bar dimensions
    debugBarDimensions() {
      this.pairs.forEach((pair) => {
      });
    }
    // Debug method to check budget integration
    debugBudgetIntegration() {
      if (this.patrimonySyncSystem) {
      }
    }
    // Debug method to validate all element pairings
    debugPairings() {
      console.log("\u{1F50D} SimpleSyncSystem Pairing Analysis:");
      console.log(`\u{1F4CA} Total pairs found: ${this.pairs.length}`);
      if (this.pairs.length === 0) {
        console.warn("\u274C No pairs found! Checking for pairing issues...");
        this.debugUnpairedElements();
        return;
      }
      this.pairs.forEach((pair, index) => {
        console.log(`
\u{1F517} Pair ${index + 1}:`);
        console.log(`  Category: "${pair.category}"`);
        console.log(`  Product: "${pair.product}"`);
        console.log(`  Patrimonio element: ${pair.patrimonio.element ? "\u2705" : "\u274C"}`);
        console.log(`  Ativos element: ${pair.ativos.element ? "\u2705" : "\u274C"}`);
        console.log(`  Input: ${pair.patrimonio.input ? "\u2705" : "\u274C"}`);
        console.log(`  Slider: ${pair.patrimonio.slider ? "\u2705" : "\u274C"}`);
        console.log(`  Bar: ${pair.ativos.bar ? "\u2705" : "\u274C"}`);
        console.log(`  Percentage: ${pair.ativos.percentage ? "\u2705" : "\u274C"}`);
        if (pair.patrimonio.slider) {
          const sliderValue = parseFloat(pair.patrimonio.slider.value) || 0;
          const percentage = sliderValue * 100;
          console.log(`  Current allocation: ${percentage.toFixed(1)}%`);
        }
      });
    }
    // Debug method to find unpaired elements
    debugUnpairedElements() {
      console.log("\n\u{1F50D} Analyzing Unpaired Elements:");
      const patrimonioElements = document.querySelectorAll(
        ".patrimonio_interactive_item[ativo-category][ativo-product]"
      );
      const ativosElements = document.querySelectorAll(
        ".ativos-grafico-item[ativo-category][ativo-product]"
      );
      console.log(`
\u{1F4CB} Found Elements:`);
      console.log(`  Patrimonio elements: ${patrimonioElements.length}`);
      console.log(`  Ativos elements: ${ativosElements.length}`);
      console.log(`
\u{1F4BC} PATRIMONIO ELEMENTS:`);
      patrimonioElements.forEach((el, index) => {
        const category = el.getAttribute("ativo-category");
        const product = el.getAttribute("ativo-product");
        const hasPair = this.pairs.some((p) => p.category === category && p.product === product);
        console.log(
          `  ${index + 1}. "${category}" + "${product}" ${hasPair ? "\u2705 PAIRED" : "\u274C UNPAIRED"}`
        );
      });
      console.log(`
\u{1F4CA} ATIVOS ELEMENTS:`);
      ativosElements.forEach((el, index) => {
        const category = el.getAttribute("ativo-category");
        const product = el.getAttribute("ativo-product");
        const hasPair = this.pairs.some((p) => p.category === category && p.product === product);
        console.log(
          `  ${index + 1}. "${category}" + "${product}" ${hasPair ? "\u2705 PAIRED" : "\u274C UNPAIRED"}`
        );
      });
      console.log(`
\u{1F50D} MISMATCH ANALYSIS:`);
      const patrimonioAttribs = Array.from(patrimonioElements).map((el) => ({
        category: el.getAttribute("ativo-category"),
        product: el.getAttribute("ativo-product"),
        type: "patrimonio"
      }));
      const ativosAttribs = Array.from(ativosElements).map((el) => ({
        category: el.getAttribute("ativo-category"),
        product: el.getAttribute("ativo-product"),
        type: "ativos"
      }));
      patrimonioAttribs.forEach((p) => {
        const hasMatch = ativosAttribs.some(
          (a) => a.category === p.category && a.product === p.product
        );
        if (!hasMatch) {
          console.warn(`\u274C PATRIMONIO ORPHAN: "${p.category}" + "${p.product}"`);
          const similarMatches = ativosAttribs.filter(
            (a) => a.category.toLowerCase() === p.category.toLowerCase() || a.product.toLowerCase() === p.product.toLowerCase()
          );
          if (similarMatches.length > 0) {
            console.log(`   \u{1F50D} Possible matches:`);
            similarMatches.forEach((match) => {
              console.log(`     - "${match.category}" + "${match.product}"`);
            });
          }
        }
      });
      ativosAttribs.forEach((a) => {
        const hasMatch = patrimonioAttribs.some(
          (p) => p.category === a.category && p.product === a.product
        );
        if (!hasMatch) {
          console.warn(`\u274C ATIVOS ORPHAN: "${a.category}" + "${a.product}"`);
        }
      });
    }
    // Debug method to test synchronization
    debugSyncTest() {
      console.log("\u{1F9EA} Testing Synchronization for All Pairs:");
      if (this.pairs.length === 0) {
        console.warn("\u274C No pairs to test!");
        return;
      }
      this.pairs.forEach((pair, index) => {
        console.log(`
\u{1F9EA} Testing Pair ${index + 1}: ${pair.category} - ${pair.product}`);
        const testValues = [0, 0.25, 0.5, 0.75, 1];
        testValues.forEach((testValue) => {
          const percentage = testValue * 100;
          if (pair.patrimonio.slider) {
            pair.patrimonio.slider.value = testValue;
            this.syncFromPatrimonio(pair);
            const barHeight = pair.ativos.bar ? pair.ativos.bar.style.height : "N/A";
            const displayedPercentage = pair.ativos.percentage ? pair.ativos.percentage.textContent : "N/A";
            console.log(`  ${percentage}% \u2192 Bar: ${barHeight}, Display: ${displayedPercentage}`);
          }
        });
        if (pair.patrimonio.slider) {
          pair.patrimonio.slider.value = 0;
          this.syncFromPatrimonio(pair);
        }
      });
    }
    // Debug method to verify cache restoration
    debugCacheSync() {
      console.log("\u{1F50D} CACHE SYNCHRONIZATION ANALYSIS");
      console.log("=".repeat(50));
      if (this.pairs.length === 0) {
        console.warn("\u274C No pairs available for cache analysis!");
        return;
      }
      console.log(`\u{1F4CA} Analyzing ${this.pairs.length} pairs for cache sync status:`);
      console.log("\nPair | Input Value | Slider % | Bar Height | Display %");
      console.log("-".repeat(65));
      this.pairs.forEach((pair, index) => {
        const inputValue = pair.patrimonio.input?.value || "0";
        const sliderValue = parseFloat(pair.patrimonio.slider?.value || 0) * 100;
        const barHeight = pair.ativos.bar?.style.height || "0px";
        const displayText = pair.ativos.percentage?.textContent || "0%";
        console.log(
          `${(index + 1).toString().padStart(4)} | ${inputValue.padEnd(11)} | ${sliderValue.toFixed(1).padStart(8)}% | ${barHeight.padStart(10)} | ${displayText.padStart(9)}`
        );
      });
      console.log("\n\u{1F50D} SYNC ISSUES DETECTED:");
      let issuesFound = 0;
      this.pairs.forEach((pair, index) => {
        const sliderValue = parseFloat(pair.patrimonio.slider?.value || 0) * 100;
        const displayText = pair.ativos.percentage?.textContent || "0%";
        const displayValue = parseFloat(displayText.replace("%", "")) || 0;
        const barHeight = pair.ativos.bar?.style.height || "0px";
        const barValue = parseFloat(barHeight.replace("px", "")) || 0;
        const expectedBarHeight = sliderValue / 100 * this.maxBarHeight;
        if (Math.abs(sliderValue - displayValue) > 0.1) {
          console.warn(
            `\u274C Pair ${index + 1}: Slider (${sliderValue.toFixed(1)}%) \u2260 Display (${displayValue.toFixed(1)}%)`
          );
          issuesFound++;
        }
        if (Math.abs(barValue - expectedBarHeight) > 1) {
          console.warn(
            `\u274C Pair ${index + 1}: Bar height (${barValue}px) \u2260 Expected (${expectedBarHeight.toFixed(1)}px)`
          );
          issuesFound++;
        }
      });
      if (issuesFound === 0) {
        console.log("\u2705 All pairs are properly synchronized!");
      } else {
        console.warn(`\u26A0\uFE0F Found ${issuesFound} synchronization issues`);
        console.log("\n\u{1F527} Try running: ReinoCalculator.data.sync.syncAllFromCachedValues()");
      }
    }
    // Debug method to check all system status
    debugFullStatus() {
      console.log("\u{1F50D} COMPLETE SimpleSyncSystem STATUS REPORT");
      console.log("=".repeat(50));
      console.log(`\u{1F4CA} Initialization: ${this.isInitialized ? "\u2705 Complete" : "\u274C Failed"}`);
      console.log(`\u{1F4CA} Pairs found: ${this.pairs.length}`);
      console.log(`\u{1F4CA} Max bar height: ${this.maxBarHeight}px`);
      console.log("\n\u{1F4B0} BUDGET INTEGRATION:");
      this.debugBudgetIntegration();
      console.log("\n\u{1F517} PAIRING ANALYSIS:");
      this.debugPairings();
      if (this.pairs.length === 0) {
        this.debugUnpairedElements();
      }
      console.log("\n\u{1F4C8} CURRENT ALLOCATIONS:");
      const allocations = this.getAllocations();
      const totalPercentage = this.getTotalAllocatedPercentage();
      console.log(`\u{1F4CA} Total allocated: ${totalPercentage.toFixed(1)}%`);
      allocations.forEach((alloc) => {
        console.log(
          `  \u2022 ${alloc.category} - ${alloc.product}: ${alloc.percentage.toFixed(1)}% (R$ ${alloc.value.toFixed(2)})`
        );
      });
      console.log("\n=".repeat(50));
      console.log("\u{1F3AF} Use these methods for specific debugging:");
      console.log("  \u2022 window.ReinoCalculator.data.sync.debugPairings()");
      console.log("  \u2022 window.ReinoCalculator.data.sync.debugUnpairedElements()");
      console.log("  \u2022 window.ReinoCalculator.data.sync.debugSyncTest()");
      console.log("  \u2022 window.ReinoCalculator.data.sync.debugBudgetIntegration()");
      console.log("  \u2022 window.ReinoCalculator.data.sync.debugCacheSync() \u2190 Cache Analysis");
      console.log(
        "  \u2022 window.ReinoCalculator.data.sync.debugZeroingIssues() \u2190 Detect zeroing problems"
      );
    }
    // Debug method to detect visual element zeroing issues
    debugZeroingIssues() {
      console.log("\u{1F50D} VISUAL ELEMENT ZEROING DETECTION");
      console.log("=".repeat(50));
      if (this.pairs.length === 0) {
        console.warn("\u274C No pairs available for zeroing analysis!");
        return;
      }
      console.log("Checking for elements that should have values but show 0%...\n");
      let zeroingIssues = 0;
      this.pairs.forEach((pair, index) => {
        const sliderValue = parseFloat(pair.patrimonio.slider?.value || 0) * 100;
        const inputValue = pair.patrimonio.input?.value || "0";
        const barHeight = pair.ativos.bar?.style.height || "0px";
        const displayText = pair.ativos.percentage?.textContent || "0%";
        const barHeightNum = parseFloat(barHeight.replace("px", "")) || 0;
        const displayPercent = parseFloat(displayText.replace("%", "")) || 0;
        const hasInputValue = inputValue !== "0" && inputValue !== "0,00" && inputValue !== "";
        const hasSliderValue = sliderValue > 0;
        const hasVisualValue = barHeightNum > 0 && displayPercent > 0;
        if ((hasInputValue || hasSliderValue) && !hasVisualValue) {
          console.warn(`\u274C ZEROING DETECTED - ${pair.category} - ${pair.product}:`);
          console.warn(`   Input: ${inputValue} | Slider: ${sliderValue.toFixed(1)}%`);
          console.warn(`   Bar: ${barHeight} | Display: ${displayText}`);
          console.warn(
            `   Expected bar height: ${(sliderValue / 100 * this.maxBarHeight).toFixed(1)}px`
          );
          zeroingIssues++;
        } else if (hasVisualValue) {
          console.log(`\u2705 ${pair.category} - ${pair.product}: ${displayText} (${barHeight})`);
        }
      });
      console.log(`
\u{1F4CA} SUMMARY:`);
      console.log(`Total pairs: ${this.pairs.length}`);
      console.log(`Zeroing issues: ${zeroingIssues}`);
      if (zeroingIssues > 0) {
        console.warn(`\u26A0\uFE0F Found ${zeroingIssues} elements with zeroing issues!`);
        console.log("\n\u{1F527} Suggested fixes:");
        console.log("1. Run: ReinoCalculator.data.sync.syncAllFromCachedValues()");
        console.log("2. Check budget constraints affecting visual updates");
        console.log("3. Verify slider and input values are properly synchronized");
      } else {
        console.log("\u2705 No zeroing issues detected!");
      }
    }
    // Cleanup method
    cleanup() {
      this.pairs = [];
      this.isInitialized = false;
      console.log("\u{1F9F9} Simple sync cleaned up");
    }
    // Get system status
    getStatus() {
      return {
        initialized: this.isInitialized,
        pairs: this.pairs.length,
        totalAllocated: this.getTotalAllocatedPercentage().toFixed(1) + "%",
        patrimonySyncConnected: !!this.patrimonySyncSystem,
        budgetRespecting: true
        // Now respects budget constraints
      };
    }
  };
  var simple_sync_default = SimpleSyncSystem;
})();
//# sourceMappingURL=simple-sync.js.map
